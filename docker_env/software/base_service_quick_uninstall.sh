#!/bin/bash

echo "=== 智能管理系统服务卸载脚本 ==="
echo "⚠️  警告：此脚本将完全删除 MySQL、Redis、MinIO 和 Node.js"
echo "⚠️  这将删除所有数据库数据和配置文件！"
echo ""

# 检测操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if [ -f /etc/debian_version ]; then
        OS="debian"
        echo "检测到 Debian/Ubuntu 系统"
    elif [ -f /etc/redhat-release ]; then
        OS="redhat"
        echo "检测到 RedHat/CentOS 系统"
    else
        OS="linux"
        echo "检测到 Linux 系统"
    fi
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    echo "检测到 macOS 系统"
else
    echo "不支持的操作系统: $OSTYPE"
    exit 1
fi

# 确认删除
echo ""
read -p "确定要继续删除所有服务吗？(输入 'YES' 确认): " confirm
if [ "$confirm" != "YES" ]; then
    echo "取消删除操作"
    exit 0
fi

echo ""
echo "开始删除服务..."

# 删除 MySQL
echo ""
echo "=== 删除 MySQL ==="
if [ "$OS" = "debian" ]; then
    echo "停止 MySQL 服务..."
    sudo systemctl stop mysql 2>/dev/null || true
    sudo systemctl disable mysql 2>/dev/null || true
    
    echo "删除 MySQL 软件包..."
    sudo apt-get remove --purge -y mysql-server mysql-client mysql-common mysql-server-core-* mysql-client-core-*
    sudo apt-get autoremove -y
    sudo apt-get autoclean
    
    echo "删除 MySQL 数据和配置文件..."
    sudo rm -rf /var/lib/mysql
    sudo rm -rf /var/log/mysql
    sudo rm -rf /etc/mysql
    sudo rm -rf /usr/lib/mysql
    
elif [ "$OS" = "redhat" ]; then
    echo "停止 MySQL 服务..."
    sudo systemctl stop mysqld 2>/dev/null || true
    sudo systemctl disable mysqld 2>/dev/null || true
    
    echo "删除 MySQL 软件包..."
    sudo yum remove -y mysql-community-server mysql-community-client mysql-community-common
    sudo yum remove -y mysql80-community-release
    
    echo "删除 MySQL 数据和配置文件..."
    sudo rm -rf /var/lib/mysql
    sudo rm -rf /var/log/mysqld.log
    sudo rm -rf /etc/my.cnf
    sudo rm -rf /etc/my.cnf.d
    
elif [ "$OS" = "macos" ]; then
    echo "停止 MySQL 服务..."
    brew services stop mysql 2>/dev/null || true
    
    echo "删除 MySQL..."
    brew uninstall mysql 2>/dev/null || true
    
    echo "删除 MySQL 数据文件..."
    rm -rf /usr/local/var/mysql
    rm -rf /usr/local/etc/my.cnf
fi

# 删除 MySQL 用户和组
sudo userdel mysql 2>/dev/null || true
sudo groupdel mysql 2>/dev/null || true

echo "✓ MySQL 删除完成"

# 删除 Redis
echo ""
echo "=== 删除 Redis ==="
if [ "$OS" = "debian" ]; then
    echo "停止 Redis 服务..."
    sudo systemctl stop redis-server 2>/dev/null || true
    sudo systemctl disable redis-server 2>/dev/null || true
    
    echo "删除 Redis 软件包..."
    sudo apt-get remove --purge -y redis-server redis-tools
    sudo apt-get autoremove -y
    
    echo "删除 Redis 数据和配置文件..."
    sudo rm -rf /var/lib/redis
    sudo rm -rf /etc/redis
    sudo rm -rf /var/log/redis
    
elif [ "$OS" = "redhat" ]; then
    echo "停止 Redis 服务..."
    sudo systemctl stop redis 2>/dev/null || true
    sudo systemctl disable redis 2>/dev/null || true
    
    echo "删除 Redis 软件包..."
    sudo yum remove -y redis
    
    echo "删除 Redis 数据和配置文件..."
    sudo rm -rf /var/lib/redis
    sudo rm -rf /etc/redis.conf
    sudo rm -rf /var/log/redis
    
elif [ "$OS" = "macos" ]; then
    echo "停止 Redis 服务..."
    brew services stop redis 2>/dev/null || true
    
    echo "删除 Redis..."
    brew uninstall redis 2>/dev/null || true
    
    echo "删除 Redis 数据文件..."
    rm -rf /usr/local/var/db/redis
    rm -rf /usr/local/etc/redis.conf
fi

# 删除 Redis 用户
sudo userdel redis 2>/dev/null || true

echo "✓ Redis 删除完成"

# 删除 MinIO
echo ""
echo "=== 删除 MinIO ==="

# 停止 MinIO 服务
if [ -f /etc/systemd/system/minio.service ]; then
    sudo systemctl stop minio 2>/dev/null || true
    sudo systemctl disable minio 2>/dev/null || true
    sudo rm -f /etc/systemd/system/minio.service
    sudo systemctl daemon-reload
fi

# 删除 MinIO 二进制文件
sudo rm -f /usr/local/bin/minio

# 删除 MinIO 数据和配置
sudo rm -rf /data/minio
sudo rm -rf /etc/default/minio
rm -rf ~/minio-data

# 删除 MinIO 用户
sudo userdel minio-user 2>/dev/null || true

# macOS 使用 brew 删除
if [ "$OS" = "macos" ]; then
    brew uninstall minio 2>/dev/null || true
fi

echo "✓ MinIO 删除完成"

# 删除 Node.js
echo ""
echo "=== 删除 Node.js ==="
if [ "$OS" = "debian" ]; then
    echo "删除 Node.js 软件包..."
    sudo apt-get remove --purge -y nodejs npm
    sudo apt-get autoremove -y
    
    # 删除 NodeSource 仓库
    sudo rm -f /etc/apt/sources.list.d/nodesource.list
    sudo apt-key del 68576280 2>/dev/null || true
    
elif [ "$OS" = "redhat" ]; then
    echo "删除 Node.js 软件包..."
    sudo yum remove -y nodejs npm
    
    # 删除 NodeSource 仓库
    sudo rm -f /etc/yum.repos.d/nodesource-*.repo
    
elif [ "$OS" = "macos" ]; then
    echo "删除 Node.js..."
    brew uninstall node 2>/dev/null || true
    brew uninstall node@18 2>/dev/null || true
fi

# 删除全局 npm 包和缓存
rm -rf ~/.npm
rm -rf ~/.nvm
rm -rf /usr/local/lib/node_modules
sudo rm -rf /usr/local/bin/npm
sudo rm -rf /usr/local/bin/npx
sudo rm -rf /usr/local/bin/node
sudo rm -rf /usr/local/bin/pnpm
sudo rm -rf /usr/local/share/man/man1/node*


echo "✓ Node.js 删除完成"

# 清理系统
echo ""
echo "=== 清理系统 ==="

if [ "$OS" = "debian" ]; then
    sudo apt-get autoremove -y
    sudo apt-get autoclean
elif [ "$OS" = "redhat" ]; then
    sudo yum autoremove -y
    sudo yum clean all
elif [ "$OS" = "macos" ]; then
    brew cleanup
fi