#!/bin/bash

echo "=== 智能管理系统快速安装脚本 ==="
echo "此脚本将帮助您安装所需的环境和依赖"
echo ""

# 检测操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if [ -f /etc/debian_version ]; then
        OS="debian"
        echo "检测到 Debian/Ubuntu 系统"
    elif [ -f /etc/redhat-release ]; then
        OS="redhat"
        echo "检测到 RedHat/CentOS 系统"
    else
        OS="linux"
        echo "检测到 Linux 系统"
    fi
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    echo "检测到 macOS 系统"
else
    echo "不支持的操作系统: $OSTYPE"
    exit 1
fi

# 检查是否以 root 权限运行
if [[ $EUID -eq 0 ]]; then
   echo "请不要以 root 权限运行此脚本"
   exit 1
fi

echo ""
echo "=== 安装基础依赖 ==="

# 更新包管理器
if [ "$OS" = "debian" ]; then
    echo "更新 apt 包列表..."
    sudo apt update
elif [ "$OS" = "redhat" ]; then
    echo "更新 yum 包列表..."
    sudo yum update -y
elif [ "$OS" = "macos" ]; then
    if ! command -v brew &> /dev/null; then
        echo "安装 Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    echo "更新 Homebrew..."
    brew update
fi

# 安装 MySQL
echo ""
echo "=== 安装 MySQL ==="
if ! command -v mysql &> /dev/null; then
    if [ "$OS" = "debian" ]; then
        sudo apt install -y mysql-server mysql-client
        sudo systemctl start mysql
        sudo systemctl enable mysql
    elif [ "$OS" = "redhat" ]; then
        sudo yum install -y https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
        sudo yum install -y mysql-community-server
        sudo systemctl start mysqld
        sudo systemctl enable mysqld
    elif [ "$OS" = "macos" ]; then
        brew install mysql
        brew services start mysql
    fi
    echo "✓ MySQL 安装完成"
else
    echo "✓ MySQL 已安装"
fi

# 安装 Redis
echo ""
echo "=== 安装 Redis ==="
if ! command -v redis-server &> /dev/null && ! command -v redis-cli &> /dev/null; then
    if [ "$OS" = "debian" ]; then
        sudo apt install -y redis-server
        # 配置 Redis 密码
        sudo sed -i 's/# requirepass foobared/requirepass root/' /etc/redis/redis.conf
        sudo systemctl start redis-server
        sudo systemctl enable redis-server
    elif [ "$OS" = "redhat" ]; then
        sudo yum install -y epel-release
        sudo yum install -y redis
        # 配置 Redis 密码
        sudo sed -i 's/# requirepass foobared/requirepass root/' /etc/redis.conf
        sudo systemctl start redis
        sudo systemctl enable redis
    elif [ "$OS" = "macos" ]; then
        brew install redis
        # macOS Redis 配置
        echo "requirepass root" >> /usr/local/etc/redis.conf
        brew services start redis
    fi
    echo "✓ Redis 安装完成，密码设置为: root"
else
    echo "✓ Redis 已安装"
    # 检查是否已设置密码
    if ! redis-cli -a root ping &> /dev/null; then
        echo "配置 Redis 密码..."
        if [ "$OS" = "debian" ]; then
            sudo sed -i 's/# requirepass foobared/requirepass root/' /etc/redis/redis.conf
            sudo systemctl restart redis-server
        elif [ "$OS" = "redhat" ]; then
            sudo sed -i 's/# requirepass foobared/requirepass root/' /etc/redis.conf
            sudo systemctl restart redis
        elif [ "$OS" = "macos" ]; then
            echo "requirepass root" >> /usr/local/etc/redis.conf
            brew services restart redis
        fi
        echo "✓ Redis 密码配置完成"
    fi
fi

# 安装 MinIO
echo ""
echo "=== 安装 MinIO ==="
if ! command -v minio &> /dev/null; then
    if [ "$OS" = "macos" ]; then
        brew install minio/stable/minio
    else
        # Linux 系统
        wget -q https://dl.min.io/server/minio/release/linux-amd64/minio -O /tmp/minio
        chmod +x /tmp/minio
        sudo mv /tmp/minio /usr/local/bin/
        
        # 创建 minio 用户和数据目录
        sudo useradd -r minio-user -s /sbin/nologin 2>/dev/null || true
        sudo mkdir -p /data/minio
        sudo chown minio-user:minio-user /data/minio
    fi
    echo "✓ MinIO 安装完成"
else
    echo "✓ MinIO 已安装"
fi

# 安装 Node.js
echo ""
echo "=== 安装 Node.js ==="
if ! command -v node &> /dev/null; then
    if [ "$OS" = "debian" ]; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    elif [ "$OS" = "redhat" ]; then
        curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
        sudo yum install -y nodejs
    elif [ "$OS" = "macos" ]; then
        brew install node@18
    fi
    echo "✓ Node.js 安装完成"
else
    echo "✓ Node.js 已安装"
fi

# 安装 pnpm
echo ""
echo "=== 安装 pnpm ==="
if ! command -v pnpm &> /dev/null; then
    npm install -g pnpm
    echo "✓ pnpm 安装完成"
else
    echo "✓ pnpm 已安装"
fi


echo "=== 配置数据库 ==="
echo "正在配置 MySQL 数据库..."

# 创建数据库配置脚本
cat > /tmp/setup_db.sql << EOF
-- 设置 root 密码为 root
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'root';
-- 创建数据库
CREATE DATABASE IF NOT EXISTS aidata CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- 创建用户，密码设置为 aiuser
CREATE USER IF NOT EXISTS 'aiuser'@'localhost' IDENTIFIED BY 'aiuser';
GRANT ALL PRIVILEGES ON aidata.* TO 'aiuser'@'localhost';
FLUSH PRIVILEGES;
EOF

# 尝试执行数据库配置
if mysql -u root < /tmp/setup_db.sql 2>/dev/null; then
    echo "✓ 数据库配置完成"
else
    echo "⚠ 数据库配置失败，请手动执行以下 SQL："
    cat /tmp/setup_db.sql
fi

rm -f /tmp/setup_db.sql

echo ""
echo "=== 安装完成 ==="
echo "环境安装完成！服务配置信息："
echo ""
echo "📊 数据库配置:"
echo "   MySQL root 密码: root"
echo "   数据库: aidata"
echo "   用户: aiuser / 密码: aiuser"
echo ""
echo "🔄 缓存配置:"
echo "   Redis 密码: root"
echo ""
echo "📁 存储配置:"
echo "   MinIO 用户名: minioadmin"
echo "   MinIO 密码: minioadmin"
echo ""
echo "🚀 接下来的步骤:"
echo "1. 启动基础服务: ./start_services.sh"
echo "2. 检查服务状态: ./check_services.sh"
echo "3. 启动开发环境: ./start_dev.sh"
echo ""
echo "🌐 访问地址:"
echo "   前端应用: http://localhost:7789"
echo "   后端API: http://localhost:8000"
echo "   MinIO控制台: http://localhost:9001"
echo ""
echo "如有问题，请查看 '环境安装和服务管理文档.md'"
