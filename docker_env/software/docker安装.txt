1、安装 docker 和 docker-compose
docker-20.10.9.tgz 和 docker-compose-linux-x86_64、docker.service 三个安装文件
# 解压 docker 到当前目录
$ tar -xvf docker-23.0.3.tgz

# 将 docker 文件移动到 /usr/bin 目录下
$ cp -p docker/* /usr/bin

# 将 docker-compose 文件复制到 /usr/local/bin/ 目录下，并重命名为 docker-compose
$ cp docker-compose-linux-x86_64 /usr/local/bin/docker-compose

# 设置 docker-compose 文件权限
$ chmod +x /usr/local/bin/docker-compose

# 将 docker.service 移到 /etc/systemd/system/ 目录
$ cp docker.service /etc/systemd/system/

# 设置 docker.service 文件权限
$ chmod +x /etc/systemd/system/docker.service

# 重新加载配置文件
$ systemctl daemon-reload

# 启动docker
$ systemctl start docker

# 设置 docker 开机自启
$ systemctl enable docker.service

2.4.2 验证安装是否成功
查看 docker 版本
$ docker -v
查看 docker-compose 版本
$ docker-compose -v

========================================================
以上是安装docker，如果系统有docker了就不需要安装了

