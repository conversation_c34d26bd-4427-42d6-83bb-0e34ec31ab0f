/*
 Navicat Premium Data Transfer

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3306
 Source Schema         : aidata

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 03/07/2025 18:51:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_dataset_aidataset
-- ----------------------------
DROP TABLE IF EXISTS `app_dataset_aidataset`;
CREATE TABLE `app_dataset_aidataset`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `group` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `minio_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `stars` int NOT NULL,
  `downloads` int NOT NULL,
  `parameters` json NOT NULL,
  `metrics` json NOT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `current_version_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `app_dataset_aidatase_current_version_id_89beb0c8_fk_app_datas`(`current_version_id` ASC) USING BTREE,
  INDEX `app_dataset_aidataset_creator_id_3c6e1068`(`creator_id` ASC) USING BTREE,
  CONSTRAINT `app_dataset_aidatase_current_version_id_89beb0c8_fk_app_datas` FOREIGN KEY (`current_version_id`) REFERENCES `app_dataset_datasetversion` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_dataset_aidataset
-- ----------------------------
INSERT INTO `app_dataset_aidataset` VALUES (28986891832337, '************', '2025-07-03 18:39:55.901453', '2025-07-03 16:27:57.888452', '测试数据', 'zngl', 'asd', 'zngl/test', 0, 0, '{}', '{}', 'online', ************, NULL);
INSERT INTO `app_dataset_aidataset` VALUES (28986997114154, '************', '2025-07-03 18:13:06.771273', '2025-07-03 16:28:23.592551', '智能侦照', 'kmg', 'qwe', 'kmg/test2', 0, 0, '{}', '{}', 'online', ************, NULL);

-- ----------------------------
-- Table structure for app_dataset_aidataset_categories
-- ----------------------------
DROP TABLE IF EXISTS `app_dataset_aidataset_categories`;
CREATE TABLE `app_dataset_aidataset_categories`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `aidataset_id` bigint NOT NULL,
  `datasetcategory_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `app_dataset_aidataset_ca_aidataset_id_datasetcate_f46c9796_uniq`(`aidataset_id` ASC, `datasetcategory_id` ASC) USING BTREE,
  INDEX `app_dataset_aidatase_datasetcategory_id_842a01e3_fk_app_datas`(`datasetcategory_id` ASC) USING BTREE,
  CONSTRAINT `app_dataset_aidatase_aidataset_id_b1c279b9_fk_app_datas` FOREIGN KEY (`aidataset_id`) REFERENCES `app_dataset_aidataset` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `app_dataset_aidatase_datasetcategory_id_842a01e3_fk_app_datas` FOREIGN KEY (`datasetcategory_id`) REFERENCES `app_dataset_datasetcategory` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_dataset_aidataset_categories
-- ----------------------------

-- ----------------------------
-- Table structure for app_dataset_datasetcategory
-- ----------------------------
DROP TABLE IF EXISTS `app_dataset_datasetcategory`;
CREATE TABLE `app_dataset_datasetcategory`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `order` int NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `parent_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `app_dataset_datasetcategory_creator_id_67aa37cd`(`creator_id` ASC) USING BTREE,
  INDEX `app_dataset_datasetcategory_parent_id_75fb01bf`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_dataset_datasetcategory
-- ----------------------------
INSERT INTO `app_dataset_datasetcategory` VALUES (28970143911552, '************', '2025-07-03 15:19:49.040815', '2025-07-03 15:19:49.040815', '文本', '1', 1, 1, ************, NULL);
INSERT INTO `app_dataset_datasetcategory` VALUES (28970199566339, '************', '2025-07-03 15:20:02.628531', '2025-07-03 15:20:02.628531', '图像', '1', 1, 1, ************, NULL);
INSERT INTO `app_dataset_datasetcategory` VALUES (28970246056291, '************', '2025-07-03 15:20:13.978392', '2025-07-03 15:20:13.978392', '音频', '1', 1, 1, ************, NULL);
INSERT INTO `app_dataset_datasetcategory` VALUES (28970289601641, '************', '2025-07-03 15:20:24.609947', '2025-07-03 15:20:24.609947', '想定', '1', 2, 1, ************, NULL);

-- ----------------------------
-- Table structure for app_dataset_datasetcomment
-- ----------------------------
DROP TABLE IF EXISTS `app_dataset_datasetcomment`;
CREATE TABLE `app_dataset_datasetcomment`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `model_id` bigint NOT NULL,
  `parent_id` bigint NULL DEFAULT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `app_dataset_datasetc_model_id_867de22d_fk_app_datas`(`model_id` ASC) USING BTREE,
  INDEX `app_dataset_datasetc_parent_id_4afe9c1e_fk_app_datas`(`parent_id` ASC) USING BTREE,
  INDEX `app_dataset_datasetcomment_user_id_6c931282_fk_sys_users_id`(`user_id` ASC) USING BTREE,
  INDEX `app_dataset_datasetcomment_creator_id_f4780746`(`creator_id` ASC) USING BTREE,
  CONSTRAINT `app_dataset_datasetc_model_id_867de22d_fk_app_datas` FOREIGN KEY (`model_id`) REFERENCES `app_dataset_aidataset` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `app_dataset_datasetc_parent_id_4afe9c1e_fk_app_datas` FOREIGN KEY (`parent_id`) REFERENCES `app_dataset_datasetcomment` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `app_dataset_datasetcomment_user_id_6c931282_fk_sys_users_id` FOREIGN KEY (`user_id`) REFERENCES `sys_users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_dataset_datasetcomment
-- ----------------------------

-- ----------------------------
-- Table structure for app_dataset_datasetversion
-- ----------------------------
DROP TABLE IF EXISTS `app_dataset_datasetversion`;
CREATE TABLE `app_dataset_datasetversion`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `version_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `minio_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `metadata` json NOT NULL,
  `changelog` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `is_latest` tinyint(1) NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `dataset_id` bigint NOT NULL,
  `parent_version_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `app_dataset_datasetversi_dataset_id_version_numbe_d8f53612_uniq`(`dataset_id` ASC, `version_number` ASC) USING BTREE,
  INDEX `app_dataset_datasetv_parent_version_id_3bdbd983_fk_app_datas`(`parent_version_id` ASC) USING BTREE,
  INDEX `app_dataset_datasetversion_creator_id_bdd95d10`(`creator_id` ASC) USING BTREE,
  CONSTRAINT `app_dataset_datasetv_dataset_id_1d905939_fk_app_datas` FOREIGN KEY (`dataset_id`) REFERENCES `app_dataset_aidataset` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `app_dataset_datasetv_parent_version_id_3bdbd983_fk_app_datas` FOREIGN KEY (`parent_version_id`) REFERENCES `app_dataset_datasetversion` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_dataset_datasetversion
-- ----------------------------

-- ----------------------------
-- Table structure for app_model_aimodel
-- ----------------------------
DROP TABLE IF EXISTS `app_model_aimodel`;
CREATE TABLE `app_model_aimodel`  (
  `id` bigint NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `group` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `minio_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `embedding_vector` longblob NULL,
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code` ASC) USING BTREE,
  INDEX `app_model_aimodel_creator_id_6614f608`(`creator_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_model_aimodel
-- ----------------------------
INSERT INTO `app_model_aimodel` VALUES (214981219456, ************, '************', '2025-07-01 00:36:04.843979', '2025-05-21 15:44:41.554067', 'yolo12', 'njust-kmg', '# YOLO12简介\n\nYOLO12 引入了一种以注意力为中心的架构，它不同于以往YOLO 模型中使用的基于 CNN 的传统方法，但仍保持了许多应用所必需的实时推理速度。该模型通过对注意力机制和整体网络架构进行新颖的方法创新，实现了最先进的物体检测精度，同时保持了实时性能。 \n\n参考翻译自 https://docs.ultralytics.com/zh/models/yolo12/#overview \n\n## 论文资料list：\n- **论文名：YOLOv12: Attention-Centric Real-Time Object Detectors**\n- 发表机构：美国纽约州立大学布法罗分校和中国科学院大学\n- arxiv：https://arxiv.org/abs/2502.12524\n- Github：https://github.com/sunsmarterjie/yolov12\n  - Colab Notebook ：[train_yolov12_object_detection.ipynb](https://colab.research.google.com/github/pyresearch/notebooks/blob/main/notebook/train_yolov12_object_detection.ipynb)\n  - Blog：[How to Train a YOLOv12 Object Detection Model on a Custom Dataset](https://blog.roboflow.com/train-yolov12-model/)\n  - Youtube：[YOLO12: Train for Real-Time Object Detection](https://youtu.be/dO8k5rgXG0M)\n- ultralytics docs：https://docs.ultralytics.com/models/yolo12/\n- YOLO Master ModelScope YOLO12权重库：https://modelscope.cn/models/yolo_master/YOLO12/summary\n- **YOLO Master Github**: [https://github.com/datawhalechina/yolo-master](https://github.com/datawhalechina/yolo-master )\n- **YOLO Master YOLOv12 YOLO12详解与测试实践文档**： [feishu link](https://wvet00aj34c.feishu.cn/docx/WrBydq19boEHN7xhp7pcLxd7n6f)\n## 主要功能\n\n- **区域注意力机制（Area Attention Mechanism）**：一种新的自我注意方法，能有效处理大的感受野。它可将特征图横向或纵向划分为 l 个大小相等的区域（默认为 4 个），从而避免复杂的操作，并保持较大的有效感受野。与标准自注意相比，这大大降低了计算成本。\n- **残差高效层聚合网络（R-ELAN，Residual Efficient Layer Aggregation Networks）**:基于 ELAN 的改进型特征聚合模块，旨在解决优化难题，尤其是在以注意力为中心的大规模模型中。R-ELAN 引入了\n  - 具有缩放功能的块级残差连接（类似于图层缩放）。\n  - 重新设计的特征聚合方法可创建类似瓶颈的结构。\n- **优化注意力架构**:YOLO12 简化了标准关注机制，以提高效率并与YOLO 框架兼容。这包括\n  - 使用 FlashAttention 尽量减少内存访问开销。\n  - 去除位置编码，使模型更简洁、更快速。\n  - 调整 MLP 比例（从通常的 4 调整为 1.2 或 2），以更好地平衡注意力层和前馈层之间的计算。\n  - 减少堆叠区块的深度，提高优化效果。\n  - 酌情利用卷积运算，提高计算效率。\n  - 在注意力机制中加入 7x7 可分离卷积（\"位置感知器\"），对位置信息进行隐式编码。\n- 全面的任务支持：YOLO12 支持一系列核心计算机视觉任务：物体检测、实例分割、图像分类、姿态估计和定向物体检测 (OBB)。\n- 效率更高：与之前的许多型号相比，以更少的参数实现更高的精度，在速度和精度之间实现了更好的平衡。\n- 灵活部署：专为跨各种平台（从边缘设备到云基础设施）部署而设计。\n\n（数据来源：https://docs.ultralytics.com/zh/models/yolo12/#overview ）\n\n\n\n您可以通过如下git clone命令，或者ModelScope SDK来下载模型\n\nSDK下载\n```bash\n#安装ModelScope\npip install modelscope\n```\n```python\n#SDK模型下载\nfrom modelscope import snapshot_download\nmodel_dir = snapshot_download(\'yolo_master/YOLO12\')\n```\nGit下载\n```\n#Git模型下载\ngit clone https://www.modelscope.cn/yolo_master/YOLO12.git\n```\n<!--\n---\nframeworks:\n- Pytorch\nlicense: Apache License 2.0\ntasks:\n- image-object-detection\n\n#model-type:\n##如 gpt、phi、llama、chatglm、baichuan 等\n#- gpt\n\n#domain:\n##如 nlp、cv、audio、multi-modal\n#- nlp\n\n#language:\n##语言代码列表 https://help.aliyun.com/document_detail/215387.html?spm=a2c4g.11186623.0.0.9f8d7467kni6Aa\n#- cn\n\n#metrics:\n##如 CIDEr、Blue、ROUGE 等\n#- CIDEr\n\n#tags:\n##各种自定义，包括 pretrained、fine-tuned、instruction-tuned、RL-tuned 等训练方法和其他\n#- pretrained\n\n#tools:\n##如 vllm、fastchat、llamacpp、AdaSeq 等\n#- vllm\nlanguage:\n  - zh\ndatasets:\n  - AI-ModelScope/coco\n  - modelscope/COCO2017_Instance_Segmentation\ntags:\n  - YOLO\n  - \'YOLO12 \'\n  - yolo_master\n---\n-->\n\n<p style=\"color: lightgrey;\">如果您是本模型的贡献者，我们邀请您根据<a href=\"https://modelscope.cn/docs/ModelScope%E6%A8%A1%E5%9E%8B%E6%8E%A5%E5%85%A5%E6%B5%81%E7%A8%8B%E6%A6%82%E8%A7%88\" style=\"color: lightgrey; text-decoration: underline;\">模型贡献文档</a>，及时完善模型卡片内容。</p>', 'njust-kmg/yolo12', 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
INSERT INTO `app_model_aimodel` VALUES (220246323840, 541150219354505, '************', '2025-06-16 21:42:08.582186', '2025-05-22 14:35:48.810840', 'deepseek-r1', 'deepseek-ai', '# DeepSeek-R1\n<!-- markdownlint-disable first-line-h1 -->\n<!-- markdownlint-disable html -->\n<!-- markdownlint-disable no-duplicate-header -->\n\n<div align=\"center\">\n  <img src=\"https://github.com/deepseek-ai/DeepSeek-V2/blob/main/figures/logo.svg?raw=true\" width=\"60%\" alt=\"DeepSeek-V3\" />\n</div>\n<hr>\n<div align=\"center\" style=\"line-height: 1;\">\n  <a href=\"https://www.deepseek.com/\" target=\"_blank\" style=\"margin: 2px;\">\n    <img alt=\"Homepage\" src=\"https://github.com/deepseek-ai/DeepSeek-V2/blob/main/figures/badge.svg?raw=true\" style=\"display: inline-block; vertical-align: middle;\"/>\n  </a>\n  <a href=\"https://chat.deepseek.com/\" target=\"_blank\" style=\"margin: 2px;\">\n    <img alt=\"Chat\" src=\"https://img.shields.io/badge/🤖%20Chat-DeepSeek%20R1-536af5?color=536af5&logoColor=white\" style=\"display: inline-block; vertical-align: middle;\"/>\n  </a>\n  <a href=\"https://huggingface.co/deepseek-ai\" target=\"_blank\" style=\"margin: 2px;\">\n    <img alt=\"Hugging Face\" src=\"https://img.shields.io/badge/%F0%9F%A4%97%20Hugging%20Face-DeepSeek%20AI-ffc107?color=ffc107&logoColor=white\" style=\"display: inline-block; vertical-align: middle;\"/>\n  </a>\n</div>\n\n<div align=\"center\" style=\"line-height: 1;\">\n  <a href=\"https://discord.gg/Tc7c45Zzu5\" target=\"_blank\" style=\"margin: 2px;\">\n    <img alt=\"Discord\" src=\"https://img.shields.io/badge/Discord-DeepSeek%20AI-7289da?logo=discord&logoColor=white&color=7289da\" style=\"display: inline-block; vertical-align: middle;\"/>\n  </a>\n  <a href=\"https://github.com/deepseek-ai/DeepSeek-V2/blob/main/figures/qr.jpeg?raw=true\" target=\"_blank\" style=\"margin: 2px;\">\n    <img alt=\"Wechat\" src=\"https://img.shields.io/badge/WeChat-DeepSeek%20AI-brightgreen?logo=wechat&logoColor=white\" style=\"display: inline-block; vertical-align: middle;\"/>\n  </a>\n  <a href=\"https://twitter.com/deepseek_ai\" target=\"_blank\" style=\"margin: 2px;\">\n    <img alt=\"Twitter Follow\" src=\"https://img.shields.io/badge/Twitter-deepseek_ai-white?logo=x&logoColor=white\" style=\"display: inline-block; vertical-align: middle;\"/>\n  </a>\n</div>\n\n<div align=\"center\" style=\"line-height: 1;\">\n  <a href=\"https://github.com/deepseek-ai/DeepSeek-R1/blob/main/LICENSE\" style=\"margin: 2px;\">\n    <img alt=\"License\" src=\"https://img.shields.io/badge/License-MIT-f5de53?&color=f5de53\" style=\"display: inline-block; vertical-align: middle;\"/>\n  </a>\n</div>\n\n\n<p align=\"center\">\n  <a href=\"https://github.com/deepseek-ai/DeepSeek-R1/blob/main/DeepSeek_R1.pdf\"><b>Paper Link</b>👁️</a>\n</p>\n\n\n## 1. Introduction\n\nWe introduce our first-generation reasoning models, DeepSeek-R1-Zero and DeepSeek-R1. \nDeepSeek-R1-Zero, a model trained via large-scale reinforcement learning (RL) without supervised fine-tuning (SFT) as a preliminary step, demonstrated remarkable performance on reasoning.\nWith RL, DeepSeek-R1-Zero naturally emerged with numerous powerful and interesting reasoning behaviors.\nHowever, DeepSeek-R1-Zero encounters challenges such as endless repetition, poor readability, and language mixing. To address these issues and further enhance reasoning performance,\nwe introduce DeepSeek-R1, which incorporates cold-start data before RL.\nDeepSeek-R1 achieves performance comparable to OpenAI-o1 across math, code, and reasoning tasks. \nTo support the research community, we have open-sourced DeepSeek-R1-Zero, DeepSeek-R1, and six dense models distilled from DeepSeek-R1 based on Llama and Qwen. DeepSeek-R1-Distill-Qwen-32B outperforms OpenAI-o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\n**NOTE: Before running DeepSeek-R1 series models locally, we kindly recommend reviewing the [Usage Recommendation](#usage-recommendations) section.**\n\n<p align=\"center\">\n  <img width=\"80%\" src=\"figures/benchmark.jpg\">\n</p>\n\n## 2. Model Summary\n\n---\n\n**Post-Training: Large-Scale Reinforcement Learning on the Base Model**\n\n-  We directly apply reinforcement learning (RL) to the base model without relying on supervised fine-tuning (SFT) as a preliminary step. This approach allows the model to explore chain-of-thought (CoT) for solving complex problems, resulting in the development of DeepSeek-R1-Zero. DeepSeek-R1-Zero demonstrates capabilities such as self-verification, reflection, and generating long CoTs, marking a significant milestone for the research community. Notably, it is the first open research to validate that reasoning capabilities of LLMs can be incentivized purely through RL, without the need for SFT. This breakthrough paves the way for future advancements in this area.\n\n-   We introduce our pipeline to develop DeepSeek-R1. The pipeline incorporates two RL stages aimed at discovering improved reasoning patterns and aligning with human preferences, as well as two SFT stages that serve as the seed for the model\'s reasoning and non-reasoning capabilities.\n    We believe the pipeline will benefit the industry by creating better models. \n\n---\n\n**Distillation: Smaller Models Can Be Powerful Too**\n\n-  We demonstrate that the reasoning patterns of larger models can be distilled into smaller models, resulting in better performance compared to the reasoning patterns discovered through RL on small models. The open source DeepSeek-R1, as well as its API, will benefit the research community to distill better smaller models in the future. \n- Using the reasoning data generated by DeepSeek-R1, we fine-tuned several dense models that are widely used in the research community. The evaluation results demonstrate that the distilled smaller dense models perform exceptionally well on benchmarks. We open-source distilled 1.5B, 7B, 8B, 14B, 32B, and 70B checkpoints based on Qwen2.5 and Llama3 series to the community.\n\n## 3. Model Downloads\n\n### DeepSeek-R1 Models\n\n<div align=\"center\">\n\n| **Model** | **#Total Params** | **#Activated Params** | **Context Length** | **Download** |\n| :------------: | :------------: | :------------: | :------------: | :------------: |\n| DeepSeek-R1-Zero | 671B | 37B | 128K   | [🤗 HuggingFace](https://huggingface.co/deepseek-ai/DeepSeek-R1-Zero)   |\n| DeepSeek-R1   | 671B | 37B |  128K   | [🤗 HuggingFace](https://huggingface.co/deepseek-ai/DeepSeek-R1)   |\n\n</div>\n\nDeepSeek-R1-Zero & DeepSeek-R1 are trained based on DeepSeek-V3-Base. \nFor more details regarding the model architecture, please refer to [DeepSeek-V3](https://github.com/deepseek-ai/DeepSeek-V3) repository.\n\n### DeepSeek-R1-Distill Models\n\n<div align=\"center\">\n\n| **Model** | **Base Model** | **Download** |\n| :------------: | :------------: | :------------: |\n| DeepSeek-R1-Distill-Qwen-1.5B  | [Qwen2.5-Math-1.5B](https://huggingface.co/Qwen/Qwen2.5-Math-1.5B) | [🤗 HuggingFace](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B)   |\n| DeepSeek-R1-Distill-Qwen-7B  | [Qwen2.5-Math-7B](https://huggingface.co/Qwen/Qwen2.5-Math-7B) | [🤗 HuggingFace](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B)   |\n| DeepSeek-R1-Distill-Llama-8B  | [Llama-3.1-8B](https://huggingface.co/meta-llama/Llama-3.1-8B) | [🤗 HuggingFace](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Llama-8B)   |\n| DeepSeek-R1-Distill-Qwen-14B   | [Qwen2.5-14B](https://huggingface.co/Qwen/Qwen2.5-14B) | [🤗 HuggingFace](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Qwen-14B)   |\n|DeepSeek-R1-Distill-Qwen-32B  | [Qwen2.5-32B](https://huggingface.co/Qwen/Qwen2.5-32B) | [🤗 HuggingFace](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Qwen-32B)   |\n| DeepSeek-R1-Distill-Llama-70B  | [Llama-3.3-70B-Instruct](https://huggingface.co/meta-llama/Llama-3.3-70B-Instruct) | [🤗 HuggingFace](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Llama-70B)   |\n\n</div>\n\nDeepSeek-R1-Distill models are fine-tuned based on open-source models, using samples generated by DeepSeek-R1.\nWe slightly change their configs and tokenizers. Please use our setting to run these models.\n\n## 4. Evaluation Results\n\n### DeepSeek-R1-Evaluation\n For all our models, the maximum generation length is set to 32,768 tokens. For benchmarks requiring sampling, we use a temperature of $0.6$, a top-p value of $0.95$, and generate 64 responses per query to estimate pass@1.\n<div align=\"center\">\n\n\n| Category | Benchmark (Metric) | Claude-3.5-Sonnet-1022 | GPT-4o 0513 | DeepSeek V3 | OpenAI o1-mini | OpenAI o1-1217 | DeepSeek R1 |\n|----------|-------------------|----------------------|------------|--------------|----------------|------------|--------------|\n| | Architecture | - | - | MoE | - | - | MoE |\n| | # Activated Params | - | - | 37B | - | - | 37B |\n| | # Total Params | - | - | 671B | - | - | 671B |\n| English | MMLU (Pass@1) | 88.3 | 87.2 | 88.5 | 85.2 | **91.8** | 90.8 |\n| | MMLU-Redux (EM) | 88.9 | 88.0 | 89.1 | 86.7 | - | **92.9** |\n| | MMLU-Pro (EM) | 78.0 | 72.6 | 75.9 | 80.3 | - | **84.0** |\n| | DROP (3-shot F1) | 88.3 | 83.7 | 91.6 | 83.9 | 90.2 | **92.2** |\n| | IF-Eval (Prompt Strict) | **86.5** | 84.3 | 86.1 | 84.8 | - | 83.3 |\n| | GPQA-Diamond (Pass@1) | 65.0 | 49.9 | 59.1 | 60.0 | **75.7** | 71.5 |\n| | SimpleQA (Correct) | 28.4 | 38.2 | 24.9 | 7.0 | **47.0** | 30.1 |\n| | FRAMES (Acc.) | 72.5 | 80.5 | 73.3 | 76.9 | - | **82.5** |\n| | AlpacaEval2.0 (LC-winrate) | 52.0 | 51.1 | 70.0 | 57.8 | - | **87.6** |\n| | ArenaHard (GPT-4-1106) | 85.2 | 80.4 | 85.5 | 92.0 | - | **92.3** |\n| Code | LiveCodeBench (Pass@1-COT) | 33.8 | 34.2 | - | 53.8 | 63.4 | **65.9** |\n| | Codeforces (Percentile) | 20.3 | 23.6 | 58.7 | 93.4 | **96.6** | 96.3 |\n| | Codeforces (Rating) | 717 | 759 | 1134 | 1820 | **2061** | 2029 |\n| | SWE Verified (Resolved) | **50.8** | 38.8 | 42.0 | 41.6 | 48.9 | 49.2 |\n| | Aider-Polyglot (Acc.) | 45.3 | 16.0 | 49.6 | 32.9 | **61.7** | 53.3 |\n| Math | AIME 2024 (Pass@1) | 16.0 | 9.3 | 39.2 | 63.6 | 79.2 | **79.8** |\n| | MATH-500 (Pass@1) | 78.3 | 74.6 | 90.2 | 90.0 | 96.4 | **97.3** |\n| | CNMO 2024 (Pass@1) | 13.1 | 10.8 | 43.2 | 67.6 | - | **78.8** |\n| Chinese | CLUEWSC (EM) | 85.4 | 87.9 | 90.9 | 89.9 | - | **92.8** |\n| | C-Eval (EM) | 76.7 | 76.0 | 86.5 | 68.9 | - | **91.8** |\n| | C-SimpleQA (Correct) | 55.4 | 58.7 | **68.0** | 40.3 | - | 63.7 |\n\n</div>\n\n\n### Distilled Model Evaluation\n\n\n<div align=\"center\">\n\n| Model                                    | AIME 2024 pass@1 | AIME 2024 cons@64 | MATH-500 pass@1 | GPQA Diamond pass@1 | LiveCodeBench pass@1 | CodeForces rating |\n|------------------------------------------|------------------|-------------------|-----------------|----------------------|----------------------|-------------------|\n| GPT-4o-0513                          | 9.3              | 13.4              | 74.6            | 49.9                 | 32.9                 | 759               |\n| Claude-3.5-Sonnet-1022             | 16.0             | 26.7                 | 78.3            | 65.0                 | 38.9                 | 717               |\n| o1-mini                              | 63.6             | 80.0              | 90.0            | 60.0                 | 53.8                 | **1820**          |\n| QwQ-32B-Preview                              | 44.0             | 60.0                 | 90.6            | 54.5               | 41.9                 | 1316              |\n| DeepSeek-R1-Distill-Qwen-1.5B       | 28.9             | 52.7              | 83.9            | 33.8                 | 16.9                 | 954               |\n| DeepSeek-R1-Distill-Qwen-7B          | 55.5             | 83.3              | 92.8            | 49.1                 | 37.6                 | 1189              |\n| DeepSeek-R1-Distill-Qwen-14B         | 69.7             | 80.0              | 93.9            | 59.1                 | 53.1                 | 1481              |\n| DeepSeek-R1-Distill-Qwen-32B        | **72.6**         | 83.3              | 94.3            | 62.1                 | 57.2                 | 1691              |\n| DeepSeek-R1-Distill-Llama-8B         | 50.4             | 80.0              | 89.1            | 49.0                 | 39.6                 | 1205              |\n| DeepSeek-R1-Distill-Llama-70B        | 70.0             | **86.7**          | **94.5**        | **65.2**             | **57.5**             | 1633              |\n\n</div>\n\n\n## 5. Chat Website & API Platform\nYou can chat with DeepSeek-R1 on DeepSeek\'s official website: [chat.deepseek.com](https://chat.deepseek.com), and switch on the button \"DeepThink\"\n\nWe also provide OpenAI-Compatible API at DeepSeek Platform: [platform.deepseek.com](https://platform.deepseek.com/)\n\n## 6. How to Run Locally\n\n### DeepSeek-R1 Models\n\nPlease visit [DeepSeek-V3](https://github.com/deepseek-ai/DeepSeek-V3) repo for more information about running DeepSeek-R1 locally.\n\n**NOTE: Hugging Face\'s Transformers has not been directly supported yet.**\n\n### DeepSeek-R1-Distill Models\n\nDeepSeek-R1-Distill models can be utilized in the same manner as Qwen or Llama models.\n\nFor instance, you can easily start a service using [vLLM](https://github.com/vllm-project/vllm):\n\n```shell\nvllm serve deepseek-ai/DeepSeek-R1-Distill-Qwen-32B --tensor-parallel-size 2 --max-model-len 32768 --enforce-eager\n```\n\nYou can also easily start a service using [SGLang](https://github.com/sgl-project/sglang)\n\n```bash\npython3 -m sglang.launch_server --model deepseek-ai/DeepSeek-R1-Distill-Qwen-32B --trust-remote-code --tp 2\n```\n\n### Usage Recommendations\n\n**We recommend adhering to the following configurations when utilizing the DeepSeek-R1 series models, including benchmarking, to achieve the expected performance:**\n\n1. Set the temperature within the range of 0.5-0.7 (0.6 is recommended) to prevent endless repetitions or incoherent outputs.\n2. **Avoid adding a system prompt; all instructions should be contained within the user prompt.**\n3. For mathematical problems, it is advisable to include a directive in your prompt such as: \"Please reason step by step, and put your final answer within \\boxed{}.\"\n4. When evaluating model performance, it is recommended to conduct multiple tests and average the results.\n\nAdditionally, we have observed that the DeepSeek-R1 series models tend to bypass thinking pattern (i.e., outputting \"\\<think\\>\\n\\n\\</think\\>\") when responding to certain queries, which can adversely affect the model\'s performance.\n**To ensure that the model engages in thorough reasoning, we recommend enforcing the model to initiate its response with \"\\<think\\>\\n\" at the beginning of every output.**\n\n## 7. License\nThis code repository and the model weights are licensed under the [MIT License](https://github.com/deepseek-ai/DeepSeek-R1/blob/main/LICENSE).\nDeepSeek-R1 series support commercial use, allow for any modifications and derivative works, including, but not limited to, distillation for training other LLMs. Please note that:\n- DeepSeek-R1-Distill-Qwen-1.5B, DeepSeek-R1-Distill-Qwen-7B, DeepSeek-R1-Distill-Qwen-14B and DeepSeek-R1-Distill-Qwen-32B are derived from [Qwen-2.5 series](https://github.com/QwenLM/Qwen2.5), which are originally licensed under [Apache 2.0 License](https://huggingface.co/Qwen/Qwen2.5-1.5B/blob/main/LICENSE), and now finetuned with 800k samples curated with DeepSeek-R1.\n- DeepSeek-R1-Distill-Llama-8B is derived from Llama3.1-8B-Base and is originally licensed under [llama3.1 license](https://huggingface.co/meta-llama/Llama-3.1-8B/blob/main/LICENSE).\n- DeepSeek-R1-Distill-Llama-70B is derived from Llama3.3-70B-Instruct and is originally licensed under [llama3.3 license](https://huggingface.co/meta-llama/Llama-3.3-70B-Instruct/blob/main/LICENSE).\n\n## 8. Citation\n```\n@misc{deepseekai2025deepseekr1incentivizingreasoningcapability,\n      title={DeepSeek-R1: Incentivizing Reasoning Capability in LLMs via Reinforcement Learning}, \n      author={DeepSeek-AI},\n      year={2025},\n      eprint={2501.12948},\n      archivePrefix={arXiv},\n      primaryClass={cs.CL},\n      url={https://arxiv.org/abs/2501.12948}, \n}\n\n```\n\n## 9. Contact\nIf you have any questions, please raise an issue or contact us at [<EMAIL>](<EMAIL>).', 'deepseek-ai/deepseek-r1', 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
INSERT INTO `app_model_aimodel` VALUES (220267705664, 541150219354505, '************', '2025-06-19 16:33:11.267154', '2025-05-22 14:41:22.901250', 'qwen72b1', 'kmg', '## Qwen3 亮点\nQwen3 是 Qwen 系列中最新一代的大规模语言模型，提供了一系列密集型和混合专家（MoE）模型。基于广泛的训练，Qwen3 在推理、指令执行、代理能力和多语言支持方面实现了突破性进展，具有以下关键特性：1\n\n在同一模型内无缝切换思考模式（适用于复杂的逻辑推理、数学和编码）和非思考模式（适用于高效、通用的对话），确保在各种场景下达到最佳性能。\n显著增强了其推理能力，在数学、代码生成和常识逻辑推理方面超越了之前的 QwQ（思考模式）和 Qwen2.5 指令模型（非思考模式）。\n卓越的人类偏好对齐，在创意写作、角色扮演、多轮对话和指令执行方面表现出色，提供更加自然、引人入胜和沉浸式的对话体验。\n精通代理能力，能够在思考和非思考模式下与外部工具精确集成，并在复杂代理任务中达到开源模型中的领先性能。\n支持 100 多种语言和方言，具备强大的多语言指令执行和翻译能力。\n## Model Overview\n\n**Qwen3-235B-A22B** has the following features:\n- Type: Causal Language Models\n- Training Stage: Pretraining & Post-training\n- Number of Parameters: 235B in total and 22B activated\n- Number of Paramaters (Non-Embedding): 234B\n- Number of Layers: 94\n- Number of Attention Heads (GQA): 64 for Q and 4 for KV\n- Number of Experts: 128\n- Number of Activated Experts: 8\n- Context Length: 32,768 natively and [131,072 tokens with YaRN](#processing-long-texts). \n\nFor more details, including benchmark evaluation, hardware requirements, and inference performance, please refer to our [blog](https://qwenlm.github.io/blog/qwen3/), [GitHub](https://github.com/QwenLM/Qwen3), and [Documentation](https://qwen.readthedocs.io/en/latest/).\n\n## Quickstart\n\nThe code of Qwen3-MoE has been in the latest Hugging Face `transformers` and we advise you to use the latest version of `transformers`.\n\nWith `transformers<4.51.0`, you will encounter the following error:\n```\nKeyError: \'qwen3_moe\'\n```\n\nThe following contains a code snippet illustrating how to use the model generate content based on given inputs. \n```python\nfrom transformers import AutoModelForCausalLM, AutoTokenizer\n\nmodel_name = \"Qwen/Qwen3-235B-A22B\"\n\n# load the tokenizer and the model\ntokenizer = AutoTokenizer.from_pretrained(model_name)\nmodel = AutoModelForCausalLM.from_pretrained(\n    model_name,\n    torch_dtype=\"auto\",\n    device_map=\"auto\"\n)\n\n# prepare the model input\nprompt = \"Give me a short introduction to large language model.\"\nmessages = [\n    {\"role\": \"user\", \"content\": prompt}\n]\ntext = tokenizer.apply_chat_template(\n    messages,\n    tokenize=False,\n    add_generation_prompt=True,\n    enable_thinking=True # Switches between thinking and non-thinking modes. Default is True.\n)\nmodel_inputs = tokenizer([text], return_tensors=\"pt\").to(model.device)\n\n# conduct text completion\ngenerated_ids = model.generate(\n    **model_inputs,\n    max_new_tokens=32768\n)\noutput_ids = generated_ids[0][len(model_inputs.input_ids[0]):].tolist() \n\n# parsing thinking content\ntry:\n    # rindex finding 151668 (</think>)\n    index = len(output_ids) - output_ids[::-1].index(151668)\nexcept ValueError:\n    index = 0\n\nthinking_content = tokenizer.decode(output_ids[:index], skip_special_tokens=True).strip(\"\\n\")\ncontent = tokenizer.decode(output_ids[index:], skip_special_tokens=True).strip(\"\\n\")\n\nprint(\"thinking content:\", thinking_content)\nprint(\"content:\", content)\n```\n\nFor deployment, you can use `sglang>=0.4.6.post1` or `vllm>=0.8.5` or to create an OpenAI-compatible API endpoint:\n- SGLang:\n    ```shell\n    python -m sglang.launch_server --model-path Qwen/Qwen3-235B-A22B --reasoning-parser qwen3 --tp 8\n    ```\n- vLLM:\n    ```shell\n    vllm serve Qwen/Qwen3-235B-A22B --enable-reasoning --reasoning-parser deepseek_r1\n    ```\n\nFor local use, applications such as Ollama, LMStudio, MLX-LM, llama.cpp, and KTransformers have also supported Qwen3.\n\n## Switching Between Thinking and Non-Thinking Mode\n\n> [!TIP]\n> The `enable_thinking` switch is also available in APIs created by SGLang and vLLM. \n> Please refer to our documentation for [SGLang](https://qwen.readthedocs.io/en/latest/deployment/sglang.html#thinking-non-thinking-modes) and [vLLM](https://qwen.readthedocs.io/en/latest/deployment/vllm.html#thinking-non-thinking-modes) users.\n\n### `enable_thinking=True`\n\nBy default, Qwen3 has thinking capabilities enabled, similar to QwQ-32B. This means the model will use its reasoning abilities to enhance the quality of generated responses. For example, when explicitly setting `enable_thinking=True` or leaving it as the default value in `tokenizer.apply_chat_template`, the model will engage its thinking mode.\n\n```python\ntext = tokenizer.apply_chat_template(\n    messages,\n    tokenize=False,\n    add_generation_prompt=True,\n    enable_thinking=True  # True is the default value for enable_thinking\n)\n```\n\nIn this mode, the model will generate think content wrapped in a `<think>...</think>` block, followed by the final response.\n\n> [!NOTE]\n> For thinking mode, use `Temperature=0.6`, `TopP=0.95`, `TopK=20`, and `MinP=0` (the default setting in `generation_config.json`). **DO NOT use greedy decoding**, as it can lead to performance degradation and endless repetitions. For more detailed guidance, please refer to the [Best Practices](#best-practices) section.\n\n\n### `enable_thinking=False`\n\nWe provide a hard switch to strictly disable the model\'s thinking behavior, aligning its functionality with the previous Qwen2.5-Instruct models. This mode is particularly useful in scenarios where disabling thinking is essential for enhancing efficiency.\n\n```python\ntext = tokenizer.apply_chat_template(\n    messages,\n    tokenize=False,\n    add_generation_prompt=True,\n    enable_thinking=False  # Setting enable_thinking=False disables thinking mode\n)\n```\n\nIn this mode, the model will not generate any think content and will not include a `<think>...</think>` block.\n\n> [!NOTE]\n> For non-thinking mode, we suggest using `Temperature=0.7`, `TopP=0.8`, `TopK=20`, and `MinP=0`. For more detailed guidance, please refer to the [Best Practices](#best-practices) section.\n\n### Advanced Usage: Switching Between Thinking and Non-Thinking Modes via User Input\n\nWe provide a soft switch mechanism that allows users to dynamically control the model\'s behavior when `enable_thinking=True`. Specifically, you can add `/think` and `/no_think` to user prompts or system messages to switch the model\'s thinking mode from turn to turn. The model will follow the most recent instruction in multi-turn conversations.\n\nHere is an example of a multi-turn conversation:\n\n```python\nfrom transformers import AutoModelForCausalLM, AutoTokenizer\n\nclass QwenChatbot:\n    def __init__(self, model_name=\"Qwen/Qwen3-235B-A22B\"):\n        self.tokenizer = AutoTokenizer.from_pretrained(model_name)\n        self.model = AutoModelForCausalLM.from_pretrained(model_name)\n        self.history = []\n\n    def generate_response(self, user_input):\n        messages = self.history + [{\"role\": \"user\", \"content\": user_input}]\n\n        text = self.tokenizer.apply_chat_template(\n            messages,\n            tokenize=False,\n            add_generation_prompt=True\n        )\n\n        inputs = self.tokenizer(text, return_tensors=\"pt\")\n        response_ids = self.model.generate(**inputs, max_new_tokens=32768)[0][len(inputs.input_ids[0]):].tolist()\n        response = self.tokenizer.decode(response_ids, skip_special_tokens=True)\n\n        # Update history\n        self.history.append({\"role\": \"user\", \"content\": user_input})\n        self.history.append({\"role\": \"assistant\", \"content\": response})\n\n        return response\n\n# Example Usage\nif __name__ == \"__main__\":\n    chatbot = QwenChatbot()\n\n    # First input (without /think or /no_think tags, thinking mode is enabled by default)\n    user_input_1 = \"How many r\'s in strawberries?\"\n    print(f\"User: {user_input_1}\")\n    response_1 = chatbot.generate_response(user_input_1)\n    print(f\"Bot: {response_1}\")\n    print(\"----------------------\")\n\n    # Second input with /no_think\n    user_input_2 = \"Then, how many r\'s in blueberries? /no_think\"\n    print(f\"User: {user_input_2}\")\n    response_2 = chatbot.generate_response(user_input_2)\n    print(f\"Bot: {response_2}\") \n    print(\"----------------------\")\n\n    # Third input with /think\n    user_input_3 = \"Really? /think\"\n    print(f\"User: {user_input_3}\")\n    response_3 = chatbot.generate_response(user_input_3)\n    print(f\"Bot: {response_3}\")\n```\n\n> [!NOTE]\n> For API compatibility, when `enable_thinking=True`, regardless of whether the user uses `/think` or `/no_think`, the model will always output a block wrapped in `<think>...</think>`. However, the content inside this block may be empty if thinking is disabled.\n> When `enable_thinking=False`, the soft switches are not valid. Regardless of any `/think` or `/no_think` tags input by the user, the model will not generate think content and will not include a `<think>...</think>` block.\n\n## Agentic Use\n\nQwen3 excels in tool calling capabilities. We recommend using [Qwen-Agent](https://github.com/QwenLM/Qwen-Agent) to make the best use of agentic ability of Qwen3. Qwen-Agent encapsulates tool-calling templates and tool-calling parsers internally, greatly reducing coding complexity.\n\nTo define the available tools, you can use the MCP configuration file, use the integrated tool of Qwen-Agent, or integrate other tools by yourself.\n```python\nfrom qwen_agent.agents import Assistant\n\n# Define LLM\nllm_cfg = {\n    \'model\': \'Qwen3-235B-A22B\',\n\n    # Use the endpoint provided by Alibaba Model Studio:\n    # \'model_type\': \'qwen_dashscope\',\n    # \'api_key\': os.getenv(\'DASHSCOPE_API_KEY\'),\n\n    # Use a custom endpoint compatible with OpenAI API:\n    \'model_server\': \'http://localhost:8000/v1\',  # api_base\n    \'api_key\': \'EMPTY\',\n\n    # Other parameters:\n    # \'generate_cfg\': {\n    #         # Add: When the response content is `<think>this is the thought</think>this is the answer;\n    #         # Do not add: When the response has been separated by reasoning_content and content.\n    #         \'thought_in_content\': True,\n    #     },\n}\n\n# Define Tools\ntools = [\n    {\'mcpServers\': {  # You can specify the MCP configuration file\n            \'time\': {\n                \'command\': \'uvx\',\n                \'args\': [\'mcp-server-time\', \'--local-timezone=Asia/Shanghai\']\n            },\n            \"fetch\": {\n                \"command\": \"uvx\",\n                \"args\": [\"mcp-server-fetch\"]\n            }\n        }\n    },\n  \'code_interpreter\',  # Built-in tools\n]\n\n# Define Agent\nbot = Assistant(llm=llm_cfg, function_list=tools)\n\n# Streaming generation\nmessages = [{\'role\': \'user\', \'content\': \'https://qwenlm.github.io/blog/ Introduce the latest developments of Qwen\'}]\nfor responses in bot.run(messages=messages):\n    pass\nprint(responses)\n```\n\n## Processing Long Texts\n\nQwen3 natively supports context lengths of up to 32,768 tokens. For conversations where the total length (including both input and output) significantly exceeds this limit, we recommend using RoPE scaling techniques to handle long texts effectively. We have validated the model\'s performance on context lengths of up to 131,072 tokens using the [YaRN](https://arxiv.org/abs/2309.00071) method.\n\nYaRN is currently supported by several inference frameworks, e.g., `transformers` and `llama.cpp` for local use, `vllm` and `sglang` for deployment. In general, there are two approaches to enabling YaRN for supported frameworks:\n\n- Modifying the model files:\n  In the `config.json` file, add the `rope_scaling` fields:\n    ```json\n    {\n        ...,\n        \"rope_scaling\": {\n            \"rope_type\": \"yarn\",\n            \"factor\": 4.0,\n            \"original_max_position_embeddings\": 32768\n        }\n    }\n    ```\n  For `llama.cpp`, you need to regenerate the GGUF file after the modification.\n\n- Passing command line arguments:\n\n  For `vllm`, you can use\n    ```shell\n    vllm serve ... --rope-scaling \'{\"rope_type\":\"yarn\",\"factor\":4.0,\"original_max_position_embeddings\":32768}\' --max-model-len 131072  \n    ```\n\n  For `sglang`, you can use\n    ```shell\n    python -m sglang.launch_server ... --json-model-override-args \'{\"rope_scaling\":{\"rope_type\":\"yarn\",\"factor\":4.0,\"original_max_position_embeddings\":32768}}\'\n    ```\n\n  For `llama-server` from `llama.cpp`, you can use\n    ```shell\n    llama-server ... --rope-scaling yarn --rope-scale 4 --yarn-orig-ctx 32768\n    ```\n\n> [!IMPORTANT]\n> If you encounter the following warning\n> ```\n> Unrecognized keys in `rope_scaling` for \'rope_type\'=\'yarn\': {\'original_max_position_embeddings\'}\n> ```\n> please upgrade `transformers>=4.51.0`.\n\n> [!NOTE]\n> All the notable open-source frameworks implement static YaRN, which means the scaling factor remains constant regardless of input length, **potentially impacting performance on shorter texts.**\n> We advise adding the `rope_scaling` configuration only when processing long contexts is required. \n> It is also recommended to modify the `factor` as needed. For example, if the typical context length for your application is 65,536 tokens, it would be better to set `factor` as 2.0. \n\n> [!NOTE]\n> The default `max_position_embeddings` in `config.json` is set to 40,960. This allocation includes reserving 32,768 tokens for outputs and 8,192 tokens for typical prompts, which is sufficient for most scenarios involving short text processing. If the average context length does not exceed 32,768 tokens, we do not recommend enabling YaRN in this scenario, as it may potentially degrade model performance.\n\n> [!TIP]\n> The endpoint provided by Alibaba Model Studio supports dynamic YaRN by default and no extra configuration is needed.\n\n## Best Practices\n\nTo achieve optimal performance, we recommend the following settings:\n\n1. **Sampling Parameters**:\n   - For thinking mode (`enable_thinking=True`), use `Temperature=0.6`, `TopP=0.95`, `TopK=20`, and `MinP=0`. **DO NOT use greedy decoding**, as it can lead to performance degradation and endless repetitions.\n   - For non-thinking mode (`enable_thinking=False`), we suggest using `Temperature=0.7`, `TopP=0.8`, `TopK=20`, and `MinP=0`.\n   - For supported frameworks, you can adjust the `presence_penalty` parameter between 0 and 2 to reduce endless repetitions. However, using a higher value may occasionally result in language mixing and a slight decrease in model performance.\n\n2. **Adequate Output Length**: We recommend using an output length of 32,768 tokens for most queries. For benchmarking on highly complex problems, such as those found in math and programming competitions, we suggest setting the max output length to 38,912 tokens. This provides the model with sufficient space to generate detailed and comprehensive responses, thereby enhancing its overall performance.\n\n3. **Standardize Output Format**: We recommend using prompts to standardize model outputs when benchmarking.\n   - **Math Problems**: Include \"Please reason step by step, and put your final answer within \\boxed{}.\" in the prompt.\n   - **Multiple-Choice Questions**: Add the following JSON structure to the prompt to standardize responses: \"Please show your choice in the `answer` field with only the choice letter, e.g., `\"answer\": \"C\"`.\"\n\n4. **No Thinking Content in History**: In multi-turn conversations, the historical model output should only include the final output part and does not need to include the thinking content. It is implemented in the provided chat template in Jinja2. However, for frameworks that do not directly use the Jinja2 chat template, it is up to the developers to ensure that the best practice is followed.\n\n### Citation\n\nIf you find our work helpful, feel free to give us a cite.\n\n```\n@misc{qwen3technicalreport,\n      title={Qwen3 Technical Report}, \n      author={Qwen Team},\n      year={2025},\n      eprint={2505.09388},\n      archivePrefix={arXiv},\n      primaryClass={cs.CL},\n      url={https://arxiv.org/abs/2505.09388}, \n}\n```', 'kmg/qwen3-235b', 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
INSERT INTO `app_model_aimodel` VALUES (23240224159479, ************, '************', '2025-06-19 15:44:10.254188', '2025-06-17 10:44:42.851838', 'U-net', 'kmg', '分割模型', 'kmg/U-MET', NULL, NULL);
INSERT INTO `app_model_aimodel` VALUES (23297004212349, NULL, '************', '2025-06-19 15:43:16.986605', '2025-06-17 14:35:45.169294', 'resnet123', 'kmg', '卷积神经网络', 'kmg/resnet18/', NULL, NULL);
INSERT INTO `app_model_aimodel` VALUES (24009395031542, ************, '************', '2025-07-01 00:13:43.404636', '2025-06-19 14:54:28.708221', 'yolo8', 'kmg', '目标检测模型', 'kmg/yolo8', NULL, NULL);
INSERT INTO `app_model_aimodel` VALUES (28610918495561, ************, '************', '2025-07-02 15:10:42.491347', '2025-07-02 14:58:07.523318', 'test_model', 'kmg', '123', 'kmg/test_model', NULL, NULL);
INSERT INTO `app_model_aimodel` VALUES (28638203491561, NULL, '************', '2025-07-03 16:39:26.425618', '2025-07-02 16:49:08.899819', 'resnet50', 'cv', '经典ResNet模型，用于图像分类任务', 'cv/resnet50', NULL, 'resnet50');

-- ----------------------------
-- Table structure for app_model_aimodel_categories
-- ----------------------------
DROP TABLE IF EXISTS `app_model_aimodel_categories`;
CREATE TABLE `app_model_aimodel_categories`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `aimodel_id` bigint NOT NULL,
  `modelcategory_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `app_model_aimodel_catego_aimodel_id_modelcategory_a734de62_uniq`(`aimodel_id` ASC, `modelcategory_id` ASC) USING BTREE,
  INDEX `app_model_aimodel_ca_modelcategory_id_746357f2_fk_app_model`(`modelcategory_id` ASC) USING BTREE,
  CONSTRAINT `app_model_aimodel_ca_aimodel_id_0d57c61f_fk_app_model` FOREIGN KEY (`aimodel_id`) REFERENCES `app_model_aimodel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `app_model_aimodel_ca_modelcategory_id_746357f2_fk_app_model` FOREIGN KEY (`modelcategory_id`) REFERENCES `app_model_modelcategory` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_model_aimodel_categories
-- ----------------------------
INSERT INTO `app_model_aimodel_categories` VALUES (12, 214981219456, 182712330688);
INSERT INTO `app_model_aimodel_categories` VALUES (22, 214981219456, 220228416192);
INSERT INTO `app_model_aimodel_categories` VALUES (23, 214981219456, 220230584576);
INSERT INTO `app_model_aimodel_categories` VALUES (44, 220267705664, 182712327936);
INSERT INTO `app_model_aimodel_categories` VALUES (45, 28610918495561, 182712329792);

-- ----------------------------
-- Table structure for app_model_aimodel_datasets
-- ----------------------------
DROP TABLE IF EXISTS `app_model_aimodel_datasets`;
CREATE TABLE `app_model_aimodel_datasets`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `aimodel_id` bigint NOT NULL,
  `aidataset_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `app_model_aimodel_datasets_aimodel_id_aidataset_id_f1dbf4f3_uniq`(`aimodel_id` ASC, `aidataset_id` ASC) USING BTREE,
  INDEX `app_model_aimodel_da_aidataset_id_7fa82f6b_fk_app_datas`(`aidataset_id` ASC) USING BTREE,
  CONSTRAINT `app_model_aimodel_da_aidataset_id_7fa82f6b_fk_app_datas` FOREIGN KEY (`aidataset_id`) REFERENCES `app_dataset_aidataset` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `app_model_aimodel_da_aimodel_id_fa9e746b_fk_app_model` FOREIGN KEY (`aimodel_id`) REFERENCES `app_model_aimodel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of app_model_aimodel_datasets
-- ----------------------------
INSERT INTO `app_model_aimodel_datasets` VALUES (2, 28638203491561, 28986891832337);
INSERT INTO `app_model_aimodel_datasets` VALUES (1, 28638203491561, 28986997114154);

-- ----------------------------
-- Table structure for app_model_modelcategory
-- ----------------------------
DROP TABLE IF EXISTS `app_model_modelcategory`;
CREATE TABLE `app_model_modelcategory`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `order` int NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `parent_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `app_model_modelcategory_creator_id_fb29dbb8`(`creator_id` ASC) USING BTREE,
  INDEX `app_model_modelcategory_parent_id_ad3d02cf`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_model_modelcategory
-- ----------------------------
INSERT INTO `app_model_modelcategory` VALUES (182712327680, '541150219354505', '2025-05-22 14:29:21.897184', '2025-05-15 19:41:20.120000', '自然语言处理', '自然语言处理', 1, 1, NULL, 220220920128);
INSERT INTO `app_model_modelcategory` VALUES (182712327936, NULL, '2025-05-15 19:41:20.124000', '2025-05-15 19:41:20.124000', '文本生成', '文本生成', 1, 1, NULL, 182712327680);
INSERT INTO `app_model_modelcategory` VALUES (182712328000, NULL, '2025-05-15 19:41:20.125000', '2025-05-15 19:41:20.125000', '文本分类', '文本分类', 1, 1, NULL, 182712327680);
INSERT INTO `app_model_modelcategory` VALUES (182712328192, NULL, '2025-05-15 19:41:20.128000', '2025-05-15 19:41:20.128000', '分词', '分词', 1, 1, NULL, 182712327680);
INSERT INTO `app_model_modelcategory` VALUES (182712328320, NULL, '2025-05-15 19:41:20.130000', '2025-05-15 19:41:20.130000', '命名实体识别', '命名实体识别', 1, 1, NULL, 182712327680);
INSERT INTO `app_model_modelcategory` VALUES (182712328448, NULL, '2025-05-15 19:41:20.132000', '2025-05-15 19:41:20.132000', '翻译', '翻译', 1, 1, NULL, 182712327680);
INSERT INTO `app_model_modelcategory` VALUES (182712328576, NULL, '2025-05-15 19:41:20.134000', '2025-05-15 19:41:20.134000', '文本摘要', '文本摘要', 1, 1, NULL, 182712327680);
INSERT INTO `app_model_modelcategory` VALUES (182712328704, NULL, '2025-05-15 19:41:20.136000', '2025-05-15 19:41:20.136000', '句子相似度', '句子相似度', 1, 1, NULL, 182712327680);
INSERT INTO `app_model_modelcategory` VALUES (182712328768, NULL, '2025-05-15 19:41:20.137000', '2025-05-15 19:41:20.137000', '预训练', '预训练', 1, 1, NULL, 182712327680);
INSERT INTO `app_model_modelcategory` VALUES (182712329344, '541150219354505', '2025-05-22 14:29:28.599412', '2025-05-15 19:41:20.148000', '语音处理', '语音处理', 1, 1, NULL, 220220920128);
INSERT INTO `app_model_modelcategory` VALUES (182712329536, NULL, '2025-05-15 19:41:20.149000', '2025-05-15 19:41:20.149000', '语音合成', '语音合成', 1, 1, NULL, 182712329344);
INSERT INTO `app_model_modelcategory` VALUES (182712329664, NULL, '2025-05-15 19:41:20.151000', '2025-05-15 19:41:20.151000', '语音降噪', '语音降噪', 1, 1, NULL, 182712329344);
INSERT INTO `app_model_modelcategory` VALUES (182712329792, NULL, '2025-05-15 19:41:20.153000', '2025-05-15 19:41:20.153000', '回声消除', '回声消除', 1, 1, NULL, 182712329344);
INSERT INTO `app_model_modelcategory` VALUES (182712329856, NULL, '2025-05-15 19:41:20.154000', '2025-05-15 19:41:20.154000', '语音分离', '语音分离', 1, 1, NULL, 182712329344);
INSERT INTO `app_model_modelcategory` VALUES (182712330432, '541150219354505', '2025-05-22 14:29:33.566537', '2025-05-15 19:41:20.163000', '计算机视觉', '计算机视觉', 1, 1, NULL, 220220920128);
INSERT INTO `app_model_modelcategory` VALUES (182712330496, NULL, '2025-05-15 19:41:20.164000', '2025-05-15 19:41:20.164000', '深度估计', '深度估计', 1, 1, NULL, 182712330432);
INSERT INTO `app_model_modelcategory` VALUES (182712330624, NULL, '2025-05-15 19:41:20.166000', '2025-05-15 19:41:20.166000', '图像分类', '图像分类', 1, 1, NULL, 182712330432);
INSERT INTO `app_model_modelcategory` VALUES (182712330688, NULL, '2025-05-15 19:41:20.167000', '2025-05-15 19:41:20.167000', '物体检测', '物体检测', 1, 1, NULL, 182712330432);
INSERT INTO `app_model_modelcategory` VALUES (182712330816, NULL, '2025-05-15 19:41:20.169000', '2025-05-15 19:41:20.169000', '图像分割', '图像分割', 1, 1, NULL, 182712330432);
INSERT INTO `app_model_modelcategory` VALUES (182712330880, NULL, '2025-05-15 19:41:20.170000', '2025-05-15 19:41:20.170000', '文本转图像', '文本转图像', 1, 1, NULL, 182712330432);
INSERT INTO `app_model_modelcategory` VALUES (182712330944, NULL, '2025-05-15 19:41:20.171000', '2025-05-15 19:41:20.171000', '图像转文本', '图像转文本', 1, 1, NULL, 182712330432);
INSERT INTO `app_model_modelcategory` VALUES (220220920128, '541150219354505', '2025-05-22 14:29:11.877445', '2025-05-22 14:29:11.877445', '任务', 'task', 1, 1, 541150219354505, NULL);
INSERT INTO `app_model_modelcategory` VALUES (220224104064, '541150219354505', '2025-05-22 14:30:01.626031', '2025-05-22 14:30:01.626031', '技术', 'technical', 2, 1, 541150219354505, NULL);
INSERT INTO `app_model_modelcategory` VALUES (220225324096, '541150219354505', '2025-05-22 14:30:20.689419', '2025-05-22 14:30:20.689419', '深度学习', 'deepLearning', 1, 1, 541150219354505, 220224104064);
INSERT INTO `app_model_modelcategory` VALUES (220226432320, '541150219354505', '2025-05-22 14:30:38.005728', '2025-05-22 14:30:38.005728', '机器学习', 'MachineLearning', 2, 1, 541150219354505, 220224104064);
INSERT INTO `app_model_modelcategory` VALUES (220227741760, '541150219354505', '2025-05-22 14:30:58.465181', '2025-05-22 14:30:58.465181', 'TransFormer', '1', 1, 1, 541150219354505, 220225324096);
INSERT INTO `app_model_modelcategory` VALUES (220228416192, '541150219354505', '2025-05-22 14:31:09.003088', '2025-05-22 14:31:09.003088', 'CNN', '2', 2, 1, 541150219354505, 220225324096);
INSERT INTO `app_model_modelcategory` VALUES (220229510784, '541150219354505', '2025-05-22 14:31:26.106076', '2025-05-22 14:31:26.106076', '框架', '3', 3, 1, 541150219354505, NULL);
INSERT INTO `app_model_modelcategory` VALUES (220230584576, '541150219354505', '2025-05-22 14:31:42.884077', '2025-05-22 14:31:42.884077', 'Pytorch', '1', 1, 1, 541150219354505, 220229510784);
INSERT INTO `app_model_modelcategory` VALUES (220231889088, '541150219354505', '2025-05-22 14:32:03.267895', '2025-05-22 14:32:03.267895', 'TensorFlow', '2', 2, 1, 541150219354505, 220229510784);
INSERT INTO `app_model_modelcategory` VALUES (16152186529812, '541150219354505', '2025-05-28 10:04:56.572829', '2025-05-28 10:03:24.914752', '监督学习', '1', 1, 1, 541150219354505, 220226432320);
INSERT INTO `app_model_modelcategory` VALUES (16152244289142, '541150219354505', '2025-05-28 10:05:03.194116', '2025-05-28 10:03:39.015706', '无监督学习', '1', 1, 1, 541150219354505, 220226432320);
INSERT INTO `app_model_modelcategory` VALUES (16152315554074, '541150219354505', '2025-05-28 10:03:56.414400', '2025-05-28 10:03:56.414400', '线性回归', '1', 1, 1, 541150219354505, 16152186529812);
INSERT INTO `app_model_modelcategory` VALUES (16152382348629, '541150219354505', '2025-05-28 10:04:12.721247', '2025-05-28 10:04:12.722254', '决策树', '2', 2, 1, 541150219354505, 16152186529812);
INSERT INTO `app_model_modelcategory` VALUES (16152453018883, '541150219354505', '2025-05-28 10:04:29.975998', '2025-05-28 10:04:29.975998', 'K近邻算法', '3', 3, 1, 541150219354505, 16152186529812);
INSERT INTO `app_model_modelcategory` VALUES (16152617575765, '541150219354505', '2025-05-28 10:05:10.150418', '2025-05-28 10:05:10.150418', 'K均值聚类', '1', 1, 1, 541150219354505, 16152244289142);
INSERT INTO `app_model_modelcategory` VALUES (16152683101943, '541150219354505', '2025-05-28 10:05:26.147517', '2025-05-28 10:05:26.147517', '运筹优化', '3', 3, 1, 541150219354505, 220224104064);
INSERT INTO `app_model_modelcategory` VALUES (16152744451243, '541150219354505', '2025-05-28 10:05:41.125620', '2025-05-28 10:05:41.125620', '线性规划', '1', 1, 1, 541150219354505, 16152683101943);
INSERT INTO `app_model_modelcategory` VALUES (16152802267895, '541150219354505', '2025-05-28 10:05:55.241314', '2025-05-28 10:05:55.241314', '动态规划', '2', 2, 1, 541150219354505, 16152683101943);
INSERT INTO `app_model_modelcategory` VALUES (16152856102981, '541150219354505', '2025-05-28 10:06:08.385685', '2025-05-28 10:06:08.385685', '组合优化', '3', 3, 1, 541150219354505, 16152683101943);
INSERT INTO `app_model_modelcategory` VALUES (16152930394699, '541150219354505', '2025-05-28 10:06:26.522708', '2025-05-28 10:06:26.522708', '旅行商问题', '1', 1, 1, 541150219354505, 16152856102981);
INSERT INTO `app_model_modelcategory` VALUES (16152984981128, '541150219354505', '2025-05-28 10:06:39.848019', '2025-05-28 10:06:39.848019', '背包问题', '2', 2, 1, 541150219354505, 16152856102981);
INSERT INTO `app_model_modelcategory` VALUES (23952739967339, '************', '2025-06-19 11:03:56.906434', '2025-06-19 11:03:56.906434', '测试', '测试', 1, 1, ************, 220220920128);

-- ----------------------------
-- Table structure for app_model_modelcomment
-- ----------------------------
DROP TABLE IF EXISTS `app_model_modelcomment`;
CREATE TABLE `app_model_modelcomment`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `model_id` bigint NOT NULL,
  `parent_id` bigint NULL DEFAULT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `app_model_modelcomment_model_id_d7e919af_fk_app_model_aimodel_id`(`model_id` ASC) USING BTREE,
  INDEX `app_model_modelcomme_parent_id_396f3f4a_fk_app_model`(`parent_id` ASC) USING BTREE,
  INDEX `app_model_modelcomment_user_id_4784ca18_fk_sys_users_id`(`user_id` ASC) USING BTREE,
  INDEX `app_model_modelcomment_creator_id_e6e7acdc`(`creator_id` ASC) USING BTREE,
  CONSTRAINT `app_model_modelcomme_parent_id_396f3f4a_fk_app_model` FOREIGN KEY (`parent_id`) REFERENCES `app_model_modelcomment` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `app_model_modelcomment_model_id_d7e919af_fk_app_model_aimodel_id` FOREIGN KEY (`model_id`) REFERENCES `app_model_aimodel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `app_model_modelcomment_user_id_4784ca18_fk_sys_users_id` FOREIGN KEY (`user_id`) REFERENCES `sys_users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_model_modelcomment
-- ----------------------------
INSERT INTO `app_model_modelcomment` VALUES (220293942848, '541150219354505', '2025-05-22 14:48:12.857121', '2025-05-22 14:48:12.857121', '嗨，我想知道 yolov12 是否支持实例分割和姿势模型？\n感谢这项工作', 541150219354505, 214981219456, NULL, 541150219354505);
INSERT INTO `app_model_modelcomment` VALUES (220294643072, '541150219354505', '2025-05-22 14:48:23.798229', '2025-05-22 14:48:23.798229', '目前，我们还没有在这些任务上尝试过 YOLOv12。但是，我们希望将来能有相关的结果。感谢您的建议！', 541150219354505, 214981219456, 220293942848, 541150219354505);

-- ----------------------------
-- Table structure for app_model_modelversion
-- ----------------------------
DROP TABLE IF EXISTS `app_model_modelversion`;
CREATE TABLE `app_model_modelversion`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `version_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `docker_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `model_weights_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `model_docs_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `test_report_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `test_failure_reason` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `model_id` bigint NOT NULL,
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `app_model_modelversion_model_id_version_number_93bd1615_uniq`(`model_id` ASC, `version_number` ASC) USING BTREE,
  INDEX `app_model_modelversion_creator_id_17bb948d_fk_sys_users_id`(`creator_id` ASC) USING BTREE,
  CONSTRAINT `app_model_modelversion_creator_id_17bb948d_fk_sys_users_id` FOREIGN KEY (`creator_id`) REFERENCES `sys_users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `app_model_modelversion_model_id_b2e49cbc_fk_app_model_aimodel_id` FOREIGN KEY (`model_id`) REFERENCES `app_model_aimodel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_model_modelversion
-- ----------------------------
INSERT INTO `app_model_modelversion` VALUES (25388745060345, NULL, '2025-06-23 12:35:30.323979', '2025-06-23 12:27:04.086370', 'v1.0', '', 'test_pass', 'test', '', '', '', NULL, ************, 24009395031542, NULL);
INSERT INTO `app_model_modelversion` VALUES (28039618469656, '************', '2025-07-01 00:13:29.977603', '2025-07-01 00:13:29.977603', 'v2.0', '', 'dev_done', NULL, NULL, NULL, NULL, NULL, ************, 24009395031542, NULL);
INSERT INTO `app_model_modelversion` VALUES (28044909179242, '************', '2025-07-01 00:35:01.655509', '2025-07-01 00:35:01.655509', 'v1.0', '', 'dev_done', NULL, NULL, NULL, NULL, NULL, ************, 214981219456, NULL);
INSERT INTO `app_model_modelversion` VALUES (28045153474828, '************', '2025-07-01 00:36:01.297643', '2025-07-01 00:36:01.297643', 'v2.0', '', 'dev_done', NULL, NULL, NULL, NULL, NULL, ************, 214981219456, NULL);
INSERT INTO `app_model_modelversion` VALUES (28046054847475, '************', '2025-07-01 00:39:41.359918', '2025-07-01 00:39:41.359918', 'v3.0', '', 'dev_done', NULL, NULL, NULL, NULL, NULL, ************, 214981219456, NULL);
INSERT INTO `app_model_modelversion` VALUES (28610969401807, '************', '2025-07-02 14:58:19.951880', '2025-07-02 14:58:19.951880', 'v1.0', '', 'dev_done', NULL, NULL, NULL, NULL, NULL, ************, 28610918495561, NULL);
INSERT INTO `app_model_modelversion` VALUES (28616825002935, '************', '2025-07-02 15:22:09.541708', '2025-07-02 15:22:09.541708', 'v2.0', '', 'dev_done', NULL, NULL, NULL, NULL, NULL, ************, 28610918495561, NULL);
INSERT INTO `app_model_modelversion` VALUES (28638203552000, NULL, '2025-07-02 16:49:08.914912', '2025-07-02 16:49:08.914912', 'v4.0.0', '初始版本', 'dev_done', 'cv/resnet50:latest', NULL, NULL, NULL, NULL, NULL, 28638203491561, 'resnet50_v4');
INSERT INTO `app_model_modelversion` VALUES (28638302437099, NULL, '2025-07-02 16:49:33.055983', '2025-07-02 16:49:33.055983', 'v5.0.0', '初始版本', 'dev_done', 'cv/resnet50:latest', NULL, NULL, NULL, NULL, NULL, 28638203491561, 'resnet50_v5');
INSERT INTO `app_model_modelversion` VALUES (28639090719103, NULL, '2025-07-02 17:07:45.511181', '2025-07-02 16:52:45.508601', 'v2.0.0', 'v2版本', 'dev_done', 'cv/resnet50:latest', 'cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe', 'cv/resnet50/v2.0.0/README.md', 'cv/resnet50/v2.0.0/测试报告.doc', NULL, NULL, 28638203491561, 'resnet50_v2');

-- ----------------------------
-- Table structure for auth_group
-- ----------------------------
DROP TABLE IF EXISTS `auth_group`;
CREATE TABLE `auth_group`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of auth_group
-- ----------------------------

-- ----------------------------
-- Table structure for auth_group_permissions
-- ----------------------------
DROP TABLE IF EXISTS `auth_group_permissions`;
CREATE TABLE `auth_group_permissions`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `auth_group_permissions_group_id_permission_id_0cd325b0_uniq`(`group_id` ASC, `permission_id` ASC) USING BTREE,
  INDEX `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm`(`permission_id` ASC) USING BTREE,
  CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of auth_group_permissions
-- ----------------------------

-- ----------------------------
-- Table structure for auth_permission
-- ----------------------------
DROP TABLE IF EXISTS `auth_permission`;
CREATE TABLE `auth_permission`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content_type_id` int NOT NULL,
  `codename` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `auth_permission_content_type_id_codename_01ab375a_uniq`(`content_type_id` ASC, `codename` ASC) USING BTREE,
  CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 153 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of auth_permission
-- ----------------------------
INSERT INTO `auth_permission` VALUES (1, 'Can add log entry', 1, 'add_logentry');
INSERT INTO `auth_permission` VALUES (2, 'Can change log entry', 1, 'change_logentry');
INSERT INTO `auth_permission` VALUES (3, 'Can delete log entry', 1, 'delete_logentry');
INSERT INTO `auth_permission` VALUES (4, 'Can view log entry', 1, 'view_logentry');
INSERT INTO `auth_permission` VALUES (5, 'Can add permission', 2, 'add_permission');
INSERT INTO `auth_permission` VALUES (6, 'Can change permission', 2, 'change_permission');
INSERT INTO `auth_permission` VALUES (7, 'Can delete permission', 2, 'delete_permission');
INSERT INTO `auth_permission` VALUES (8, 'Can view permission', 2, 'view_permission');
INSERT INTO `auth_permission` VALUES (9, 'Can add group', 3, 'add_group');
INSERT INTO `auth_permission` VALUES (10, 'Can change group', 3, 'change_group');
INSERT INTO `auth_permission` VALUES (11, 'Can delete group', 3, 'delete_group');
INSERT INTO `auth_permission` VALUES (12, 'Can view group', 3, 'view_group');
INSERT INTO `auth_permission` VALUES (13, 'Can add content type', 4, 'add_contenttype');
INSERT INTO `auth_permission` VALUES (14, 'Can change content type', 4, 'change_contenttype');
INSERT INTO `auth_permission` VALUES (15, 'Can delete content type', 4, 'delete_contenttype');
INSERT INTO `auth_permission` VALUES (16, 'Can view content type', 4, 'view_contenttype');
INSERT INTO `auth_permission` VALUES (17, 'Can add session', 5, 'add_session');
INSERT INTO `auth_permission` VALUES (18, 'Can change session', 5, 'change_session');
INSERT INTO `auth_permission` VALUES (19, 'Can delete session', 5, 'delete_session');
INSERT INTO `auth_permission` VALUES (20, 'Can view session', 5, 'view_session');
INSERT INTO `auth_permission` VALUES (21, 'Can add task result', 6, 'add_taskresult');
INSERT INTO `auth_permission` VALUES (22, 'Can change task result', 6, 'change_taskresult');
INSERT INTO `auth_permission` VALUES (23, 'Can delete task result', 6, 'delete_taskresult');
INSERT INTO `auth_permission` VALUES (24, 'Can view task result', 6, 'view_taskresult');
INSERT INTO `auth_permission` VALUES (25, 'Can add chord counter', 7, 'add_chordcounter');
INSERT INTO `auth_permission` VALUES (26, 'Can change chord counter', 7, 'change_chordcounter');
INSERT INTO `auth_permission` VALUES (27, 'Can delete chord counter', 7, 'delete_chordcounter');
INSERT INTO `auth_permission` VALUES (28, 'Can view chord counter', 7, 'view_chordcounter');
INSERT INTO `auth_permission` VALUES (29, 'Can add group result', 8, 'add_groupresult');
INSERT INTO `auth_permission` VALUES (30, 'Can change group result', 8, 'change_groupresult');
INSERT INTO `auth_permission` VALUES (31, 'Can delete group result', 8, 'delete_groupresult');
INSERT INTO `auth_permission` VALUES (32, 'Can view group result', 8, 'view_groupresult');
INSERT INTO `auth_permission` VALUES (33, 'Can add crontab', 9, 'add_crontabschedule');
INSERT INTO `auth_permission` VALUES (34, 'Can change crontab', 9, 'change_crontabschedule');
INSERT INTO `auth_permission` VALUES (35, 'Can delete crontab', 9, 'delete_crontabschedule');
INSERT INTO `auth_permission` VALUES (36, 'Can view crontab', 9, 'view_crontabschedule');
INSERT INTO `auth_permission` VALUES (37, 'Can add interval', 10, 'add_intervalschedule');
INSERT INTO `auth_permission` VALUES (38, 'Can change interval', 10, 'change_intervalschedule');
INSERT INTO `auth_permission` VALUES (39, 'Can delete interval', 10, 'delete_intervalschedule');
INSERT INTO `auth_permission` VALUES (40, 'Can view interval', 10, 'view_intervalschedule');
INSERT INTO `auth_permission` VALUES (41, 'Can add periodic task', 11, 'add_periodictask');
INSERT INTO `auth_permission` VALUES (42, 'Can change periodic task', 11, 'change_periodictask');
INSERT INTO `auth_permission` VALUES (43, 'Can delete periodic task', 11, 'delete_periodictask');
INSERT INTO `auth_permission` VALUES (44, 'Can view periodic task', 11, 'view_periodictask');
INSERT INTO `auth_permission` VALUES (45, 'Can add periodic tasks', 12, 'add_periodictasks');
INSERT INTO `auth_permission` VALUES (46, 'Can change periodic tasks', 12, 'change_periodictasks');
INSERT INTO `auth_permission` VALUES (47, 'Can delete periodic tasks', 12, 'delete_periodictasks');
INSERT INTO `auth_permission` VALUES (48, 'Can view periodic tasks', 12, 'view_periodictasks');
INSERT INTO `auth_permission` VALUES (49, 'Can add solar event', 13, 'add_solarschedule');
INSERT INTO `auth_permission` VALUES (50, 'Can change solar event', 13, 'change_solarschedule');
INSERT INTO `auth_permission` VALUES (51, 'Can delete solar event', 13, 'delete_solarschedule');
INSERT INTO `auth_permission` VALUES (52, 'Can view solar event', 13, 'view_solarschedule');
INSERT INTO `auth_permission` VALUES (53, 'Can add clocked', 14, 'add_clockedschedule');
INSERT INTO `auth_permission` VALUES (54, 'Can change clocked', 14, 'change_clockedschedule');
INSERT INTO `auth_permission` VALUES (55, 'Can delete clocked', 14, 'delete_clockedschedule');
INSERT INTO `auth_permission` VALUES (56, 'Can view clocked', 14, 'view_clockedschedule');
INSERT INTO `auth_permission` VALUES (57, 'Can add blacklisted token', 15, 'add_blacklistedtoken');
INSERT INTO `auth_permission` VALUES (58, 'Can change blacklisted token', 15, 'change_blacklistedtoken');
INSERT INTO `auth_permission` VALUES (59, 'Can delete blacklisted token', 15, 'delete_blacklistedtoken');
INSERT INTO `auth_permission` VALUES (60, 'Can view blacklisted token', 15, 'view_blacklistedtoken');
INSERT INTO `auth_permission` VALUES (61, 'Can add outstanding token', 16, 'add_outstandingtoken');
INSERT INTO `auth_permission` VALUES (62, 'Can change outstanding token', 16, 'change_outstandingtoken');
INSERT INTO `auth_permission` VALUES (63, 'Can delete outstanding token', 16, 'delete_outstandingtoken');
INSERT INTO `auth_permission` VALUES (64, 'Can view outstanding token', 16, 'view_outstandingtoken');
INSERT INTO `auth_permission` VALUES (65, 'Can add captcha store', 17, 'add_captchastore');
INSERT INTO `auth_permission` VALUES (66, 'Can change captcha store', 17, 'change_captchastore');
INSERT INTO `auth_permission` VALUES (67, 'Can delete captcha store', 17, 'delete_captchastore');
INSERT INTO `auth_permission` VALUES (68, 'Can view captcha store', 17, 'view_captchastore');
INSERT INTO `auth_permission` VALUES (69, 'Can add casbin rule', 18, 'add_casbinrule');
INSERT INTO `auth_permission` VALUES (70, 'Can change casbin rule', 18, 'change_casbinrule');
INSERT INTO `auth_permission` VALUES (71, 'Can delete casbin rule', 18, 'delete_casbinrule');
INSERT INTO `auth_permission` VALUES (72, 'Can view casbin rule', 18, 'view_casbinrule');
INSERT INTO `auth_permission` VALUES (73, 'Can add 系统-岗位表', 19, 'add_post');
INSERT INTO `auth_permission` VALUES (74, 'Can change 系统-岗位表', 19, 'change_post');
INSERT INTO `auth_permission` VALUES (75, 'Can delete 系统-岗位表', 19, 'delete_post');
INSERT INTO `auth_permission` VALUES (76, 'Can view 系统-岗位表', 19, 'view_post');
INSERT INTO `auth_permission` VALUES (77, 'Can add 系统-部门表', 20, 'add_dept');
INSERT INTO `auth_permission` VALUES (78, 'Can change 系统-部门表', 20, 'change_dept');
INSERT INTO `auth_permission` VALUES (79, 'Can delete 系统-部门表', 20, 'delete_dept');
INSERT INTO `auth_permission` VALUES (80, 'Can view 系统-部门表', 20, 'view_dept');
INSERT INTO `auth_permission` VALUES (81, 'Can add 系统-菜单表', 21, 'add_menu');
INSERT INTO `auth_permission` VALUES (82, 'Can change 系统-菜单表', 21, 'change_menu');
INSERT INTO `auth_permission` VALUES (83, 'Can delete 系统-菜单表', 21, 'delete_menu');
INSERT INTO `auth_permission` VALUES (84, 'Can view 系统-菜单表', 21, 'view_menu');
INSERT INTO `auth_permission` VALUES (85, 'Can add 系统-API接口表', 22, 'add_apis');
INSERT INTO `auth_permission` VALUES (86, 'Can change 系统-API接口表', 22, 'change_apis');
INSERT INTO `auth_permission` VALUES (87, 'Can delete 系统-API接口表', 22, 'delete_apis');
INSERT INTO `auth_permission` VALUES (88, 'Can view 系统-API接口表', 22, 'view_apis');
INSERT INTO `auth_permission` VALUES (89, 'Can add 系统-角色表', 23, 'add_role');
INSERT INTO `auth_permission` VALUES (90, 'Can change 系统-角色表', 23, 'change_role');
INSERT INTO `auth_permission` VALUES (91, 'Can delete 系统-角色表', 23, 'delete_role');
INSERT INTO `auth_permission` VALUES (92, 'Can view 系统-角色表', 23, 'view_role');
INSERT INTO `auth_permission` VALUES (93, 'Can add 用户表', 24, 'add_users');
INSERT INTO `auth_permission` VALUES (94, 'Can change 用户表', 24, 'change_users');
INSERT INTO `auth_permission` VALUES (95, 'Can delete 用户表', 24, 'delete_users');
INSERT INTO `auth_permission` VALUES (96, 'Can view 用户表', 24, 'view_users');
INSERT INTO `auth_permission` VALUES (97, 'Can add 系统-字典数值', 25, 'add_dictdata');
INSERT INTO `auth_permission` VALUES (98, 'Can change 系统-字典数值', 25, 'change_dictdata');
INSERT INTO `auth_permission` VALUES (99, 'Can delete 系统-字典数值', 25, 'delete_dictdata');
INSERT INTO `auth_permission` VALUES (100, 'Can view 系统-字典数值', 25, 'view_dictdata');
INSERT INTO `auth_permission` VALUES (101, 'Can add 系统-字典类型', 26, 'add_dicttype');
INSERT INTO `auth_permission` VALUES (102, 'Can change 系统-字典类型', 26, 'change_dicttype');
INSERT INTO `auth_permission` VALUES (103, 'Can delete 系统-字典类型', 26, 'delete_dicttype');
INSERT INTO `auth_permission` VALUES (104, 'Can view 系统-字典类型', 26, 'view_dicttype');
INSERT INTO `auth_permission` VALUES (105, 'Can add 服务监控', 27, 'add_monitormanage');
INSERT INTO `auth_permission` VALUES (106, 'Can change 服务监控', 27, 'change_monitormanage');
INSERT INTO `auth_permission` VALUES (107, 'Can delete 服务监控', 27, 'delete_monitormanage');
INSERT INTO `auth_permission` VALUES (108, 'Can view 服务监控', 27, 'view_monitormanage');
INSERT INTO `auth_permission` VALUES (109, 'Can add 操作日志', 28, 'add_operationlog');
INSERT INTO `auth_permission` VALUES (110, 'Can change 操作日志', 28, 'change_operationlog');
INSERT INTO `auth_permission` VALUES (111, 'Can delete 操作日志', 28, 'delete_operationlog');
INSERT INTO `auth_permission` VALUES (112, 'Can view 操作日志', 28, 'view_operationlog');
INSERT INTO `auth_permission` VALUES (113, 'Can add 消息中心', 29, 'add_messagecenter');
INSERT INTO `auth_permission` VALUES (114, 'Can change 消息中心', 29, 'change_messagecenter');
INSERT INTO `auth_permission` VALUES (115, 'Can delete 消息中心', 29, 'delete_messagecenter');
INSERT INTO `auth_permission` VALUES (116, 'Can view 消息中心', 29, 'view_messagecenter');
INSERT INTO `auth_permission` VALUES (117, 'Can add 消息中心目标用户表', 30, 'add_messagecentertargetuser');
INSERT INTO `auth_permission` VALUES (118, 'Can change 消息中心目标用户表', 30, 'change_messagecentertargetuser');
INSERT INTO `auth_permission` VALUES (119, 'Can delete 消息中心目标用户表', 30, 'delete_messagecentertargetuser');
INSERT INTO `auth_permission` VALUES (120, 'Can view 消息中心目标用户表', 30, 'view_messagecentertargetuser');
INSERT INTO `auth_permission` VALUES (121, 'Can add AI数据集', 31, 'add_aidataset');
INSERT INTO `auth_permission` VALUES (122, 'Can change AI数据集', 31, 'change_aidataset');
INSERT INTO `auth_permission` VALUES (123, 'Can delete AI数据集', 31, 'delete_aidataset');
INSERT INTO `auth_permission` VALUES (124, 'Can view AI数据集', 31, 'view_aidataset');
INSERT INTO `auth_permission` VALUES (125, 'Can add 数据集分类', 32, 'add_datasetcategory');
INSERT INTO `auth_permission` VALUES (126, 'Can change 数据集分类', 32, 'change_datasetcategory');
INSERT INTO `auth_permission` VALUES (127, 'Can delete 数据集分类', 32, 'delete_datasetcategory');
INSERT INTO `auth_permission` VALUES (128, 'Can view 数据集分类', 32, 'view_datasetcategory');
INSERT INTO `auth_permission` VALUES (129, 'Can add 数据集评论', 33, 'add_datasetcomment');
INSERT INTO `auth_permission` VALUES (130, 'Can change 数据集评论', 33, 'change_datasetcomment');
INSERT INTO `auth_permission` VALUES (131, 'Can delete 数据集评论', 33, 'delete_datasetcomment');
INSERT INTO `auth_permission` VALUES (132, 'Can view 数据集评论', 33, 'view_datasetcomment');
INSERT INTO `auth_permission` VALUES (133, 'Can add 数据集版本', 34, 'add_datasetversion');
INSERT INTO `auth_permission` VALUES (134, 'Can change 数据集版本', 34, 'change_datasetversion');
INSERT INTO `auth_permission` VALUES (135, 'Can delete 数据集版本', 34, 'delete_datasetversion');
INSERT INTO `auth_permission` VALUES (136, 'Can view 数据集版本', 34, 'view_datasetversion');
INSERT INTO `auth_permission` VALUES (137, 'Can add AI模型', 35, 'add_aimodel');
INSERT INTO `auth_permission` VALUES (138, 'Can change AI模型', 35, 'change_aimodel');
INSERT INTO `auth_permission` VALUES (139, 'Can delete AI模型', 35, 'delete_aimodel');
INSERT INTO `auth_permission` VALUES (140, 'Can view AI模型', 35, 'view_aimodel');
INSERT INTO `auth_permission` VALUES (141, 'Can add 模型分类', 36, 'add_modelcategory');
INSERT INTO `auth_permission` VALUES (142, 'Can change 模型分类', 36, 'change_modelcategory');
INSERT INTO `auth_permission` VALUES (143, 'Can delete 模型分类', 36, 'delete_modelcategory');
INSERT INTO `auth_permission` VALUES (144, 'Can view 模型分类', 36, 'view_modelcategory');
INSERT INTO `auth_permission` VALUES (145, 'Can add 模型评论', 37, 'add_modelcomment');
INSERT INTO `auth_permission` VALUES (146, 'Can change 模型评论', 37, 'change_modelcomment');
INSERT INTO `auth_permission` VALUES (147, 'Can delete 模型评论', 37, 'delete_modelcomment');
INSERT INTO `auth_permission` VALUES (148, 'Can view 模型评论', 37, 'view_modelcomment');
INSERT INTO `auth_permission` VALUES (149, 'Can add 模型版本', 38, 'add_modelversion');
INSERT INTO `auth_permission` VALUES (150, 'Can change 模型版本', 38, 'change_modelversion');
INSERT INTO `auth_permission` VALUES (151, 'Can delete 模型版本', 38, 'delete_modelversion');
INSERT INTO `auth_permission` VALUES (152, 'Can view 模型版本', 38, 'view_modelversion');

-- ----------------------------
-- Table structure for captcha_captchastore
-- ----------------------------
DROP TABLE IF EXISTS `captcha_captchastore`;
CREATE TABLE `captcha_captchastore`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `challenge` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `response` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `hashkey` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `expiration` datetime(6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `hashkey`(`hashkey` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of captcha_captchastore
-- ----------------------------

-- ----------------------------
-- Table structure for casbin_rule
-- ----------------------------
DROP TABLE IF EXISTS `casbin_rule`;
CREATE TABLE `casbin_rule`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ptype` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `v0` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `v1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `v2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `v3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `v4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `v5` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of casbin_rule
-- ----------------------------

-- ----------------------------
-- Table structure for django_admin_log
-- ----------------------------
DROP TABLE IF EXISTS `django_admin_log`;
CREATE TABLE `django_admin_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `action_time` datetime(6) NOT NULL,
  `object_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `object_repr` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `action_flag` smallint UNSIGNED NOT NULL,
  `change_message` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content_type_id` int NULL DEFAULT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `django_admin_log_content_type_id_c4bce8eb_fk_django_co`(`content_type_id` ASC) USING BTREE,
  INDEX `django_admin_log_user_id_c564eba6_fk_sys_users_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `django_admin_log_content_type_id_c4bce8eb_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `django_admin_log_user_id_c564eba6_fk_sys_users_id` FOREIGN KEY (`user_id`) REFERENCES `sys_users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `django_admin_log_chk_1` CHECK (`action_flag` >= 0)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_admin_log
-- ----------------------------

-- ----------------------------
-- Table structure for django_celery_beat_clockedschedule
-- ----------------------------
DROP TABLE IF EXISTS `django_celery_beat_clockedschedule`;
CREATE TABLE `django_celery_beat_clockedschedule`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `clocked_time` datetime(6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_celery_beat_clockedschedule
-- ----------------------------

-- ----------------------------
-- Table structure for django_celery_beat_crontabschedule
-- ----------------------------
DROP TABLE IF EXISTS `django_celery_beat_crontabschedule`;
CREATE TABLE `django_celery_beat_crontabschedule`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `minute` varchar(240) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `hour` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `day_of_week` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `day_of_month` varchar(124) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `month_of_year` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `timezone` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_celery_beat_crontabschedule
-- ----------------------------

-- ----------------------------
-- Table structure for django_celery_beat_intervalschedule
-- ----------------------------
DROP TABLE IF EXISTS `django_celery_beat_intervalschedule`;
CREATE TABLE `django_celery_beat_intervalschedule`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `every` int NOT NULL,
  `period` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_celery_beat_intervalschedule
-- ----------------------------

-- ----------------------------
-- Table structure for django_celery_beat_periodictask
-- ----------------------------
DROP TABLE IF EXISTS `django_celery_beat_periodictask`;
CREATE TABLE `django_celery_beat_periodictask`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `task` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `kwargs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `queue` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `exchange` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `routing_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `expires` datetime(6) NULL DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL,
  `last_run_at` datetime(6) NULL DEFAULT NULL,
  `total_run_count` int UNSIGNED NOT NULL,
  `date_changed` datetime(6) NOT NULL,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `crontab_id` int NULL DEFAULT NULL,
  `interval_id` int NULL DEFAULT NULL,
  `solar_id` int NULL DEFAULT NULL,
  `one_off` tinyint(1) NOT NULL,
  `start_time` datetime(6) NULL DEFAULT NULL,
  `priority` int UNSIGNED NULL DEFAULT NULL,
  `headers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `clocked_id` int NULL DEFAULT NULL,
  `expire_seconds` int UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name` ASC) USING BTREE,
  INDEX `django_celery_beat_p_crontab_id_d3cba168_fk_django_ce`(`crontab_id` ASC) USING BTREE,
  INDEX `django_celery_beat_p_interval_id_a8ca27da_fk_django_ce`(`interval_id` ASC) USING BTREE,
  INDEX `django_celery_beat_p_solar_id_a87ce72c_fk_django_ce`(`solar_id` ASC) USING BTREE,
  INDEX `django_celery_beat_p_clocked_id_47a69f82_fk_django_ce`(`clocked_id` ASC) USING BTREE,
  CONSTRAINT `django_celery_beat_p_clocked_id_47a69f82_fk_django_ce` FOREIGN KEY (`clocked_id`) REFERENCES `django_celery_beat_clockedschedule` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `django_celery_beat_p_crontab_id_d3cba168_fk_django_ce` FOREIGN KEY (`crontab_id`) REFERENCES `django_celery_beat_crontabschedule` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `django_celery_beat_p_interval_id_a8ca27da_fk_django_ce` FOREIGN KEY (`interval_id`) REFERENCES `django_celery_beat_intervalschedule` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `django_celery_beat_p_solar_id_a87ce72c_fk_django_ce` FOREIGN KEY (`solar_id`) REFERENCES `django_celery_beat_solarschedule` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `django_celery_beat_periodictask_chk_1` CHECK (`total_run_count` >= 0),
  CONSTRAINT `django_celery_beat_periodictask_chk_2` CHECK (`priority` >= 0),
  CONSTRAINT `django_celery_beat_periodictask_chk_3` CHECK (`expire_seconds` >= 0)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_celery_beat_periodictask
-- ----------------------------

-- ----------------------------
-- Table structure for django_celery_beat_periodictasks
-- ----------------------------
DROP TABLE IF EXISTS `django_celery_beat_periodictasks`;
CREATE TABLE `django_celery_beat_periodictasks`  (
  `ident` smallint NOT NULL,
  `last_update` datetime(6) NOT NULL,
  PRIMARY KEY (`ident`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_celery_beat_periodictasks
-- ----------------------------

-- ----------------------------
-- Table structure for django_celery_beat_solarschedule
-- ----------------------------
DROP TABLE IF EXISTS `django_celery_beat_solarschedule`;
CREATE TABLE `django_celery_beat_solarschedule`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `event` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `latitude` decimal(9, 6) NOT NULL,
  `longitude` decimal(9, 6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `django_celery_beat_solar_event_latitude_longitude_ba64999a_uniq`(`event` ASC, `latitude` ASC, `longitude` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_celery_beat_solarschedule
-- ----------------------------

-- ----------------------------
-- Table structure for django_celery_results_chordcounter
-- ----------------------------
DROP TABLE IF EXISTS `django_celery_results_chordcounter`;
CREATE TABLE `django_celery_results_chordcounter`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `group_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `sub_tasks` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `count` int UNSIGNED NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `group_id`(`group_id` ASC) USING BTREE,
  CONSTRAINT `django_celery_results_chordcounter_chk_1` CHECK (`count` >= 0)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_celery_results_chordcounter
-- ----------------------------

-- ----------------------------
-- Table structure for django_celery_results_groupresult
-- ----------------------------
DROP TABLE IF EXISTS `django_celery_results_groupresult`;
CREATE TABLE `django_celery_results_groupresult`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `group_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `date_created` datetime(6) NOT NULL,
  `date_done` datetime(6) NOT NULL,
  `content_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content_encoding` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `group_id`(`group_id` ASC) USING BTREE,
  INDEX `django_cele_date_cr_bd6c1d_idx`(`date_created` ASC) USING BTREE,
  INDEX `django_cele_date_do_caae0e_idx`(`date_done` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_celery_results_groupresult
-- ----------------------------

-- ----------------------------
-- Table structure for django_celery_results_taskresult
-- ----------------------------
DROP TABLE IF EXISTS `django_celery_results_taskresult`;
CREATE TABLE `django_celery_results_taskresult`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content_encoding` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `date_done` datetime(6) NOT NULL,
  `traceback` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `meta` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `task_args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `task_kwargs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `task_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `worker` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `date_created` datetime(6) NOT NULL,
  `periodic_task_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `task_id`(`task_id` ASC) USING BTREE,
  INDEX `django_cele_task_na_08aec9_idx`(`task_name` ASC) USING BTREE,
  INDEX `django_cele_status_9b6201_idx`(`status` ASC) USING BTREE,
  INDEX `django_cele_worker_d54dd8_idx`(`worker` ASC) USING BTREE,
  INDEX `django_cele_date_cr_f04a50_idx`(`date_created` ASC) USING BTREE,
  INDEX `django_cele_date_do_f59aad_idx`(`date_done` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_celery_results_taskresult
-- ----------------------------

-- ----------------------------
-- Table structure for django_content_type
-- ----------------------------
DROP TABLE IF EXISTS `django_content_type`;
CREATE TABLE `django_content_type`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `app_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `django_content_type_app_label_model_76bd3d3b_uniq`(`app_label` ASC, `model` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_content_type
-- ----------------------------
INSERT INTO `django_content_type` VALUES (1, 'admin', 'logentry');
INSERT INTO `django_content_type` VALUES (22, 'app_apis', 'apis');
INSERT INTO `django_content_type` VALUES (31, 'app_dataset', 'aidataset');
INSERT INTO `django_content_type` VALUES (32, 'app_dataset', 'datasetcategory');
INSERT INTO `django_content_type` VALUES (33, 'app_dataset', 'datasetcomment');
INSERT INTO `django_content_type` VALUES (34, 'app_dataset', 'datasetversion');
INSERT INTO `django_content_type` VALUES (20, 'app_dept', 'dept');
INSERT INTO `django_content_type` VALUES (25, 'app_dict', 'dictdata');
INSERT INTO `django_content_type` VALUES (26, 'app_dict', 'dicttype');
INSERT INTO `django_content_type` VALUES (21, 'app_menu', 'menu');
INSERT INTO `django_content_type` VALUES (29, 'app_message', 'messagecenter');
INSERT INTO `django_content_type` VALUES (30, 'app_message', 'messagecentertargetuser');
INSERT INTO `django_content_type` VALUES (35, 'app_model', 'aimodel');
INSERT INTO `django_content_type` VALUES (36, 'app_model', 'modelcategory');
INSERT INTO `django_content_type` VALUES (37, 'app_model', 'modelcomment');
INSERT INTO `django_content_type` VALUES (38, 'app_model', 'modelversion');
INSERT INTO `django_content_type` VALUES (27, 'app_monitor', 'monitormanage');
INSERT INTO `django_content_type` VALUES (28, 'app_operation_log', 'operationlog');
INSERT INTO `django_content_type` VALUES (19, 'app_post', 'post');
INSERT INTO `django_content_type` VALUES (23, 'app_role', 'role');
INSERT INTO `django_content_type` VALUES (24, 'app_user', 'users');
INSERT INTO `django_content_type` VALUES (3, 'auth', 'group');
INSERT INTO `django_content_type` VALUES (2, 'auth', 'permission');
INSERT INTO `django_content_type` VALUES (17, 'captcha', 'captchastore');
INSERT INTO `django_content_type` VALUES (18, 'casbin_adapter', 'casbinrule');
INSERT INTO `django_content_type` VALUES (4, 'contenttypes', 'contenttype');
INSERT INTO `django_content_type` VALUES (14, 'django_celery_beat', 'clockedschedule');
INSERT INTO `django_content_type` VALUES (9, 'django_celery_beat', 'crontabschedule');
INSERT INTO `django_content_type` VALUES (10, 'django_celery_beat', 'intervalschedule');
INSERT INTO `django_content_type` VALUES (11, 'django_celery_beat', 'periodictask');
INSERT INTO `django_content_type` VALUES (12, 'django_celery_beat', 'periodictasks');
INSERT INTO `django_content_type` VALUES (13, 'django_celery_beat', 'solarschedule');
INSERT INTO `django_content_type` VALUES (7, 'django_celery_results', 'chordcounter');
INSERT INTO `django_content_type` VALUES (8, 'django_celery_results', 'groupresult');
INSERT INTO `django_content_type` VALUES (6, 'django_celery_results', 'taskresult');
INSERT INTO `django_content_type` VALUES (5, 'sessions', 'session');
INSERT INTO `django_content_type` VALUES (15, 'token_blacklist', 'blacklistedtoken');
INSERT INTO `django_content_type` VALUES (16, 'token_blacklist', 'outstandingtoken');

-- ----------------------------
-- Table structure for django_migrations
-- ----------------------------
DROP TABLE IF EXISTS `django_migrations`;
CREATE TABLE `django_migrations`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `app` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `applied` datetime(6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 89 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_migrations
-- ----------------------------
INSERT INTO `django_migrations` VALUES (1, 'contenttypes', '0001_initial', '2025-07-02 18:59:18.385254');
INSERT INTO `django_migrations` VALUES (2, 'app_role', '0001_initial', '2025-07-02 18:59:18.438092');
INSERT INTO `django_migrations` VALUES (3, 'app_post', '0001_initial', '2025-07-02 18:59:18.471214');
INSERT INTO `django_migrations` VALUES (4, 'app_dept', '0001_initial', '2025-07-02 18:59:18.511137');
INSERT INTO `django_migrations` VALUES (5, 'app_user', '0001_initial', '2025-07-02 18:59:18.804188');
INSERT INTO `django_migrations` VALUES (6, 'admin', '0001_initial', '2025-07-02 18:59:19.017084');
INSERT INTO `django_migrations` VALUES (7, 'admin', '0002_logentry_remove_auto_add', '2025-07-02 18:59:19.027609');
INSERT INTO `django_migrations` VALUES (8, 'admin', '0003_logentry_add_action_flag_choices', '2025-07-02 18:59:19.047821');
INSERT INTO `django_migrations` VALUES (9, 'app_apis', '0001_initial', '2025-07-02 18:59:19.104083');
INSERT INTO `django_migrations` VALUES (10, 'app_apis', '0002_initial', '2025-07-02 18:59:19.221611');
INSERT INTO `django_migrations` VALUES (11, 'app_dataset', '0001_initial', '2025-07-02 18:59:19.317365');
INSERT INTO `django_migrations` VALUES (12, 'app_dataset', '0002_initial', '2025-07-02 18:59:20.406459');
INSERT INTO `django_migrations` VALUES (13, 'app_dept', '0002_initial', '2025-07-02 18:59:20.617612');
INSERT INTO `django_migrations` VALUES (14, 'app_dict', '0001_initial', '2025-07-02 18:59:20.703165');
INSERT INTO `django_migrations` VALUES (15, 'app_dict', '0002_initial', '2025-07-02 18:59:20.930644');
INSERT INTO `django_migrations` VALUES (16, 'app_menu', '0001_initial', '2025-07-02 18:59:20.962447');
INSERT INTO `django_migrations` VALUES (17, 'app_menu', '0002_initial', '2025-07-02 18:59:21.130226');
INSERT INTO `django_migrations` VALUES (18, 'app_message', '0001_initial', '2025-07-02 18:59:21.183893');
INSERT INTO `django_migrations` VALUES (19, 'app_message', '0002_initial', '2025-07-02 18:59:21.630145');
INSERT INTO `django_migrations` VALUES (20, 'app_model', '0001_initial', '2025-07-02 18:59:21.765246');
INSERT INTO `django_migrations` VALUES (21, 'app_model', '0002_initial', '2025-07-02 18:59:23.050145');
INSERT INTO `django_migrations` VALUES (22, 'app_monitor', '0001_initial', '2025-07-02 18:59:23.079549');
INSERT INTO `django_migrations` VALUES (23, 'app_monitor', '0002_initial', '2025-07-02 18:59:23.177466');
INSERT INTO `django_migrations` VALUES (24, 'app_operation_log', '0001_initial', '2025-07-02 18:59:23.207107');
INSERT INTO `django_migrations` VALUES (25, 'app_operation_log', '0002_initial', '2025-07-02 18:59:23.302501');
INSERT INTO `django_migrations` VALUES (26, 'app_post', '0002_initial', '2025-07-02 18:59:23.448659');
INSERT INTO `django_migrations` VALUES (27, 'app_role', '0002_initial', '2025-07-02 18:59:23.766077');
INSERT INTO `django_migrations` VALUES (28, 'contenttypes', '0002_remove_content_type_name', '2025-07-02 18:59:23.940056');
INSERT INTO `django_migrations` VALUES (29, 'auth', '0001_initial', '2025-07-02 18:59:24.527987');
INSERT INTO `django_migrations` VALUES (30, 'auth', '0002_alter_permission_name_max_length', '2025-07-02 18:59:24.681639');
INSERT INTO `django_migrations` VALUES (31, 'auth', '0003_alter_user_email_max_length', '2025-07-02 18:59:24.694673');
INSERT INTO `django_migrations` VALUES (32, 'auth', '0004_alter_user_username_opts', '2025-07-02 18:59:24.711230');
INSERT INTO `django_migrations` VALUES (33, 'auth', '0005_alter_user_last_login_null', '2025-07-02 18:59:24.722167');
INSERT INTO `django_migrations` VALUES (34, 'auth', '0006_require_contenttypes_0002', '2025-07-02 18:59:24.728166');
INSERT INTO `django_migrations` VALUES (35, 'auth', '0007_alter_validators_add_error_messages', '2025-07-02 18:59:24.740940');
INSERT INTO `django_migrations` VALUES (36, 'auth', '0008_alter_user_username_max_length', '2025-07-02 18:59:24.753009');
INSERT INTO `django_migrations` VALUES (37, 'auth', '0009_alter_user_last_name_max_length', '2025-07-02 18:59:24.764588');
INSERT INTO `django_migrations` VALUES (38, 'auth', '0010_alter_group_name_max_length', '2025-07-02 18:59:24.818563');
INSERT INTO `django_migrations` VALUES (39, 'auth', '0011_update_proxy_permissions', '2025-07-02 18:59:24.849767');
INSERT INTO `django_migrations` VALUES (40, 'auth', '0012_alter_user_first_name_max_length', '2025-07-02 18:59:24.860138');
INSERT INTO `django_migrations` VALUES (41, 'captcha', '0001_initial', '2025-07-02 18:59:24.914523');
INSERT INTO `django_migrations` VALUES (42, 'captcha', '0002_alter_captchastore_id', '2025-07-02 18:59:24.923545');
INSERT INTO `django_migrations` VALUES (43, 'casbin_adapter', '0001_initial', '2025-07-02 18:59:24.963780');
INSERT INTO `django_migrations` VALUES (44, 'casbin_adapter', '0002_alter_casbinrule_id', '2025-07-02 18:59:25.030655');
INSERT INTO `django_migrations` VALUES (45, 'django_celery_beat', '0001_initial', '2025-07-02 18:59:25.358483');
INSERT INTO `django_migrations` VALUES (46, 'django_celery_beat', '0002_auto_20161118_0346', '2025-07-02 18:59:25.480273');
INSERT INTO `django_migrations` VALUES (47, 'django_celery_beat', '0003_auto_20161209_0049', '2025-07-02 18:59:25.515073');
INSERT INTO `django_migrations` VALUES (48, 'django_celery_beat', '0004_auto_20170221_0000', '2025-07-02 18:59:25.526637');
INSERT INTO `django_migrations` VALUES (49, 'django_celery_beat', '0005_add_solarschedule_events_choices', '2025-07-02 18:59:25.534635');
INSERT INTO `django_migrations` VALUES (50, 'django_celery_beat', '0006_auto_20180322_0932', '2025-07-02 18:59:25.656221');
INSERT INTO `django_migrations` VALUES (51, 'django_celery_beat', '0007_auto_20180521_0826', '2025-07-02 18:59:25.912775');
INSERT INTO `django_migrations` VALUES (52, 'django_celery_beat', '0008_auto_20180914_1922', '2025-07-02 18:59:25.943055');
INSERT INTO `django_migrations` VALUES (53, 'django_celery_beat', '0006_auto_20180210_1226', '2025-07-02 18:59:25.962481');
INSERT INTO `django_migrations` VALUES (54, 'django_celery_beat', '0006_periodictask_priority', '2025-07-02 18:59:26.059576');
INSERT INTO `django_migrations` VALUES (55, 'django_celery_beat', '0009_periodictask_headers', '2025-07-02 18:59:26.152632');
INSERT INTO `django_migrations` VALUES (56, 'django_celery_beat', '0010_auto_20190429_0326', '2025-07-02 18:59:26.363898');
INSERT INTO `django_migrations` VALUES (57, 'django_celery_beat', '0011_auto_20190508_0153', '2025-07-02 18:59:26.495819');
INSERT INTO `django_migrations` VALUES (58, 'django_celery_beat', '0012_periodictask_expire_seconds', '2025-07-02 18:59:26.613886');
INSERT INTO `django_migrations` VALUES (59, 'django_celery_beat', '0013_auto_20200609_0727', '2025-07-02 18:59:26.627113');
INSERT INTO `django_migrations` VALUES (60, 'django_celery_beat', '0014_remove_clockedschedule_enabled', '2025-07-02 18:59:26.686715');
INSERT INTO `django_migrations` VALUES (61, 'django_celery_beat', '0015_edit_solarschedule_events_choices', '2025-07-02 18:59:26.697234');
INSERT INTO `django_migrations` VALUES (62, 'django_celery_beat', '0016_alter_crontabschedule_timezone', '2025-07-02 18:59:26.710197');
INSERT INTO `django_migrations` VALUES (63, 'django_celery_beat', '0017_alter_crontabschedule_month_of_year', '2025-07-02 18:59:26.722254');
INSERT INTO `django_migrations` VALUES (64, 'django_celery_beat', '0018_improve_crontab_helptext', '2025-07-02 18:59:26.733776');
INSERT INTO `django_migrations` VALUES (65, 'django_celery_results', '0001_initial', '2025-07-02 18:59:26.800558');
INSERT INTO `django_migrations` VALUES (66, 'django_celery_results', '0002_add_task_name_args_kwargs', '2025-07-02 18:59:27.013808');
INSERT INTO `django_migrations` VALUES (67, 'django_celery_results', '0003_auto_20181106_1101', '2025-07-02 18:59:27.025872');
INSERT INTO `django_migrations` VALUES (68, 'django_celery_results', '0004_auto_20190516_0412', '2025-07-02 18:59:27.132047');
INSERT INTO `django_migrations` VALUES (69, 'django_celery_results', '0005_taskresult_worker', '2025-07-02 18:59:27.244267');
INSERT INTO `django_migrations` VALUES (70, 'django_celery_results', '0006_taskresult_date_created', '2025-07-02 18:59:27.410409');
INSERT INTO `django_migrations` VALUES (71, 'django_celery_results', '0007_remove_taskresult_hidden', '2025-07-02 18:59:27.491884');
INSERT INTO `django_migrations` VALUES (72, 'django_celery_results', '0008_chordcounter', '2025-07-02 18:59:27.532827');
INSERT INTO `django_migrations` VALUES (73, 'django_celery_results', '0009_groupresult', '2025-07-02 18:59:27.895208');
INSERT INTO `django_migrations` VALUES (74, 'django_celery_results', '0010_remove_duplicate_indices', '2025-07-02 18:59:27.914954');
INSERT INTO `django_migrations` VALUES (75, 'django_celery_results', '0011_taskresult_periodic_task_name', '2025-07-02 18:59:27.999986');
INSERT INTO `django_migrations` VALUES (76, 'sessions', '0001_initial', '2025-07-02 18:59:28.046061');
INSERT INTO `django_migrations` VALUES (77, 'token_blacklist', '0001_initial', '2025-07-02 18:59:28.303353');
INSERT INTO `django_migrations` VALUES (78, 'token_blacklist', '0002_outstandingtoken_jti_hex', '2025-07-02 18:59:28.432874');
INSERT INTO `django_migrations` VALUES (79, 'token_blacklist', '0003_auto_20171017_2007', '2025-07-02 18:59:28.475026');
INSERT INTO `django_migrations` VALUES (80, 'token_blacklist', '0004_auto_20171017_2013', '2025-07-02 18:59:28.582522');
INSERT INTO `django_migrations` VALUES (81, 'token_blacklist', '0005_remove_outstandingtoken_jti', '2025-07-02 18:59:28.671033');
INSERT INTO `django_migrations` VALUES (82, 'token_blacklist', '0006_auto_20171017_2113', '2025-07-02 18:59:28.721965');
INSERT INTO `django_migrations` VALUES (83, 'token_blacklist', '0007_auto_20171017_2214', '2025-07-02 18:59:29.131341');
INSERT INTO `django_migrations` VALUES (84, 'token_blacklist', '0008_migrate_to_bigautofield', '2025-07-02 18:59:29.457845');
INSERT INTO `django_migrations` VALUES (85, 'token_blacklist', '0010_fix_migrate_to_bigautofield', '2025-07-02 18:59:29.502190');
INSERT INTO `django_migrations` VALUES (86, 'token_blacklist', '0011_linearizes_history', '2025-07-02 18:59:29.507258');
INSERT INTO `django_migrations` VALUES (87, 'token_blacklist', '0012_alter_outstandingtoken_user', '2025-07-02 18:59:29.545285');
INSERT INTO `django_migrations` VALUES (88, 'app_model', '0003_aimodel_datasets', '2025-07-03 16:04:31.229569');

-- ----------------------------
-- Table structure for django_session
-- ----------------------------
DROP TABLE IF EXISTS `django_session`;
CREATE TABLE `django_session`  (
  `session_key` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `session_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `expire_date` datetime(6) NOT NULL,
  PRIMARY KEY (`session_key`) USING BTREE,
  INDEX `django_session_expire_date_a5c62663`(`expire_date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of django_session
-- ----------------------------

-- ----------------------------
-- Table structure for sys_apis
-- ----------------------------
DROP TABLE IF EXISTS `sys_apis`;
CREATE TABLE `sys_apis`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `path` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `api_group` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `method` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `enable_datasource` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sys_apis_path_api_group_method_7aabfee9_uniq`(`path` ASC, `api_group` ASC, `method` ASC) USING BTREE,
  INDEX `sys_apis_enable_datasource_d6b5f064`(`enable_datasource` ASC) USING BTREE,
  INDEX `sys_apis_creator_id_81a7bd77`(`creator_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_apis
-- ----------------------------
INSERT INTO `sys_apis` VALUES (29775558784, NULL, '2023-09-17 11:00:43.106000', '2023-09-17 11:00:43.106000', '/system/role/role-id-to-menu/:role_id/', '获取单个角色API权限', 'role', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (208122656320, NULL, '2023-10-19 17:05:16.505000', '2023-10-19 17:05:16.505000', '/system/dict-type/', '添加字典类型信息', 'dict', 'POST', '0', NULL);
INSERT INTO `sys_apis` VALUES (208129003520, NULL, '2023-10-19 17:06:55.680000', '2023-10-19 17:06:55.680000', '/system/dict-type/', '获取字典类型列表', 'dict', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (208132094400, NULL, '2023-10-19 17:07:43.975000', '2023-10-19 17:07:43.975000', '/system/dict-type/:dict-type_id/', '删除字典类型', 'dict', 'DELETE', '0', NULL);
INSERT INTO `sys_apis` VALUES (208133283712, NULL, '2023-10-19 17:08:02.558000', '2023-10-19 17:08:02.558000', '/system/dict-type/:dict-type_id/', '更新字典类型', 'dict', 'PUT', '0', NULL);
INSERT INTO `sys_apis` VALUES (208134697920, NULL, '2023-10-19 17:08:24.655000', '2023-10-19 17:08:24.655000', '/system/dict-type/:dict-type_id/', '获取字典类型信息', 'dict', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (208143283520, NULL, '2023-10-19 17:10:38.805000', '2023-10-19 17:10:38.805000', '/system/dict-data/', '添加字典数值信息', 'dict', 'POST', '0', NULL);
INSERT INTO `sys_apis` VALUES (208144666112, NULL, '2023-10-19 17:11:00.408000', '2023-10-19 17:11:00.408000', '/system/dict-data/', '获取字典数值列表', 'dict', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (208146358016, NULL, '2023-10-19 17:11:26.844000', '2023-10-19 17:11:26.844000', '/system/dict-data/:dict-data_id/', '删除字典数值', 'dict', 'DELETE', '0', NULL);
INSERT INTO `sys_apis` VALUES (208148071680, NULL, '2023-10-19 17:11:53.620000', '2023-10-19 17:11:53.620000', '/system/dict-data/:dict-data_id/', '更新字典数值', 'dict', 'PUT', '0', NULL);
INSERT INTO `sys_apis` VALUES (208149170880, NULL, '2023-10-19 17:12:10.795000', '2023-10-19 17:12:10.795000', '/system/dict-data/:dict-data_id/', '查询字典数值信息', 'dict', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (208158315008, NULL, '2023-10-19 17:14:33.672000', '2023-10-19 17:14:33.672000', '/system/menu/menu-tree-simple/', '获取界面菜单树型接口-简单版本', 'menu', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (208182767616, NULL, '2023-10-19 17:20:55.744000', '2023-10-19 17:20:55.744000', '/system/user/user-info/', '获取当前用户信息', 'user', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (208184439552, NULL, '2023-10-19 17:21:21.868000', '2023-10-19 17:21:21.868000', '/system/update-user-info/', '修改当前用户信息', 'user', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (230337122560, NULL, '2023-10-23 17:30:17.540000', '2023-10-23 17:30:17.540000', '/system/role/get-all-roles/', '获取所有角色名称', 'role', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (230339457856, NULL, '2023-10-23 17:30:54.029000', '2023-10-23 17:30:54.029000', '/system/post/get-all-posts/', '获取所有岗位名称', 'post', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (423593769024, '541150219354505', '2023-11-27 16:17:32.641000', '2023-11-27 16:17:32.641000', '/job/crontab/periodic-task/', '查询定时任务调度列表', 'job', 'GET', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (423598306496, '541150219354505', '2023-11-27 16:18:43.539000', '2023-11-27 16:18:43.539000', '/job/crontab/task-result/', '查询定时任务调度详细', 'job', 'GET', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (423599807936, '541150219354505', '2023-11-27 16:19:06.999000', '2023-11-27 16:19:06.999000', '/job/crontab/periodic-task/', '新增定时任务调度', 'job', 'POST', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (423603733440, '541150219354505', '2023-11-27 16:20:44.394000', '2023-11-27 16:20:08.336000', '/job/crontab/periodic-task/:job_id/', '修改定时任务调度', 'job', 'PUT', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (423607226112, '541150219354505', '2023-11-27 16:21:02.908000', '2023-11-27 16:21:02.908000', '/job/crontab/periodic-task/:job_id/', '删除定时任务调度', 'job', 'DELETE', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (423610525504, '541150219354505', '2023-11-27 16:21:54.461000', '2023-11-27 16:21:54.461000', '/job/crontab/periodic-task/enabled/:job_id/', '调度定时任务', 'job', 'PUT', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (423612291584, '541150219354505', '2023-11-27 16:22:22.056000', '2023-11-27 16:22:22.056000', '/job/crontab/periodic-task/tasklist/', '获取本地所有的内置定时任务', 'job', 'GET', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (444430774784, '541150219354505', '2023-12-01 10:43:50.856000', '2023-12-01 10:43:50.856000', '/system/apis/get-all-api-group/', '获取API所有的分组', 'apis', 'GET', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (444583075072, '541150219354505', '2023-12-01 16:47:00.763000', '2023-12-01 11:23:30.548000', '/tool/monitor/get-system-info/', '实时获取本机监控信息', 'monitor', 'GET', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (456847716224, '541150219354505', '2023-12-03 16:37:25.566000', '2023-12-03 16:37:25.566000', '/system/operation-log/delete-all-logs/', '操作日志-清空数据', 'operation_log', 'GET', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (456852236224, '541150219354505', '2023-12-03 16:38:36.191000', '2023-12-03 16:38:36.191000', '/system/operation-log/:log_id/', '操作日志-删除数据', 'operation_log', 'DELETE', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (456853998848, '541150219354505', '2023-12-03 16:39:03.732000', '2023-12-03 16:39:03.732000', '/system/operation-log/', '操作日志-获取数据列表', 'operation_log', 'GET', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (517416997504, '541150219354505', '2023-12-14 15:30:40.586000', '2023-12-14 15:30:40.586000', '/system/message-center/', '查询信息列表', 'message', 'GET', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (517420513792, '541150219354505', '2023-12-14 15:31:44.630000', '2023-12-14 15:31:35.528000', '/system/message-center/:message_id/', '查询信息详细', 'message', 'GET', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (517429348480, '541150219354505', '2023-12-14 15:33:53.570000', '2023-12-14 15:33:53.570000', '/system/message-center/get-self-receive/', '查询我接收的信息列表', 'message', 'GET', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (517430787776, '541150219354505', '2023-12-14 15:34:16.059000', '2023-12-14 15:34:16.059000', '/system/message-center/', '新增信息', 'message', 'POST', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (517432827840, '541150219354505', '2023-12-14 15:34:47.935000', '2023-12-14 15:34:47.935000', '/system/message-center/:message_id/', '删除信息', 'message', 'DELETE', '0', 541150219354505);
INSERT INTO `sys_apis` VALUES (637940542400, '541150219354505', '2024-01-05 10:37:00.975000', '2024-01-05 10:37:00.975000', '/system/user/auth/', '用户角色个人权限信息', 'auth', 'GET', '1', 541150219354505);
INSERT INTO `sys_apis` VALUES (90743268800150, NULL, '2023-08-23 17:06:14.550000', '2023-08-23 17:06:14.550000', '/system/dept/:dept_id/', '更新部门信息', 'dept', 'PUT', '0', NULL);
INSERT INTO `sys_apis` VALUES (96577568100591, NULL, '2023-08-23 17:06:20.384000', '2023-08-23 17:06:20.384000', '/system/dept/:dept_id/', '删除部门信息', 'dept', 'DELETE', '0', NULL);
INSERT INTO `sys_apis` VALUES (141914787100125, NULL, '2023-08-23 17:07:05.722000', '2023-08-23 17:07:05.722000', '/system/dept/:dept_id/', '获取部门信息', 'dept', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (195580817900813, NULL, '2023-08-23 17:07:59.388000', '2023-08-23 17:07:59.388000', '/system/dept/', '获取部门列表', 'dept', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (247035582800744, NULL, '2023-08-23 17:08:50.842000', '2023-08-23 17:08:50.842000', '/system/dept/', '添加部门信息', 'dept', 'POST', '0', NULL);
INSERT INTO `sys_apis` VALUES (310843027100161, NULL, '2023-08-23 17:22:10.277000', '2023-08-23 17:22:10.277000', '/system/apis/', '添加API信息', 'apis', 'POST', '0', NULL);
INSERT INTO `sys_apis` VALUES (324866616600411, NULL, '2023-08-23 17:22:24.301000', '2023-08-23 17:22:24.301000', '/system/apis/', '获取API列表', 'apis', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (345536676700546, NULL, '2023-08-23 17:22:44.971000', '2023-08-23 17:22:44.971000', '/system/apis/:api_id/', '获取API信息', 'apis', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (362784584600938, NULL, '2023-08-23 17:23:02.219000', '2023-08-23 17:23:02.219000', '/system/apis/:api_id/', '删除API信息', 'apis', 'DELETE', '0', NULL);
INSERT INTO `sys_apis` VALUES (371215327200723, NULL, '2023-08-23 17:23:10.650000', '2023-08-23 17:23:10.650000', '/system/apis/:api_id/', '更新API信息', 'apis', 'PUT', '0', NULL);
INSERT INTO `sys_apis` VALUES (517649286600945, NULL, '2023-08-23 17:01:21.933000', '2023-08-23 17:01:21.933000', '/system/post/', '添加岗位信息', 'post', 'POST', '0', NULL);
INSERT INTO `sys_apis` VALUES (532031994000284, NULL, '2023-08-23 17:01:36.316000', '2023-08-23 17:01:36.316000', '/system/post/', '获取岗位列表', 'post', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (541143409226678, NULL, '2023-08-31 17:10:42.718000', '2023-08-31 17:10:42.718000', '/system/menu/', '获取菜单列表', 'menu', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (541143609139789, NULL, '2023-08-31 17:11:30.381000', '2023-08-31 17:11:30.381000', '/system/menu/', '添加菜单信息', 'menu', 'POST', '0', NULL);
INSERT INTO `sys_apis` VALUES (541143771157364, NULL, '2023-09-17 11:01:57.384000', '2023-08-31 17:12:09.009000', '/system/menu/:menu_id/', '获取菜单信息', 'menu', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (541143816816558, NULL, '2023-09-17 11:01:53.455000', '2023-08-31 17:12:19.895000', '/system/menu/:menu_id/', '删除菜单信息', 'menu', 'DELETE', '0', NULL);
INSERT INTO `sys_apis` VALUES (541143897246531, NULL, '2023-09-17 11:01:47.411000', '2023-08-31 17:12:39.071000', '/system/menu/:menu_id/', '更新菜单信息', 'menu', 'PUT', '0', NULL);
INSERT INTO `sys_apis` VALUES (541144112649207, NULL, '2023-10-19 17:14:50.591000', '2023-08-31 17:13:30.427000', '/system/menu/menu-tree/', '获取菜单树形列表', 'menu', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (541144228592353, NULL, '2023-08-31 17:13:58.070000', '2023-08-31 17:13:58.070000', '/system/role/', '获取角色列表', 'role', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (541144299710971, NULL, '2023-08-31 17:14:15.026000', '2023-08-31 17:14:15.026000', '/system/role/', '添加角色信息', 'role', 'POST', '0', NULL);
INSERT INTO `sys_apis` VALUES (541144399661236, NULL, '2023-09-17 11:01:11.776000', '2023-08-31 17:14:38.856000', '/system/role/:role_id/', '删除角色信息', 'role', 'DELETE', '0', NULL);
INSERT INTO `sys_apis` VALUES (541144616150237, NULL, '2023-09-17 11:01:07.455000', '2023-08-31 17:15:30.471000', '/system/role/:role_id/', '获取角色信息', 'role', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (541144660706328, NULL, '2023-09-17 11:01:02.606000', '2023-08-31 17:15:41.094000', '/system/role/:role_id/', '更新角色信息', 'role', 'PUT', '0', NULL);
INSERT INTO `sys_apis` VALUES (541508472194203, NULL, '2023-09-17 11:01:40.076000', '2023-09-01 17:21:20.512000', '/system/user/:user_id/', '更新用户信息', 'user', 'PUT', '0', NULL);
INSERT INTO `sys_apis` VALUES (541508570034733, NULL, '2023-09-17 11:01:33.581000', '2023-09-01 17:21:43.839000', '/system/user/:user_id/', '获取用户信息', 'user', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (541508653081952, NULL, '2023-09-17 11:01:30.257000', '2023-09-01 17:22:03.639000', '/system/user/:user_id/', '删除用户信息', 'user', 'DELETE', '0', NULL);
INSERT INTO `sys_apis` VALUES (541508804928339, NULL, '2023-09-01 17:22:39.842000', '2023-09-01 17:22:39.842000', '/system/user/', '添加用户信息', 'user', 'POST', '0', NULL);
INSERT INTO `sys_apis` VALUES (541509091344777, NULL, '2023-09-01 17:23:48.129000', '2023-09-01 17:23:48.129000', '/system/user/', '获取用户列表', 'user', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (569646089000281, NULL, '2023-08-23 17:02:13.931000', '2023-08-23 17:02:13.931000', '/system/post/:post_id/', '获取岗位信息', 'post', 'GET', '0', NULL);
INSERT INTO `sys_apis` VALUES (581633986900336, NULL, '2023-08-23 17:02:25.918000', '2023-08-23 17:02:25.918000', '/system/post/:post_id/', '删除岗位信息', 'post', 'DELETE', '0', NULL);
INSERT INTO `sys_apis` VALUES (591806089400182, NULL, '2023-08-23 17:02:36.091000', '2023-08-23 17:02:36.091000', '/system/post/:post_id/', '更新岗位信息', 'post', 'PUT', '0', NULL);
INSERT INTO `sys_apis` VALUES (619964488200256, NULL, '2023-08-24 11:27:47.492000', '2023-08-24 11:27:47.492000', '/system/dept/dept-tree/', '获取部门树形列表', 'dept', 'GET', '0', NULL);

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `dept_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dept_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sort` int NOT NULL,
  `leader` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `email` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `parent_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `dept_key`(`dept_key` ASC) USING BTREE,
  UNIQUE INDEX `sys_dept_dept_name_dept_key_5518fe01_uniq`(`dept_name` ASC, `dept_key` ASC) USING BTREE,
  INDEX `sys_dept_creator_id_0f3b99d7`(`creator_id` ASC) USING BTREE,
  INDEX `sys_dept_parent_id_479051d0`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (213709339200, '************', '2025-05-28 10:17:15.194948', '2025-05-21 10:13:28.425353', '技术维护部门', 'jswhbm', 1, '曹以驰', '', '', '0', 541150219354505, NULL);
INSERT INTO `sys_dept` VALUES (213711275328, '541150219354505', '2025-05-21 10:13:58.677410', '2025-05-21 10:13:58.677410', '智能管理部门', 'znglbm', 1, '张哲', NULL, NULL, '0', 541150219354505, 213709339200);
INSERT INTO `sys_dept` VALUES (213712474240, '541150219354505', '2025-05-21 10:14:17.410068', '2025-05-21 10:14:17.410068', '智能训练部门', 'znxlbm', 2, NULL, NULL, NULL, '0', 541150219354505, 213709339200);
INSERT INTO `sys_dept` VALUES (213713567936, '************', '2025-05-28 10:28:34.714215', '2025-05-21 10:14:34.499065', '智能测试部门', 'zncsbm', 3, '', NULL, NULL, '0', 541150219354505, 213709339200);
INSERT INTO `sys_dept` VALUES (213715073792, '541150219354505', '2025-05-21 10:14:58.028890', '2025-05-21 10:14:58.028890', '智能应用部门', 'znyybm', 4, NULL, NULL, NULL, '0', 541150219354505, 213709339200);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `sort` int NOT NULL,
  `dict_label` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dict_value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dict_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `remark` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sys_dict_data_dict_label_dict_value_dict_type_4fc8a994_uniq`(`dict_label` ASC, `dict_value` ASC, `dict_type` ASC) USING BTREE,
  INDEX `sys_dict_data_creator_id_6264b5dc`(`creator_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (2142562432, NULL, '2023-09-12 11:04:37.538000', '2023-09-12 11:04:37.538000', 3, '未知', '2', 'sys_user_sex', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2145471872, NULL, '2023-09-12 11:05:22.998000', '2023-09-12 11:05:22.998000', 0, '目录', 'M', 'sys_menu_type', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2146028928, NULL, '2023-09-12 11:05:31.702000', '2023-09-12 11:05:31.702000', 1, '菜单', 'C', 'sys_menu_type', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2146541632, NULL, '2023-09-12 11:05:39.713000', '2023-09-12 11:05:39.713000', 2, '按钮', 'F', 'sys_menu_type', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2148299328, NULL, '2023-09-12 11:06:07.177000', '2023-09-12 11:06:07.177000', 0, '显示', '0', 'sys_show_hide', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2148816448, NULL, '2023-09-12 11:06:15.257000', '2023-09-12 11:06:15.257000', 1, '隐藏', '1', 'sys_show_hide', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2149911552, NULL, '2023-09-12 11:06:32.368000', '2023-09-12 11:06:32.368000', 1, '是', '0', 'sys_num_yes_no', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2150559168, NULL, '2023-09-12 11:06:42.487000', '2023-09-12 11:06:42.487000', 1, '否', '1', 'sys_num_yes_no', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2153187072, NULL, '2023-09-12 14:56:20.061000', '2023-09-12 11:07:23.548000', 0, '正常', '0', 'sys_yes_no', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2153897792, NULL, '2023-09-12 14:56:25.959000', '2023-09-12 11:07:34.653000', 1, '停用', '1', 'sys_yes_no', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2156130880, NULL, '2023-09-12 11:08:09.545000', '2023-09-12 11:08:09.545000', 1, '创建(POST)', 'POST', 'sys_method_api', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2156676928, NULL, '2023-09-12 11:08:18.077000', '2023-09-12 11:08:18.077000', 1, '查询(GET)', 'GET', 'sys_method_api', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2157233856, NULL, '2023-09-12 11:08:26.779000', '2023-09-12 11:08:26.779000', 1, '修改(PUT)', 'PUT', 'sys_method_api', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (2159468032, NULL, '2023-09-12 11:09:01.688000', '2023-09-12 11:09:01.688000', 1, '删除(DELETE)', 'DELETE', 'sys_method_api', '0', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (506443966400, '541150219354505', '2023-12-12 15:53:06.975000', '2023-12-12 15:53:06.975000', 1, '按用户', '0', 'sys_notice_type', '0', NULL, 541150219354505);
INSERT INTO `sys_dict_data` VALUES (506444722112, '541150219354505', '2023-12-12 15:53:18.783000', '2023-12-12 15:53:18.783000', 2, '按角色', '1', 'sys_notice_type', '0', NULL, 541150219354505);
INSERT INTO `sys_dict_data` VALUES (506446082944, '541150219354505', '2023-12-12 15:53:40.046000', '2023-12-12 15:53:40.046000', 3, '按部门', '2', 'sys_notice_type', '0', NULL, 541150219354505);
INSERT INTO `sys_dict_data` VALUES (506446891712, '541150219354505', '2023-12-12 15:53:52.683000', '2023-12-12 15:53:52.683000', 4, '通知公告', '3', 'sys_notice_type', '0', NULL, 541150219354505);
INSERT INTO `sys_dict_data` VALUES (545107425737310, NULL, '2023-09-11 15:42:17.902000', '2023-09-11 15:42:17.902000', 1, '男', '0', 'sys_user_sex', '0', '', NULL);
INSERT INTO `sys_dict_data` VALUES (545107468653428, NULL, '2023-09-11 15:42:28.134000', '2023-09-11 15:42:28.134000', 2, '女', '1', 'sys_user_sex', '0', '', NULL);

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `dict_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dict_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `remark` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `dict_name`(`dict_name` ASC) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type` ASC) USING BTREE,
  UNIQUE INDEX `sys_dict_type_dict_name_dict_type_bcdb4673_uniq`(`dict_name` ASC, `dict_type` ASC) USING BTREE,
  INDEX `sys_dict_type_creator_id_d66340e7`(`creator_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (506442600448, '541150219354505', '2023-12-12 15:52:45.632000', '2023-12-12 15:52:45.632000', '信息类型', 'sys_notice_type', '0', NULL, 541150219354505);
INSERT INTO `sys_dict_type` VALUES (545106760873017, NULL, '2023-09-11 15:39:39.386000', '2023-09-11 15:39:39.386000', '用户性别', 'sys_user_sex', '0', '性别字典', NULL);
INSERT INTO `sys_dict_type` VALUES (545186208771211, NULL, '2023-09-11 20:55:21.241000', '2023-09-11 20:55:21.241000', '菜单类型', 'sys_menu_type', '0', '菜单类型', NULL);
INSERT INTO `sys_dict_type` VALUES (545186358604333, NULL, '2023-09-11 20:55:56.964000', '2023-09-11 20:55:56.964000', '菜单状态', 'sys_show_hide', '0', '菜单状态', NULL);
INSERT INTO `sys_dict_type` VALUES (545186413918814, NULL, '2023-09-11 20:56:10.152000', '2023-09-11 20:56:10.152000', '数字是否', 'sys_num_yes_no', '0', '数字是否', NULL);
INSERT INTO `sys_dict_type` VALUES (545186489282068, NULL, '2023-09-11 20:56:28.120000', '2023-09-11 20:56:28.120000', '状态是否', 'sys_yes_no', '0', '状态是否', NULL);
INSERT INTO `sys_dict_type` VALUES (545186565144444, '541150219354505', '2023-11-09 15:51:02.889000', '2023-09-11 20:56:46.207000', '网络请求方法', 'sys_method_api', '0', '', NULL);

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `icon` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `menu_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `sort` int NOT NULL,
  `path` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `component` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `is_iframe` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `is_link` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `menu_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `is_hide` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `is_keep_alive` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `is_affix` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `permission` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `remark` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `parent_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sys_menu_creator_id_e639fb96`(`creator_id` ASC) USING BTREE,
  INDEX `sys_menu_parent_id_84e9a06a`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (100, '541150219354505', '2025-05-22 15:12:27.149523', '2023-01-01 00:00:00.000000', 'iconfont icon-crew_feature', '模型管理', 10, '/model', 'Layout', '1', NULL, 'M', '0', '1', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (101, '541150219354505', '2025-05-21 15:57:42.054699', '2023-01-01 00:00:00.000000', 'iconfont icon--chaifenhang', '模型展示', 1, '/model/models', '/model/index', '1', NULL, 'C', '0', '1', '1', NULL, '0', NULL, NULL, 100);
INSERT INTO `sys_menu` VALUES (102, '************', '2025-05-21 11:17:49.299609', '2023-01-01 00:00:00.000000', 'InfoFilled', '模型详情', 2, '/model/models/:id', '/model/detail/index', '1', NULL, 'C', '1', '1', '1', NULL, '0', NULL, NULL, 100);
INSERT INTO `sys_menu` VALUES (43286452544, '541150219354505', '2024-01-13 15:25:50.821000', '2024-01-13 15:25:50.821000', 'elementList', '系统日志', 2, '/system/system-log/', '/log/system/index', '1', NULL, 'C', '0', '1', '1', 'log:system:list', '0', NULL, 541150219354505, 457773413824);
INSERT INTO `sys_menu` VALUES (183121607680, '************', '2025-05-21 11:17:57.832407', '2025-05-15 21:27:55.120000', 'iconfont icon-shuxing', '模型分类', 3, '/model/category', '/model/category/index', '1', NULL, 'C', '0', '1', '1', NULL, '0', NULL, 541150219354505, 100);
INSERT INTO `sys_menu` VALUES (211147415680, '541150219354505', '2025-05-21 00:10:20.851438', '2025-05-20 23:06:18.370683', NULL, '模型添加', 1, '/model/maintenance', '/model/maintenance/index', '1', NULL, 'F', '0', '1', '1', 'model:maintenance:add', '0', NULL, 541150219354505, 211147415680);
INSERT INTO `sys_menu` VALUES (211399077952, '************', '2025-05-21 11:18:01.124509', '2025-05-21 00:11:50.593047', 'iconfont icon-chazhaobiaodanliebiao', '模型维护', 4, '/model/maintenance', '/model/maintenance/index', '1', NULL, 'C', '0', '1', '1', NULL, '0', NULL, 541150219354505, 100);
INSERT INTO `sys_menu` VALUES (211401374144, '541150219354505', '2025-05-21 00:12:26.471684', '2025-05-21 00:12:26.472685', NULL, '模型添加', 1, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'model:maintenance:add', '0', NULL, 541150219354505, 211399077952);
INSERT INTO `sys_menu` VALUES (211405154496, '541150219354505', '2025-05-21 00:13:25.539566', '2025-05-21 00:13:25.540569', NULL, '模型修改', 2, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'model:maintenance:edit', '0', NULL, 541150219354505, 211399077952);
INSERT INTO `sys_menu` VALUES (211407963840, '541150219354505', '2025-05-21 00:14:09.435089', '2025-05-21 00:14:09.435089', NULL, '模型删除', 3, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'model:maintenance:delete', '0', NULL, 541150219354505, 211399077952);
INSERT INTO `sys_menu` VALUES (211411829248, '541150219354505', '2025-05-21 00:15:09.832481', '2025-05-21 00:15:09.832481', NULL, '模型导出', 4, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'model:maintenance:export', '0', NULL, 541150219354505, 211399077952);
INSERT INTO `sys_menu` VALUES (219624086976, '541150219354505', '2025-05-22 15:12:19.517119', '2025-05-22 11:53:46.359457', 'iconfont icon-shenqingkaiban', '数据集管理', 11, '/dataset', 'Layout', '1', NULL, 'M', '0', '1', '1', NULL, '0', NULL, 541150219354505, NULL);
INSERT INTO `sys_menu` VALUES (219627666048, '************', '2025-07-03 18:15:01.364609', '2025-05-22 11:54:42.282324', 'iconfont icon-zidingyibuju', '数据集展示', 1, '/dataset/datasets', '/dataset/index', '1', NULL, 'C', '0', '1', '1', NULL, '0', NULL, 541150219354505, 219624086976);
INSERT INTO `sys_menu` VALUES (219632334784, '541150219354505', '2025-05-23 20:52:16.449261', '2025-05-22 11:55:55.231773', NULL, '数据集详情', 2, '/dataset/datasets/:id', '/dataset/detail/index', '1', NULL, 'C', '1', '1', '1', NULL, '0', NULL, 541150219354505, 219624086976);
INSERT INTO `sys_menu` VALUES (219637094848, '************', '2025-07-03 18:15:17.641824', '2025-05-22 11:57:09.607148', 'iconfont icon-juxingkaobei', '数据集分类', 3, '/dataset/category', '/dataset/category/index', '1', NULL, 'C', '0', '1', '1', NULL, '0', NULL, 541150219354505, 219624086976);
INSERT INTO `sys_menu` VALUES (219640071424, '************', '2025-07-03 18:15:28.170687', '2025-05-22 11:57:56.116014', 'iconfont icon--chaifenlie', '数据集维护', 4, '/dataset/maintenance', '/dataset/maintenance/index', '1', NULL, 'C', '0', '1', '1', NULL, '0', NULL, 541150219354505, 219624086976);
INSERT INTO `sys_menu` VALUES (220408408128, '541150219354505', '2025-05-22 15:18:01.377040', '2025-05-22 15:18:01.377040', NULL, '数据集添加', 1, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'dataset:maintenance:add', '0', NULL, 541150219354505, 219640071424);
INSERT INTO `sys_menu` VALUES (220410273920, '541150219354505', '2025-05-22 15:18:30.530386', '2025-05-22 15:18:30.530386', NULL, '数据集修改', 2, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'dataset:maintenance:edit', '0', NULL, 541150219354505, 219640071424);
INSERT INTO `sys_menu` VALUES (220412032896, '541150219354505', '2025-05-25 23:43:20.936245', '2025-05-22 15:18:58.014439', NULL, '数据集删除', 3, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'dataset:maintenance:delete', '0', '删除', 541150219354505, 219640071424);
INSERT INTO `sys_menu` VALUES (238562361088, '541150219354505', '2025-05-25 22:06:32.385227', '2025-05-25 22:05:36.892980', NULL, '版本管理', 5, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'model:maintenance:version', '0', NULL, 541150219354505, 211399077952);
INSERT INTO `sys_menu` VALUES (285082180800, '541150219354505', '2025-05-16 20:54:56.486000', '2023-11-02 15:06:49.076000', 'elementPlace', '个人中心', 3, '/personal', '/personal/index', '1', NULL, 'C', '0', '1', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (384888858368, '541150219354505', '2023-12-12 14:56:19.181000', '2023-11-20 16:18:08.412000', 'iconfont icon-zujian', '系统工具', 2, '/tool', 'Layout', '1', NULL, 'M', '0', '1', '1', NULL, '0', NULL, 541150219354505, NULL);
INSERT INTO `sys_menu` VALUES (384901100480, '************', '2025-05-21 10:35:33.675642', '2023-11-20 16:21:19.695000', 'elementCpu', '服务监控', 3, '/tool/monitor/', '/tool/monitor/index', '1', NULL, 'C', '0', '1', '1', 'tool:monitor:list', '0', NULL, 541150219354505, 457773413824);
INSERT INTO `sys_menu` VALUES (384924956608, '541150219354505', '2023-12-01 11:22:50.000000', '2023-11-20 16:27:32.447000', 'elementAlarmClock', '定时任务', 2, '/job/crontab/', '/tool/job/index', '1', NULL, 'C', '0', '1', '1', 'tool:job:list', '0', NULL, 541150219354505, 384888858368);
INSERT INTO `sys_menu` VALUES (384928228864, '541150219354505', '2023-11-20 16:28:23.576000', '2023-11-20 16:28:23.576000', NULL, '新增', 1, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'tool:job:add', '0', NULL, 541150219354505, 384924956608);
INSERT INTO `sys_menu` VALUES (384929917440, '541150219354505', '2023-11-20 16:28:49.960000', '2023-11-20 16:28:49.960000', NULL, '编辑', 2, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'tool:job:edit', '0', NULL, 541150219354505, 384924956608);
INSERT INTO `sys_menu` VALUES (384931381952, '541150219354505', '2023-11-20 16:29:12.843000', '2023-11-20 16:29:12.843000', NULL, '删除', 3, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'tool:job:delete', '0', NULL, 541150219354505, 384924956608);
INSERT INTO `sys_menu` VALUES (384934012416, '541150219354505', '2023-11-20 16:29:53.944000', '2023-11-20 16:29:53.944000', NULL, '开关', 4, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'tool:job:run', '0', NULL, 541150219354505, 384924956608);
INSERT INTO `sys_menu` VALUES (419535754688, '541150219354505', '2023-11-26 22:40:46.167000', '2023-11-26 22:40:46.167000', NULL, '日志', 5, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'tool:job:log', '0', NULL, 541150219354505, 384924956608);
INSERT INTO `sys_menu` VALUES (457773413824, '541150219354505', '2025-05-22 14:55:58.180590', '2023-12-03 20:38:29.591000', 'elementNotebook', '日志系统', 15, '/log', 'Layout', '1', NULL, 'M', '0', '1', '1', NULL, '0', NULL, 541150219354505, 7298689300002);
INSERT INTO `sys_menu` VALUES (458000302592, '541150219354505', '2023-12-12 12:56:49.208000', '2023-12-03 21:37:34.728000', 'elementCalendar', '操作日志', 1, '/system/operation-log/', '/log/operation/index', '1', NULL, 'C', '0', '1', '1', 'log:operation:list', '0', NULL, 541150219354505, 457773413824);
INSERT INTO `sys_menu` VALUES (458003252032, '541150219354505', '2023-12-03 21:38:20.813000', '2023-12-03 21:38:20.813000', NULL, '删除', 1, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'log:operation:delete', '0', NULL, 541150219354505, 458000302592);
INSERT INTO `sys_menu` VALUES (458004760128, '541150219354505', '2023-12-03 21:38:44.377000', '2023-12-03 21:38:44.377000', NULL, '清空', 2, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'log:operation:clean', '0', NULL, 541150219354505, 458000302592);
INSERT INTO `sys_menu` VALUES (479138297792, '541150219354505', '2023-12-12 12:55:38.832000', '2023-12-07 17:22:15.903000', 'elementDocument', '信息中心', 9, '/system/message-center', '/system/notice/index', '1', NULL, 'C', '0', '1', '1', NULL, '0', NULL, 541150219354505, 7298689300002);
INSERT INTO `sys_menu` VALUES (479144112576, '541150219354505', '2023-12-07 17:24:29.634000', '2023-12-07 17:23:46.759000', NULL, '新增信息', 1, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'system:notice:add', '0', NULL, 541150219354505, 479138297792);
INSERT INTO `sys_menu` VALUES (479146012288, '541150219354505', '2023-12-07 17:24:33.728000', '2023-12-07 17:24:16.442000', NULL, '删除信息', 2, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'system:notice:delete', '0', NULL, 541150219354505, 479138297792);
INSERT INTO `sys_menu` VALUES (506472197952, '541150219354505', '2023-12-12 16:00:28.093000', '2023-12-12 16:00:28.093000', NULL, '查看信息', 3, NULL, NULL, NULL, NULL, 'F', NULL, NULL, NULL, 'system:notice:view', '0', NULL, 541150219354505, 479138297792);
INSERT INTO `sys_menu` VALUES (7298689300002, '541150219354505', '2023-12-04 14:28:20.651000', '2023-08-24 14:31:50.645000', 'elementSetting', '系统设置', 1, '/system', 'Layout', '1', '', 'M', '0', '0', '1', '', '0', '', NULL, NULL);
INSERT INTO `sys_menu` VALUES (28183947308613, '************', '2025-07-03 11:25:00.972927', '2025-07-01 10:00:46.511492', 'iconfont icon-diannaobangong', '智能应用', 1, '/application', 'Layout', '1', NULL, 'M', '0', '1', '1', NULL, '0', NULL, ************, NULL);
INSERT INTO `sys_menu` VALUES (28184547940725, '************', '2025-07-01 10:03:50.127878', '2025-07-01 10:03:13.149529', 'elementDataAnalysis', '大模型任务演示', 1, '/demo/aiTask', '/demo/aiTask/index', '1', NULL, 'C', '0', '1', '1', NULL, '0', NULL, ************, 28183947308613);
INSERT INTO `sys_menu` VALUES (28185410413028, '************', '2025-07-01 10:06:43.714295', '2025-07-01 10:06:43.714295', 'iconfont icon-diqiu', 'AIChat', 2, 'demo/llmChat', 'llmChat/index', '1', NULL, 'C', '0', '1', '1', NULL, '0', NULL, ************, 28183947308613);
INSERT INTO `sys_menu` VALUES (268338419700006, '************', '2025-05-27 14:36:38.127572', '2023-08-24 14:36:11.683000', 'elementWallet', 'API管理', 2, '/system/api', '/system/api/index', '1', '', 'C', '0', '1', '1', 'system:api:list', '0', '', NULL, 7298689300002);
INSERT INTO `sys_menu` VALUES (324683656700002, '541150219354505', '2023-12-12 12:54:42.494000', '2023-08-24 14:37:08.029000', 'elementMessageBox', '岗位管理', 3, '/system/post', '/system/post/index', '1', '', 'C', '0', '1', '1', 'system:post:list', '0', '', NULL, 7298689300002);
INSERT INTO `sys_menu` VALUES (357134961700006, '541150219354505', '2023-12-12 12:55:08.947000', '2023-08-24 14:37:40.480000', 'elementPlatform', '部门管理', 4, '/system/dept', '/system/dept/index', '1', '', 'C', '0', '1', '1', 'system:dept:list', '0', '', NULL, 7298689300002);
INSERT INTO `sys_menu` VALUES (538569709516226, '541150219354505', '2023-12-12 12:55:23.790000', '2023-08-24 14:43:44.891000', 'elementFinished', '菜单管理', 5, '/system/menu', '/system/menu/index', '1', '', 'C', '0', '1', '1', 'system:menu:list', '0', '菜单', NULL, 7298689300002);
INSERT INTO `sys_menu` VALUES (542593991904854, NULL, '2023-09-04 17:14:48.579000', '2023-09-04 17:14:48.579000', '', '添加api', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:api:add', '0', '', NULL, 268338419700006);
INSERT INTO `sys_menu` VALUES (542594110045814, NULL, '2023-09-04 17:15:16.746000', '2023-09-04 17:15:16.746000', '', '编辑api', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:api:edit', '0', '', NULL, 268338419700006);
INSERT INTO `sys_menu` VALUES (542594206556749, NULL, '2023-09-04 17:15:39.756000', '2023-09-04 17:15:39.756000', '', '删除api', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:api:delete', '0', '', NULL, 268338419700006);
INSERT INTO `sys_menu` VALUES (542594449490837, NULL, '2023-09-04 17:16:37.676000', '2023-09-04 17:16:37.676000', '', '添加岗位', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:post:add', '0', '', NULL, 324683656700002);
INSERT INTO `sys_menu` VALUES (542594511251963, NULL, '2023-09-04 17:16:52.401000', '2023-09-04 17:16:52.401000', '', '编辑岗位', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:post:edit', '0', '', NULL, 324683656700002);
INSERT INTO `sys_menu` VALUES (542594574887944, NULL, '2023-09-04 17:17:07.573000', '2023-09-04 17:17:07.573000', '', '删除岗位', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:post:delete', '0', '', NULL, 324683656700002);
INSERT INTO `sys_menu` VALUES (542594636888145, NULL, '2023-09-04 17:17:22.355000', '2023-09-04 17:17:22.355000', '', '导出岗位', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:post:export', '0', '', NULL, 324683656700002);
INSERT INTO `sys_menu` VALUES (542594825191424, NULL, '2023-09-04 17:18:07.250000', '2023-09-04 17:18:07.250000', '', '添加部门', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:dept:add', '0', '', NULL, 357134961700006);
INSERT INTO `sys_menu` VALUES (542594873530777, NULL, '2023-09-04 17:18:18.775000', '2023-09-04 17:18:18.775000', '', '编辑部门', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:dept:edit', '0', '', NULL, 357134961700006);
INSERT INTO `sys_menu` VALUES (542594930380374, NULL, '2023-09-04 17:18:32.329000', '2023-09-04 17:18:32.329000', '', '删除部门', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:dept:delete', '0', '', NULL, 357134961700006);
INSERT INTO `sys_menu` VALUES (542594993353654, NULL, '2023-09-04 17:18:47.343000', '2023-09-04 17:18:47.343000', '', '导出部门', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:dept:export', '0', '', NULL, 357134961700006);
INSERT INTO `sys_menu` VALUES (542595409881595, NULL, '2023-09-04 17:20:26.651000', '2023-09-04 17:20:26.651000', '', '添加菜单', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:menu:add', '0', '', NULL, 538569709516226);
INSERT INTO `sys_menu` VALUES (542595489577566, NULL, '2023-09-04 17:20:45.652000', '2023-09-04 17:20:45.652000', '', '修改菜单', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:menu:edit', '0', '', NULL, 538569709516226);
INSERT INTO `sys_menu` VALUES (542595568258514, NULL, '2023-09-04 17:21:04.411000', '2023-09-04 17:21:04.411000', '', '删除菜单', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:menu:delete', '0', '', NULL, 538569709516226);
INSERT INTO `sys_menu` VALUES (542596157969268, NULL, '2023-09-04 17:23:25.009000', '2023-09-04 17:23:25.009000', 'elementUserFilled', '角色管理', 6, '/system/role', '/system/role/index', '1', '', 'C', '0', '1', '1', 'system:role:list', '0', '', NULL, 7298689300002);
INSERT INTO `sys_menu` VALUES (542596856497045, NULL, '2023-09-04 17:26:11.551000', '2023-09-04 17:26:11.551000', '', '新增角色', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:role:add', '0', '', NULL, 542596157969268);
INSERT INTO `sys_menu` VALUES (542597283951149, NULL, '2023-09-04 17:27:53.464000', '2023-09-04 17:27:53.464000', '', '删除角色', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:role:delete', '0', '', NULL, 542596157969268);
INSERT INTO `sys_menu` VALUES (542597333875949, NULL, '2023-09-04 17:28:05.367000', '2023-09-04 17:28:05.367000', '', '编辑角色', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:role:edit', '0', '', NULL, 542596157969268);
INSERT INTO `sys_menu` VALUES (542597388884246, NULL, '2023-09-04 17:28:18.482000', '2023-09-04 17:28:18.482000', '', '导出角色', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:role:export', '0', '', NULL, 542596157969268);
INSERT INTO `sys_menu` VALUES (542598150314000, NULL, '2023-09-04 17:31:20.021000', '2023-09-04 17:31:20.021000', 'elementUser', '用户管理', 7, '/system/user', '/system/user/index', '1', '', 'C', '0', '1', '1', 'system:user:list', '0', '', NULL, 7298689300002);
INSERT INTO `sys_menu` VALUES (542598287434186, NULL, '2023-09-04 17:31:52.713000', '2023-09-04 17:31:52.713000', '', '添加用户', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:user:add', '0', '', NULL, 542598150314000);
INSERT INTO `sys_menu` VALUES (542598338923462, NULL, '2023-09-04 17:32:04.989000', '2023-09-04 17:32:04.989000', '', '删除用户', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:user:delete', '0', '', NULL, 542598150314000);
INSERT INTO `sys_menu` VALUES (542598380740673, NULL, '2023-09-04 17:32:14.959000', '2023-09-04 17:32:14.959000', '', '编辑用户', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:user:edit', '0', '', NULL, 542598150314000);
INSERT INTO `sys_menu` VALUES (542598541692895, NULL, '2023-09-04 17:32:53.333000', '2023-09-04 17:32:53.333000', '', '导出用户', 1, '', '', NULL, '', 'F', '0', '1', '1', 'system:user:export', '0', '', NULL, 542598150314000);
INSERT INTO `sys_menu` VALUES (545179689782607, NULL, '2023-09-11 20:29:26.993000', '2023-09-11 20:29:26.993000', 'elementCellphone', '字典管理', 8, '/system/dict', '/system/dict/index', '1', '', 'C', '0', '1', '1', 'system:dict:list', '0', '', NULL, 7298689300002);
INSERT INTO `sys_menu` VALUES (545180315367243, NULL, '2023-09-11 20:31:56.144000', '2023-09-11 20:31:56.144000', '', '添加字典类型', 1, '', '', '', '', 'F', '0', '1', '1', 'system:dictT:add', '0', '', NULL, 545179689782607);
INSERT INTO `sys_menu` VALUES (545180635585576, NULL, '2023-09-11 20:33:12.490000', '2023-09-11 20:33:12.490000', '', '编辑字典类型', 1, '', '', '', '', 'F', '0', '1', '1', 'system:dictT:edit', '0', '', NULL, 545179689782607);
INSERT INTO `sys_menu` VALUES (545180720667033, NULL, '2023-09-11 20:33:32.775000', '2023-09-11 20:33:32.775000', '', '删除字典类型', 1, '', '', '', '', 'F', '0', '1', '1', 'system:dictT:delete', '0', '', NULL, 545179689782607);
INSERT INTO `sys_menu` VALUES (545180793786335, NULL, '2023-09-11 20:33:50.208000', '2023-09-11 20:33:50.208000', '', '导出字典类型', 1, '', '', '', '', 'F', '0', '1', '1', 'system:dictT:export', '0', '', NULL, 545179689782607);
INSERT INTO `sys_menu` VALUES (545180864145784, NULL, '2023-09-11 20:34:06.983000', '2023-09-11 20:34:06.983000', '', '新增字典数据', 1, '', '', '', '', 'F', '0', '1', '1', 'system:dictD:add', '0', '', NULL, 545179689782607);
INSERT INTO `sys_menu` VALUES (545180930025717, NULL, '2023-09-11 20:34:22.690000', '2023-09-11 20:34:22.690000', '', '修改字典数据', 1, '', '', '', '', 'F', '0', '1', '1', 'system:dictD:edit', '0', '', NULL, 545179689782607);
INSERT INTO `sys_menu` VALUES (545181009289674, NULL, '2023-09-11 20:34:41.588000', '2023-09-11 20:34:41.588000', '', '删除字典数据', 1, '', '', '', '', 'F', '0', '1', '1', 'system:dictD:delete', '0', '', NULL, 545179689782607);

-- ----------------------------
-- Table structure for sys_message_center
-- ----------------------------
DROP TABLE IF EXISTS `sys_message_center`;
CREATE TABLE `sys_message_center`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `target_type` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sys_message_center_creator_id_c3ffb8e9`(`creator_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_message_center
-- ----------------------------
INSERT INTO `sys_message_center` VALUES (43330263808, '541150219354505', '2024-01-13 15:37:15.372000', '2024-01-13 15:37:15.372000', '发给用户的', '<p><strong>测试水水水水水水水水水水水水撒啊啊啊啊啊啊</strong></p>', '0', NULL, 541150219354505);
INSERT INTO `sys_message_center` VALUES (43331630656, '541150219354505', '2024-01-13 15:37:36.729000', '2024-01-13 15:37:36.729000', '发给角色的', '<p>撒啊水水水水水水水水说法伽师</p>', '1', NULL, 541150219354505);
INSERT INTO `sys_message_center` VALUES (43332798016, '541150219354505', '2024-01-13 15:37:54.969000', '2024-01-13 15:37:54.970000', '发给部门的', '<p>啊啊啊啊啊啊啊啊啊啊啊啊啊</p>', '2', NULL, 541150219354505);
INSERT INTO `sys_message_center` VALUES (43333811200, '541150219354505', '2024-01-13 15:38:10.800000', '2024-01-13 15:38:10.800000', '公告', '<p>这是一份公告</p>', '3', NULL, 541150219354505);

-- ----------------------------
-- Table structure for sys_message_center_target_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_message_center_target_dept`;
CREATE TABLE `sys_message_center_target_dept`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `messagecenter_id` bigint NOT NULL,
  `dept_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sys_message_center_targe_messagecenter_id_dept_id_3adb7dc5_uniq`(`messagecenter_id` ASC, `dept_id` ASC) USING BTREE,
  INDEX `sys_message_center_target_dept_messagecenter_id_f778cb42`(`messagecenter_id` ASC) USING BTREE,
  INDEX `sys_message_center_target_dept_dept_id_95144393`(`dept_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_message_center_target_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_message_center_target_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_message_center_target_role`;
CREATE TABLE `sys_message_center_target_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `messagecenter_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sys_message_center_targe_messagecenter_id_role_id_6078cb77_uniq`(`messagecenter_id` ASC, `role_id` ASC) USING BTREE,
  INDEX `sys_message_center_target_role_messagecenter_id_e246c7f5`(`messagecenter_id` ASC) USING BTREE,
  INDEX `sys_message_center_target_role_role_id_63509f8a`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_message_center_target_role
-- ----------------------------
INSERT INTO `sys_message_center_target_role` VALUES (1, 43331630656, 444421914176);
INSERT INTO `sys_message_center_target_role` VALUES (2, 43331630656, 540775921959829);

-- ----------------------------
-- Table structure for sys_message_center_target_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_message_center_target_user`;
CREATE TABLE `sys_message_center_target_user`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `is_read` tinyint(1) NULL DEFAULT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `messagecenter_id` bigint NOT NULL,
  `users_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sys_message_center_target_user_creator_id_f9a7693b`(`creator_id` ASC) USING BTREE,
  INDEX `sys_message_center_target_user_messagecenter_id_64702a2d`(`messagecenter_id` ASC) USING BTREE,
  INDEX `sys_message_center_target_user_users_id_877b0128`(`users_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_message_center_target_user
-- ----------------------------
INSERT INTO `sys_message_center_target_user` VALUES (43330264960, '541150219354505', '2024-01-13 15:37:15.390000', '2024-01-13 15:37:15.390000', 0, 541150219354505, 43330263808, 541150219354505);
INSERT INTO `sys_message_center_target_user` VALUES (43331632448, '541150219354505', '2024-01-13 15:37:36.757000', '2024-01-13 15:37:36.757000', 0, 541150219354505, 43331630656, 541150219354505);
INSERT INTO `sys_message_center_target_user` VALUES (43332799680, '541150219354505', '2024-01-13 15:37:54.995000', '2024-01-13 15:37:54.995000', 0, 541150219354505, 43332798016, 541150219354505);
INSERT INTO `sys_message_center_target_user` VALUES (43333812224, '541150219354505', '2024-01-13 15:38:13.084000', '2024-01-13 15:38:10.816000', 1, 541150219354505, 43333811200, 541150219354505);

-- ----------------------------
-- Table structure for sys_monitor
-- ----------------------------
DROP TABLE IF EXISTS `sys_monitor`;
CREATE TABLE `sys_monitor`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `online` tinyint(1) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `days` smallint NOT NULL,
  `interval` smallint NOT NULL,
  `islocal` tinyint(1) NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sys_monitor_creator_id_45feb80c`(`creator_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_monitor
-- ----------------------------

-- ----------------------------
-- Table structure for sys_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `request_modular` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `request_path` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `request_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `request_method` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `request_msg` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `request_ip` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `request_browser` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `response_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `request_os` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `json_result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `status` tinyint(1) NOT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sys_operation_log_creator_id_f2f05184`(`creator_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_operation_log
-- ----------------------------
INSERT INTO `sys_operation_log` VALUES (28675658382586, NULL, '2025-07-02 19:21:33.970957', '2025-07-02 19:21:33.159332', '登录模块', '/login/', '{\'username\': \'paopao\', \'password\': \'******\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'请求成功\'}', 1, 541150219354505);
INSERT INTO `sys_operation_log` VALUES (28675976796142, NULL, '2025-07-02 19:22:51.777245', '2025-07-02 19:22:50.897674', '登录模块', '/login/', '{\'username\': \'admin\', \'password\': \'******\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'请求成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28912438809311, NULL, '2025-07-03 11:25:00.995672', '2025-07-03 11:25:00.881059', '系统-菜单表', '/system/menu/28183947308613/', '{\'id\': 28183947308613, \'modifier_name\': \'admin\', \'creator_name\': \'admin\', \'create_datetime\': \'2025-07-01 10:00:46\', \'update_datetime\': \'2025-07-01 10:01:35\', \'modifier\': \'************\', \'icon\': \'iconfont icon-diannaobangong\', \'menu_name\': \'智能应用\', \'sort\': 1, \'path\': \'/application\', \'component\': \'Layout\', \'is_iframe\': \'1\', \'is_link\': None, \'menu_type\': \'M\', \'is_hide\': \'0\', \'is_keep_alive\': \'1\', \'is_affix\': \'1\', \'permission\': None, \'status\': \'0\', \'remark\': None, \'creator\': ************, \'parent\': None, \'children\': [{\'id\': 28184547940725, \'modifier_name\': \'admin\', \'creator_name\': \'admin\', \'create_datetime\': \'2025-07-01 10:03:13\', \'update_datetime\': \'2025-07-01 10:03:50\', \'modifier\': \'************\', \'icon\': \'elementDataAnalysis\', \'menu_name\': \'大模型任务演示\', \'sort\': 1, \'path\': \'/demo/aiTask\', \'component\': \'/demo/aiTask/index\', \'is_iframe\': \'1\', \'is_link\': None, \'menu_type\': \'C\', \'is_hide\': \'0\', \'is_keep_alive\': \'1\', \'is_affix\': \'1\', \'permission\': None, \'status\': \'0\', \'remark\': None, \'creator\': ************, \'parent\': 28183947308613, \'children\': []}, {\'id\': 28185410413028, \'modifier_name\': \'admin\', \'creator_name\': \'admin\', \'create_datetime\': \'2025-07-01 10:06:43\', \'update_datetime\': \'2025-07-01 10:06:43\', \'modifier\': \'************\', \'icon\': \'iconfont icon-diqiu\', \'menu_name\': \'AIChat\', \'sort\': 2, \'path\': \'demo/llmChat\', \'component\': \'llmChat/index\', \'is_iframe\': \'1\', \'is_link\': None, \'menu_type\': \'C\', \'is_hide\': \'0\', \'is_keep_alive\': \'1\', \'is_affix\': \'1\', \'permission\': None, \'status\': \'0\', \'remark\': None, \'creator\': ************, \'parent\': 28183947308613, \'children\': []}]}', 'PUT', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'更新成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28969965836101, NULL, '2025-07-03 15:19:06.779899', '2025-07-03 15:19:05.566118', '登录模块', '/login/', '{\'username\': \'admin\', \'password\': \'******\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'请求成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28970143721304, NULL, '2025-07-03 15:19:49.054672', '2025-07-03 15:19:48.995403', '数据集分类', '/dataset/categories/', '{\'name\': \'文本\', \'code\': \'1\', \'parent\': None, \'order\': 1, \'is_active\': True}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'新增成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28970199382304, NULL, '2025-07-03 15:20:02.641361', '2025-07-03 15:20:02.583129', '数据集分类', '/dataset/categories/', '{\'name\': \'图像\', \'code\': \'1\', \'parent\': None, \'order\': 1, \'is_active\': True}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'新增成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28970245995800, NULL, '2025-07-03 15:20:13.991524', '2025-07-03 15:20:13.963030', '数据集分类', '/dataset/categories/', '{\'name\': \'音频\', \'code\': \'1\', \'parent\': None, \'order\': 1, \'is_active\': True}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'新增成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28970289407351, NULL, '2025-07-03 15:20:24.622802', '2025-07-03 15:20:24.562652', '数据集分类', '/dataset/categories/', '{\'name\': \'想定\', \'code\': \'1\', \'parent\': None, \'order\': 2, \'is_active\': True}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'新增成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28986816183905, NULL, '2025-07-03 16:27:39.469456', '2025-07-03 16:27:39.419103', 'AI数据集', '/dataset/datasets/get_upload_url/', '{\'filename\': \'课程视频4.mp4\', \'name\': \'测试侦查数据\', \'group\': \'zngl\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'获取上传URL成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28986816804192, NULL, '2025-07-03 16:27:39.587632', '2025-07-03 16:27:39.571937', 'AI数据集', '/dataset/datasets/upload/', '{\'name\': \'测试侦查数据\', \'group\': \'zngl\', \'description\': \'asd\', \'status\': \'online\', \'category_ids\': \'28970143911552\', \'files\': \'[{\"name\":\"课程视频4.mp4\",\"size\":27632923,\"object_name\":\"zngl/测试侦查数据/课程视频4.mp4\",\"relative_path\":\"课程视频4.mp4\"}]\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '400', 'Windows 10', '{\'code\': 400, \'msg\': \'数据集名称必须是3-63个字符，只能包含小写字母、数字、点和连字符\'}', 0, ************);
INSERT INTO `sys_operation_log` VALUES (28986891633879, NULL, '2025-07-03 16:27:57.931594', '2025-07-03 16:27:57.840565', 'AI数据集', '/dataset/datasets/upload/', '{\'name\': \'test\', \'group\': \'zngl\', \'description\': \'asd\', \'status\': \'online\', \'category_ids\': \'28970143911552\', \'files\': \'[{\"name\":\"课程视频4.mp4\",\"size\":27632923,\"object_name\":\"zngl/测试侦查数据/课程视频4.mp4\",\"relative_path\":\"课程视频4.mp4\"}]\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'数据集创建成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28986995851597, NULL, '2025-07-03 16:28:23.349861', '2025-07-03 16:28:23.285536', 'AI数据集', '/dataset/datasets/get_upload_url/', '{\'filename\': \'FormatFactory_setup.exe\', \'name\': \'test2\', \'group\': \'kmg\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'获取上传URL成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28986996938101, NULL, '2025-07-03 16:28:23.625302', '2025-07-03 16:28:23.549007', 'AI数据集', '/dataset/datasets/upload/', '{\'name\': \'test2\', \'group\': \'kmg\', \'description\': \'qwe\', \'status\': \'online\', \'category_ids\': \'28970199566339\', \'files\': \'[{\"name\":\"FormatFactory_setup.exe\",\"size\":127225568,\"object_name\":\"kmg/test2/FormatFactory_setup.exe\",\"relative_path\":\"FormatFactory_setup.exe\"}]\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'数据集创建成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28987236417067, NULL, '2025-07-03 16:29:22.132196', '2025-07-03 16:29:22.015672', 'AI模型', '/model/models/28638203491561/', '{\'id\': 28638203491561, \'modifier_name\': None, \'creator_name\': None, \'create_datetime\': \'2025-07-02 16:49:08\', \'update_datetime\': \'2025-07-02 16:49:08\', \'categories\': [], \'datasets\': [], \'comments\': [], \'latest_version\': {\'id\': 28639090719103, \'version_number\': \'v2.0.0\', \'description\': \'v2版本\', \'status\': \'dev_done\', \'docker_image\': \'cv/resnet50:latest\', \'model_weights_path\': \'cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe\', \'model_docs_path\': \'cv/resnet50/v2.0.0/README.md\', \'test_report_path\': \'cv/resnet50/v2.0.0/测试报告.doc\', \'test_failure_reason\': None, \'create_datetime\': \'2025-07-02T16:52:45.508601\', \'creator\': None}, \'versions\': [{\'id\': 28639090719103, \'modifier_name\': None, \'creator_name\': \'\', \'create_datetime\': \'2025-07-02 16:52:45\', \'update_datetime\': \'2025-07-02 17:07:45\', \'files\': [{\'id\': \'cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe\', \'filename\': \'AlgerMusicPlayer-4.8.2-win-x64.exe\', \'file_path\': \'cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe\', \'file_size\': 106185555, \'last_modified\': \'2025-07-02T09:09:04.948000+00:00\', \'download_url\': \'http://localhost:9000/models/cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T082842Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=9954632fad96ca84dd085b749f023067415ef6253ecbbb353e0bf5b931676705\', \'type\': \'model_weights\'}], \'model_name\': \'resnet50\', \'model_group\': \'cv\', \'minio_path\': \'cv/resnet50\', \'code\': \'resnet50_v2\', \'modifier\': None, \'version_number\': \'v2.0.0\', \'description\': \'v2版本\', \'status\': \'dev_done\', \'docker_image\': \'cv/resnet50:latest\', \'model_weights_path\': \'cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe\', \'model_docs_path\': \'cv/resnet50/v2.0.0/README.md\', \'test_report_path\': \'cv/resnet50/v2.0.0/测试报告.doc\', \'test_failure_reason\': None, \'model\': 28638203491561, \'creator\': None}, {\'id\': 28638302437099, \'modifier_name\': None, \'creator_name\': \'\', \'create_datetime\': \'2025-07-02 16:49:33\', \'update_datetime\': \'2025-07-02 16:49:33\', \'files\': [], \'model_name\': \'resnet50\', \'model_group\': \'cv\', \'minio_path\': \'cv/resnet50\', \'code\': \'resnet50_v5\', \'modifier\': None, \'version_number\': \'v5.0.0\', \'description\': \'初始版本\', \'status\': \'dev_done\', \'docker_image\': \'cv/resnet50:latest\', \'model_weights_path\': None, \'model_docs_path\': None, \'test_report_path\': None, \'test_failure_reason\': None, \'model\': 28638203491561, \'creator\': None}, {\'id\': 28638203552000, \'modifier_name\': None, \'creator_name\': \'\', \'create_datetime\': \'2025-07-02 16:49:08\', \'update_datetime\': \'2025-07-02 16:49:08\', \'files\': [], \'model_name\': \'resnet50\', \'model_group\': \'cv\', \'minio_path\': \'cv/resnet50\', \'code\': \'resnet50_v4\', \'modifier\': None, \'version_number\': \'v4.0.0\', \'description\': \'初始版本\', \'status\': \'dev_done\', \'docker_image\': \'cv/resnet50:latest\', \'model_weights_path\': None, \'model_docs_path\': None, \'test_report_path\': None, \'test_failure_reason\': None, \'model\': 28638203491561, \'creator\': None}], \'current_status\': \'dev_done\', \'current_docker_image\': \'cv/resnet50:latest\', \'code\': \'resnet50\', \'modifier\': None, \'name\': \'resnet50\', \'group\': \'cv\', \'description\': \'经典ResNet模型，用于图像分类任务\', \'embedding_vector\': None, \'minio_path\': \'cv/resnet50\', \'creator\': None, \'categoryMap\': {\'220220920128\': None, \'220224104064\': None, \'220229510784\': None}, \'dataset_ids\': [28986997114154], \'category_ids\': []}', 'PUT', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'更新成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (28989711894110, NULL, '2025-07-03 16:39:26.489946', '2025-07-03 16:39:26.380184', 'AI模型', '/model/models/28638203491561/', '{\'id\': 28638203491561, \'modifier_name\': \'admin\', \'creator_name\': None, \'create_datetime\': \'2025-07-02 16:49:08\', \'update_datetime\': \'2025-07-03 16:29:22\', \'categories\': [], \'datasets\': [{\'id\': 28986997114154, \'name\': \'test2\', \'group\': \'kmg\', \'description\': \'qwe\'}], \'comments\': [], \'latest_version\': {\'id\': 28639090719103, \'version_number\': \'v2.0.0\', \'description\': \'v2版本\', \'status\': \'dev_done\', \'docker_image\': \'cv/resnet50:latest\', \'model_weights_path\': \'cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe\', \'model_docs_path\': \'cv/resnet50/v2.0.0/README.md\', \'test_report_path\': \'cv/resnet50/v2.0.0/测试报告.doc\', \'test_failure_reason\': None, \'create_datetime\': \'2025-07-02T16:52:45.508601\', \'creator\': None}, \'versions\': [{\'id\': 28639090719103, \'modifier_name\': None, \'creator_name\': \'\', \'create_datetime\': \'2025-07-02 16:52:45\', \'update_datetime\': \'2025-07-02 17:07:45\', \'files\': [{\'id\': \'cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe\', \'filename\': \'AlgerMusicPlayer-4.8.2-win-x64.exe\', \'file_path\': \'cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe\', \'file_size\': 106185555, \'last_modified\': \'2025-07-02T09:09:04.948000+00:00\', \'download_url\': \'http://localhost:9000/models/cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T083914Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=f36db700b7b544091dc9e90502bca762e0faa9c4fb89d19931bb44ed87daf6c7\', \'type\': \'model_weights\'}], \'model_name\': \'resnet50\', \'model_group\': \'cv\', \'minio_path\': \'cv/resnet50\', \'code\': \'resnet50_v2\', \'modifier\': None, \'version_number\': \'v2.0.0\', \'description\': \'v2版本\', \'status\': \'dev_done\', \'docker_image\': \'cv/resnet50:latest\', \'model_weights_path\': \'cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe\', \'model_docs_path\': \'cv/resnet50/v2.0.0/README.md\', \'test_report_path\': \'cv/resnet50/v2.0.0/测试报告.doc\', \'test_failure_reason\': None, \'model\': 28638203491561, \'creator\': None}, {\'id\': 28638302437099, \'modifier_name\': None, \'creator_name\': \'\', \'create_datetime\': \'2025-07-02 16:49:33\', \'update_datetime\': \'2025-07-02 16:49:33\', \'files\': [], \'model_name\': \'resnet50\', \'model_group\': \'cv\', \'minio_path\': \'cv/resnet50\', \'code\': \'resnet50_v5\', \'modifier\': None, \'version_number\': \'v5.0.0\', \'description\': \'初始版本\', \'status\': \'dev_done\', \'docker_image\': \'cv/resnet50:latest\', \'model_weights_path\': None, \'model_docs_path\': None, \'test_report_path\': None, \'test_failure_reason\': None, \'model\': 28638203491561, \'creator\': None}, {\'id\': 28638203552000, \'modifier_name\': None, \'creator_name\': \'\', \'create_datetime\': \'2025-07-02 16:49:08\', \'update_datetime\': \'2025-07-02 16:49:08\', \'files\': [], \'model_name\': \'resnet50\', \'model_group\': \'cv\', \'minio_path\': \'cv/resnet50\', \'code\': \'resnet50_v4\', \'modifier\': None, \'version_number\': \'v4.0.0\', \'description\': \'初始版本\', \'status\': \'dev_done\', \'docker_image\': \'cv/resnet50:latest\', \'model_weights_path\': None, \'model_docs_path\': None, \'test_report_path\': None, \'test_failure_reason\': None, \'model\': 28638203491561, \'creator\': None}], \'current_status\': \'dev_done\', \'current_docker_image\': \'cv/resnet50:latest\', \'code\': \'resnet50\', \'modifier\': \'************\', \'name\': \'resnet50\', \'group\': \'cv\', \'description\': \'经典ResNet模型，用于图像分类任务\', \'embedding_vector\': None, \'minio_path\': \'cv/resnet50\', \'creator\': None, \'categoryMap\': {\'220220920128\': None, \'220224104064\': None, \'220229510784\': None}, \'dataset_ids\': [28986997114154, 28986891832337], \'category_ids\': []}', 'PUT', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'更新成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (29012732918086, NULL, '2025-07-03 18:13:06.822243', '2025-07-03 18:13:06.748118', 'AI数据集', '/dataset/datasets/28986997114154/', '{\'id\': 28986997114154, \'modifier_name\': None, \'creator_name\': \'admin\', \'create_datetime\': \'2025-07-03 16:28:23\', \'update_datetime\': \'2025-07-03 16:28:23\', \'categories\': [{\'id\': 28970199566339, \'modifier_name\': \'admin\', \'creator_name\': \'admin\', \'create_datetime\': \'2025-07-03 15:20:02\', \'update_datetime\': \'2025-07-03 15:20:02\', \'modifier\': \'************\', \'name\': \'图像\', \'code\': \'1\', \'order\': 1, \'is_active\': True, \'creator\': ************, \'parent\': None}], \'leaf_categories\': [{\'id\': 28970199566339, \'modifier_name\': \'admin\', \'creator_name\': \'admin\', \'create_datetime\': \'2025-07-03 15:20:02\', \'update_datetime\': \'2025-07-03 15:20:02\', \'modifier\': \'************\', \'name\': \'图像\', \'code\': \'1\', \'order\': 1, \'is_active\': True, \'creator\': ************, \'parent\': None}], \'comments\': [], \'file_list\': [{\'name\': \'FormatFactory_setup.exe\', \'size\': 15, \'last_modified\': \'2025-07-03T08:28:23.389000Z\'}], \'models\': [{\'id\': 28638203491561, \'name\': \'resnet50\', \'group\': \'cv\', \'description\': \'经典ResNet模型，用于图像分类任务\'}], \'current_version\': None, \'versions\': [], \'latest_version\': None, \'modifier\': None, \'name\': \'智能侦照\', \'group\': \'kmg\', \'description\': \'qwe\', \'minio_path\': \'kmg/test2\', \'stars\': 0, \'downloads\': 0, \'parameters\': {}, \'metrics\': {}, \'status\': \'online\', \'creator\': ************, \'categoryMap\': {}, \'category_ids\': []}', 'PUT', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'更新成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (29012861828273, NULL, '2025-07-03 18:13:38.277454', '2025-07-03 18:13:38.219474', 'AI数据集', '/dataset/datasets/28986891832337/', '{\'id\': 28986891832337, \'modifier_name\': None, \'creator_name\': \'admin\', \'create_datetime\': \'2025-07-03 16:27:57\', \'update_datetime\': \'2025-07-03 16:27:57\', \'categories\': [{\'id\': 28970143911552, \'modifier_name\': \'admin\', \'creator_name\': \'admin\', \'create_datetime\': \'2025-07-03 15:19:49\', \'update_datetime\': \'2025-07-03 15:19:49\', \'modifier\': \'************\', \'name\': \'文本\', \'code\': \'1\', \'order\': 1, \'is_active\': True, \'creator\': ************, \'parent\': None}], \'leaf_categories\': [{\'id\': 28970143911552, \'modifier_name\': \'admin\', \'creator_name\': \'admin\', \'create_datetime\': \'2025-07-03 15:19:49\', \'update_datetime\': \'2025-07-03 15:19:49\', \'modifier\': \'************\', \'name\': \'文本\', \'code\': \'1\', \'order\': 1, \'is_active\': True, \'creator\': ************, \'parent\': None}], \'comments\': [], \'file_list\': [], \'models\': [{\'id\': 28638203491561, \'name\': \'resnet50\', \'group\': \'cv\', \'description\': \'经典ResNet模型，用于图像分类任务\'}], \'current_version\': None, \'versions\': [], \'latest_version\': None, \'modifier\': None, \'name\': \'测试数据\', \'group\': \'zngl\', \'description\': \'asd\', \'minio_path\': \'zngl/test\', \'stars\': 0, \'downloads\': 0, \'parameters\': {}, \'metrics\': {}, \'status\': \'online\', \'creator\': ************, \'categoryMap\': {}, \'category_ids\': []}', 'PUT', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'更新成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (29013202262924, NULL, '2025-07-03 18:15:01.377308', '2025-07-03 18:15:01.333524', '系统-菜单表', '/system/menu/219627666048/', '{\'id\': 219627666048, \'modifier_name\': \'泡泡\', \'creator_name\': \'泡泡\', \'create_datetime\': \'2025-05-22 11:54:42\', \'update_datetime\': \'2025-05-22 11:54:42\', \'modifier\': \'541150219354505\', \'icon\': \'iconfont icon-zidingyibuju\', \'menu_name\': \'数据集展示\', \'sort\': 1, \'path\': \'/dataset/datasets\', \'component\': \'/dataset/index\', \'is_iframe\': \'1\', \'is_link\': None, \'menu_type\': \'C\', \'is_hide\': \'0\', \'is_keep_alive\': \'1\', \'is_affix\': \'1\', \'permission\': None, \'status\': \'0\', \'remark\': None, \'creator\': 541150219354505, \'parent\': 219624086976, \'children\': []}', 'PUT', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'更新成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (29013268959682, NULL, '2025-07-03 18:15:17.656573', '2025-07-03 18:15:17.617217', '系统-菜单表', '/system/menu/219637094848/', '{\'id\': 219637094848, \'modifier_name\': \'泡泡\', \'creator_name\': \'泡泡\', \'create_datetime\': \'2025-05-22 11:57:09\', \'update_datetime\': \'2025-05-22 11:57:09\', \'modifier\': \'541150219354505\', \'icon\': \'iconfont icon-juxingkaobei\', \'menu_name\': \'数据集分类\', \'sort\': 3, \'path\': \'/dataset/category\', \'component\': \'/dataset/category/index\', \'is_iframe\': \'1\', \'is_link\': None, \'menu_type\': \'C\', \'is_hide\': \'0\', \'is_keep_alive\': \'1\', \'is_affix\': \'1\', \'permission\': None, \'status\': \'0\', \'remark\': None, \'creator\': 541150219354505, \'parent\': 219624086976, \'children\': []}', 'PUT', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'更新成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (29013312098564, NULL, '2025-07-03 18:15:28.181396', '2025-07-03 18:15:28.149475', '系统-菜单表', '/system/menu/219640071424/', '{\'id\': 219640071424, \'modifier_name\': \'泡泡\', \'creator_name\': \'泡泡\', \'create_datetime\': \'2025-05-22 11:57:56\', \'update_datetime\': \'2025-05-22 11:58:10\', \'modifier\': \'541150219354505\', \'icon\': \'iconfont icon--chaifenlie\', \'menu_name\': \'数据集维护\', \'sort\': 4, \'path\': \'/dataset/maintenance\', \'component\': \'/dataset/maintenance/index\', \'is_iframe\': \'1\', \'is_link\': None, \'menu_type\': \'C\', \'is_hide\': \'0\', \'is_keep_alive\': \'1\', \'is_affix\': \'1\', \'permission\': None, \'status\': \'0\', \'remark\': None, \'creator\': 541150219354505, \'parent\': 219624086976, \'children\': [{\'id\': 220408408128, \'modifier_name\': \'泡泡\', \'creator_name\': \'泡泡\', \'create_datetime\': \'2025-05-22 15:18:01\', \'update_datetime\': \'2025-05-22 15:18:01\', \'modifier\': \'541150219354505\', \'icon\': None, \'menu_name\': \'数据集添加\', \'sort\': 1, \'path\': None, \'component\': None, \'is_iframe\': None, \'is_link\': None, \'menu_type\': \'F\', \'is_hide\': None, \'is_keep_alive\': None, \'is_affix\': None, \'permission\': \'dataset:maintenance:add\', \'status\': \'0\', \'remark\': None, \'creator\': 541150219354505, \'parent\': 219640071424, \'children\': []}, {\'id\': 220410273920, \'modifier_name\': \'泡泡\', \'creator_name\': \'泡泡\', \'create_datetime\': \'2025-05-22 15:18:30\', \'update_datetime\': \'2025-05-22 15:18:30\', \'modifier\': \'541150219354505\', \'icon\': None, \'menu_name\': \'数据集修改\', \'sort\': 2, \'path\': None, \'component\': None, \'is_iframe\': None, \'is_link\': None, \'menu_type\': \'F\', \'is_hide\': None, \'is_keep_alive\': None, \'is_affix\': None, \'permission\': \'dataset:maintenance:edit\', \'status\': \'0\', \'remark\': None, \'creator\': 541150219354505, \'parent\': 219640071424, \'children\': []}, {\'id\': 220412032896, \'modifier_name\': \'泡泡\', \'creator_name\': \'泡泡\', \'create_datetime\': \'2025-05-22 15:18:58\', \'update_datetime\': \'2025-05-25 23:43:20\', \'modifier\': \'541150219354505\', \'icon\': None, \'menu_name\': \'数据集删除\', \'sort\': 3, \'path\': None, \'component\': None, \'is_iframe\': None, \'is_link\': None, \'menu_type\': \'F\', \'is_hide\': None, \'is_keep_alive\': None, \'is_affix\': None, \'permission\': \'dataset:maintenance:delete\', \'status\': \'0\', \'remark\': \'删除\', \'creator\': 541150219354505, \'parent\': 219640071424, \'children\': []}]}', 'PUT', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'更新成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (29013391702351, NULL, '2025-07-03 18:15:48.535088', '2025-07-03 18:15:47.583501', '登录模块', '/login/', '{\'username\': \'admin\', \'password\': \'******\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'请求成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (29014007395889, NULL, '2025-07-03 18:18:17.935337', '2025-07-03 18:18:17.899560', 'AI数据集', '/dataset/datasets/get_upload_url/', '{\'filename\': \'DesktopMgr64.exe\', \'name\': \'测试数据2\', \'group\': \'zncs\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'获取上传URL成功\'}', 1, ************);
INSERT INTO `sys_operation_log` VALUES (29014009111690, NULL, '2025-07-03 18:18:18.332675', '2025-07-03 18:18:18.318926', 'AI数据集', '/dataset/datasets/upload/', '{\'name\': \'测试数据2\', \'group\': \'zncs\', \'description\': \'test2\', \'status\': \'online\', \'category_ids\': \'28970143911552\', \'files\': \'[{\"name\":\"DesktopMgr64.exe\",\"size\":465504,\"object_name\":\"zncs/测试数据2/DesktopMgr64.exe\",\"relative_path\":\"DesktopMgr64.exe\"}]\'}', 'POST', NULL, '127.0.0.1', 'Edge 138.0.0', '400', 'Windows 10', '{\'code\': 400, \'msg\': \'数据集名称必须是3-63个字符，只能包含小写字母、数字、点和连字符\'}', 0, ************);
INSERT INTO `sys_operation_log` VALUES (29019323928039, NULL, '2025-07-03 18:39:55.933659', '2025-07-03 18:39:55.880208', 'AI数据集', '/dataset/datasets/28986891832337/', '{\'id\': 28986891832337, \'modifier_name\': \'admin\', \'creator_name\': \'admin\', \'create_datetime\': \'2025-07-03 16:27:57\', \'update_datetime\': \'2025-07-03 18:13:38\', \'categories\': [], \'leaf_categories\': [], \'comments\': [], \'file_list\': [], \'models\': [{\'id\': 28638203491561, \'name\': \'resnet50\', \'group\': \'cv\', \'description\': \'经典ResNet模型，用于图像分类任务\'}], \'current_version\': None, \'versions\': [], \'latest_version\': None, \'modifier\': \'************\', \'name\': \'测试数据\', \'group\': \'zngl\', \'description\': \'asd\', \'minio_path\': \'zngl/test\', \'stars\': 0, \'downloads\': 0, \'parameters\': {}, \'metrics\': {}, \'status\': \'online\', \'creator\': ************, \'categoryMap\': {\'28970143911552\': None, \'28970199566339\': None, \'28970246056291\': None, \'28970289601641\': None}, \'category_ids\': []}', 'PUT', NULL, '127.0.0.1', 'Edge 138.0.0', '200', 'Windows 10', '{\'code\': 200, \'msg\': \'更新成功\'}', 1, ************);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `post_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `post_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `sort` int NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `remark` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sys_post_post_name_post_code_4dd36caa_uniq`(`post_name` ASC, `post_code` ASC) USING BTREE,
  INDEX `sys_post_creator_id_2baeedd4`(`creator_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (43274229824, '541150219354505', '2025-05-25 23:58:27.734380', '2024-01-13 15:22:39.841000', '测试', 'TEST', 3, '1', '12', 541150219354505);
INSERT INTO `sys_post` VALUES (62104692200590, '541150219354505', '2025-05-25 23:58:09.203668', '2023-08-23 15:15:37.358000', '首席执行官', 'CEO', 1, '0', '123', NULL);
INSERT INTO `sys_post` VALUES (517572581700082, NULL, '2023-08-23 15:39:20.600000', '2023-08-23 15:39:20.600000', '首席技术执行官', 'CTO', 2, '0', '44444', NULL);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `role_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `role_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `sort` int NOT NULL,
  `admin` tinyint(1) NOT NULL,
  `data_scope` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `role_key`(`role_key` ASC) USING BTREE,
  INDEX `sys_role_creator_id_160d93d0`(`creator_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (154555344320, '541150219354505', '2025-05-10 17:28:47.255000', '2025-05-10 17:28:47.255000', '普通用户', 'common', '0', 3, 0, '5', NULL, 541150219354505);
INSERT INTO `sys_role` VALUES (164353133888, '************', '2025-07-01 10:06:59.044868', '2025-05-12 12:00:17.717000', '智能管理后台管理员', 'dev', '0', 4, 0, '5', NULL, 541150219354505);
INSERT INTO `sys_role` VALUES (444421914176, '541150219354505', '2023-12-24 10:43:20.702000', '2023-12-01 10:41:32.409000', '测试角色', 'test', '0', 2, 0, '5', NULL, 541150219354505);
INSERT INTO `sys_role` VALUES (540775921959829, '************', '2025-05-28 10:54:54.484076', '2023-08-30 16:50:26.926000', '管理员', 'admin', '0', 1, 0, '5', NULL, NULL);

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_id` bigint NOT NULL,
  `dept_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sys_role_dept_role_id_dept_id_bf5fcaa6_uniq`(`role_id` ASC, `dept_id` ASC) USING BTREE,
  INDEX `sys_role_dept_role_id_1ff2a70f`(`role_id` ASC) USING BTREE,
  INDEX `sys_role_dept_dept_id_82ec2f3e`(`dept_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_id` bigint NOT NULL,
  `menu_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sys_role_menu_role_id_menu_id_0c6d08c2_uniq`(`role_id` ASC, `menu_id` ASC) USING BTREE,
  INDEX `sys_role_menu_role_id_e0dcb43b`(`role_id` ASC) USING BTREE,
  INDEX `sys_role_menu_menu_id_5c7ca896`(`menu_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 223 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (6, 154555344320, 43286452544);
INSERT INTO `sys_role_menu` VALUES (10, 154555344320, 285082180800);
INSERT INTO `sys_role_menu` VALUES (7, 154555344320, 384888858368);
INSERT INTO `sys_role_menu` VALUES (1, 154555344320, 384901100480);
INSERT INTO `sys_role_menu` VALUES (11, 154555344320, 457773413824);
INSERT INTO `sys_role_menu` VALUES (2, 154555344320, 458000302592);
INSERT INTO `sys_role_menu` VALUES (4, 154555344320, 458003252032);
INSERT INTO `sys_role_menu` VALUES (8, 154555344320, 458004760128);
INSERT INTO `sys_role_menu` VALUES (13, 154555344320, 479138297792);
INSERT INTO `sys_role_menu` VALUES (5, 154555344320, 479144112576);
INSERT INTO `sys_role_menu` VALUES (9, 154555344320, 479146012288);
INSERT INTO `sys_role_menu` VALUES (12, 154555344320, 506472197952);
INSERT INTO `sys_role_menu` VALUES (3, 154555344320, 7298689300002);
INSERT INTO `sys_role_menu` VALUES (174, 164353133888, 100);
INSERT INTO `sys_role_menu` VALUES (175, 164353133888, 101);
INSERT INTO `sys_role_menu` VALUES (176, 164353133888, 102);
INSERT INTO `sys_role_menu` VALUES (156, 164353133888, 183121607680);
INSERT INTO `sys_role_menu` VALUES (164, 164353133888, 211399077952);
INSERT INTO `sys_role_menu` VALUES (165, 164353133888, 211401374144);
INSERT INTO `sys_role_menu` VALUES (166, 164353133888, 211405154496);
INSERT INTO `sys_role_menu` VALUES (167, 164353133888, 211407963840);
INSERT INTO `sys_role_menu` VALUES (157, 164353133888, 211411829248);
INSERT INTO `sys_role_menu` VALUES (212, 164353133888, 219624086976);
INSERT INTO `sys_role_menu` VALUES (215, 164353133888, 219627666048);
INSERT INTO `sys_role_menu` VALUES (216, 164353133888, 219632334784);
INSERT INTO `sys_role_menu` VALUES (214, 164353133888, 219637094848);
INSERT INTO `sys_role_menu` VALUES (213, 164353133888, 219640071424);
INSERT INTO `sys_role_menu` VALUES (217, 164353133888, 220408408128);
INSERT INTO `sys_role_menu` VALUES (218, 164353133888, 220410273920);
INSERT INTO `sys_role_menu` VALUES (219, 164353133888, 220412032896);
INSERT INTO `sys_role_menu` VALUES (202, 164353133888, 238562361088);
INSERT INTO `sys_role_menu` VALUES (28, 164353133888, 384901100480);
INSERT INTO `sys_role_menu` VALUES (33, 164353133888, 457773413824);
INSERT INTO `sys_role_menu` VALUES (15, 164353133888, 458000302592);
INSERT INTO `sys_role_menu` VALUES (29, 164353133888, 458003252032);
INSERT INTO `sys_role_menu` VALUES (31, 164353133888, 458004760128);
INSERT INTO `sys_role_menu` VALUES (23, 164353133888, 7298689300002);
INSERT INTO `sys_role_menu` VALUES (221, 164353133888, 28183947308613);
INSERT INTO `sys_role_menu` VALUES (220, 164353133888, 28184547940725);
INSERT INTO `sys_role_menu` VALUES (222, 164353133888, 28185410413028);
INSERT INTO `sys_role_menu` VALUES (159, 164353133888, 268338419700006);
INSERT INTO `sys_role_menu` VALUES (24, 164353133888, 357134961700006);
INSERT INTO `sys_role_menu` VALUES (36, 164353133888, 538569709516226);
INSERT INTO `sys_role_menu` VALUES (172, 164353133888, 542593991904854);
INSERT INTO `sys_role_menu` VALUES (178, 164353133888, 542594110045814);
INSERT INTO `sys_role_menu` VALUES (170, 164353133888, 542594206556749);
INSERT INTO `sys_role_menu` VALUES (14, 164353133888, 542594825191424);
INSERT INTO `sys_role_menu` VALUES (22, 164353133888, 542594873530777);
INSERT INTO `sys_role_menu` VALUES (40, 164353133888, 542594930380374);
INSERT INTO `sys_role_menu` VALUES (26, 164353133888, 542594993353654);
INSERT INTO `sys_role_menu` VALUES (27, 164353133888, 542595409881595);
INSERT INTO `sys_role_menu` VALUES (41, 164353133888, 542595489577566);
INSERT INTO `sys_role_menu` VALUES (39, 164353133888, 542595568258514);
INSERT INTO `sys_role_menu` VALUES (45, 164353133888, 542596157969268);
INSERT INTO `sys_role_menu` VALUES (19, 164353133888, 542596856497045);
INSERT INTO `sys_role_menu` VALUES (25, 164353133888, 542597283951149);
INSERT INTO `sys_role_menu` VALUES (44, 164353133888, 542597333875949);
INSERT INTO `sys_role_menu` VALUES (20, 164353133888, 542597388884246);
INSERT INTO `sys_role_menu` VALUES (18, 164353133888, 542598150314000);
INSERT INTO `sys_role_menu` VALUES (37, 164353133888, 542598287434186);
INSERT INTO `sys_role_menu` VALUES (34, 164353133888, 542598338923462);
INSERT INTO `sys_role_menu` VALUES (30, 164353133888, 542598380740673);
INSERT INTO `sys_role_menu` VALUES (42, 164353133888, 542598541692895);
INSERT INTO `sys_role_menu` VALUES (63, 444421914176, 285082180800);
INSERT INTO `sys_role_menu` VALUES (50, 444421914176, 384888858368);
INSERT INTO `sys_role_menu` VALUES (69, 444421914176, 384901100480);
INSERT INTO `sys_role_menu` VALUES (74, 444421914176, 384924956608);
INSERT INTO `sys_role_menu` VALUES (47, 444421914176, 384928228864);
INSERT INTO `sys_role_menu` VALUES (51, 444421914176, 384929917440);
INSERT INTO `sys_role_menu` VALUES (77, 444421914176, 384931381952);
INSERT INTO `sys_role_menu` VALUES (53, 444421914176, 384934012416);
INSERT INTO `sys_role_menu` VALUES (93, 444421914176, 419535754688);
INSERT INTO `sys_role_menu` VALUES (76, 444421914176, 457773413824);
INSERT INTO `sys_role_menu` VALUES (48, 444421914176, 458000302592);
INSERT INTO `sys_role_menu` VALUES (70, 444421914176, 458003252032);
INSERT INTO `sys_role_menu` VALUES (73, 444421914176, 458004760128);
INSERT INTO `sys_role_menu` VALUES (100, 444421914176, 479138297792);
INSERT INTO `sys_role_menu` VALUES (72, 444421914176, 479144112576);
INSERT INTO `sys_role_menu` VALUES (52, 444421914176, 479146012288);
INSERT INTO `sys_role_menu` VALUES (78, 444421914176, 506472197952);
INSERT INTO `sys_role_menu` VALUES (61, 444421914176, 7298689300002);
INSERT INTO `sys_role_menu` VALUES (62, 444421914176, 268338419700006);
INSERT INTO `sys_role_menu` VALUES (91, 444421914176, 324683656700002);
INSERT INTO `sys_role_menu` VALUES (65, 444421914176, 357134961700006);
INSERT INTO `sys_role_menu` VALUES (98, 444421914176, 538569709516226);
INSERT INTO `sys_role_menu` VALUES (86, 444421914176, 542593991904854);
INSERT INTO `sys_role_menu` VALUES (96, 444421914176, 542594110045814);
INSERT INTO `sys_role_menu` VALUES (82, 444421914176, 542594206556749);
INSERT INTO `sys_role_menu` VALUES (58, 444421914176, 542594449490837);
INSERT INTO `sys_role_menu` VALUES (68, 444421914176, 542594511251963);
INSERT INTO `sys_role_menu` VALUES (54, 444421914176, 542594574887944);
INSERT INTO `sys_role_menu` VALUES (84, 444421914176, 542594636888145);
INSERT INTO `sys_role_menu` VALUES (49, 444421914176, 542594825191424);
INSERT INTO `sys_role_menu` VALUES (60, 444421914176, 542594873530777);
INSERT INTO `sys_role_menu` VALUES (87, 444421914176, 542594930380374);
INSERT INTO `sys_role_menu` VALUES (67, 444421914176, 542594993353654);
INSERT INTO `sys_role_menu` VALUES (99, 444421914176, 542595409881595);
INSERT INTO `sys_role_menu` VALUES (88, 444421914176, 542595489577566);
INSERT INTO `sys_role_menu` VALUES (85, 444421914176, 542595568258514);
INSERT INTO `sys_role_menu` VALUES (94, 444421914176, 542596157969268);
INSERT INTO `sys_role_menu` VALUES (56, 444421914176, 542596856497045);
INSERT INTO `sys_role_menu` VALUES (66, 444421914176, 542597283951149);
INSERT INTO `sys_role_menu` VALUES (92, 444421914176, 542597333875949);
INSERT INTO `sys_role_menu` VALUES (57, 444421914176, 542597388884246);
INSERT INTO `sys_role_menu` VALUES (55, 444421914176, 542598150314000);
INSERT INTO `sys_role_menu` VALUES (79, 444421914176, 542598287434186);
INSERT INTO `sys_role_menu` VALUES (75, 444421914176, 542598338923462);
INSERT INTO `sys_role_menu` VALUES (71, 444421914176, 542598380740673);
INSERT INTO `sys_role_menu` VALUES (90, 444421914176, 542598541692895);
INSERT INTO `sys_role_menu` VALUES (83, 444421914176, 545179689782607);
INSERT INTO `sys_role_menu` VALUES (80, 444421914176, 545180315367243);
INSERT INTO `sys_role_menu` VALUES (64, 444421914176, 545180635585576);
INSERT INTO `sys_role_menu` VALUES (59, 444421914176, 545180720667033);
INSERT INTO `sys_role_menu` VALUES (89, 444421914176, 545180793786335);
INSERT INTO `sys_role_menu` VALUES (97, 444421914176, 545180864145784);
INSERT INTO `sys_role_menu` VALUES (95, 444421914176, 545180930025717);
INSERT INTO `sys_role_menu` VALUES (81, 444421914176, 545181009289674);
INSERT INTO `sys_role_menu` VALUES (197, 540775921959829, 100);
INSERT INTO `sys_role_menu` VALUES (198, 540775921959829, 101);
INSERT INTO `sys_role_menu` VALUES (199, 540775921959829, 102);
INSERT INTO `sys_role_menu` VALUES (203, 540775921959829, 43286452544);
INSERT INTO `sys_role_menu` VALUES (181, 540775921959829, 183121607680);
INSERT INTO `sys_role_menu` VALUES (189, 540775921959829, 211399077952);
INSERT INTO `sys_role_menu` VALUES (191, 540775921959829, 211401374144);
INSERT INTO `sys_role_menu` VALUES (190, 540775921959829, 211405154496);
INSERT INTO `sys_role_menu` VALUES (192, 540775921959829, 211407963840);
INSERT INTO `sys_role_menu` VALUES (182, 540775921959829, 211411829248);
INSERT INTO `sys_role_menu` VALUES (207, 540775921959829, 219624086976);
INSERT INTO `sys_role_menu` VALUES (206, 540775921959829, 219627666048);
INSERT INTO `sys_role_menu` VALUES (204, 540775921959829, 219632334784);
INSERT INTO `sys_role_menu` VALUES (205, 540775921959829, 219637094848);
INSERT INTO `sys_role_menu` VALUES (208, 540775921959829, 219640071424);
INSERT INTO `sys_role_menu` VALUES (209, 540775921959829, 220408408128);
INSERT INTO `sys_role_menu` VALUES (210, 540775921959829, 220410273920);
INSERT INTO `sys_role_menu` VALUES (211, 540775921959829, 220412032896);
INSERT INTO `sys_role_menu` VALUES (183, 540775921959829, 238562361088);
INSERT INTO `sys_role_menu` VALUES (117, 540775921959829, 285082180800);
INSERT INTO `sys_role_menu` VALUES (104, 540775921959829, 384888858368);
INSERT INTO `sys_role_menu` VALUES (123, 540775921959829, 384901100480);
INSERT INTO `sys_role_menu` VALUES (128, 540775921959829, 384924956608);
INSERT INTO `sys_role_menu` VALUES (101, 540775921959829, 384928228864);
INSERT INTO `sys_role_menu` VALUES (105, 540775921959829, 384929917440);
INSERT INTO `sys_role_menu` VALUES (131, 540775921959829, 384931381952);
INSERT INTO `sys_role_menu` VALUES (107, 540775921959829, 384934012416);
INSERT INTO `sys_role_menu` VALUES (147, 540775921959829, 419535754688);
INSERT INTO `sys_role_menu` VALUES (130, 540775921959829, 457773413824);
INSERT INTO `sys_role_menu` VALUES (102, 540775921959829, 458000302592);
INSERT INTO `sys_role_menu` VALUES (124, 540775921959829, 458003252032);
INSERT INTO `sys_role_menu` VALUES (127, 540775921959829, 458004760128);
INSERT INTO `sys_role_menu` VALUES (186, 540775921959829, 479138297792);
INSERT INTO `sys_role_menu` VALUES (187, 540775921959829, 479144112576);
INSERT INTO `sys_role_menu` VALUES (180, 540775921959829, 479146012288);
INSERT INTO `sys_role_menu` VALUES (188, 540775921959829, 506472197952);
INSERT INTO `sys_role_menu` VALUES (115, 540775921959829, 7298689300002);
INSERT INTO `sys_role_menu` VALUES (116, 540775921959829, 268338419700006);
INSERT INTO `sys_role_menu` VALUES (145, 540775921959829, 324683656700002);
INSERT INTO `sys_role_menu` VALUES (119, 540775921959829, 357134961700006);
INSERT INTO `sys_role_menu` VALUES (152, 540775921959829, 538569709516226);
INSERT INTO `sys_role_menu` VALUES (140, 540775921959829, 542593991904854);
INSERT INTO `sys_role_menu` VALUES (150, 540775921959829, 542594110045814);
INSERT INTO `sys_role_menu` VALUES (136, 540775921959829, 542594206556749);
INSERT INTO `sys_role_menu` VALUES (112, 540775921959829, 542594449490837);
INSERT INTO `sys_role_menu` VALUES (122, 540775921959829, 542594511251963);
INSERT INTO `sys_role_menu` VALUES (108, 540775921959829, 542594574887944);
INSERT INTO `sys_role_menu` VALUES (138, 540775921959829, 542594636888145);
INSERT INTO `sys_role_menu` VALUES (103, 540775921959829, 542594825191424);
INSERT INTO `sys_role_menu` VALUES (114, 540775921959829, 542594873530777);
INSERT INTO `sys_role_menu` VALUES (141, 540775921959829, 542594930380374);
INSERT INTO `sys_role_menu` VALUES (121, 540775921959829, 542594993353654);
INSERT INTO `sys_role_menu` VALUES (153, 540775921959829, 542595409881595);
INSERT INTO `sys_role_menu` VALUES (142, 540775921959829, 542595489577566);
INSERT INTO `sys_role_menu` VALUES (139, 540775921959829, 542595568258514);
INSERT INTO `sys_role_menu` VALUES (148, 540775921959829, 542596157969268);
INSERT INTO `sys_role_menu` VALUES (110, 540775921959829, 542596856497045);
INSERT INTO `sys_role_menu` VALUES (120, 540775921959829, 542597283951149);
INSERT INTO `sys_role_menu` VALUES (146, 540775921959829, 542597333875949);
INSERT INTO `sys_role_menu` VALUES (111, 540775921959829, 542597388884246);
INSERT INTO `sys_role_menu` VALUES (109, 540775921959829, 542598150314000);
INSERT INTO `sys_role_menu` VALUES (133, 540775921959829, 542598287434186);
INSERT INTO `sys_role_menu` VALUES (129, 540775921959829, 542598338923462);
INSERT INTO `sys_role_menu` VALUES (125, 540775921959829, 542598380740673);
INSERT INTO `sys_role_menu` VALUES (144, 540775921959829, 542598541692895);
INSERT INTO `sys_role_menu` VALUES (195, 540775921959829, 545179689782607);
INSERT INTO `sys_role_menu` VALUES (194, 540775921959829, 545180315367243);
INSERT INTO `sys_role_menu` VALUES (185, 540775921959829, 545180635585576);
INSERT INTO `sys_role_menu` VALUES (184, 540775921959829, 545180720667033);
INSERT INTO `sys_role_menu` VALUES (196, 540775921959829, 545180793786335);
INSERT INTO `sys_role_menu` VALUES (201, 540775921959829, 545180864145784);
INSERT INTO `sys_role_menu` VALUES (200, 540775921959829, 545180930025717);
INSERT INTO `sys_role_menu` VALUES (193, 540775921959829, 545181009289674);

-- ----------------------------
-- Table structure for sys_users
-- ----------------------------
DROP TABLE IF EXISTS `sys_users`;
CREATE TABLE `sys_users`  (
  `password` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `last_login` datetime(6) NULL DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  `id` bigint NOT NULL,
  `modifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_datetime` datetime(6) NULL DEFAULT NULL,
  `create_datetime` datetime(6) NULL DEFAULT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `username` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `nickname` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `employee_no` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `gender` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `last_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `is_delete` tinyint(1) NOT NULL,
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creator_id` bigint NULL DEFAULT NULL,
  `dept_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  UNIQUE INDEX `nickname`(`nickname` ASC) USING BTREE,
  UNIQUE INDEX `employee_no`(`employee_no` ASC) USING BTREE,
  INDEX `sys_users_creator_id_4deb45c3`(`creator_id` ASC) USING BTREE,
  INDEX `sys_users_dept_id_43668958`(`dept_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_users
-- ----------------------------
INSERT INTO `sys_users` VALUES ('pbkdf2_sha256$600000$woUtJWvr2JWiyR04BMsiyX$YBloexXvn4AxbSdb7aM9cwIUJ7wSwbBUio0Aj1cUP74=', NULL, 0, 1, 1, '2025-05-21 10:18:17.763608', ************, '541150219354505', '2025-05-21 10:18:17.763608', '2025-05-21 10:18:17.763608', '0', 'admin', 'admin', NULL, NULL, '13160006316', NULL, '0', NULL, 0, NULL, 541150219354505, 213711275328);
INSERT INTO `sys_users` VALUES ('pbkdf2_sha256$600000$1SVwiFM9iAPk7r8BkWUCSO$jDlI97PDqf2DsIrleSvH8sNz4URxpTYUp0oWT9xbzQs=', NULL, 1, 1, 1, '2023-08-31 17:37:46.000000', 541150219354505, '541150219354505', '2025-05-22 15:56:11.625800', '2023-08-31 17:37:46.379000', '0', 'paopao', '泡泡', NULL, '<EMAIL>', '13229671229', NULL, '0', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTcwMjcxNjIwNSwiaWF0IjoxNzAyNjI5ODA1LCJqdGkiOiI2MTNhNDAzNWYyOTA0MTk4YjIzOWNjZDhlMGNmY2JmZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.XL5Ksj-9gpYh-ktU3_3mHC3aBQidUzwmy9-Odv2h2Oo', 0, NULL, NULL, 213711275328);

-- ----------------------------
-- Table structure for sys_users_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_users_post`;
CREATE TABLE `sys_users_post`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `users_id` bigint NOT NULL,
  `post_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sys_users_post_users_id_post_id_4e6e7837_uniq`(`users_id` ASC, `post_id` ASC) USING BTREE,
  INDEX `sys_users_post_users_id_6e6341d1`(`users_id` ASC) USING BTREE,
  INDEX `sys_users_post_post_id_92a20fd8`(`post_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_users_post
-- ----------------------------
INSERT INTO `sys_users_post` VALUES (4, 541150219354505, 62104692200590);

-- ----------------------------
-- Table structure for sys_users_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_users_role`;
CREATE TABLE `sys_users_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `users_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sys_users_role_users_id_role_id_35beedf2_uniq`(`users_id` ASC, `role_id` ASC) USING BTREE,
  INDEX `sys_users_role_users_id_6e197ce2`(`users_id` ASC) USING BTREE,
  INDEX `sys_users_role_role_id_972c6b01`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_users_role
-- ----------------------------
INSERT INTO `sys_users_role` VALUES (6, ************, 164353133888);
INSERT INTO `sys_users_role` VALUES (5, 541150219354505, 540775921959829);

-- ----------------------------
-- Table structure for token_blacklist_blacklistedtoken
-- ----------------------------
DROP TABLE IF EXISTS `token_blacklist_blacklistedtoken`;
CREATE TABLE `token_blacklist_blacklistedtoken`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `blacklisted_at` datetime(6) NOT NULL,
  `token_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `token_id`(`token_id` ASC) USING BTREE,
  CONSTRAINT `token_blacklist_blacklistedtoken_token_id_3cc7fe56_fk` FOREIGN KEY (`token_id`) REFERENCES `token_blacklist_outstandingtoken` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of token_blacklist_blacklistedtoken
-- ----------------------------

-- ----------------------------
-- Table structure for token_blacklist_outstandingtoken
-- ----------------------------
DROP TABLE IF EXISTS `token_blacklist_outstandingtoken`;
CREATE TABLE `token_blacklist_outstandingtoken`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `token` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime(6) NULL DEFAULT NULL,
  `expires_at` datetime(6) NOT NULL,
  `user_id` bigint NULL DEFAULT NULL,
  `jti` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `token_blacklist_outstandingtoken_jti_hex_d9bdf6f7_uniq`(`jti` ASC) USING BTREE,
  INDEX `token_blacklist_outs_user_id_83bc629a_fk_sys_users`(`user_id` ASC) USING BTREE,
  CONSTRAINT `token_blacklist_outs_user_id_83bc629a_fk_sys_users` FOREIGN KEY (`user_id`) REFERENCES `sys_users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 459 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of token_blacklist_outstandingtoken
-- ----------------------------
INSERT INTO `token_blacklist_outstandingtoken` VALUES (1, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njg0NzkzOCwiaWF0IjoxNzQ2NzYxNTM4LCJqdGkiOiI3ZGUwODAyMjg0NzM0MDMxOGRiOTJmZTE1MjZhOTcwMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.r5T81hDc2BpwwBItQe0K9cj3qOYAnUkE6vsN3w2GhCc', '2025-05-09 03:32:18.853000', '2025-05-10 03:32:18.000000', 541150219354505, '7de08022847340318db92fe1526a9703');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (2, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njg0NzkzOCwiaWF0IjoxNzQ2NzYxNTM4LCJqdGkiOiIyZTgxNTgxMDU3Y2Y0YjcxYmM1NjQxYmQzYjZhNTk4OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.qXa1Jk9gToZHMsOXMg5L3Q566dCcvtdChlkvUBO4rVc', '2025-05-09 03:32:18.860000', '2025-05-10 03:32:18.000000', 541150219354505, '2e81581057cf4b71bc5641bd3b6a5988');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (3, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDM4MiwiaWF0IjoxNzQ2ODYzOTgyLCJqdGkiOiJjYjc1MDJjMWIwMTg0ZWNhYTgwMjM5MjI3OTg3ODEzNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.2-uyroOivktJQE4CT2NIXOec4rol8kjQKxk5uza98qk', '2025-05-10 07:59:42.384000', '2025-05-11 07:59:42.000000', 541150219354505, 'cb7502c1b0184ecaa802392279878137');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (4, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDM4MiwiaWF0IjoxNzQ2ODYzOTgyLCJqdGkiOiI1YmM0NGE1ZDVlZWU0YjkxOThkZTlhNjI4NjI5NmZhMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.QRy1WhNCw81nf9f3pQpNTTfZFGFOzWGz8W1Z62YZ6xw', '2025-05-10 07:59:42.391000', '2025-05-11 07:59:42.000000', 541150219354505, '5bc44a5d5eee4b9198de9a6286296fa0');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (5, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDU0OSwiaWF0IjoxNzQ2ODY0MTQ5LCJqdGkiOiIyYWY2MjkxMmVjMGI0ZWU4YmJmZmQzOTQ2MzkwOTliOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.7gqKdI3TQB-k-3x9ojJpzcDal-z7ClCzi4t7DvihrlI', '2025-05-10 08:02:29.195000', '2025-05-11 08:02:29.000000', 541150219354505, '2af62912ec0b4ee8bbffd394639099b9');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (6, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDU0OSwiaWF0IjoxNzQ2ODY0MTQ5LCJqdGkiOiI5MmU4NDc2MGJkZGM0MWMzYTYxYzQwY2JjOWE1NzFhNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.tceJv_FzmF6X6BL79b-U8f3IvKpwwFe8RstGajIP7lw', '2025-05-10 08:02:29.204000', '2025-05-11 08:02:29.000000', 541150219354505, '92e84760bddc41c3a61c40cbc9a571a6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (7, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDc5OCwiaWF0IjoxNzQ2ODY0Mzk4LCJqdGkiOiIwNTU2NjU4OTNlN2Y0NTg2YmI1NWVkOGQ1YjkxNGE1MCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.E3boZE72VzH_D2EsgVMZT_vXbRfUXG_NWdJE4Z8DaQk', '2025-05-10 08:06:38.552000', '2025-05-11 08:06:38.000000', 541150219354505, '055665893e7f4586bb55ed8d5b914a50');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (8, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDc5OCwiaWF0IjoxNzQ2ODY0Mzk4LCJqdGkiOiI1ZDhkY2IyODFkOGM0YTI2YWVjNTZjMGU4OTEwM2RhZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.6ik3eD-Vym0fflORXxcvfU1nZGnsdNQ0H1Az_yaq-qM', '2025-05-10 08:06:38.559000', '2025-05-11 08:06:38.000000', 541150219354505, '5d8dcb281d8c4a26aec56c0e89103dad');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (9, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDgxMSwiaWF0IjoxNzQ2ODY0NDExLCJqdGkiOiIzYzYyMTVjNmJjNGI0M2UwODhkMDQzYTk2ZDQwNzYzMSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.bcM4XFfUHanNowEhW-ANscHbmFOpQhsS4aWgouOXm34', '2025-05-10 08:06:51.137000', '2025-05-11 08:06:51.000000', 541150219354505, '3c6215c6bc4b43e088d043a96d407631');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (10, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDgxMSwiaWF0IjoxNzQ2ODY0NDExLCJqdGkiOiI2OTYxMWE0NzhkOTA0YTU5YjU4M2RjZGYyZDMyODNmYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.YumUylh43Mdg7bLSKFHfCn6x3L9NBmdMUUtSA-vaZO8', '2025-05-10 08:06:51.143000', '2025-05-11 08:06:51.000000', 541150219354505, '69611a478d904a59b583dcdf2d3283fa');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (11, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MTYwNCwiaWF0IjoxNzQ2ODY1MjA0LCJqdGkiOiJkMmU4Y2VhODg3MGY0MzM0YTMxZTNmYTlmZDFlNGMxYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.FwW1q9oDqupCwWao3cMYVjaDMrZTBK6TnhL9paD8g0M', '2025-05-10 08:20:04.830000', '2025-05-11 08:20:04.000000', 541150219354505, 'd2e8cea8870f4334a31e3fa9fd1e4c1c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (12, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MTYwNCwiaWF0IjoxNzQ2ODY1MjA0LCJqdGkiOiJiYTYzMDlkZGQ2Zjk0MDMyYWE1N2FjMDExOTgzNjM2MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.m-30WtlX3zuJAABoL0h42TuN6jotR1NII74vWafGtl8', '2025-05-10 08:20:04.837000', '2025-05-11 08:20:04.000000', 541150219354505, 'ba6309ddd6f94032aa57ac0119836361');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (13, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjA2MywiaWF0IjoxNzQ2ODY1NjYzLCJqdGkiOiI1YjhkMjkwNGQ2ZDM0YTBhOWE3YmQxNTE4NGY3NDA2MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ZnosxiJardo_db9zf1MPkGB9MFjONx8hYMXViS9KfEM', '2025-05-10 08:27:43.339000', '2025-05-11 08:27:43.000000', 541150219354505, '5b8d2904d6d34a0a9a7bd15184f74061');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (14, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjA2MywiaWF0IjoxNzQ2ODY1NjYzLCJqdGkiOiI0NmZkMGNjNjgyYWU0ZWQ5YWVmMWFmZDFmMzFjODlkZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.x_VoLWGDcjC6FehmPHBd6JvAJsOSPO1ghRhRsdO-O2Q', '2025-05-10 08:27:43.375000', '2025-05-11 08:27:43.000000', 541150219354505, '46fd0cc682ae4ed9aef1afd1f31c89de');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (15, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjE3OCwiaWF0IjoxNzQ2ODY1Nzc4LCJqdGkiOiIwMWYxMWZlYzBhOTA0MGUyODEwM2RkNzIzOGNlYTQ2NiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.SiwaAWiNMDmwUfr5EzApXlp0CZ9i7FIO0S-8VWafx8Y', '2025-05-10 08:29:38.919000', '2025-05-11 08:29:38.000000', 541150219354505, '01f11fec0a9040e28103dd7238cea466');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (16, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjE3OCwiaWF0IjoxNzQ2ODY1Nzc4LCJqdGkiOiI2MzEwYmVlMDIzNjY0YWFkYTNmZDlmYzUxNTRjMWIxZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.zIsZiY8nU0QMVYqyXz8fD0dkUdcf1BqVNXyFc6eQn9o', '2025-05-10 08:29:38.956000', '2025-05-11 08:29:38.000000', 541150219354505, '6310bee023664aada3fd9fc5154c1b1e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (17, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjI2OSwiaWF0IjoxNzQ2ODY1ODY5LCJqdGkiOiJjOGViNmRkZDNkZjI0OWY5YThhMmIyZTJlOGYzMWRmNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.eVLRrMVVvLwW7wIQtUKTFiAn30rytSC7o0xlDUg9WYk', '2025-05-10 08:31:09.737000', '2025-05-11 08:31:09.000000', 541150219354505, 'c8eb6ddd3df249f9a8a2b2e2e8f31df6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (18, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjI2OSwiaWF0IjoxNzQ2ODY1ODY5LCJqdGkiOiI4NmNjN2FjMmQ2Zjg0ZjhkYTUxNTRjOWFlM2ViZWMxMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.DuPBPyD3e--QminbEHxVhTLc2DkahCBBSGHMNWYyt7s', '2025-05-10 08:31:09.773000', '2025-05-11 08:31:09.000000', 541150219354505, '86cc7ac2d6f84f8da5154c9ae3ebec13');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (19, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjYzNiwiaWF0IjoxNzQ2ODY2MjM2LCJqdGkiOiJjNGRhZTg3YzFhZGM0ZTdiYThjODVkYmMyZWM0NzU1YSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.GjsOAJp1YUSsAZhX0FvUBT2dSgLkV7gBhIzDMAwpzzU', '2025-05-10 08:37:16.207000', '2025-05-11 08:37:16.000000', NULL, 'c4dae87c1adc4e7ba8c85dbc2ec4755a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (20, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjYzNiwiaWF0IjoxNzQ2ODY2MjM2LCJqdGkiOiIwZDliYmIzYWM5NTg0OThkYWUzN2Y3NGU0MGFlYTU4MyIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.1_NUMKU2AvyLpJWFOfbUG6exBM6Aa84-vDfsaZms6zo', '2025-05-10 08:37:16.241000', '2025-05-11 08:37:16.000000', NULL, '0d9bbb3ac958498dae37f74e40aea583');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (21, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjgzOSwiaWF0IjoxNzQ2ODY2NDM5LCJqdGkiOiJiNzUzN2M2YmQ2ZWY0ZmQ3OWQxMzIxZTc0NjhmZTQ1NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.WVV50GRY_0qmIojCBvYN8HGA3to8Ma1KIk4vpZw7x1w', '2025-05-10 08:40:39.003000', '2025-05-11 08:40:39.000000', 541150219354505, 'b7537c6bd6ef4fd79d1321e7468fe457');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (22, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjgzOSwiaWF0IjoxNzQ2ODY2NDM5LCJqdGkiOiI5N2Q3NDk5N2I0ZTM0ODA4ODlhMTRjNjljMTJiZTk3MiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.GcCXbKetJIEdK-IwVD0nCUp3Ad7mP1rSkvR75w97Dbw', '2025-05-10 08:40:39.039000', '2025-05-11 08:40:39.000000', 541150219354505, '97d74997b4e3480889a14c69c12be972');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (23, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MzQwMywiaWF0IjoxNzQ2ODY3MDAzLCJqdGkiOiIyYjdiZGYzOWNiN2Y0ZGU0YmI3OGM2MWE4YzFlNTk3MCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.paTBQLui6nPewcwArgc-X9rVikY1iPCDij1WZl5CaoE', '2025-05-10 08:50:03.518000', '2025-05-11 08:50:03.000000', 541150219354505, '2b7bdf39cb7f4de4bb78c61a8c1e5970');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (24, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MzQwMywiaWF0IjoxNzQ2ODY3MDAzLCJqdGkiOiI1NzlhY2ZkMTg1YjQ0YWQyYTk3MGQzOGQ0MjU5OWI5NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.4adxVIRw88-oB3diAiEfaT05sUrFV0e1oB6Pz6YM-6g', '2025-05-10 08:50:03.553000', '2025-05-11 08:50:03.000000', 541150219354505, '579acfd185b44ad2a970d38d42599b97');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (25, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NDU1NSwiaWF0IjoxNzQ2ODY4MTU1LCJqdGkiOiJiMDc2NmNjMDI0OTY0ZjUyYmU5MGEyZTFiODQ1NzFhYSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.26uKln058V1acP84MVgkmnjsh5zhz4k-sTpls6O35p0', '2025-05-10 09:09:15.756000', '2025-05-11 09:09:15.000000', NULL, 'b0766cc024964f52be90a2e1b84571aa');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (26, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NDU1NSwiaWF0IjoxNzQ2ODY4MTU1LCJqdGkiOiIxMjJmMjgyYjM3MDA0MDI3ODU1YmFiMDMwZmM4YTI4ZSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.y4GB3lJc2yKFgPmlXb7e1VEKCxnPM61dYOHNYcXMU4o', '2025-05-10 09:09:15.791000', '2025-05-11 09:09:15.000000', NULL, '122f282b37004027855bab030fc8a28e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (27, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NDkzNiwiaWF0IjoxNzQ2ODY4NTM2LCJqdGkiOiIzZDU0N2ExZDg1Mjg0Y2NhYjI3OGUyN2UxMjFiNDNiNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ETxXFj0b0o2fr7Tc1UJHZRbOVcrlXph-wb-RVrsJrRY', '2025-05-10 09:15:36.788000', '2025-05-11 09:15:36.000000', 541150219354505, '3d547a1d85284ccab278e27e121b43b7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (28, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NDkzNiwiaWF0IjoxNzQ2ODY4NTM2LCJqdGkiOiJkMWE2ZWJkOGJiOGY0ZWIxODU3ZTBmYzM2NWY5NTdjYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.3XtrNk3UxF1kjX1os5KtdN8mVEgxYtFgMx1_cd8Q1vY', '2025-05-10 09:15:36.822000', '2025-05-11 09:15:36.000000', 541150219354505, 'd1a6ebd8bb8f4eb1857e0fc365f957ca');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (29, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTI4MCwiaWF0IjoxNzQ2ODY4ODgwLCJqdGkiOiJlYmUwZTI3NzJkODA0YzA3YjMzZTE3NWNiZTI0NTM0NyIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.YKYLsFBjDuAyC1iR9hjv2BjwzS_X9u4hy7HXkHotdsU', '2025-05-10 09:21:20.314000', '2025-05-11 09:21:20.000000', NULL, 'ebe0e2772d804c07b33e175cbe245347');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (30, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTI4MCwiaWF0IjoxNzQ2ODY4ODgwLCJqdGkiOiJmZTE1MjkxY2MxYzc0ZWViOGY2MmUyNDRmMjYyMzcyZCIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.zdQgjLMBQLcWH_TPR94nDWX7JfHgS0XPKwBYxCK_vR4', '2025-05-10 09:21:20.350000', '2025-05-11 09:21:20.000000', NULL, 'fe15291cc1c74eeb8f62e244f262372d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (31, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTYyOCwiaWF0IjoxNzQ2ODY5MjI4LCJqdGkiOiIxNDhmZGI0MTZlNjk0MzI0Yjc3MjYzYWE0NDU3MzYzNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yexRh1EIvJ1AL8tpGjaPnE3UtIsUrz2aFN3UCyD2zXY', '2025-05-10 09:27:08.121000', '2025-05-11 09:27:08.000000', 541150219354505, '148fdb416e694324b77263aa44573637');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (32, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTYyOCwiaWF0IjoxNzQ2ODY5MjI4LCJqdGkiOiI4NjQzNGVhNjM1MDc0ZmExYTNjMWE4OTMzNGUyNjY5YSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.aLIRoCW7_eZuDgKko0S-KvgTxpuCZUZtPzRfM1ciqQs', '2025-05-10 09:27:08.365000', '2025-05-11 09:27:08.000000', 541150219354505, '86434ea635074fa1a3c1a89334e2669a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (33, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTgxMCwiaWF0IjoxNzQ2ODY5NDEwLCJqdGkiOiIwNGExYWQxMzExNTg0MjQ1Yjc2NDFlYjBiYzkxYjRhZSIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.MC0714HqWYKoFUefVBpZb2tbOBrztfhFPhWDPMRArlw', '2025-05-10 09:30:10.690000', '2025-05-11 09:30:10.000000', NULL, '04a1ad1311584245b7641eb0bc91b4ae');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (34, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTgxMCwiaWF0IjoxNzQ2ODY5NDEwLCJqdGkiOiIwMjNlNmVlZjZhZWI0YTUyOGJmNmNmNmNhNzMwZDY4ZSIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.3ExDJc1laLXrczqQAdCXkLshIv25dowjH36YL-OUyrg', '2025-05-10 09:30:10.726000', '2025-05-11 09:30:10.000000', NULL, '023e6eef6aeb4a528bf6cf6ca730d68e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (35, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTkwMywiaWF0IjoxNzQ2ODY5NTAzLCJqdGkiOiJlOTFkOGEyNDcwOTc0MmIwYjE4YjViNDAyMTNiYmIyMiIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.h_KH7RU_pUiW4z-gZaJhEaILGGTVzb7PfbEzhAJHTwg', '2025-05-10 09:31:43.473000', '2025-05-11 09:31:43.000000', NULL, 'e91d8a24709742b0b18b5b40213bbb22');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (36, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTkwMywiaWF0IjoxNzQ2ODY5NTAzLCJqdGkiOiI0ZmY0ZTM1NmZmZWE0ZDVmOTdkYTYzNzI5ODBhMTM2ZiIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.S-ctd193zJ6ciNyPlHkLZADZX2vCw9I-JfzRkzFuVQQ', '2025-05-10 09:31:43.487000', '2025-05-11 09:31:43.000000', NULL, '4ff4e356ffea4d5f97da6372980a136f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (37, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTkxNiwiaWF0IjoxNzQ2ODY5NTE2LCJqdGkiOiJlMGFmODkwYmNkMWQ0ODFlYWYxZWY5YzliZDc3ZThhOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.p2WXKEbE9w0Ocw3jvXAHBUauDynvdrohpCu3rQ3K1oo', '2025-05-10 09:31:56.446000', '2025-05-11 09:31:56.000000', 541150219354505, 'e0af890bcd1d481eaf1ef9c9bd77e8a8');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (38, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTkxNiwiaWF0IjoxNzQ2ODY5NTE2LCJqdGkiOiJhMmQyMDQwZmZhYTI0MjQ5YjgxMmRkZDRjZjk1ZTgwYiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.TDWvSW4CknQHsOu29dEyNq5nQxEm3qlUdU1Nz7RHe9E', '2025-05-10 09:31:56.479000', '2025-05-11 09:31:56.000000', 541150219354505, 'a2d2040ffaa24249b812ddd4cf95e80b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (39, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTk0NiwiaWF0IjoxNzQ2ODY5NTQ2LCJqdGkiOiJjOTI4MGE2ZGZlYjI0ZTQ5YThiODQzNzc2MGY2NDFmYyIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.jZ274JWGvwnJqK04-BIhakluHcdxBGAEscmtp6DPXZY', '2025-05-10 09:32:26.725000', '2025-05-11 09:32:26.000000', NULL, 'c9280a6dfeb24e49a8b8437760f641fc');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (40, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTk0NiwiaWF0IjoxNzQ2ODY5NTQ2LCJqdGkiOiI1ZDA5OTUzZjhmMDg0NGI2OTQxMDZkM2UyNDkzNGFmNyIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.zhGqE5xGg66oTCi9eIWj9v_zDG1rsnMTuouDF5dgiV0', '2025-05-10 09:32:26.761000', '2025-05-11 09:32:26.000000', NULL, '5d09953f8f0844b694106d3e24934af7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (41, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NjAxNywiaWF0IjoxNzQ2ODY5NjE3LCJqdGkiOiI1YmQwZTU2YTY2N2Q0OGQ2OGM1NWM2OTc5YTkwMWVhZCIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.ZExsNUkhTZJfbVirmnppL39DAEgj3Ur2K9t4Hd8N0MA', '2025-05-10 09:33:37.820000', '2025-05-11 09:33:37.000000', NULL, '5bd0e56a667d48d68c55c6979a901ead');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (42, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NjAxNywiaWF0IjoxNzQ2ODY5NjE3LCJqdGkiOiIxMjE2NjdjYTQ2MzQ0NGFjYTk2NTMxMjBjNTk1NTFjMiIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.E7gVwF28ng04PcRmeMMSz7op3SB6c42vX4b85fKrNe0', '2025-05-10 09:33:37.855000', '2025-05-11 09:33:37.000000', NULL, '121667ca463444aca9653120c59551c2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (43, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjI1OSwiaWF0IjoxNzQ2OTQ1ODU5LCJqdGkiOiIyYzg0MDM3ZDJjYTU0ZDJlOWVkZmVlYzI2YzdjMTQ5NCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.7U9-PR_oEDkNOfC8iKCf5VfdGX1SotPh9OpcNlQrW7o', '2025-05-11 06:44:19.189000', '2025-05-12 06:44:19.000000', 541150219354505, '2c84037d2ca54d2e9edfeec26c7c1494');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (44, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjI1OSwiaWF0IjoxNzQ2OTQ1ODU5LCJqdGkiOiIyZjhkMzQ2MTg2MmY0NjE2OTA0OGFlZGFkMjgxZDQ2NSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.KXVPY3fkYggtxSQmex4boddurs-m-iMSWTI6j5NTdic', '2025-05-11 06:44:19.199000', '2025-05-12 06:44:19.000000', 541150219354505, '2f8d3461862f46169048aedad281d465');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (45, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjg4OSwiaWF0IjoxNzQ2OTQ2NDg5LCJqdGkiOiI2ZGI0ZGRjYTNiY2Y0N2M0YmZlZGZlZTE1YTk3NzVlMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.-9ulCEk0eZV_v5RkCtnocDGQ2cfueM_JUO-2we2fY5s', '2025-05-11 06:54:49.056000', '2025-05-12 06:54:49.000000', 541150219354505, '6db4ddca3bcf47c4bfedfee15a9775e0');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (46, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjg4OSwiaWF0IjoxNzQ2OTQ2NDg5LCJqdGkiOiI1NTYxNDA2MTUxMjg0NjBiYWUwYzFjMzQ5MWJmNzY4NSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Kl1Jmm7YPBz7bqv7CzqjOQYNRsJ8sOTgLWZ0VujCgMY', '2025-05-11 06:54:49.063000', '2025-05-12 06:54:49.000000', 541150219354505, '556140615128460bae0c1c3491bf7685');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (47, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjg5NSwiaWF0IjoxNzQ2OTQ2NDk1LCJqdGkiOiJlNTQ0OTg2OGVkNTQ0OGJjYTk4YjZlNWNjOWJhNGViYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.2XIifY2paE8GNWEwBQefL7TAyoL6H4I3gkVyxGQSf9o', '2025-05-11 06:54:55.889000', '2025-05-12 06:54:55.000000', 541150219354505, 'e5449868ed5448bca98b6e5cc9ba4ebc');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (48, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjg5NSwiaWF0IjoxNzQ2OTQ2NDk1LCJqdGkiOiI5ODVhNmE4OTk3MTE0OTQ3YmFkNmI3ZjE5MzkzYzVmYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Izi_JNvIOHFR7a8xMkDtRzvCVibbB-FWuHkrLoLuH7E', '2025-05-11 06:54:55.894000', '2025-05-12 06:54:55.000000', 541150219354505, '985a6a8997114947bad6b7f19393c5fa');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (49, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjk3MSwiaWF0IjoxNzQ2OTQ2NTcxLCJqdGkiOiJhMmY4MzcyYWM4NjI0MzAxYjBkNzI4MmJlN2IyYmEwNSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.5CV87Hjk3WrWKyBJtBXXUNuNl-Q4k1csJfB_HtjRj5U', '2025-05-11 06:56:11.345000', '2025-05-12 06:56:11.000000', NULL, 'a2f8372ac8624301b0d7282be7b2ba05');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (50, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjk3MSwiaWF0IjoxNzQ2OTQ2NTcxLCJqdGkiOiIyZjAwZTk5M2FiM2I0ZjMyYmYxMzFmMDIxZDNlNDJiOCIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.teIwx87IPbGxLleZx18dk5yns12xnSA3Ym4uEAIBa1Y', '2025-05-11 06:56:11.351000', '2025-05-12 06:56:11.000000', NULL, '2f00e993ab3b4f32bf131f021d3e42b8');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (51, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzAzMCwiaWF0IjoxNzQ2OTQ2NjMwLCJqdGkiOiI3Mjc5OTg1NDM3ZDc0NTc3YTQzMTA5NTgwOWU0M2M0NCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.JPxucD2vGWLkJluUIERBC3JA4tfVZdpJMmVNF8h-4H0', '2025-05-11 06:57:10.061000', '2025-05-12 06:57:10.000000', 541150219354505, '7279985437d74577a431095809e43c44');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (52, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzAzMCwiaWF0IjoxNzQ2OTQ2NjMwLCJqdGkiOiI2ZmJjZjkwMzMxMGE0NDMxYWIwMjVjODQyZGVmNDkwOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Z5BDQ5a9qH6Vd-8TrSOp88lTOok3PqvegcPwoIXX5_g', '2025-05-11 06:57:10.068000', '2025-05-12 06:57:10.000000', 541150219354505, '6fbcf903310a4431ab025c842def4908');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (53, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA1MCwiaWF0IjoxNzQ2OTQ2NjUwLCJqdGkiOiI5NzVkNzc0YTA0ZDU0NTAzOTM3NWE4MWQyZDg5NGE2NSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.MJdaYtrMNnmmiGt5yWV-Mjz-PFaykt3ycjwDGqQdC64', '2025-05-11 06:57:30.628000', '2025-05-12 06:57:30.000000', 541150219354505, '975d774a04d545039375a81d2d894a65');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (54, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA1MCwiaWF0IjoxNzQ2OTQ2NjUwLCJqdGkiOiI3NzQxNDgwNjdiZTQ0MTFhYmJjYTBlM2RhNmI0NDgwYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Qge8E65Xvn8TPgl8fH4PqhcH5NZEP6fmLwB9UahUrFg', '2025-05-11 06:57:30.633000', '2025-05-12 06:57:30.000000', 541150219354505, '774148067be4411abbca0e3da6b4480a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (55, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA1NiwiaWF0IjoxNzQ2OTQ2NjU2LCJqdGkiOiIzZTRlNTNjZGQ2MmU0YjJmOTkwMDc5M2RjNTk0NTZjMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.1SSanOawGG_OTqEiSJkWTRRPdWyjHn9dUz2ixQBRtoc', '2025-05-11 06:57:36.200000', '2025-05-12 06:57:36.000000', 541150219354505, '3e4e53cdd62e4b2f9900793dc59456c3');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (56, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA1NiwiaWF0IjoxNzQ2OTQ2NjU2LCJqdGkiOiI1MzNjMjEwYTQwMWM0OWQwYWFhYWY5MmMyYmJhYTcyZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.hAm2zqGDzM0nDFos9_nzGC_4dPJXPVyE-h8oWPm1pFY', '2025-05-11 06:57:36.206000', '2025-05-12 06:57:36.000000', 541150219354505, '533c210a401c49d0aaaaf92c2bbaa72e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (57, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA2MywiaWF0IjoxNzQ2OTQ2NjYzLCJqdGkiOiI0NjA1ZTdlYjNlYTA0MTMzOGQxYjdmYjIwOTM0YWIyOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.YuC50LIWy7weAe4oVt024Ylh16Fmzsc0Tg9swm5296Q', '2025-05-11 06:57:43.798000', '2025-05-12 06:57:43.000000', 541150219354505, '4605e7eb3ea041338d1b7fb20934ab29');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (58, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA2MywiaWF0IjoxNzQ2OTQ2NjYzLCJqdGkiOiJlZmNlMDUwY2I2MjM0YmMzYWMyZDIyN2MyNTVhMDgzNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.TIXDg6BwhBdn2ZD5-FRmvT2RhfdHA7PnU_9ahVX1zgk', '2025-05-11 06:57:43.803000', '2025-05-12 06:57:43.000000', 541150219354505, 'efce050cb6234bc3ac2d227c255a0834');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (59, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA3NiwiaWF0IjoxNzQ2OTQ2Njc2LCJqdGkiOiIwNGEzZmEzYWQ1Mzg0MGM2ODBhMGJiOGRiYTE3NTlmMyIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.KFD4s6uPN8OwIJLCpHS7MwNHQBBc_7OqeMVd3am4LIo', '2025-05-11 06:57:56.432000', '2025-05-12 06:57:56.000000', NULL, '04a3fa3ad53840c680a0bb8dba1759f3');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (60, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA3NiwiaWF0IjoxNzQ2OTQ2Njc2LCJqdGkiOiIxOTg4ZDg3YTMzNjM0ZGQ5YjgzZjJhMWNjYjFkZWQ1NiIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0._HDOmVaBxm4hbJQUMRG1FwYJVYsbu2vuFlgpI_n7Fl0', '2025-05-11 06:57:56.438000', '2025-05-12 06:57:56.000000', NULL, '1988d87a33634dd9b83f2a1ccb1ded56');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (61, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA4MSwiaWF0IjoxNzQ2OTQ2NjgxLCJqdGkiOiJiOTI0NjhhNTQ2OWY0ZWE2OGIxNGE4MDFmODk3ZjY0ZiIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.cqo8ZKSzUEOUO2qF5gMVHy3TjRz55dz1LPC03tIBpcA', '2025-05-11 06:58:01.132000', '2025-05-12 06:58:01.000000', NULL, 'b92468a5469f4ea68b14a801f897f64f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (62, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA4MSwiaWF0IjoxNzQ2OTQ2NjgxLCJqdGkiOiJlYzk5ODZkM2QzZmM0MGEzODk5NmRkNmRjNzQzMWZhZSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.WgytmHGXLxBpBGARIJjDrYFuA3BbnVmkOaPpSBpjzOQ', '2025-05-11 06:58:01.138000', '2025-05-12 06:58:01.000000', NULL, 'ec9986d3d3fc40a38996dd6dc7431fae');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (63, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzEzMiwiaWF0IjoxNzQ2OTQ2NzMyLCJqdGkiOiIwNDNlYmFlMzU1NTg0NDM2ODliZGEyOGNlMmViMDMzOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.QOvz8VkLjExRozaodEWNvLZxCLrUj6wmxY_HM6pT5HQ', '2025-05-11 06:58:52.697000', '2025-05-12 06:58:52.000000', 541150219354505, '043ebae35558443689bda28ce2eb0338');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (64, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzEzMiwiaWF0IjoxNzQ2OTQ2NzMyLCJqdGkiOiJmZWU1ZjgwNDJlMzU0ZjM3OWMxNGZkYTM3Yjk2NDllMiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ftB4Pp7_vnCYuTzZnZHNwo3jPEw7Jk5JykQQZOuZHIM', '2025-05-11 06:58:52.702000', '2025-05-12 06:58:52.000000', 541150219354505, 'fee5f8042e354f379c14fda37b9649e2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (65, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE0OCwiaWF0IjoxNzQ2OTQ2NzQ4LCJqdGkiOiI1MjM5ZDRlNzA2YmY0MzA4OGE5MDdjNzQ4YTMyZTE4MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.j2zPFBVhc-U9pXuq98AxTNauCAsk6hAQVuQyz9luLz0', '2025-05-11 06:59:08.929000', '2025-05-12 06:59:08.000000', 541150219354505, '5239d4e706bf43088a907c748a32e181');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (66, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE0OCwiaWF0IjoxNzQ2OTQ2NzQ4LCJqdGkiOiJiM2U4OTA1OTk2OGM0NTVmOTI4NjliOWIxNGNhMmNjNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yNPukw0iv7Md6Dlc2FvpnHu8aOrOkaZoA_9NcT19hCs', '2025-05-11 06:59:08.936000', '2025-05-12 06:59:08.000000', 541150219354505, 'b3e89059968c455f92869b9b14ca2cc4');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (67, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE1NSwiaWF0IjoxNzQ2OTQ2NzU1LCJqdGkiOiI4MWM0ZTIzNmIwNmE0NWZhODRjNzk2M2NjZTBmYjhkMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.G7KjxJtDdB5itSFJzz9rqSFi-DhJfi4ZCER3kiXASys', '2025-05-11 06:59:15.292000', '2025-05-12 06:59:15.000000', 541150219354505, '81c4e236b06a45fa84c7963cce0fb8d3');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (68, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE1NSwiaWF0IjoxNzQ2OTQ2NzU1LCJqdGkiOiIyNTc4NzQxYzIxNTc0ODM2OTk5ZjJmNmM4NTk3OTU0ZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.pnRwbIdes87vZQoMemU_BBW6h7Hhk5PPuCSqcG0fu6I', '2025-05-11 06:59:15.298000', '2025-05-12 06:59:15.000000', 541150219354505, '2578741c21574836999f2f6c8597954e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (69, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE2NCwiaWF0IjoxNzQ2OTQ2NzY0LCJqdGkiOiJjMTE3MTVlOWYwMjE0MmFiOThiOGY5NzJlNjMzZWZiNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.zhkE5EesfgbtT8LmD8QUuQ7FRAWoiovKqsthSvhFoPo', '2025-05-11 06:59:24.731000', '2025-05-12 06:59:24.000000', 541150219354505, 'c11715e9f02142ab98b8f972e633efb4');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (70, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE2NCwiaWF0IjoxNzQ2OTQ2NzY0LCJqdGkiOiJmNzE4OTJjNWI2ZmE0NjU2OWI4YzY1YzA2N2EwZTg5ZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.o3nKrOObcoVp5-EZ7ETBGXsCrZtc_u8TBtfZuHGKVu8', '2025-05-11 06:59:24.738000', '2025-05-12 06:59:24.000000', 541150219354505, 'f71892c5b6fa46569b8c65c067a0e89d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (71, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE4OSwiaWF0IjoxNzQ2OTQ2Nzg5LCJqdGkiOiIzODZjNjI0ZjIxMWM0ZDUwOWIxMGQ5ZDliZTE3M2UxMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ieWjr4cVR4SGKy3Av2UHc1f0h36Qyuf5qumeH6pibxw', '2025-05-11 06:59:49.278000', '2025-05-12 06:59:49.000000', 541150219354505, '386c624f211c4d509b10d9d9be173e10');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (72, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE4OSwiaWF0IjoxNzQ2OTQ2Nzg5LCJqdGkiOiI2MDZhODA5OGU5ZmE0ODhkODQwYTliZDdlZGZjYmYxMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.nFFrEjVMYkJZyJXKkQ0I7b57OKzEoMCA_AgM2FyNqoo', '2025-05-11 06:59:49.283000', '2025-05-12 06:59:49.000000', 541150219354505, '606a8098e9fa488d840a9bd7edfcbf13');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (73, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzM0NywiaWF0IjoxNzQ2OTQ2OTQ3LCJqdGkiOiI4NzJjMWQ5MzA3NTA0YTFmYTIxNWE2NWU4MzYyNmRjMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0._e30o-l6ck3Jc_Tx1TR27JVFVtAlG_nuSgoGp9xM2aI', '2025-05-11 07:02:27.967000', '2025-05-12 07:02:27.000000', 541150219354505, '872c1d9307504a1fa215a65e83626dc3');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (74, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzM0NywiaWF0IjoxNzQ2OTQ2OTQ3LCJqdGkiOiIwZjI5NGFhZTQ4MjQ0MDM5OTA1ZTdkNTg4N2QzOGFjYiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Hl2KbJMwkoG9XRxTzuZ3x7IFsnwjBpv_WWrkYLqY_rs', '2025-05-11 07:02:27.974000', '2025-05-12 07:02:27.000000', 541150219354505, '0f294aae48244039905e7d5887d38acb');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (75, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzM1OSwiaWF0IjoxNzQ2OTQ2OTU5LCJqdGkiOiI0ZGEwZDU5ZWE5MTc0NzNmOTE3NzVkYzlmZTEyMjRiNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yS9f03Of4RMUcl1Mzhl9GDLILCeICdSf4yHLEnT7t7g', '2025-05-11 07:02:39.473000', '2025-05-12 07:02:39.000000', 541150219354505, '4da0d59ea917473f91775dc9fe1224b7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (76, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzM1OSwiaWF0IjoxNzQ2OTQ2OTU5LCJqdGkiOiIwNGUxNDMyOWYwYjY0MGM3YWJkYTc2MjAyNGZlNGI0ZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.mMKwR_0AETaM8Z8j3G11H6EoK3ukk0DAtCCzVmWhxd0', '2025-05-11 07:02:39.479000', '2025-05-12 07:02:39.000000', 541150219354505, '04e14329f0b640c7abda762024fe4b4f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (77, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQyOSwiaWF0IjoxNzQ2OTQ3MDI5LCJqdGkiOiJkNzg3YmY1NTEyZTU0ZDRkOTI2MzE3NTZkMmZjNzg1MSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.9IMYlcZKPb4QsM-Bc6UZ-v2CrDnmCDTo0_QGFCKr83o', '2025-05-11 07:03:49.661000', '2025-05-12 07:03:49.000000', NULL, 'd787bf5512e54d4d92631756d2fc7851');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (78, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQyOSwiaWF0IjoxNzQ2OTQ3MDI5LCJqdGkiOiI1YWU0YTg4ZWI0NmU0OGUyODBlOTNmN2ZlNTliNDUxZCIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.yitY9gqzoU3xG5ybQyQbYBkMrCicZZ9qaWnqcHbhels', '2025-05-11 07:03:49.691000', '2025-05-12 07:03:49.000000', NULL, '5ae4a88eb46e48e280e93f7fe59b451d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (79, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQ1MSwiaWF0IjoxNzQ2OTQ3MDUxLCJqdGkiOiJiMThlZGY2NWEzODQ0YmQ4ODUwNzBlMTFkMDk3ZWFjNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.3xbSllZyJ0HYduhyJXZWlca_Z0pAtdBwbTWKZhIeo7I', '2025-05-11 07:04:11.877000', '2025-05-12 07:04:11.000000', 541150219354505, 'b18edf65a3844bd885070e11d097eac4');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (80, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQ1MSwiaWF0IjoxNzQ2OTQ3MDUxLCJqdGkiOiJkNGI3OWQwYzM1Njk0NjU5YWM2NTBlNjZhNTNmYzE1ZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.YyCkEZDUAgv6o0RmMJ0JO11YAYzZx_euNWb3SRv_CLM', '2025-05-11 07:04:11.884000', '2025-05-12 07:04:11.000000', 541150219354505, 'd4b79d0c35694659ac650e66a53fc15e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (81, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQ3MiwiaWF0IjoxNzQ2OTQ3MDcyLCJqdGkiOiJmYjBlN2RiMDE3MWU0NjcwYjJiMGM2ZWIwOTZiZTMwMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Kwl6kjx61Db1CP_4bfNwI5EHBsPFZvhDge9fG-RXPK8', '2025-05-11 07:04:32.199000', '2025-05-12 07:04:32.000000', 541150219354505, 'fb0e7db0171e4670b2b0c6eb096be303');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (82, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQ3MiwiaWF0IjoxNzQ2OTQ3MDcyLCJqdGkiOiJmNDcyOWI2YjllYmM0MWQxODAwYjZmNGMwYThiM2RjMiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.FeozOUBZIW55bzhkv_dSWXmddafdtSMMsJFqrKI2cFY', '2025-05-11 07:04:32.203000', '2025-05-12 07:04:32.000000', 541150219354505, 'f4729b6b9ebc41d1800b6f4c0a8b3dc2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (83, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzczNywiaWF0IjoxNzQ2OTQ3MzM3LCJqdGkiOiI1OWMyMWU2M2ZlODc0MTVlYmY0ZGY3MzYyZWM4NDM3NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.uab2Xt18o5tCI5MUaDqjS_kxSjbuCkR5KRaGH7jWFdY', '2025-05-11 07:08:57.585000', '2025-05-12 07:08:57.000000', 541150219354505, '59c21e63fe87415ebf4df7362ec84377');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (84, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzczNywiaWF0IjoxNzQ2OTQ3MzM3LCJqdGkiOiI1ZmI3ZTc1MzE1N2I0NWE5ODMwZTJjM2M3YWViMjdlNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.y8qJr8AtrB9R9ncCoo6HEVgua9zALgHD6hkEko9kJRc', '2025-05-11 07:08:57.592000', '2025-05-12 07:08:57.000000', 541150219354505, '5fb7e753157b45a9830e2c3c7aeb27e6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (85, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDA3NywiaWF0IjoxNzQ2OTQ3Njc3LCJqdGkiOiIzYjU3ODMzNGI3YTA0NGRlOGZhNGUyYjdjMjM5OTFlYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.7_IfDsAQN18Met3kuBr35tv_02nYhrTnaiiRHkYR5TY', '2025-05-11 07:14:37.642000', '2025-05-12 07:14:37.000000', 541150219354505, '3b578334b7a044de8fa4e2b7c23991ec');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (86, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDA3NywiaWF0IjoxNzQ2OTQ3Njc3LCJqdGkiOiJjN2E5MGIzZmY2NzA0ZDMxYTUwZTI2NTgzMjdlNWY2YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.YNHoBmxwuz4ETk-7-EE6SdsVjg_9RY1yn0Gmk5fswHY', '2025-05-11 07:14:37.647000', '2025-05-12 07:14:37.000000', 541150219354505, 'c7a90b3ff6704d31a50e2658327e5f6c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (87, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDA4NSwiaWF0IjoxNzQ2OTQ3Njg1LCJqdGkiOiJhMTU1MzE5NjQ1MWY0MWM1YTVkZWMxMGRmOTBmNmY5YSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.e4ay3A-V_bH2rxpejtEYQk1qkIsc5YkKyhJ77wHU1f8', '2025-05-11 07:14:45.358000', '2025-05-12 07:14:45.000000', 541150219354505, 'a1553196451f41c5a5dec10df90f6f9a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (88, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDA4NSwiaWF0IjoxNzQ2OTQ3Njg1LCJqdGkiOiI3Zjg0YThhOTQ2ZGI0MjNlOTA1YTZhN2EzNTU1NzYyNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.nADk7JhBlfXqT4wOxiwLajPryj5l0U5xpowNvtL4c2E', '2025-05-11 07:14:45.364000', '2025-05-12 07:14:45.000000', 541150219354505, '7f84a8a946db423e905a6a7a35557627');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (89, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDM4NSwiaWF0IjoxNzQ2OTQ3OTg1LCJqdGkiOiI0ZDE1MTZlNzRmNDA0NjNiOTRjMzhiYmI1YjNiMGYwOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.147nvJR9ixA9zaPKZymc-Ec0en5qrDHZGwdBWSqklS8', '2025-05-11 07:19:45.288000', '2025-05-12 07:19:45.000000', 541150219354505, '4d1516e74f40463b94c38bbb5b3b0f09');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (90, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDM4NSwiaWF0IjoxNzQ2OTQ3OTg1LCJqdGkiOiI5YTE5NjhjM2FkMmM0ZDAxODNjZWEyYzE3MWExZmI4YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.o-UVktjEXLvX167RJFjcqyxqmjfkIeo6NqLxS7BJpoM', '2025-05-11 07:19:45.293000', '2025-05-12 07:19:45.000000', 541150219354505, '9a1968c3ad2c4d0183cea2c171a1fb8c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (91, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDM5MiwiaWF0IjoxNzQ2OTQ3OTkyLCJqdGkiOiJjNzFiMDhiZjUwMGE0MTQ4YTgxMTZiODE1NDI2YTk5OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.M0qhY_KwyBwhkaLtz4YD9EeVby7dHH-h1CsNNSqvymo', '2025-05-11 07:19:52.775000', '2025-05-12 07:19:52.000000', 541150219354505, 'c71b08bf500a4148a8116b815426a998');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (92, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDM5MiwiaWF0IjoxNzQ2OTQ3OTkyLCJqdGkiOiIxN2Y0Mjk2ZDlhYmU0YzQ5YmJiZDZhMjk4MzQ2MDkyMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ocYv2uADtkCXetdbjkz7oTjqqZ2wxqRqWgrzdyGmFCg', '2025-05-11 07:19:52.781000', '2025-05-12 07:19:52.000000', 541150219354505, '17f4296d9abe4c49bbbd6a2983460920');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (93, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzOTkyNSwiaWF0IjoxNzQ2OTUzNTI1LCJqdGkiOiI5YmQyZWI3Y2IwNTU0MDZlOTcwM2ZkNjgwN2ZmODliOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Cag7YylpSweXXgXrVa76b1LbOOG9WRtFxMHwqyjKAQo', '2025-05-11 08:52:05.976000', '2025-05-12 08:52:05.000000', 541150219354505, '9bd2eb7cb055406e9703fd6807ff89b9');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (94, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzOTkyNSwiaWF0IjoxNzQ2OTUzNTI1LCJqdGkiOiI4MzA0ZjkyOTJhZWE0MmUyYTJmOWEwYTllYjM3NDEzNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.UlPzU5HEqHfhcxXe6XNkxWM9P4oMFmpMjcOmrka-70Y', '2025-05-11 08:52:05.999000', '2025-05-12 08:52:05.000000', 541150219354505, '8304f9292aea42e2a2f9a0a9eb374134');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (95, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzA0MDI0NCwiaWF0IjoxNzQ2OTUzODQ0LCJqdGkiOiI2YWEzZWFmNzgzMWY0NmE3YmFmMjMwYzM4ZDk0NmFkMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.hwG2REt5fh9sVS2vT6dvEGYVlGSn7dEViNObOMQhrLE', '2025-05-11 08:57:24.487000', '2025-05-12 08:57:24.000000', 541150219354505, '6aa3eaf7831f46a7baf230c38d946ad0');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (96, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzA0MDI0NCwiaWF0IjoxNzQ2OTUzODQ0LCJqdGkiOiI4OTMxMDkwMTY4Y2Q0YTRmODdhNjViMjQxY2Y1MmM1MiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Xrvm1OHSSrXRbJl3XSNaCOCl2QAkB9nGEO_RyY6w0h8', '2025-05-11 08:57:24.494000', '2025-05-12 08:57:24.000000', 541150219354505, '8931090168cd4a4f87a65b241cf52c52');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (97, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODg5NiwiaWF0IjoxNzQ3MDIyNDk2LCJqdGkiOiI0N2FlOTFlZTQ0N2Q0ZTAyYmM0NzU3ZTI1NzFmYjAxMCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.ePPDzZTBFGfSmG22gYFlS1nzW_R-NYVxJ8jlBk6vqSs', '2025-05-12 04:01:36.923000', '2025-05-13 04:01:36.000000', NULL, '47ae91ee447d4e02bc4757e2571fb010');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (98, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODg5NiwiaWF0IjoxNzQ3MDIyNDk2LCJqdGkiOiIzZDgzM2I4YjljY2Y0MWM4ODQ2YjZiYmU5MWE0OTgyYiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.VXnM-aH3wmebPDyruCTUY3nKDFy4546sMarxdRbacr8', '2025-05-12 04:01:36.930000', '2025-05-13 04:01:36.000000', NULL, '3d833b8b9ccf41c8846b6bbe91a4982b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (99, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODkwNSwiaWF0IjoxNzQ3MDIyNTA1LCJqdGkiOiJiYTY3N2I0NjViM2Q0MDY4YmY4OGM5YWY5MGQyZmY3MCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.2kWeBJK1pQfri2lRTgbF8tnlUhdau_wKGPYpxaDUxQQ', '2025-05-12 04:01:45.006000', '2025-05-13 04:01:45.000000', 541150219354505, 'ba677b465b3d4068bf88c9af90d2ff70');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (100, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODkwNSwiaWF0IjoxNzQ3MDIyNTA1LCJqdGkiOiI4ZWQ0YzUxNWNkMGI0M2ZjOWQ5ODg5MGNlMmNmYWY1YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.PDWO1fFpiX2ho7vkAX9IPIsPq34SuoKMrA17d0IVnjg', '2025-05-12 04:01:45.012000', '2025-05-13 04:01:45.000000', 541150219354505, '8ed4c515cd0b43fc9d98890ce2cfaf5c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (101, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODk0MSwiaWF0IjoxNzQ3MDIyNTQxLCJqdGkiOiI5MWRlNWVlMmJiMWU0YjgyYjk4ZTYwMTk0YzdkMzYzNSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.Jhq_CDHsGyrjA54AWut-FccmBZ27BD4i4bDg5r-CVvM', '2025-05-12 04:02:21.967000', '2025-05-13 04:02:21.000000', NULL, '91de5ee2bb1e4b82b98e60194c7d3635');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (102, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODk0MSwiaWF0IjoxNzQ3MDIyNTQxLCJqdGkiOiIzOWMzMGE4YTFlODE0ZTNlYmQxYWQ1YmQ3YTVkMGM3ZSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.IJ8y71qlbv0_i0Rvy_9CuulljpjL01k15W9RbcgxGiA', '2025-05-12 04:02:21.972000', '2025-05-13 04:02:21.000000', NULL, '39c30a8a1e814e3ebd1ad5bd7a5d0c7e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (103, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODk4NiwiaWF0IjoxNzQ3MDIyNTg2LCJqdGkiOiI1ZmM1OWQzODI4M2I0ZDA4YjA1YjNjY2Q3YjIyOTU0ZiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.-8tejxVrE7fpwcON-KCB-1XYWI0mUsIJB6Q8xkyu4Us', '2025-05-12 04:03:06.350000', '2025-05-13 04:03:06.000000', NULL, '5fc59d38283b4d08b05b3ccd7b22954f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (104, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODk4NiwiaWF0IjoxNzQ3MDIyNTg2LCJqdGkiOiI1ZWE0NTNlOTdmYTI0MDI1YWRiNTgwMjQ0MTRlZDkyNyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.-4NGSqqEZb0PG3TOfySE2FKDUjlL8IQ_xWCGjiHrA30', '2025-05-12 04:03:06.355000', '2025-05-13 04:03:06.000000', NULL, '5ea453e97fa24025adb58024414ed927');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (105, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTAwOCwiaWF0IjoxNzQ3MDIyNjA4LCJqdGkiOiJlOTE2ZDkyMjBkNDY0MjE3OWMzMjM2YzBiZTU4Y2VjNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Ctyr2goFnCOklbrOsBtCl8xOCmr6F1SsaOAJbynLtos', '2025-05-12 04:03:28.135000', '2025-05-13 04:03:28.000000', 541150219354505, 'e916d9220d4642179c3236c0be58cec7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (106, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTAwOCwiaWF0IjoxNzQ3MDIyNjA4LCJqdGkiOiIwZjE3YzYyODVjOTM0YmVkYjJkOWU3MWZmNDlkNTUzZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.SxQS4QYCXpAObAX9H2HtxVldrQHwOvO8rdSL2bJJaxc', '2025-05-12 04:03:28.140000', '2025-05-13 04:03:28.000000', 541150219354505, '0f17c6285c934bedb2d9e71ff49d553f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (107, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTA1NiwiaWF0IjoxNzQ3MDIyNjU2LCJqdGkiOiIxZjI3ZjVlYTQ5YjM0MzllOTFjZjA4MTE1ODg0NDQ2NCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.HWgnWOYLwfYcooxkvSMTmhUa4IAPZ0TYX2-tqO4IJio', '2025-05-12 04:04:16.065000', '2025-05-13 04:04:16.000000', NULL, '1f27f5ea49b3439e91cf081158844464');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (108, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTA1NiwiaWF0IjoxNzQ3MDIyNjU2LCJqdGkiOiI2ZTJmZGIyMTEyODE0ZjYxOGNiMGFjMjEzYjQ4ZjU4NyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.gBdhotEPw4XZyAfW0gYUy1NcBZYTkWxO1knD1qBOqX0', '2025-05-12 04:04:16.072000', '2025-05-13 04:04:16.000000', NULL, '6e2fdb2112814f618cb0ac213b48f587');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (109, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTE1MywiaWF0IjoxNzQ3MDIyNzUzLCJqdGkiOiJiNzliM2M5OTQyNTg0ZjI5ODUzNGU2ZDI3OGU2MmQ2MSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.jW9xMhJOLF-x5EvXemaOH70PsVHu9qaF6aidcQYOTMw', '2025-05-12 04:05:53.354000', '2025-05-13 04:05:53.000000', NULL, 'b79b3c9942584f298534e6d278e62d61');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (110, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTE1MywiaWF0IjoxNzQ3MDIyNzUzLCJqdGkiOiJkZGJkNWRlMWNmN2U0MjUzOTcwMGFkOTI1OGM1ZjI5NCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.2S1sqPRof_q3trTPM_xuhsKEAs1aiSz0ObIv_iHoDOE', '2025-05-12 04:05:53.359000', '2025-05-13 04:05:53.000000', NULL, 'ddbd5de1cf7e42539700ad9258c5f294');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (111, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE1ODYzMCwiaWF0IjoxNzQ3MDcyMjMwLCJqdGkiOiI5Zjg0MTkyMzNjOTA0MmVmYjA1NWY5NGE5YzgwZDQ0YiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.P3mT3j2z7a4ggVnQCUXSWzFrL4KgP1MzZT5L0Kp5dtM', '2025-05-12 17:50:30.555000', '2025-05-13 17:50:30.000000', NULL, '9f8419233c9042efb055f94a9c80d44b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (112, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE1ODYzMCwiaWF0IjoxNzQ3MDcyMjMwLCJqdGkiOiI4MDNlZGYyNTZlNWM0ZTZhYmUzMmZmZWJiOWZmMzA5MyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.Gev17e5EOguGnbnPr4hiRBTYC6b9A3LLhsin4iop6e8', '2025-05-12 17:50:30.578000', '2025-05-13 17:50:30.000000', NULL, '803edf256e5c4e6abe32ffebb9ff3093');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (113, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE4MjQ3MywiaWF0IjoxNzQ3MDk2MDczLCJqdGkiOiJiYTQ3MjE4MWNkYmI0Y2Y5YWU3YmNjZDQxYTY5ZGExZCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.XV-ouImPingYw2Tg1Up9CLbfYx_nBNaH-mkqGE-VzGA', '2025-05-13 00:27:53.109000', '2025-05-14 00:27:53.000000', NULL, 'ba472181cdbb4cf9ae7bccd41a69da1d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (114, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE4MjQ3MywiaWF0IjoxNzQ3MDk2MDczLCJqdGkiOiI4NWU5MDk5M2RlMmM0ZjBkYmNkMTEwYzVlNjQwNDM1MyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.9kKdJPgtJLtBnpnUljaVy3yZTcZ1U6AU6iFHrJbe35U', '2025-05-13 00:27:53.178000', '2025-05-14 00:27:53.000000', NULL, '85e90993de2c4f0dbcd110c5e6404353');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (115, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE4NDUyMSwiaWF0IjoxNzQ3MDk4MTIxLCJqdGkiOiJhYTE5ZTM4NWVmNzg0ZDk3YjljMjAwMjFiMTExNGIwNiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.mKA087I6e9ZG3k0cemERFf9BbXN2vrZPeqXDBzXus88', '2025-05-13 01:02:01.757000', '2025-05-14 01:02:01.000000', NULL, 'aa19e385ef784d97b9c20021b1114b06');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (116, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE4NDUyMSwiaWF0IjoxNzQ3MDk4MTIxLCJqdGkiOiIyOTk3Nzk4ODllNGQ0MGUzYTBhODZmNTlmODdmNTAwZCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.n158aV0jZim9683uDkEA08fp_X1h3Y3Y_UsB5v6JgXI', '2025-05-13 01:02:01.797000', '2025-05-14 01:02:01.000000', NULL, '299779889e4d40e3a0a86f59f87f500d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (117, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI3OTczNiwiaWF0IjoxNzQ3MTkzMzM2LCJqdGkiOiIxMDdmYWE2ZjE5Nzc0NTU5YTBhOGI4N2YwMzI4YTdlYiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.sOMiCc9RUwxQfq5OqLzKGipfGpT2YwehoZqhlJHtCvs', '2025-05-14 03:28:56.270000', '2025-05-15 03:28:56.000000', NULL, '107faa6f19774559a0a8b87f0328a7eb');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (118, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI3OTczNiwiaWF0IjoxNzQ3MTkzMzM2LCJqdGkiOiIxMmZlNTMyNzljMDU0MzkyYTI4YzA1ZDBhZjEyYzRlZiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.uhyQmCuA27QTfOizDZ2E-dfgt1Hsk55ZbFWZuZVdfnA', '2025-05-14 03:28:56.316000', '2025-05-15 03:28:56.000000', NULL, '12fe53279c054392a28c05d0af12c4ef');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (119, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI4MTE3NiwiaWF0IjoxNzQ3MTk0Nzc2LCJqdGkiOiIyNTFjMjg2MDIzNjU0NDg3OTAzMzA1ZDU3ZWZmMTc0YSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.3aO48s4MahcPGTqozBKTBLwdlumg0iJVfOMU3WZgf9Q', '2025-05-14 03:52:56.824000', '2025-05-15 03:52:56.000000', NULL, '251c286023654487903305d57eff174a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (120, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI4MTE3NiwiaWF0IjoxNzQ3MTk0Nzc2LCJqdGkiOiI3NmQyNWJmOTlhYzE0MDVmODdlMjU0ODk2NTkxOGZlNyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.lPFKCC55F6Xw9J_gR1SlLQH6IiiHqlVNEg6L-l6ly-E', '2025-05-14 03:52:56.859000', '2025-05-15 03:52:56.000000', NULL, '76d25bf99ac1405f87e2548965918fe7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (121, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI4MzU2NywiaWF0IjoxNzQ3MTk3MTY3LCJqdGkiOiJiMmEwMDZhNzc0N2I0MGVkYWZiZTdkZWZlMGE2NWQ4NSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.oKD_3XB9rEWDCOkztHVpUHfXl5zied7gUktqiXhM234', '2025-05-14 04:32:47.713000', '2025-05-15 04:32:47.000000', 541150219354505, 'b2a006a7747b40edafbe7defe0a65d85');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (122, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI4MzU2NywiaWF0IjoxNzQ3MTk3MTY3LCJqdGkiOiJiOWU2YzJlMzUzM2Q0MzhiODkzOGMzZTliODBhZDRjNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.vJ3CHBe8YvH61k2E80T0WPlu4IfKMRIYSn7WeyrzTi4', '2025-05-14 04:32:47.759000', '2025-05-15 04:32:47.000000', 541150219354505, 'b9e6c2e3533d438b8938c3e9b80ad4c6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (123, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI5MDY5NCwiaWF0IjoxNzQ3MjA0Mjk0LCJqdGkiOiI2NGVmNWIxOThmZmQ0MWFlODk3YjA2NzI0OTVkZTdiNSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.5jr7oscg2XlpKWje-LqcbgW1J2oENRXdIi2eRrlzgVA', '2025-05-14 06:31:34.067000', '2025-05-15 06:31:34.000000', NULL, '64ef5b198ffd41ae897b0672495de7b5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (124, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI5MDY5NCwiaWF0IjoxNzQ3MjA0Mjk0LCJqdGkiOiJkYWY5Njk3ZDY5ZTU0OGI3YmVlZWFkYjY4ZjdiMGNkNCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.aet3AGV4ZvlywmDSRForzVOk-PhX3EdWOX8QMPOsHvM', '2025-05-14 06:31:34.072000', '2025-05-15 06:31:34.000000', NULL, 'daf9697d69e548b7beeeadb68f7b0cd4');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (125, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI5MjE0NiwiaWF0IjoxNzQ3MjA1NzQ2LCJqdGkiOiIyNGY4NzQwODZkZDk0ZmI5YTExNzYzNTlmNmY2MzA2YyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.ZqSPRJKnQfS9fXbXJFFlXCatCCpViHDp34APH7hDpvM', '2025-05-14 06:55:46.262000', '2025-05-15 06:55:46.000000', NULL, '24f874086dd94fb9a1176359f6f6306c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (126, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI5MjE0NiwiaWF0IjoxNzQ3MjA1NzQ2LCJqdGkiOiI2NTFkYjJiZmRhYjg0MGNlYjM0YThjNjA3M2MxNTJhYyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0._KU-iA66xT7yPPxNtf0SnTMskXZ0zhGXfVsrItWBUQc', '2025-05-14 06:55:46.286000', '2025-05-15 06:55:46.000000', NULL, '651db2bfdab840ceb34a8c6073c152ac');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (127, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI5MjE5MiwiaWF0IjoxNzQ3MjA1NzkyLCJqdGkiOiI3MzRlYzg4OGM4Mjg0ZWFkOWJlN2ZhOWNiZmIxOWNkZSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.6sE831J2pKLyecVnUvDbokLGvYu77mCUp8RuXtTt3VE', '2025-05-14 06:56:32.302000', '2025-05-15 06:56:32.000000', NULL, '734ec888c8284ead9be7fa9cbfb19cde');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (128, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzI5MjE5MiwiaWF0IjoxNzQ3MjA1NzkyLCJqdGkiOiJkYzQ1OTYwYTIwYTA0NTMxYmFlNDg5MWIzNmEwYTA3ZCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.7D-CxzN0w8HLYcfNvduFuMqA3eW0AYDkqc30m_US5b0', '2025-05-14 06:56:32.344000', '2025-05-15 06:56:32.000000', NULL, 'dc45960a20a04531bae4891b36a0a07d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (129, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM4NDcwMywiaWF0IjoxNzQ3Mjk4MzAzLCJqdGkiOiIzYzExODkxOTQ5ZDI0OGFjYTExYmVlZGQ2OTJkYzZkNSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.4SYTyRLd_25hzsauczvuXq4EGsuKg-8c16yFFdVaoSU', '2025-05-15 08:38:23.085000', '2025-05-16 08:38:23.000000', NULL, '3c11891949d248aca11beedd692dc6d5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (130, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM4NDcwMywiaWF0IjoxNzQ3Mjk4MzAzLCJqdGkiOiIzZTA3ZGUyNmNiMzQ0YTFmYmY4YzVlZWFlNWFhZTY0NiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.sVxIj-7zU9YWM2GaJG4h6cv8DPmJaW13r4K0wUyvEO8', '2025-05-15 08:38:23.094000', '2025-05-16 08:38:23.000000', NULL, '3e07de26cb344a1fbf8c5eeae5aae646');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (131, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM4OTA5OSwiaWF0IjoxNzQ3MzAyNjk5LCJqdGkiOiI2NzRiOGYzZjM1Y2U0YjNiYTg5NmQyZDk0ZjZlNjE2OSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.KQs1s-YMBfPWyqfzT7ipbeY1J2oh1mOtOqurwYpVt54', '2025-05-15 09:51:39.364000', '2025-05-16 09:51:39.000000', NULL, '674b8f3f35ce4b3ba896d2d94f6e6169');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (132, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM4OTA5OSwiaWF0IjoxNzQ3MzAyNjk5LCJqdGkiOiIwYzk2N2Y4YWUyZTE0M2EyODRmOTJlMGIwMjMzNjhkZSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.Z_Lny3KNIEGGzybJnU-NrHTqHy_Ah6KI_Wj2DyeLvVg', '2025-05-15 09:51:39.369000', '2025-05-16 09:51:39.000000', NULL, '0c967f8ae2e143a284f92e0b023368de');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (133, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM4OTMwOCwiaWF0IjoxNzQ3MzAyOTA4LCJqdGkiOiJjODFiY2MwZGY2Njk0MzFkOWNlYzM5NDY4YWQ0MGExOCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.qrSMmViqWE5Te2jmwZJahro8Hzc7JPBYY2YnzyN7AwM', '2025-05-15 09:55:08.611000', '2025-05-16 09:55:08.000000', NULL, 'c81bcc0df669431d9cec39468ad40a18');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (134, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM4OTMwOCwiaWF0IjoxNzQ3MzAyOTA4LCJqdGkiOiI5YjgyN2RiYjIwMGQ0MGU5OGVjZjIxNmJlZDlkZWUzZiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.UUZqb4mx9sSrbD_sLPi5hvJLwwfTpgPYdZU0pOqn7Yk', '2025-05-15 09:55:08.615000', '2025-05-16 09:55:08.000000', NULL, '9b827dbb200d40e98ecf216bed9dee3f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (135, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NTc5MywiaWF0IjoxNzQ3MzA5MzkzLCJqdGkiOiI0MzgwNTIzMTM2ODY0ZWJmYWU4MmI5YWQ2ZWIwNDI3NCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.Ju6OeZp6FHA11lZ5OeEsLEJDqw4lubtXlZuIUpCTeS8', '2025-05-15 11:43:13.865000', '2025-05-16 11:43:13.000000', NULL, '4380523136864ebfae82b9ad6eb04274');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (136, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NTc5MywiaWF0IjoxNzQ3MzA5MzkzLCJqdGkiOiJjYmVkNjExOTBmMDQ0ZmNjOTYxOWJhZDJkNDNmYzA0YyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.ZCPTqstQLvzdQk0Ie_2O8iPBUOELbrsg0Tu333G_G4I', '2025-05-15 11:43:13.871000', '2025-05-16 11:43:13.000000', NULL, 'cbed61190f044fcc9619bad2d43fc04c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (137, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NTgxOSwiaWF0IjoxNzQ3MzA5NDE5LCJqdGkiOiIyNzYxODk4NGEyYWQ0MTZkOGRmYjdiMDkwNGQ1MDkxMiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.UkXwg2pkIKzYUT8h6PgelsPxCK59ODKzqcnjSDyMY6Q', '2025-05-15 11:43:39.959000', '2025-05-16 11:43:39.000000', 541150219354505, '27618984a2ad416d8dfb7b0904d50912');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (138, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NTgxOSwiaWF0IjoxNzQ3MzA5NDE5LCJqdGkiOiI0ZDNkNWYwYTBlY2Y0NDQ1ODJkMWJkMjEzMzZjYzQxOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.4VpBqnnyh7Zxu4ja_fH06n3JWRpNc0Y-ohArh0S3OOs', '2025-05-15 11:43:39.964000', '2025-05-16 11:43:39.000000', 541150219354505, '4d3d5f0a0ecf444582d1bd21336cc418');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (139, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjAyOSwiaWF0IjoxNzQ3MzA5NjI5LCJqdGkiOiI2YjRkYjY4NTQzMzY0YjA4ODUyODNmMzliNDRlODk3YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.nQ6lyF3EXGxrxEh9M014jXt7nukVUWgfDyhsXza_b20', '2025-05-15 11:47:09.678000', '2025-05-16 11:47:09.000000', 541150219354505, '6b4db68543364b0885283f39b44e897c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (140, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjAyOSwiaWF0IjoxNzQ3MzA5NjI5LCJqdGkiOiI5NWRlNzg2MTYyY2I0N2IzOWEzY2JlZjBjOTliZDZjNSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.T2tsZCxA7-6Wq2T3xZ7GMS5Dcti9pJDRqqwzEowUqoA', '2025-05-15 11:47:09.682000', '2025-05-16 11:47:09.000000', 541150219354505, '95de786162cb47b39a3cbef0c99bd6c5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (141, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjAzMywiaWF0IjoxNzQ3MzA5NjMzLCJqdGkiOiI3MWU2YzNlMWE5NmM0Mzc1YjM1MjJjMmIzMWYwZGUxZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.1WeVoWNN-sICXRp-IOURjL6_mRZNArL3ZDcr2FAvYfM', '2025-05-15 11:47:13.528000', '2025-05-16 11:47:13.000000', 541150219354505, '71e6c3e1a96c4375b3522c2b31f0de1e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (142, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjAzMywiaWF0IjoxNzQ3MzA5NjMzLCJqdGkiOiIxOThhOTQ5ZDhhNTM0ZTE3YjFiMTkwOWI2YjllYTFjNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Z504huxxjMG25DLNmGNJULatH9oVE--SgYErXi9FO0Q', '2025-05-15 11:47:13.534000', '2025-05-16 11:47:13.000000', 541150219354505, '198a949d8a534e17b1b1909b6b9ea1c6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (143, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjE5NywiaWF0IjoxNzQ3MzA5Nzk3LCJqdGkiOiI1MzMwYjMyNzEzYzU0OWM1OTU2ODkxZTE0MTFiMWY3ZCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.nNlfCaes31o-itd3W7RU2jMvo7n8qxaEMO24IWN5S8g', '2025-05-15 11:49:57.299000', '2025-05-16 11:49:57.000000', NULL, '5330b32713c549c5956891e1411b1f7d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (144, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjE5NywiaWF0IjoxNzQ3MzA5Nzk3LCJqdGkiOiIwMGE3ZDY4MTg0MTU0MGY1YjdjODVmOTlkNTJjYzBiMSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.jgJISaFFDGQw9JYkNHZmwEHkDhCBq4tB1Vm1-p9lLDQ', '2025-05-15 11:49:57.302000', '2025-05-16 11:49:57.000000', NULL, '00a7d681841540f5b7c85f99d52cc0b1');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (145, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjIxMywiaWF0IjoxNzQ3MzA5ODEzLCJqdGkiOiJkOGYxNGEyOGVlYjM0OGQ4YTFiOTYwMmZhZmE2ZTMxOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.NLWWjD7GYWGyByUnBMEzVmvaNefcHRPhs-W6_2z755A', '2025-05-15 11:50:13.689000', '2025-05-16 11:50:13.000000', 541150219354505, 'd8f14a28eeb348d8a1b9602fafa6e319');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (146, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjIxMywiaWF0IjoxNzQ3MzA5ODEzLCJqdGkiOiJhOTMxOWNkZTZmN2U0OTIyYmJmMTNkOWI1ZmEwY2I0NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0._CEM0FKL5OG1g0ROLlusvTbKVRxBfaZgBZsmdfhGHWk', '2025-05-15 11:50:13.693000', '2025-05-16 11:50:13.000000', 541150219354505, 'a9319cde6f7e4922bbf13d9b5fa0cb47');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (147, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjM0OCwiaWF0IjoxNzQ3MzA5OTQ4LCJqdGkiOiIzMTA0MmEzNDk0MTU0ZDkzYTI5M2FlODIwMTBlMTJhYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.GD1a5aPqDN4bYVxpkJ1vIL8syxRgby0tJJwLJhsGpyw', '2025-05-15 11:52:28.865000', '2025-05-16 11:52:28.000000', 541150219354505, '31042a3494154d93a293ae82010e12aa');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (148, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjM0OCwiaWF0IjoxNzQ3MzA5OTQ4LCJqdGkiOiJmMDUzMTcxMDVlZGM0NzMyOGNmM2Q3ZWU1M2M3YmUzNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.NHi1Qo41DU4zVPoLQB4arLyAcWJbteOJIpVey1POPjg', '2025-05-15 11:52:28.870000', '2025-05-16 11:52:28.000000', 541150219354505, 'f05317105edc47328cf3d7ee53c7be36');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (149, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjU4NCwiaWF0IjoxNzQ3MzEwMTg0LCJqdGkiOiI5NzQwM2RiZGVkN2U0ZWVhYjcwYWQ2MzYxYWY5NTg1MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.OFDR7-T2p-gOLRNKLXcIxsTiTp2DYPfbB6YFwDnijdU', '2025-05-15 11:56:24.809000', '2025-05-16 11:56:24.000000', 541150219354505, '97403dbded7e4eeab70ad6361af95851');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (150, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5NjU4NCwiaWF0IjoxNzQ3MzEwMTg0LCJqdGkiOiJjYjE1YjYzOGVlMTY0OGEzODI0ZGFlYzU4MjYyYjFjNSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.eUSnrPzUrDssmzHirFsbyO_kVTXleSZfPlH7mgbH78c', '2025-05-15 11:56:24.818000', '2025-05-16 11:56:24.000000', 541150219354505, 'cb15b638ee1648a3824daec58262b1c5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (151, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5OTA4OSwiaWF0IjoxNzQ3MzEyNjg5LCJqdGkiOiJlZTJmYzExNmJlNGE0NTUzODAwZWQ4YWY4MTE3NThhYiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.wqVDQZdm-MNUTrDln8S8ph-RcAtDZ3-W4--Da41UQZg', '2025-05-15 12:38:09.333000', '2025-05-16 12:38:09.000000', NULL, 'ee2fc116be4a4553800ed8af811758ab');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (152, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5OTA4OSwiaWF0IjoxNzQ3MzEyNjg5LCJqdGkiOiI5M2U5Y2FmNjg3MGY0NmU3YTgwMzE5MTYxOGYyMjRmNiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.3mxR2DWYnyuuytaBfOEtOi9ZtwCITJLtr51oK2T9PZM', '2025-05-15 12:38:09.340000', '2025-05-16 12:38:09.000000', NULL, '93e9caf6870f46e7a803191618f224f6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (153, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5OTU5OSwiaWF0IjoxNzQ3MzEzMTk5LCJqdGkiOiI2ODYzN2Q4NTY1Zjg0NWQ2YjYxZjg5YjRkMDdiYmY3ZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.IaL6T3Dll-iV5twvg4UG85Y9KvybxJyXKa6h77Q_uKI', '2025-05-15 12:46:39.130000', '2025-05-16 12:46:39.000000', 541150219354505, '68637d8565f845d6b61f89b4d07bbf7e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (154, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzM5OTU5OSwiaWF0IjoxNzQ3MzEzMTk5LCJqdGkiOiI4NTg0MTNjODY0MDg0NTY0YTBmYzA0YTAyNjA0ZmU5OSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.vbI1rL5bBcc3bsml6lYKPob1OxsjzJRcsoVCpQdj13g', '2025-05-15 12:46:39.135000', '2025-05-16 12:46:39.000000', 541150219354505, '858413c864084564a0fc04a02604fe99');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (155, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQwMjA5MSwiaWF0IjoxNzQ3MzE1NjkxLCJqdGkiOiJlNTQ2ODRlMjRlYjc0MjJiYjc5Zjk1MGRkOGE5ZTE3NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.vGtqXcdS7O2pC0GSER-n3IQq2AirWM2taktO70JfNo4', '2025-05-15 13:28:11.964000', '2025-05-16 13:28:11.000000', 541150219354505, 'e54684e24eb7422bb79f950dd8a9e177');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (156, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQwMjA5MSwiaWF0IjoxNzQ3MzE1NjkxLCJqdGkiOiJjNDUyZmJjYWVkZGE0M2I0YWRjYjEyNWZkYzgxYzhiNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.GNklxvrAJAa03ovzv642Y34nQ0Y4GLmifpqGtjyME4s', '2025-05-15 13:28:11.969000', '2025-05-16 13:28:11.000000', 541150219354505, 'c452fbcaedda43b4adcb125fdc81c8b6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (157, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQwNTkwNiwiaWF0IjoxNzQ3MzE5NTA2LCJqdGkiOiI2ZDViODAzOGU5YjE0NjVkYjRjNWJmMmEyYzRjNDlhNyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.V2kPoL0h2obttXKxKyxO3myanciOALwatWYI8VerREI', '2025-05-15 14:31:46.969000', '2025-05-16 14:31:46.000000', NULL, '6d5b8038e9b1465db4c5bf2a2c4c49a7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (158, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQwNTkwNiwiaWF0IjoxNzQ3MzE5NTA2LCJqdGkiOiI3NmIyOGFmMGFlOWI0NzE1OTE1ZTkyOTViYjQ3MzYxYSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.6-cwXuQVuEc37WeSFRRRGLY7AXC5izAQq3VskmhV6aI', '2025-05-15 14:31:46.975000', '2025-05-16 14:31:46.000000', NULL, '76b28af0ae9b4715915e9295bb47361a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (159, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQwNTkyNywiaWF0IjoxNzQ3MzE5NTI3LCJqdGkiOiI0YTFjODBjNTY2Y2M0MmQwOWY3NDYyZWViYzQwMzgxMiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.wKFyNDA4f4_qOTuVpeiDIj_qDNE-a2bAIqyW4DEW3GU', '2025-05-15 14:32:07.898000', '2025-05-16 14:32:07.000000', 541150219354505, '4a1c80c566cc42d09f7462eebc403812');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (160, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQwNTkyNywiaWF0IjoxNzQ3MzE5NTI3LCJqdGkiOiIzNmZjYzUyNjAzYTI0OTZlYWUwN2NmZmFhYmJiOWUyMiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.-2Zk_DLTtda1fch59Ni0_-1aO7dU4zMQ2IGR-NSeNGU', '2025-05-15 14:32:07.905000', '2025-05-16 14:32:07.000000', 541150219354505, '36fcc52603a2496eae07cffaabbb9e22');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (161, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ0NTc1MywiaWF0IjoxNzQ3MzU5MzUzLCJqdGkiOiI0MTVjMjBkODIzMmY0ZmM2YjBmZjNhZGY5OTNmNDUzZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yy6upwL3czvHnSzwFRwbu1yjPnJ-raBYJ0EWgROMEvc', '2025-05-16 01:35:53.965000', '2025-05-17 01:35:53.000000', 541150219354505, '415c20d8232f4fc6b0ff3adf993f453d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (162, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ0NTc1MywiaWF0IjoxNzQ3MzU5MzUzLCJqdGkiOiIzNWI1NDUwYjNiZTc0ZGRkYTZkZmYzNTczODQ2MjJiZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.w4H7mhAKa13jBa6wHdiCL0qQG5SK_uGggdY5yG5-UDc', '2025-05-16 01:35:53.969000', '2025-05-17 01:35:53.000000', 541150219354505, '35b5450b3be74ddda6dff357384622be');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (163, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ0NjQzOSwiaWF0IjoxNzQ3MzYwMDM5LCJqdGkiOiI0MDMwZmJmMTRkMjk0MTExODA0NThkMzkyZmM3Yzk0OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yr0AsEb3-KcvCcUTCUIYhFT9tzOxrFdnFX1H2HMUGz8', '2025-05-16 01:47:19.452000', '2025-05-17 01:47:19.000000', 541150219354505, '4030fbf14d29411180458d392fc7c948');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (164, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ0NjQzOSwiaWF0IjoxNzQ3MzYwMDM5LCJqdGkiOiJiMDJkYmJlNTEwZGY0NGM5YTA5MTFhODgxNTZjNTc5ZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.VX_19QzR1GPSCK54Tl5lfFKSl-ou3RKxPxNFsabfY9U', '2025-05-16 01:47:19.458000', '2025-05-17 01:47:19.000000', 541150219354505, 'b02dbbe510df44c9a0911a88156c579f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (165, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ0NjY1MiwiaWF0IjoxNzQ3MzYwMjUyLCJqdGkiOiJmOTM3OTcyNTQ3YTg0ZGE2OTE1NjQ1MzIwOGIxMWJkMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.0e1vJn_yjHHSRB3n50eHmeG6845Y2Z0xfffjEKo6Spo', '2025-05-16 01:50:52.862000', '2025-05-17 01:50:52.000000', 541150219354505, 'f937972547a84da69156453208b11bd3');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (166, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ0NjY1MiwiaWF0IjoxNzQ3MzYwMjUyLCJqdGkiOiJjYmY3MjZiNDgyZDg0OGE5YWUyMWIxMDM4OGJjYTIzYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.-QUVjGuViLlQdyzDD-v6ZmAhSRSWh7TEfwGWbzvpSoM', '2025-05-16 01:50:52.866000', '2025-05-17 01:50:52.000000', 541150219354505, 'cbf726b482d848a9ae21b10388bca23a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (167, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4NjMwNiwiaWF0IjoxNzQ3Mzk5OTA2LCJqdGkiOiIyMzQ3MDYwNDYwMTM0NzMxYWM3YmU5N2Q5NDgwYzJlYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.RcjlvHTU6yWlitCZlGmPxJ8o2QWRf10aaqOkvrBbpkg', '2025-05-16 12:51:46.550000', '2025-05-17 12:51:46.000000', 541150219354505, '2347060460134731ac7be97d9480c2ec');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (168, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4NjMwNiwiaWF0IjoxNzQ3Mzk5OTA2LCJqdGkiOiJiZGY2MzY4OTk3NzY0NTI0OGM5NTQ1MGQxZTcyYzZjNSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.l_GsJg3ViCA2ooWuyI-RjFasQIbrSnSN9svLr1cdwfc', '2025-05-16 12:51:46.556000', '2025-05-17 12:51:46.000000', 541150219354505, 'bdf63689977645248c95450d1e72c6c5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (169, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4NjMyMywiaWF0IjoxNzQ3Mzk5OTIzLCJqdGkiOiJiN2RhZGYxMTAxZGI0ZGY1OTAzYTBkMzQ0YzVmOWYxMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.bkbjWpAfCLzKPplHN_s3qmwsy8Atoio2bSHmQ1IfqZA', '2025-05-16 12:52:03.519000', '2025-05-17 12:52:03.000000', 541150219354505, 'b7dadf1101db4df5903a0d344c5f9f13');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (170, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4NjMyMywiaWF0IjoxNzQ3Mzk5OTIzLCJqdGkiOiI5MjcwMDJkYzg0OTY0ZjFmYjg5MzUyYTdlNTA1ZmNkOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.JQwaN-UwZ_QtyCq8QWLXwJztj5oURa8HDIVMQKTozq4', '2025-05-16 12:52:03.533000', '2025-05-17 12:52:03.000000', 541150219354505, '927002dc84964f1fb89352a7e505fcd8');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (171, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4NjM5MCwiaWF0IjoxNzQ3Mzk5OTkwLCJqdGkiOiI1M2FkYWNiMTI2OWM0MmFhYjA2ZmU3YjU5MGQ5ZjdlNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.41tcmNyMP3U6dPwSAgEITBAm77uw5fmnjiXwTIMtnhg', '2025-05-16 12:53:10.627000', '2025-05-17 12:53:10.000000', 541150219354505, '53adacb1269c42aab06fe7b590d9f7e4');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (172, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4NjM5MCwiaWF0IjoxNzQ3Mzk5OTkwLCJqdGkiOiJhMzQ0ZGFjODQzMDE0N2UwOTBiNDE0YmM0YjYyOWNmNSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.gJXIpfDvP-ljqJmB-JJe71peFh-vac3Wd4VVuOWdUss', '2025-05-16 12:53:10.632000', '2025-05-17 12:53:10.000000', 541150219354505, 'a344dac8430147e090b414bc4b629cf5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (173, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4NjU4NCwiaWF0IjoxNzQ3NDAwMTg0LCJqdGkiOiI5OTAxNDU4NDA5Zjk0NGI3OTU3OTkwN2MxNWE0YmRkZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.F1-P-_LNq24Dzv4YkEw7TRnzds2wsth_zfJE6UPU3Og', '2025-05-16 12:56:24.827000', '2025-05-17 12:56:24.000000', 541150219354505, '9901458409f944b79579907c15a4bddf');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (174, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4NjU4NCwiaWF0IjoxNzQ3NDAwMTg0LCJqdGkiOiJlMTg5MjgyNmJiNWE0YWZiYTQwZGE0NjM2N2JjNWQ4MCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.50pBN2J3TY4fSfCTXIY832N3wepnu7RPvE655DADtyw', '2025-05-16 12:56:24.830000', '2025-05-17 12:56:24.000000', 541150219354505, 'e1892826bb5a4afba40da46367bc5d80');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (175, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4NjgyNywiaWF0IjoxNzQ3NDAwNDI3LCJqdGkiOiIwZGZjMDU4NzI2MzM0Y2E5YmY4NTkyMjdmOTM2Nzk0YiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.lDrzVlA8owzPLMv0N3cKNKL6Tikzvh94nWKbktvzJKA', '2025-05-16 13:00:27.019000', '2025-05-17 13:00:27.000000', 541150219354505, '0dfc058726334ca9bf859227f936794b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (176, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4NjgyNywiaWF0IjoxNzQ3NDAwNDI3LCJqdGkiOiI5ZWRiZDExYTZmNTQ0MzkyYWJiMDU5MGZjYjNmM2ZjZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Mb34pMZOXnNgJxTXFyWkEES7P3ggVaZGGOXywqyW7q0', '2025-05-16 13:00:27.023000', '2025-05-17 13:00:27.000000', 541150219354505, '9edbd11a6f544392abb0590fcb3f3fcf');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (177, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4ODAwOSwiaWF0IjoxNzQ3NDAxNjA5LCJqdGkiOiI3OTg3MzNhZDNkOWU0NjdlYmZmOTFkOGNjYWYwNGMzOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.pVlYeG6hTw2leF6ZqdbAxC4feNR9xvT7_aYq02hsXvo', '2025-05-16 13:20:09.466000', '2025-05-17 13:20:09.000000', 541150219354505, '798733ad3d9e467ebff91d8ccaf04c38');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (178, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzQ4ODAwOSwiaWF0IjoxNzQ3NDAxNjA5LCJqdGkiOiJiNDhmZjY1MWE1YWY0MzY3YTNlODUyZWVhMzdmM2U3ZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.H1lq0GVLwcusvnrs-e5YmDn5J_kPYbvuGug1ZQWD-SY', '2025-05-16 13:20:09.474000', '2025-05-17 13:20:09.000000', 541150219354505, 'b48ff651a5af4367a3e852eea37f3e7f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (179, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU3Njg4NiwiaWF0IjoxNzQ3NDkwNDg2LCJqdGkiOiIxN2MzMjRmY2JiNGU0ZWFmOTdkYjNiOGVjZmUxYTA0ZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.CwMBCC0ZSb7hJ0xV1Bf5E3ybOgsOgr5Z2jNTCh7awTE', '2025-05-17 14:01:26.442000', '2025-05-18 14:01:26.000000', 541150219354505, '17c324fcbb4e4eaf97db3b8ecfe1a04e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (180, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU3Njg4NiwiaWF0IjoxNzQ3NDkwNDg2LCJqdGkiOiJjN2ViOWRkNzUwMWM0NTJjOGI4NTU1MDZmMDhjZTdmYiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.KXT2AF_ls7I06Qi3swSK6r34HASXSeidjYjLAx_9DJs', '2025-05-17 14:01:26.507000', '2025-05-18 14:01:26.000000', 541150219354505, 'c7eb9dd7501c452c8b855506f08ce7fb');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (181, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU3Njk0OSwiaWF0IjoxNzQ3NDkwNTQ5LCJqdGkiOiI4YzNmOGU0MGViZjM0MDRlYjE4MWNmNmI3M2Q4ZWQ3OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.liHWn1XQIFwnUxXaNe4hTCMjVncExLWNdZWtHAVM2q8', '2025-05-17 14:02:29.858000', '2025-05-18 14:02:29.000000', 541150219354505, '8c3f8e40ebf3404eb181cf6b73d8ed78');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (182, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU3Njk0OSwiaWF0IjoxNzQ3NDkwNTQ5LCJqdGkiOiIwZmEzNjBlNWU4NGU0NjE1YWMzNDE5M2FkM2M4ZjgwNSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.jLPZZwQtaRZr1G0nLI5dfFwPmVmbLcPPW2j-CwiV_P8', '2025-05-17 14:02:29.896000', '2025-05-18 14:02:29.000000', 541150219354505, '0fa360e5e84e4615ac34193ad3c8f805');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (183, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU3NzAxMCwiaWF0IjoxNzQ3NDkwNjEwLCJqdGkiOiI0ZTkwYzUxZjkxYzk0Zjk4OGNkMmUzMTM4M2I5OTgzMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.k3py4MUXwx5chcio9Dp3zfW3Zrnn0D4bY0RSTT14N1A', '2025-05-17 14:03:30.972000', '2025-05-18 14:03:30.000000', 541150219354505, '4e90c51f91c94f988cd2e31383b99833');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (184, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU3NzAxMSwiaWF0IjoxNzQ3NDkwNjExLCJqdGkiOiIxMzBjNjkzNGVmZWI0ZjAxOWQ3MGNlZTFiZWZmZTAzMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.dto87yMqEZ1tyFsNsLaNtMstxixAMeqvUHUa6A_9vWE', '2025-05-17 14:03:31.006000', '2025-05-18 14:03:31.000000', 541150219354505, '130c6934efeb4f019d70cee1beffe033');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (185, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU3NzA2MSwiaWF0IjoxNzQ3NDkwNjYxLCJqdGkiOiJkZDhkYzZmN2E3M2I0MjdhYWMyOTdiOGMzM2I3Y2Y5YSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.UKH-Q3ArLz8IUYFMbhn9Nf3_wZvnE4_1KsOhWAJJTqA', '2025-05-17 14:04:21.413000', '2025-05-18 14:04:21.000000', 541150219354505, 'dd8dc6f7a73b427aac297b8c33b7cf9a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (186, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU3NzA2MSwiaWF0IjoxNzQ3NDkwNjYxLCJqdGkiOiJkMGE0YWVlNTkxMDI0OGE0YjE4ZDgwYTBlY2MzNzU3ZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.X7PBnv6CVbNeAQfnTOs6R4ERkEy05FBazTGfrbonfjg', '2025-05-17 14:04:21.449000', '2025-05-18 14:04:21.000000', 541150219354505, 'd0a4aee5910248a4b18d80a0ecc3757e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (187, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU3NzM1NCwiaWF0IjoxNzQ3NDkwOTU0LCJqdGkiOiJhYjMwMjhiNzQ0NzQ0MmU1OWIzMThmN2MzM2Y3ZjMyYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ak6orBwhHIY3VH0ZJv3J2E0AWnfLU4rNLDn8iONTMis', '2025-05-17 14:09:14.362000', '2025-05-18 14:09:14.000000', 541150219354505, 'ab3028b7447442e59b318f7c33f7f32a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (188, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU3NzM1NCwiaWF0IjoxNzQ3NDkwOTU0LCJqdGkiOiIxOGIwNjRlYTA1ZTc0YTk0YThiOTM3MTc2OTZmODU5ZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.tEt0839Bw1e83YfGUKU3HN3TOJeqez1KtY6g5iFbEoI', '2025-05-17 14:09:14.379000', '2025-05-18 14:09:14.000000', 541150219354505, '18b064ea05e74a94a8b93717696f859f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (189, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU4MDI4NSwiaWF0IjoxNzQ3NDkzODg1LCJqdGkiOiJhZjgzNzM2MWU3MDI0Y2E0YWZlZGYwMmFkY2VkYjI0YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.5aF32P1HuS7qNCKjlHwuTFDp5dBwUq9E8ZQZfiTtd64', '2025-05-17 14:58:05.906000', '2025-05-18 14:58:05.000000', 541150219354505, 'af837361e7024ca4afedf02adcedb24c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (190, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU4MDI4NSwiaWF0IjoxNzQ3NDkzODg1LCJqdGkiOiJlODhjNmEzOGFlZTY0MTEwOWM4MzkxMzU3NDFhNzEyNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.tQbmXHU7v-H_uGMHnu3-QOV3QHUwpS6xSLRmeDI0mgM', '2025-05-17 14:58:05.949000', '2025-05-18 14:58:05.000000', 541150219354505, 'e88c6a38aee641109c839135741a7127');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (191, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU4MDg1NSwiaWF0IjoxNzQ3NDk0NDU1LCJqdGkiOiIyODJkZmM5MDMzYjk0MmQ2OTI3NGIyZjYxNmUzMjRkNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.WAPLieYDGQT1F3vYiSzAalqmjglswwthqE-stl5JzgI', '2025-05-17 15:07:35.459000', '2025-05-18 15:07:35.000000', 541150219354505, '282dfc9033b942d69274b2f616e324d4');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (192, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU4MDg1NSwiaWF0IjoxNzQ3NDk0NDU1LCJqdGkiOiJjNTgyMjY4NGY4ZjI0MjQzOGQyYTQ4N2E0ZmUzYmY2NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.JTSaD5a-FudSgPlujmmhIqdwtZrI_9FuzEwf0XYrz7k', '2025-05-17 15:07:35.510000', '2025-05-18 15:07:35.000000', 541150219354505, 'c5822684f8f242438d2a487a4fe3bf67');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (193, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU4MTAyMSwiaWF0IjoxNzQ3NDk0NjIxLCJqdGkiOiI5ZTI1YmQ4YmI4ZjA0ZmE0YmVjOWRhMjU2NTVlNTliMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.3ljyZZzojOQNzTxpgcfHezcdnLa2XMy4lgvrI3eZ4-s', '2025-05-17 15:10:21.776000', '2025-05-18 15:10:21.000000', 541150219354505, '9e25bd8bb8f04fa4bec9da25655e59b3');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (194, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU4MTAyMSwiaWF0IjoxNzQ3NDk0NjIxLCJqdGkiOiIyZGE2OTBhMGE0MjQ0NDliOWYyYmE5YzI5NjI0ZWZiYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.y5I4xBJbONwzHZJv5HUcFbmzP2SPlFNJ6CuDODanytU', '2025-05-17 15:10:21.811000', '2025-05-18 15:10:21.000000', 541150219354505, '2da690a0a424449b9f2ba9c29624efba');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (195, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU4MTUxMSwiaWF0IjoxNzQ3NDk1MTExLCJqdGkiOiJjMTEzMTAzNDdhMWQ0Njk4YTZlZGY3NTJhOGYxMGZmNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0._C-EdL1yBbZQrlNOHEzi6PjV7oWmqiRD5HgqFQNGmSQ', '2025-05-17 15:18:31.325000', '2025-05-18 15:18:31.000000', 541150219354505, 'c11310347a1d4698a6edf752a8f10ff6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (196, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzU4MTUxMSwiaWF0IjoxNzQ3NDk1MTExLCJqdGkiOiI5NWExNDVjMjZhZjk0OTVlOGJjYjQzNWM2YmUxZTZjNSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.CLJDgU-_FBR32uF95PICsdC-k9DZWqwLV0WDc85ubgc', '2025-05-17 15:18:31.363000', '2025-05-18 15:18:31.000000', 541150219354505, '95a145c26af9495e8bcb435c6be1e6c5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (197, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY1MDIyMCwiaWF0IjoxNzQ3NTYzODIwLCJqdGkiOiI3OWYwZDdiZmU2MGM0ZDkzOTY2ZWQxYjY3ZmMyYTQ4MCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.RnFCw98nrBxbYEFUVqoAv59z08RS0rHEu1H1y4U6KmM', '2025-05-18 10:23:40.375000', '2025-05-19 10:23:40.000000', 541150219354505, '79f0d7bfe60c4d93966ed1b67fc2a480');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (198, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY1MDIyMCwiaWF0IjoxNzQ3NTYzODIwLCJqdGkiOiJhZjcyYTM2MDdiNDc0MmZkOWIzNzZmZjc4MWRlNThjNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.JQNaGriEmeeEFLZ2btQ_sl1ik670-vdDqXFD9izCRkA', '2025-05-18 10:23:40.379000', '2025-05-19 10:23:40.000000', 541150219354505, 'af72a3607b4742fd9b376ff781de58c7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (199, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY1MDIzOCwiaWF0IjoxNzQ3NTYzODM4LCJqdGkiOiI4NjRhYjY0OGViNDY0MTNiYTY0ZmUzYzBiMTA1MjA2ZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Dej3KmUsQbntx211blSZVyg6utuZGmLx69zmxRcFWjY', '2025-05-18 10:23:58.097000', '2025-05-19 10:23:58.000000', 541150219354505, '864ab648eb46413ba64fe3c0b105206f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (200, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY1MDIzOCwiaWF0IjoxNzQ3NTYzODM4LCJqdGkiOiI5ZDNkNGQ1NzdkNWI0NjJiOTAwOWJkYzZmNmU5MTA1NCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.xmXzNkGtT0JFNjgwh3fqG59Z71EzlzWOrEaXl2Fv7IM', '2025-05-18 10:23:58.102000', '2025-05-19 10:23:58.000000', 541150219354505, '9d3d4d577d5b462b9009bdc6f6e91054');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (201, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY1MDI2NiwiaWF0IjoxNzQ3NTYzODY2LCJqdGkiOiJiNjdlNzliNGM3MWI0MjJkYjViNGY2NzAzMTJkNmVkYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.KyTxL1Xn18i2gsEvaBZYDlO8xrqr8tSGwYQR7ZKp7sE', '2025-05-18 10:24:26.459000', '2025-05-19 10:24:26.000000', 541150219354505, 'b67e79b4c71b422db5b4f670312d6edc');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (202, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY1MDI2NiwiaWF0IjoxNzQ3NTYzODY2LCJqdGkiOiJkYjlkNWNiYjY5MjA0YTI4OGM5NmQ4YWY4ZWNkNTZmMiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.EmnGwiMIVOkLhfhArBVqMzjIv5v4BZxvFLYypw0LfFQ', '2025-05-18 10:24:26.465000', '2025-05-19 10:24:26.000000', 541150219354505, 'db9d5cbb69204a288c96d8af8ecd56f2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (203, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY1MDI4MywiaWF0IjoxNzQ3NTYzODgzLCJqdGkiOiJkMTQ0YjZmOTQ2ODY0ZTY3YTBhMGZjNGIxMjkzNmI5YSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Xd-GvOBJEB2NkkJH1T9WmEweysU8KaJIIL1Sx_3-xwg', '2025-05-18 10:24:43.735000', '2025-05-19 10:24:43.000000', 541150219354505, 'd144b6f946864e67a0a0fc4b12936b9a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (204, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY1MDI4MywiaWF0IjoxNzQ3NTYzODgzLCJqdGkiOiJhOTFhNWQxMGNmZTQ0NmU0OTZlOTExM2QyMThiN2JlNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.zU2-wx04ZvhTiqC4xV9sWWD8mFWlNXvOQGfUt8GcZtE', '2025-05-18 10:24:43.741000', '2025-05-19 10:24:43.000000', 541150219354505, 'a91a5d10cfe446e496e9113d218b7be6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (205, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY1MDMwOSwiaWF0IjoxNzQ3NTYzOTA5LCJqdGkiOiJmYjZjYTMyYzMyZjQ0YTY2OTljYzlkNjA1MGJiNWVhNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ruzYZEifaoGbb76RhIx2NIYjwuRzZ0-nd0VAfliq88U', '2025-05-18 10:25:09.620000', '2025-05-19 10:25:09.000000', 541150219354505, 'fb6ca32c32f44a6699cc9d6050bb5ea6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (206, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY1MDMwOSwiaWF0IjoxNzQ3NTYzOTA5LCJqdGkiOiJiMjgwYjUzZmZiOTY0MzQ3OGFmZDQ1Y2Y3ZmQ4MDMzNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.LB0THlTQZusiBOTRKfPZuASXBZKzadRW3PvpJhIev3M', '2025-05-18 10:25:09.625000', '2025-05-19 10:25:09.000000', 541150219354505, 'b280b53ffb9643478afd45cf7fd80334');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (207, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY2MzIyNywiaWF0IjoxNzQ3NTc2ODI3LCJqdGkiOiIzMWJlNzhhOWI0ZDk0Mjc4YWU2YTYzMjRhMjRiYzk4OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.H1VU245tIjG_Mzmb9BSM768r0an84xTI2Vgoh3WhrOM', '2025-05-18 14:00:27.916000', '2025-05-19 14:00:27.000000', 541150219354505, '31be78a9b4d94278ae6a6324a24bc988');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (208, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY2MzIyNywiaWF0IjoxNzQ3NTc2ODI3LCJqdGkiOiI3NDUzY2NkMmY5M2I0YmMxYTE5OTdhZWI2YzQ4MDhkNSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Eswl7X6wAdMP4lA7WcLiv9b67FMxAzR7jidGQmp90qU', '2025-05-18 14:00:27.950000', '2025-05-19 14:00:27.000000', 541150219354505, '7453ccd2f93b4bc1a1997aeb6c4808d5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (209, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY2MzI2NCwiaWF0IjoxNzQ3NTc2ODY0LCJqdGkiOiI4YWVkYjZlNzU0MDU0OTY3YjJkM2ViNjZjZjIwZDA0NSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.zTwHBBqj0dyneAGYTjNASJ99axWbL3CI11E6WnCS_2A', '2025-05-18 14:01:04.666000', '2025-05-19 14:01:04.000000', 541150219354505, '8aedb6e754054967b2d3eb66cf20d045');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (210, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY2MzI2NCwiaWF0IjoxNzQ3NTc2ODY0LCJqdGkiOiJjMmI5NmQxZDZiMTc0YTljOWU3NTZmYThlNDM0ZDM5NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yztWUcolmOVF5G8S0FTANHLAnn3igfPU6r3FREWhhOQ', '2025-05-18 14:01:04.724000', '2025-05-19 14:01:04.000000', 541150219354505, 'c2b96d1d6b174a9c9e756fa8e434d397');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (211, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY2MzM0MywiaWF0IjoxNzQ3NTc2OTQzLCJqdGkiOiIzYjFjY2Q5OGEwODg0NzliYWIwOWJiNWFkNGRkNGY0OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.2_SI3kWnGqdNd1pk2EzeW-7pJ0-DnOeDKPkzzzxaWNU', '2025-05-18 14:02:23.552000', '2025-05-19 14:02:23.000000', 541150219354505, '3b1ccd98a088479bab09bb5ad4dd4f48');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (212, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY2MzM0MywiaWF0IjoxNzQ3NTc2OTQzLCJqdGkiOiJlNjlmNTZmM2RmMjQ0ZTVmYWM0Yjg5MWI1NWYxYWIyZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.m7uTT40hltWi_O_yQpJyy7Wtzi3KOqok-QjSBoQpaXM', '2025-05-18 14:02:23.598000', '2025-05-19 14:02:23.000000', 541150219354505, 'e69f56f3df244e5fac4b891b55f1ab2e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (213, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY2MzQxOSwiaWF0IjoxNzQ3NTc3MDE5LCJqdGkiOiI0NzBhZTNkY2FkYTk0ZTkyYjk3ZjliYjM1ZWY0NjZjMSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.nKMKE2Dc9gE_zFDeDZIxXkRQQkIeBpY_J0INVnDEi6w', '2025-05-18 14:03:39.682000', '2025-05-19 14:03:39.000000', 541150219354505, '470ae3dcada94e92b97f9bb35ef466c1');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (214, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzY2MzQxOSwiaWF0IjoxNzQ3NTc3MDE5LCJqdGkiOiIxZmFjOTRhOTYwNmI0MzZhOGNjNTI4NGUyMTIzZGUwMSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.cmubq_XyS_hWzOCKfAM52szEGlCFqa707bvd716Cn0Q', '2025-05-18 14:03:39.719000', '2025-05-19 14:03:39.000000', 541150219354505, '1fac94a9606b436a8cc5284e2123de01');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (215, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzc5NjE5OCwiaWF0IjoxNzQ3NzA5Nzk4LCJqdGkiOiI4ZjNmNmYzMjNkNGQ0ZmJjYWQwZDM1YThjMzJmZTI4NCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.drkfUpgyr-sRZxxFZs-NHyx4p-gp7FlKvgfmhk4m5NQ', '2025-05-20 02:56:38.632978', '2025-05-21 02:56:38.000000', 541150219354505, '8f3f6f323d4d4fbcad0d35a8c32fe284');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (216, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzc5NjE5OCwiaWF0IjoxNzQ3NzA5Nzk4LCJqdGkiOiI5ODVjYzViZjI3N2U0Njg0ODA0NmQwZGFlYzlhZWJjNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.fcJy-tQCdS_MQt-KG7LU13nY9Q02_e_LZuY0R-_0I7Y', '2025-05-20 02:56:38.643470', '2025-05-21 02:56:38.000000', 541150219354505, '985cc5bf277e46848046d0daec9aebc6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (217, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzgxMzczNiwiaWF0IjoxNzQ3NzI3MzM2LCJqdGkiOiIwZmNjZDdkMDYxNTk0MDIwODQ5ZmM3YjQ3MmZiNGQyZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.0zQxIZ0mXrEcON1iQw2OViiJ38Tu6-34ha3xNdRwf4M', '2025-05-20 07:48:56.983817', '2025-05-21 07:48:56.000000', 541150219354505, '0fccd7d061594020849fc7b472fb4d2d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (218, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzgxMzczNiwiaWF0IjoxNzQ3NzI3MzM2LCJqdGkiOiIxOWEyMDI1NWViZDg0MGY3OTJiMTVjOTE3NDM5YmY5NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ppdvV5BfIGWfdfVAvHD6QFxSMSaN1oQaKZbkl82v0NQ', '2025-05-20 07:48:56.990815', '2025-05-21 07:48:56.000000', 541150219354505, '19a20255ebd840f792b15c917439bf97');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (219, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzgxNDg0NiwiaWF0IjoxNzQ3NzI4NDQ2LCJqdGkiOiJiZDBlZTZlZWYyZDM0MGZjYWE2YWE4NjdhYTY5MjIwOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.6njsC6fPZgicVCU50_fAC-4_L2gaiPWa8NBpO2YpOkE', '2025-05-20 08:07:26.628695', '2025-05-21 08:07:26.000000', 541150219354505, 'bd0ee6eef2d340fcaa6aa867aa692209');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (220, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzgxNDg0NiwiaWF0IjoxNzQ3NzI4NDQ2LCJqdGkiOiIwZjc1YzgzYmM5N2E0MmE3OTlhZmE0OGUwYmQ5MWEzMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.lbjEr2riJNhjmkcN10P8ufOkAJIgC3a46iJT8JhWCRI', '2025-05-20 08:07:26.633701', '2025-05-21 08:07:26.000000', 541150219354505, '0f75c83bc97a42a799afa48e0bd91a33');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (221, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzgxNTI4MywiaWF0IjoxNzQ3NzI4ODgzLCJqdGkiOiJmN2IyMjg0YzA0ODg0NDM1OTUzMGE3ODgzYWJlOGMwYiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.VFABOIc5qmRKn8oMhlg8haiL0RG8QS9ddL1g6thy5hc', '2025-05-20 08:14:43.179854', '2025-05-21 08:14:43.000000', 541150219354505, 'f7b2284c048844359530a7883abe8c0b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (222, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzgxNTI4MywiaWF0IjoxNzQ3NzI4ODgzLCJqdGkiOiJkZDRjZjkzZTNkMDM0NGJjOGM1NzViNTU0ZDUwZGQzNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.zGRLEID3r1RFiWFh3NhlWyzTHTVHWzSgFQCtMXYQW1M', '2025-05-20 08:14:43.185854', '2025-05-21 08:14:43.000000', 541150219354505, 'dd4cf93e3d0344bc8c575b554d50dd34');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (223, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzgyNzM2NywiaWF0IjoxNzQ3NzQwOTY3LCJqdGkiOiI5NTVlYWViMTIwYjQ0ODgyYWM1NmE5NDI0ZTczNzE1MyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yVP9ScyzTE6MW0PaowgQYY9gyeRXc7g-C82NF9u33Go', '2025-05-20 11:36:07.985405', '2025-05-21 11:36:07.000000', 541150219354505, '955eaeb120b44882ac56a9424e737153');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (224, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzgyNzM2NywiaWF0IjoxNzQ3NzQwOTY3LCJqdGkiOiJlZmRhZjQ5NjJkOWE0MTM2YjM1MDQ0NGEwZWViODk3NCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ztltpHy2dLCRNwYja_l5XhqlVQFkadjx_ZZxzHZtXdA', '2025-05-20 11:36:07.994891', '2025-05-21 11:36:07.000000', 541150219354505, 'efdaf4962d9a4136b350444a0eeb8974');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (225, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzgyNzQzNCwiaWF0IjoxNzQ3NzQxMDM0LCJqdGkiOiIwMjNlMzdiY2QyMTk0OWYxODE3NDQwNTg2M2IxYzkxYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.m37W_Pp9vL2rOkK4hWlBr5fA3Gvdev_abqnuGNkRqqw', '2025-05-20 11:37:14.396561', '2025-05-21 11:37:14.000000', 541150219354505, '023e37bcd21949f18174405863b1c91a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (226, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzgyNzQzNCwiaWF0IjoxNzQ3NzQxMDM0LCJqdGkiOiJhMzdjNGFiZmM4MWU0NDQ1OGQxYzM5NWU0MGQ1YmQ0ZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ikp88bGntPhXo6BYTFATPLdQSCPfZyYRFChVI92hJr0', '2025-05-20 11:37:14.403924', '2025-05-21 11:37:14.000000', 541150219354505, 'a37c4abfc81e44458d1c395e40d5bd4f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (227, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg0MjgzMywiaWF0IjoxNzQ3NzU2NDMzLCJqdGkiOiIzY2UzMThjZDJkZTc0ODJjODZiY2VkMDYwNGM0ZTU1YiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.UPNhHwe1nxlQ5o5ypCqml8-gbn__jUj0LeomzaxXE_I', '2025-05-20 15:53:53.864760', '2025-05-21 15:53:53.000000', 541150219354505, '3ce318cd2de7482c86bced0604c4e55b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (228, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg0MjgzMywiaWF0IjoxNzQ3NzU2NDMzLCJqdGkiOiJjNGJjOTkxZjU1ODg0M2I4OGNmOWIzZTMzMjAzMDY2NCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.GMIOZAHWXsKPJM9X4xWO_uXNtbtZkGaa91DDWQtkIGw', '2025-05-20 15:53:53.872956', '2025-05-21 15:53:53.000000', 541150219354505, 'c4bc991f558843b88cf9b3e332030664');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (229, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg0NDE2NCwiaWF0IjoxNzQ3NzU3NzY0LCJqdGkiOiJiOWU5YzRmZjBkMDM0NTY0YjA3MTU5NDY0OGY2NzM2MCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.rbJkt0oEvun295moFHkC12DqUYiJRPgUfHlqwF93cc4', '2025-05-20 16:16:04.758713', '2025-05-21 16:16:04.000000', 541150219354505, 'b9e9c4ff0d034564b071594648f67360');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (230, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg0NDE2NCwiaWF0IjoxNzQ3NzU3NzY0LCJqdGkiOiJmYWQ0OGJjNTM4YmI0NzkzODZkODI3ZjBiZWI4ZjI5MyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ZQysK9F8fHQvOOewS9ylHNp9eA9PwhD5r4GpxxrpnTw', '2025-05-20 16:16:04.797481', '2025-05-21 16:16:04.000000', 541150219354505, 'fad48bc538bb479386d827f0beb8f293');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (231, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg0NDk5MSwiaWF0IjoxNzQ3NzU4NTkxLCJqdGkiOiI5MDA5NmE0ZDViZGI0MGQ5OGRmNmQ2NTAwMjMwOTRlNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.L43-jxaPZAAv3iIwdaQKt-CZZHsQN4hnAr3E-t-uN4M', '2025-05-20 16:29:51.084048', '2025-05-21 16:29:51.000000', 541150219354505, '90096a4d5bdb40d98df6d650023094e4');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (232, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg0NDk5MSwiaWF0IjoxNzQ3NzU4NTkxLCJqdGkiOiJiNzBiNTY5ODNiY2E0ZjY3YTVmMzAzZDhlZGM0MTNjZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.qNqW5MkN-31aqpSbkkfqLpqQN7tV8kZDHbUDhzvsQ5Y', '2025-05-20 16:29:51.120961', '2025-05-21 16:29:51.000000', 541150219354505, 'b70b56983bca4f67a5f303d8edc413ce');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (233, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MDMxMCwiaWF0IjoxNzQ3NzkzOTEwLCJqdGkiOiJiNjJiNDU4OTRkN2Q0OTE2YmFhNTZiZWMzOTgyZDc0NSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.nHpMkJIJV5kCE_sj46ff7yltiBhuj9XfpxnC4JHEW-A', '2025-05-21 02:18:30.229799', '2025-05-22 02:18:30.000000', ************, 'b62b45894d7d4916baa56bec3982d745');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (234, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MDMxMCwiaWF0IjoxNzQ3NzkzOTEwLCJqdGkiOiI3MTAzMWU2YTU5NGQ0MjUxOWJkNzgxNmZmMjlmYzhmOCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.v7soXUA1gtMOzoZkzFKOvkOf6Rp7cVQnEuO_6VffOzA', '2025-05-21 02:18:30.239147', '2025-05-22 02:18:30.000000', ************, '71031e6a594d42519bd7816ff29fc8f8');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (235, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MDU0MywiaWF0IjoxNzQ3Nzk0MTQzLCJqdGkiOiIzZjEyM2UxZTYwODk0M2JjYjA0N2QwYTQ4ZTBiNTkxMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.J9q7dUyeVp2GOKJv9jAxGJlYCqkw-GTHN5MCGCbN1Bg', '2025-05-21 02:22:23.189400', '2025-05-22 02:22:23.000000', 541150219354505, '3f123e1e608943bcb047d0a48e0b5910');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (236, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MDU0MywiaWF0IjoxNzQ3Nzk0MTQzLCJqdGkiOiI5NWNmZjgxMDcxOWI0MWU0OGNjMTBiMGE3ZDEyNjNjZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.tZwbbFy6nL8PeRa2E_2Upz_KShhFZHsgaG3EIM3obtE', '2025-05-21 02:22:23.196420', '2025-05-22 02:22:23.000000', 541150219354505, '95cff810719b41e48cc10b0a7d1263cf');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (237, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MDcyNSwiaWF0IjoxNzQ3Nzk0MzI1LCJqdGkiOiIwOWJkOWUzYWU1MjA0OWZkOTUxZGZmZWNiYjM3MDVhZiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.p9D1Nz05kXj7b036EYrJW1TLJry2puiV89dIODzjqMQ', '2025-05-21 02:25:25.550966', '2025-05-22 02:25:25.000000', ************, '09bd9e3ae52049fd951dffecbb3705af');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (238, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MDcyNSwiaWF0IjoxNzQ3Nzk0MzI1LCJqdGkiOiIzNjBhMjg4NmZjMWI0MWZlYmZkZDJmY2I2Y2Y4MDg5MyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.RNuP5gJV_CCOoYnSm0ENFmrq0xNIu1mbacuS9ILSrvk', '2025-05-21 02:25:25.558887', '2025-05-22 02:25:25.000000', ************, '360a2886fc1b41febfdd2fcb6cf80893');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (239, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MTI0NSwiaWF0IjoxNzQ3Nzk0ODQ1LCJqdGkiOiI0OTU1ZGQwNjFhZWU0OWFiYjI1YjQxODNiZTNhOGQ2YiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.2oREUIusLNjGGbp3gb6yfgjJwGxYf03LjYyQV8WqVeE', '2025-05-21 02:34:05.256369', '2025-05-22 02:34:05.000000', ************, '4955dd061aee49abb25b4183be3a8d6b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (240, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MTI0NSwiaWF0IjoxNzQ3Nzk0ODQ1LCJqdGkiOiJhN2M2YTk5NTNmZDk0YzhiOTUyNDdkZmVkNDIzNGJjOCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.DQPfAkPDu_WVxTh0dmY90VQPjbJbwCWxpsm9R57kNI0', '2025-05-21 02:34:05.264366', '2025-05-22 02:34:05.000000', ************, 'a7c6a9953fd94c8b95247dfed4234bc8');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (241, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MTM3MywiaWF0IjoxNzQ3Nzk0OTczLCJqdGkiOiIzZmU1YTk4NWMwMGM0NDcyODMxYzAxZjY3MTFkMGQ0MiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.op17BFPrlFjA-tAvBqiy_r339bV99cUOJM_NyQ89evE', '2025-05-21 02:36:13.645985', '2025-05-22 02:36:13.000000', ************, '3fe5a985c00c4472831c01f6711d0d42');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (242, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MTM3MywiaWF0IjoxNzQ3Nzk0OTczLCJqdGkiOiJkNWZiOTg0Nzk0Yjc0MjVkYjI4NDYyMzkxMTk0MjgzMiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.p3sSccwKZxp5UN-a62qczg9VlVocxOAPRDbEHaSVU6E', '2025-05-21 02:36:13.651843', '2025-05-22 02:36:13.000000', ************, 'd5fb984794b7425db284623911942832');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (243, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MTU2OSwiaWF0IjoxNzQ3Nzk1MTY5LCJqdGkiOiJkZTdkYzY0MjI5MDk0YWE2OGM5NzZjMTQzZDA1ZTc4NyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.lz5s32HAoh4yN50H-XEYOV7CAFqpmOvqTjvoNMJKkXE', '2025-05-21 02:39:29.299438', '2025-05-22 02:39:29.000000', ************, 'de7dc64229094aa68c976c143d05e787');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (244, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MTU2OSwiaWF0IjoxNzQ3Nzk1MTY5LCJqdGkiOiI0ZWU5NzQ4ZWEyNjM0MzkyYTA5NjM5YWRkMmZiMmY4NSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.qH4A_IFvcgQuESHJ1kYRvkKLIkaFNqs6gWoc-A-RAGI', '2025-05-21 02:39:29.309437', '2025-05-22 02:39:29.000000', ************, '4ee9748ea2634392a09639add2fb2f85');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (245, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MTgzNSwiaWF0IjoxNzQ3Nzk1NDM1LCJqdGkiOiI1ODUzOWUxN2YyMTE0NTA0OTk3YmI2MDExODAyYTg3YyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.eGiaJxod6iNUl4NAQGAfglaqfa6SadPGJv8pwkF4w64', '2025-05-21 02:43:55.618260', '2025-05-22 02:43:55.000000', ************, '58539e17f2114504997bb6011802a87c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (246, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MTgzNSwiaWF0IjoxNzQ3Nzk1NDM1LCJqdGkiOiJjMTNmZWJhMjU2ZmI0Y2M5ODY2NmU3YmU5YmUzY2U4NiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.xkXVfme62yz6Ei_jZLUyHez6R5ANUbmklmV8Je65uEY', '2025-05-21 02:43:55.625260', '2025-05-22 02:43:55.000000', ************, 'c13feba256fb4cc98666e7be9be3ce86');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (247, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MzkwMywiaWF0IjoxNzQ3Nzk3NTAzLCJqdGkiOiJkMDgxYzc1OTdlYjI0MWMxODdhNzc0ZGM5ZTQwNWQxMSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.Mc-HyhCR3D389buYSkGwSp43UWv9m6Tha8LiAZXzYpw', '2025-05-21 03:18:23.077484', '2025-05-22 03:18:23.000000', ************, 'd081c7597eb241c187a774dc9e405d11');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (248, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4MzkwMywiaWF0IjoxNzQ3Nzk3NTAzLCJqdGkiOiI3MTAzZDBmM2ZhYWU0MzM0YmZjNTIzMjdhNDMxYTc2NiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.XZ762DKJAYqCnxFqv2MQhEiOrXbzRhQitB6IkDNRBFM', '2025-05-21 03:18:23.084484', '2025-05-22 03:18:23.000000', ************, '7103d0f3faae4334bfc52327a431a766');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (249, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4NTExOCwiaWF0IjoxNzQ3Nzk4NzE4LCJqdGkiOiIwNjcxOTUwOTgxNmY0Mjg3YmE3YWEyYmVhODdkNGJhNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.2wVxThFct8hfYc27zCc72boiNZ-sec2xiDN19kGj4Zc', '2025-05-21 03:38:38.464968', '2025-05-22 03:38:38.000000', 541150219354505, '06719509816f4287ba7aa2bea87d4ba4');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (250, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg4NTExOCwiaWF0IjoxNzQ3Nzk4NzE4LCJqdGkiOiJjYjliNjM2YzUwZDQ0MmVkOWU2ZmFkOWU3OWE4YWUxNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.vBfCS7K2-crIz5jUBw2YwRN21kDmPU0uBVTCN5XaNwY', '2025-05-21 03:38:38.472733', '2025-05-22 03:38:38.000000', 541150219354505, 'cb9b636c50d442ed9e6fad9e79a8ae14');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (251, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg5NDkyOCwiaWF0IjoxNzQ3ODA4NTI4LCJqdGkiOiI0ZjM3ZjhmMWJmYTI0NjZmYjA1YmRmZTNiYzQ1OWEzMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.QQazHrgI1O4Mfrz_-mFrkfM_VTjbh3opsrfgisQ57rw', '2025-05-21 06:22:08.087895', '2025-05-22 06:22:08.000000', 541150219354505, '4f37f8f1bfa2466fb05bdfe3bc459a33');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (252, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzg5NDkyOCwiaWF0IjoxNzQ3ODA4NTI4LCJqdGkiOiJiZjNiMTEzM2NiOTQ0MDI4YWM2YWZkYTAwODVmNGNlYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.mWbx4nC_zs1CaIBzpwswfW7HBcwSKLYjfvSEpChPSNM', '2025-05-21 06:22:08.091899', '2025-05-22 06:22:08.000000', 541150219354505, 'bf3b1133cb944028ac6afda0085f4cec');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (253, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzkwMDQ1MiwiaWF0IjoxNzQ3ODE0MDUyLCJqdGkiOiIyNzkyZjBlYzFhNjc0MDcwYmJlZTkzNWQxOWZjMzk0OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.6RPmbuE_AwJdmcK2M_o1lIdKB7mW7ZEmlAJHeBc-AOU', '2025-05-21 07:54:12.164878', '2025-05-22 07:54:12.000000', 541150219354505, '2792f0ec1a674070bbee935d19fc3948');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (254, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzkwMDQ1MiwiaWF0IjoxNzQ3ODE0MDUyLCJqdGkiOiI0ODMzYmQwOGI5ZjE0MjljODEwZWRkNjU3NzQzZjJlZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.XWyHGDMHqbaS2Wk8HsUsm_OOBdLvIaq9hiOb_dZyHA4', '2025-05-21 07:54:12.171886', '2025-05-22 07:54:12.000000', 541150219354505, '4833bd08b9f1429c810edd657743f2ed');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (255, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzkwMDQ5MSwiaWF0IjoxNzQ3ODE0MDkxLCJqdGkiOiIxY2Q0YWI2MzQwOGI0OGNmODc4MjAwZmQ3OGMxMjZjOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.VmI4dpPwH0mXTH9qTUcrMpFKDHwdX9GAwxlE9WrSFLg', '2025-05-21 07:54:51.428309', '2025-05-22 07:54:51.000000', 541150219354505, '1cd4ab63408b48cf878200fd78c126c8');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (256, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzkwMDQ5MSwiaWF0IjoxNzQ3ODE0MDkxLCJqdGkiOiIyMGQzNTA5YTNiYTM0ZGE4ODk4YjdlMzM1NzQwZmQ2MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.xy7Nfuyj00_Gte0cIG259vSDrNyRWGS8hrgWQDNrt8I', '2025-05-21 07:54:51.437311', '2025-05-22 07:54:51.000000', 541150219354505, '20d3509a3ba34da8898b7e335740fd61');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (257, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzkwMDUyNCwiaWF0IjoxNzQ3ODE0MTI0LCJqdGkiOiJhOWVhYTMyZDVlOTY0YTRhYTk3OTA4YTM0MzJkZjdjOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.JigmHrRnplya7XhaQB1DS2a9cYNXWNgw1Z9aeJADo6k', '2025-05-21 07:55:24.332076', '2025-05-22 07:55:24.000000', 541150219354505, 'a9eaa32d5e964a4aa97908a3432df7c8');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (258, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzkwMDUyNCwiaWF0IjoxNzQ3ODE0MTI0LCJqdGkiOiJkZDZhZGZjMWQ2NjU0OGRiOWUwZDJjNjljZTYxOWU4YiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.NG04yXjLFlhQjfxNE79ij__XNKedNws3iGlq7yiXdnA', '2025-05-21 07:55:24.339409', '2025-05-22 07:55:24.000000', 541150219354505, 'dd6adfc1d66548db9e0d2c69ce619e8b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (259, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzkwMDY3NiwiaWF0IjoxNzQ3ODE0Mjc2LCJqdGkiOiI1OTM5ZmM0OGJhM2Y0YTM5OWY5YTAyMzkzZjY5ZThkYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.o4IU2PZp2Ee5VjPSCdGGnZo6ogrEUocedG-KHBV8cAg', '2025-05-21 07:57:56.556944', '2025-05-22 07:57:56.000000', 541150219354505, '5939fc48ba3f4a399f9a02393f69e8dc');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (260, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzkwMDY3NiwiaWF0IjoxNzQ3ODE0Mjc2LCJqdGkiOiIwYzZhMjRhNjc4OTM0YWFlYjRhZjBiMDAxN2QyZGJlMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.94ulCwEwf5Qv9OVIBJcSjcLcrD_s0BiA_rpzrGnyfCk', '2025-05-21 07:57:56.565600', '2025-05-22 07:57:56.000000', 541150219354505, '0c6a24a678934aaeb4af0b0017d2dbe0');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (261, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzkwMDcwMCwiaWF0IjoxNzQ3ODE0MzAwLCJqdGkiOiI3NTFkYjkxMGUzNDc0NTUwODE5ZThhNGMzYmIyZjMyZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Tblt6ab_NlnHFR0kZU-pcyzFYQILmg-0uvEKgQ6JpU8', '2025-05-21 07:58:20.234856', '2025-05-22 07:58:20.000000', 541150219354505, '751db910e3474550819e8a4c3bb2f32e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (262, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzkwMDcwMCwiaWF0IjoxNzQ3ODE0MzAwLCJqdGkiOiI1NGE0MDUyMmFjMTY0OGI4OGEyYzRmNjdmYjA1MzJkMSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.qEBCJ5GqVrvid9PuyEPk_0KCgwXC98uf1bDdTpkAgzI', '2025-05-21 07:58:20.242883', '2025-05-22 07:58:20.000000', 541150219354505, '54a40522ac1648b88a2c4f67fb0532d1');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (263, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk3Mjg3MCwiaWF0IjoxNzQ3ODg2NDcwLCJqdGkiOiJkNmNkOTZiZjVkZmU0ZGJmYTE0ZjM5MzMxNWVlN2QyMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Nv7wP06rNoFzE5_zzmwlR2AVj09s_0JvNbGB-Nm-nX4', '2025-05-22 04:01:10.080021', '2025-05-23 04:01:10.000000', 541150219354505, 'd6cd96bf5dfe4dbfa14f393315ee7d23');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (264, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk3Mjg3MCwiaWF0IjoxNzQ3ODg2NDcwLCJqdGkiOiJkOWQyM2UzYjIzZDI0NjY0OTBjYTQyYmQ1NWE5NmViYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.sKy-0rcKm_-NPjpbx6VLmosbYJFUKzqd6OMMFk837gs', '2025-05-22 04:01:10.086020', '2025-05-23 04:01:10.000000', 541150219354505, 'd9d23e3b23d2466490ca42bd55a96ebc');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (265, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4MTQ2NywiaWF0IjoxNzQ3ODk1MDY3LCJqdGkiOiI5MzcwNDhhNDE2ZWU0MmMxOTU3YTA1MDE4OTYxMTMzYiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.44e221by2mlyltAXrs6o5Cv-8ULh0AOfzK6k_ukNZyc', '2025-05-22 06:24:27.298933', '2025-05-23 06:24:27.000000', 541150219354505, '937048a416ee42c1957a05018961133b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (266, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4MTQ2NywiaWF0IjoxNzQ3ODk1MDY3LCJqdGkiOiI5MzdmODRlNGNiODM0Mjg3YTQ5YzMxYzBiNDQ5N2RhZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.TjEdWafcNh9F2mPjhcTougOLAuQG1CvQ-3G3FTDADds', '2025-05-22 06:24:27.304061', '2025-05-23 06:24:27.000000', 541150219354505, '937f84e4cb834287a49c31c0b4497dae');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (267, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4MTU3MSwiaWF0IjoxNzQ3ODk1MTcxLCJqdGkiOiI1NmJhMGFkOWUyMjI0NmIwYmNlNjBmM2FiMjdjMGQ1NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Tm-8SXf7jxzqxJX03SkK0e-z9GdxXpgaOYIh1VOmX10', '2025-05-22 06:26:11.539386', '2025-05-23 06:26:11.000000', 541150219354505, '56ba0ad9e22246b0bce60f3ab27c0d57');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (268, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4MTU3MSwiaWF0IjoxNzQ3ODk1MTcxLCJqdGkiOiJhNWVhMDkxN2I1NDE0ZmUzOTMxNmI3MTBhNGI0ZTY4YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.T4lTJ7WAP1p9VXASIfwo6c0cwkeDi5xf8Iqbobld_I4', '2025-05-22 06:26:11.544384', '2025-05-23 06:26:11.000000', 541150219354505, 'a5ea0917b5414fe39316b710a4b4e68c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (269, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4MzExMCwiaWF0IjoxNzQ3ODk2NzEwLCJqdGkiOiJlMDA3ODk0MDQ0OGU0YTIwYTkzNjc1NjRjMDBjZWRhYSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.fda-WGtSpLZ7zb2CxwV6AMmQNoh4wKUHDpg6mT7hYWQ', '2025-05-22 06:51:50.702446', '2025-05-23 06:51:50.000000', NULL, 'e0078940448e4a20a9367564c00cedaa');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (270, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4MzExMCwiaWF0IjoxNzQ3ODk2NzEwLCJqdGkiOiI1NmY0NzZmYjZjZDQ0MDI5OWRiNWZhM2E1MjA3ZWI4ZiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.dbdFOy4J_5GXBp0qxBcuCcza8fKyNk9WG7HDoj64tH0', '2025-05-22 06:51:50.710446', '2025-05-23 06:51:50.000000', NULL, '56f476fb6cd440299db5fa3a5207eb8f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (271, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4MzMyMywiaWF0IjoxNzQ3ODk2OTIzLCJqdGkiOiI0YTZlYWY0MzJkOTk0MWQ0OTE4NmZiZDVjMjFmZGRjYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.1kxxItU8l6TXUkxEeshMJUBznfIjxT7Q5uLD828zWbc', '2025-05-22 06:55:23.132299', '2025-05-23 06:55:23.000000', 541150219354505, '4a6eaf432d9941d49186fbd5c21fddcc');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (272, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4MzMyMywiaWF0IjoxNzQ3ODk2OTIzLCJqdGkiOiIyNWE1ZjVmYWVlMDA0NWQyYWI2N2U1NzkxNTBkNTc1YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.dPMCymQ34UmZyWsYHaZWUASaJeyt8Ad_jObS7LSTUyY', '2025-05-22 06:55:23.140375', '2025-05-23 06:55:23.000000', 541150219354505, '25a5f5faee0045d2ab67e579150d575c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (273, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4MzM2OSwiaWF0IjoxNzQ3ODk2OTY5LCJqdGkiOiJjNWVlZjI0OWFhOTA0ZGRkYTA4NjVlZGVlOGE3MjM4YiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.Ibg08zMX-ely3A7-3HvRVvV0XN4LReTJVOekZZakBXw', '2025-05-22 06:56:09.102769', '2025-05-23 06:56:09.000000', NULL, 'c5eef249aa904ddda0865edee8a7238b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (274, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4MzM2OSwiaWF0IjoxNzQ3ODk2OTY5LCJqdGkiOiJmMTgzNzc2NmJmMjQ0MTAyYTI1NTExNzkxOWU1NzFjYyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0._kfQHbzT8Cfznn9CdNuqA7MDcQpNljJf5unG6l--YPU', '2025-05-22 06:56:09.110770', '2025-05-23 06:56:09.000000', NULL, 'f1837766bf244102a255117919e571cc');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (275, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDI1MCwiaWF0IjoxNzQ3ODk3ODUwLCJqdGkiOiJjMGE5ZmY0YWFmMzM0MTZjODRlMTljZjY1NDJkZTVkNSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.4mFRLHwi1142FM4bJrHpsrXC2_lErO3q3wOvCHuBXdk', '2025-05-22 07:10:50.647821', '2025-05-23 07:10:50.000000', 541150219354505, 'c0a9ff4aaf33416c84e19cf6542de5d5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (276, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDI1MCwiaWF0IjoxNzQ3ODk3ODUwLCJqdGkiOiI1MjM4ODZiNDBkMGE0NDNjYWE3ZTY1ZTUwMDMwNjAyYiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.04jTeaaiYX7ty6E3ag3Jh2jf7439qpEJhiv9oAk_8ns', '2025-05-22 07:10:50.656569', '2025-05-23 07:10:50.000000', 541150219354505, '523886b40d0a443caa7e65e50030602b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (277, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDM1NywiaWF0IjoxNzQ3ODk3OTU3LCJqdGkiOiJkZWE1OTE0M2U3MDg0ZDVkYTMzZWZiOGFjNDAxZTRjMSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.BAv0-Gr-_qLcm8sZW9teCI4CX1VCYZByTr6RKfPFw6w', '2025-05-22 07:12:37.849420', '2025-05-23 07:12:37.000000', 541150219354505, 'dea59143e7084d5da33efb8ac401e4c1');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (278, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDM1NywiaWF0IjoxNzQ3ODk3OTU3LCJqdGkiOiI3MzEzYzIwZDg5MDI0MDJiYTI2YzUyOTdiNmRiMGI2NSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Mza5mT9_ssytkp36VkcOlwCavwp-c3Hs7GFiO3vh3w0', '2025-05-22 07:12:37.854596', '2025-05-23 07:12:37.000000', 541150219354505, '7313c20d8902402ba26c5297b6db0b65');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (279, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDc1MywiaWF0IjoxNzQ3ODk4MzUzLCJqdGkiOiI5OWRkMGJkODEzYTU0MjY1YmE2MTMwZjc2YzE3ODRiMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.WfAXXFPhnABknPJ3NhqmhGP0JUBAibODFPdwbPDy72Q', '2025-05-22 07:19:13.511774', '2025-05-23 07:19:13.000000', 541150219354505, '99dd0bd813a54265ba6130f76c1784b0');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (280, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDc1MywiaWF0IjoxNzQ3ODk4MzUzLCJqdGkiOiIwZTk5YTA3ZWMyOWE0ZjQ1YWEwMTdlN2RkOGI2YmM4MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.DMrsAVxLBpxP1PRLbvydXJYOeO-Qw5lPkCpsfk38z-M', '2025-05-22 07:19:13.518942', '2025-05-23 07:19:13.000000', 541150219354505, '0e99a07ec29a4f45aa017e7dd8b6bc81');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (281, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDgzNSwiaWF0IjoxNzQ3ODk4NDM1LCJqdGkiOiJjMWMzNDlmOTYzNTU0ZWU2OTg3YmI0MzZkYjViZThiOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.A4JYL7CXHpxK9GvPxE5p48r2AJ9s8lKwFU4RbmMZcm4', '2025-05-22 07:20:35.900999', '2025-05-23 07:20:35.000000', 541150219354505, 'c1c349f963554ee6987bb436db5be8b9');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (282, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDgzNSwiaWF0IjoxNzQ3ODk4NDM1LCJqdGkiOiJhZmE4ODUwZGNkNDY0NzBjYWRlNWRjNjNjMjI2YWI0OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ukGkXHTIJoSvNnaUrcOcRomM_KBjX2KHqSI8OChdE_A', '2025-05-22 07:20:35.907974', '2025-05-23 07:20:35.000000', 541150219354505, 'afa8850dcd46470cade5dc63c226ab48');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (283, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDk4NywiaWF0IjoxNzQ3ODk4NTg3LCJqdGkiOiJlNDRkNmNjZGE2MTY0NDU1YWI0ODkwNzczNTZmODcyMSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.sDjI84oO9h9wDia809Vk8I-IL8wnJVPsRJSCRB-TIt0', '2025-05-22 07:23:07.489669', '2025-05-23 07:23:07.000000', NULL, 'e44d6ccda6164455ab489077356f8721');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (284, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDk4NywiaWF0IjoxNzQ3ODk4NTg3LCJqdGkiOiJiZGE1NWU3ZWNlZTY0ZTk4ODE4NjJkM2UyMDdjZmU4NiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.ihug9fq35Sp2V8euhEcCritZbnqhpKrzffoMbETqDi8', '2025-05-22 07:23:07.496685', '2025-05-23 07:23:07.000000', NULL, 'bda55e7ecee64e9881862d3e207cfe86');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (285, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NjM2MCwiaWF0IjoxNzQ3ODk5OTYwLCJqdGkiOiI4YzNlMDU3MzM5ZWY0MGU5ODZiMjEzNDhiNTEwMTM2OSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.iFaqyUlMwIr-CRvxfOvDPJE3vpXNJU2DqsAsuAPHvEo', '2025-05-22 07:46:00.381780', '2025-05-23 07:46:00.000000', 541150219354505, '8c3e057339ef40e986b21348b5101369');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (286, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NjM2MCwiaWF0IjoxNzQ3ODk5OTYwLCJqdGkiOiIzZjY2ZmVlMmJlYjE0YWRhODE2NjE0MmFlMDVhNGM5ZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.IO9d8JSEFmT1dE47Yue-eKdFma2PWUyE6TNarZNkc9Y', '2025-05-22 07:46:00.420449', '2025-05-23 07:46:00.000000', 541150219354505, '3f66fee2beb14ada8166142ae05a4c9d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (287, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NzA2NSwiaWF0IjoxNzQ3OTAwNjY1LCJqdGkiOiJiODM5YjM2ZGEzNWE0NGI3YWUxNmYwYjMyNzBiMTM5ZiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.OONhH5btyhu9BRUe2EbFubbgeBZjbvEHfbGyaa6jkwU', '2025-05-22 07:57:45.132472', '2025-05-23 07:57:45.000000', ************, 'b839b36da35a44b7ae16f0b3270b139f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (288, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NzA2NSwiaWF0IjoxNzQ3OTAwNjY1LCJqdGkiOiIyYTE0MTA1MjAwMjY0ZmY2OWEwMWRjOWQ1YjdmMzRhMSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.eTDn-ss3O9qZY_8NrYPHuL8jezisSu4391R5emBVfQs', '2025-05-22 07:57:45.142144', '2025-05-23 07:57:45.000000', ************, '2a14105200264ff69a01dc9d5b7f34a1');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (289, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDk1NCwiaWF0IjoxNzQ3ODk4NTU0LCJqdGkiOiJjNDdmNTBhYmNkZTA0MzQwYmE4OWQ1YzY5YmE4MzM4NiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Rlq_icaGcWjpO5zXnJhhNdSN6uJn6wNCfRv2meJ1HVU', '2025-05-22 07:22:34.056962', '2025-05-23 07:22:34.000000', 541150219354505, 'c47f50abcde04340ba89d5c69ba83386');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (290, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NDk1NCwiaWF0IjoxNzQ3ODk4NTU0LCJqdGkiOiI1MDdjNmI4NmIwNGI0ZDAzYjQ2YTUzYWEyMWQ0MTA3MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.-lm3FDjLoD0AcQNrj3GDhXsPkcwNW5X9bszEuGU0GEI', '2025-05-22 07:22:34.106366', '2025-05-23 07:22:34.000000', 541150219354505, '507c6b86b04b4d03b46a53aa21d41071');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (291, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NjU0OSwiaWF0IjoxNzQ3OTAwMTQ5LCJqdGkiOiJiYmUzZTZmY2NlY2Q0ZGRlYjM1MWZjZWJhOGVmNTRhOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.q9dZXmeSo_ZTJqBZ0mF_Cyeze7nFuRTK_NzYVuy5q8E', '2025-05-22 07:49:09.630304', '2025-05-23 07:49:09.000000', 541150219354505, 'bbe3e6fccecd4ddeb351fceba8ef54a9');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (292, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Nzk4NjU0OSwiaWF0IjoxNzQ3OTAwMTQ5LCJqdGkiOiJhYzUxNjlmNGJmNzg0NTM0YTU4NDQxMDQwOWQxY2E3OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.nAyHArexot5dJYclO3e1tPPJRCCr1JdbuD9vNn7uJ3c', '2025-05-22 07:49:09.669785', '2025-05-23 07:49:09.000000', 541150219354505, 'ac5169f4bf784534a584410409d1ca78');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (293, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA2OTk3MywiaWF0IjoxNzQ3OTgzNTczLCJqdGkiOiJkMWIzYzYyNTE5ZTM0ODQyYjEyYTYwYWYyMGI3ZGI5YiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yrgU-DPVgMA1JeLpy-vIIE3Ypoao8NwNw-HGlrf59TU', '2025-05-23 06:59:33.799240', '2025-05-24 06:59:33.000000', 541150219354505, 'd1b3c62519e34842b12a60af20b7db9b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (294, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA2OTk3MywiaWF0IjoxNzQ3OTgzNTczLCJqdGkiOiIzNzYyMDRlZTcxNzk0YTExOTM1NmFlMDVlMWU3MzM4MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.fZP91u2NQH24uisSBcvn1s96sAHmBQLrodMPf1jcbFU', '2025-05-23 06:59:33.810259', '2025-05-24 06:59:33.000000', 541150219354505, '376204ee71794a119356ae05e1e73381');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (295, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5MDQ2OSwiaWF0IjoxNzQ4MDA0MDY5LCJqdGkiOiI2ZmJkZmFhOGE4NTg0Y2M0YWQ0MTY2ZDM0YmMyOTFmZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.9o8G9MOdscUqIkZ5MBCmH2Tb0s5W2vI4bMD-oHQbL_Q', '2025-05-23 12:41:09.386483', '2025-05-24 12:41:09.000000', 541150219354505, '6fbdfaa8a8584cc4ad4166d34bc291fe');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (296, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5MDQ2OSwiaWF0IjoxNzQ4MDA0MDY5LCJqdGkiOiJjMTBiZjc4YWZiYjA0NjgyYjQ1Y2Y0MjY3OGI4NjlkNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.csuwAhZvQQCk2YWKKgqLTIkNxuA7xIMbd4RRgtKdxZs', '2025-05-23 12:41:09.423407', '2025-05-24 12:41:09.000000', 541150219354505, 'c10bf78afbb04682b45cf42678b869d7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (297, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5MDc0MiwiaWF0IjoxNzQ4MDA0MzQyLCJqdGkiOiI0NGNkOWJkMjAyZGU0YjRkOWU2ZWViOTZhMGQ3NzkyZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.UEC53wf9Bt2wsI6t7Oy4W-KrGTh-rs2Ptup8I-hTskI', '2025-05-23 12:45:42.992219', '2025-05-24 12:45:42.000000', 541150219354505, '44cd9bd202de4b4d9e6eeb96a0d7792f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (298, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5MDc0MywiaWF0IjoxNzQ4MDA0MzQzLCJqdGkiOiIxNDc5MTVkNDFmMzA0NjMxOTkwMGQ3OThhMWE2Yzg0OSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.1bTjNMpUCMOIjwmXHtONk_4nJ2TS3nxvjbu7veoJIZU', '2025-05-23 12:45:43.036374', '2025-05-24 12:45:43.000000', 541150219354505, '147915d41f3046319900d798a1a6c849');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (299, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5MTA4OSwiaWF0IjoxNzQ4MDA0Njg5LCJqdGkiOiI2MjViOWU5N2U2ZDM0NzRiYWM5ZDBlZGU2ZTk0MjA5NSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.k-vAqdy3phSBOBS-HEHx0b7rIhUwbv6lXvX6P9QFoDg', '2025-05-23 12:51:29.445863', '2025-05-24 12:51:29.000000', 541150219354505, '625b9e97e6d3474bac9d0ede6e942095');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (300, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5MTA4OSwiaWF0IjoxNzQ4MDA0Njg5LCJqdGkiOiI0ZWFhYWU4YmVjY2Y0MDU5YTBjNWNmMTFkM2U5MGNlYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.PLnQvKy8PvlJL2q50QY5hChMiAvpr9VwJph6u38MPj8', '2025-05-23 12:51:29.469460', '2025-05-24 12:51:29.000000', 541150219354505, '4eaaae8beccf4059a0c5cf11d3e90cea');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (301, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5MTE1MSwiaWF0IjoxNzQ4MDA0NzUxLCJqdGkiOiJlODg0NGQxMmEyMTA0ODAzODBlMWJhOWU0N2Y0MjljNSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ZYcIor0kSZhaVrZwaAupE8OMyYQAasznEVdVDw-thPM', '2025-05-23 12:52:31.352852', '2025-05-24 12:52:31.000000', 541150219354505, 'e8844d12a210480380e1ba9e47f429c5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (302, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5MTE1MSwiaWF0IjoxNzQ4MDA0NzUxLCJqdGkiOiIxNjMyZjc0OTE5NWY0ZjUwYWU4OWYyYzQ0ZTRhMzk4NCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.dYdLniE3pLXpp5da7Z9oRMq7e6RrIivawQNAuwFFig8', '2025-05-23 12:52:31.387164', '2025-05-24 12:52:31.000000', 541150219354505, '1632f749195f4f50ae89f2c44e4a3984');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (303, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5NDYzOSwiaWF0IjoxNzQ4MDA4MjM5LCJqdGkiOiI1MzI4YmFlN2JjZjI0M2QwOGU1MDIzYTg5YzFkYTJhZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.xHGhSedDLVN6oWsjc-xbuX8dpdejvzO7P87eYCiXjZE', '2025-05-23 13:50:39.081459', '2025-05-24 13:50:39.000000', 541150219354505, '5328bae7bcf243d08e5023a89c1da2ad');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (304, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5NDYzOSwiaWF0IjoxNzQ4MDA4MjM5LCJqdGkiOiJkYmRhNjgxNmJkY2E0Yjk3YmE2OGRhMmQxOTRjOTlkZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.g6K5SJPqmg3ZRHHDlaU2MfA0FgpJqHfXKBUJoIKpbvk', '2025-05-23 13:50:39.124299', '2025-05-24 13:50:39.000000', 541150219354505, 'dbda6816bdca4b97ba68da2d194c99de');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (305, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5NDcxMiwiaWF0IjoxNzQ4MDA4MzEyLCJqdGkiOiIzZTBlZGFhMzQ3NDA0N2ZiOGQyMTFjZmVjMzUxNWQ2NiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.pN_u66XBJzEm1OoIQZfedw4OhU6n9v_fFrAG7FSyJdA', '2025-05-23 13:51:52.816787', '2025-05-24 13:51:52.000000', 541150219354505, '3e0edaa3474047fb8d211cfec3515d66');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (306, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5NDcxMiwiaWF0IjoxNzQ4MDA4MzEyLCJqdGkiOiI1NGE0NzUzMjI0ODE0ZTJmYWQ3NDk2NjQ4MzczOThiMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.0wAxuUBl6suqWZyqTzOEi1bE2e_epjGIMk2qdptkeJA', '2025-05-23 13:51:52.823889', '2025-05-24 13:51:52.000000', 541150219354505, '54a4753224814e2fad749664837398b0');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (307, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5ODkwMCwiaWF0IjoxNzQ4MDEyNTAwLCJqdGkiOiI3OGE0MGE4ZTQzYmM0YjU4YmJkZmYxNjQ1NTMwNTE4YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ONsAojmDqQpNLm0Hl1lNMhkZ-ohczGbMMQIXxPsEioE', '2025-05-23 15:01:40.544023', '2025-05-24 15:01:40.000000', 541150219354505, '78a40a8e43bc4b58bbdff1645530518c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (308, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODA5ODkwMCwiaWF0IjoxNzQ4MDEyNTAwLCJqdGkiOiIwZjBjZDNhYjA0NTI0NzAzOGZkYWY0NjVhZmJiNTVmMiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ynVsI8_uGkwtE77ZozM4gfZFVFSbJm67ucAPAjHL5TM', '2025-05-23 15:01:40.583640', '2025-05-24 15:01:40.000000', 541150219354505, '0f0cd3ab045247038fdaf465afbb55f2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (309, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.MXtaSiA0cfnmEOLal2Ip-tJJctVznTnW13lunAnK2Hw', '2025-05-25 11:31:33.272454', '2025-05-26 11:31:33.000000', 541150219354505, '8158249ee41241258ae6cccf3e3dc4e7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (310, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODI1OTA5MywiaWF0IjoxNzQ4MTcyNjkzLCJqdGkiOiI4YTI3M2Y0ZjQ1NTc0YWUzOTRlOGUyNDZlY2U4ZDZkOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.XjYFM_JlshdCOOHyAlOXKuDR18eeIUp9Rln5kuLzYX8', '2025-05-25 11:31:33.314485', '2025-05-26 11:31:33.000000', 541150219354505, '8a273f4f45574ae394e8e246ece8d6d9');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (311, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.5Z_JsHPhxso491pM0BKSlabqrLHt2DoUSjLCNhS__3w', '2025-05-25 14:05:46.994458', '2025-05-26 14:05:46.000000', 541150219354505, '7824813c4e794ec3b61b2d5cb6f4d732');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (312, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.Vk5PvHv7dMlnPFS36ofRSZ8gQhih9bNChdyVWYZJIaY', '2025-05-25 14:05:47.003290', '2025-05-26 14:05:47.000000', 541150219354505, '0a655d636d8d4435b6825c308e25d067');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (313, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.y76F9utCWMqi4APL5kd_c26Fe-E8c3I6mwqPwBxHzwE', '2025-05-25 14:06:44.824823', '2025-05-26 14:06:44.000000', 541150219354505, '44efccbf2cda4a3d8abe9bc499283be5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (314, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODI2ODQwNCwiaWF0IjoxNzQ4MTgyMDA0LCJqdGkiOiIzNDQwZjY4YmM0MmY0OGU4YTA4NDY3MTkxMjMzYzk3YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.w4jgtRKyEzE19OtElvIz7f6Vcs3gzUarta5IbiP9T5I', '2025-05-25 14:06:44.833482', '2025-05-26 14:06:44.000000', 541150219354505, '3440f68bc42f48e8a08467191233c97c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (315, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQxNDA3OSwiaWF0IjoxNzQ4MzI3Njc5LCJqdGkiOiJmMjE4ZGY2ZGRlNmE0YjgwOThlOGYxYzUyNTQ3YzhkNSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.7j6sLeSpXZGYJa0X0PPcsqLTmthjwUFZGlSqRKjSvKU', '2025-05-27 06:34:39.154503', '2025-05-28 06:34:39.000000', 541150219354505, 'f218df6dde6a4b8098e8f1c52547c8d5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (316, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQxNDA3OSwiaWF0IjoxNzQ4MzI3Njc5LCJqdGkiOiJlZjc4YTg2Y2JhOGI0MjUzYTViNWUyMmY5NDk4ZTAwNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.iW1DQfTNw0YWKGouA-wnpU3KyAiK41C_4YhENNYxyYE', '2025-05-27 06:34:39.161787', '2025-05-28 06:34:39.000000', 541150219354505, 'ef78a86cba8b4253a5b5e22f9498e007');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (317, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQxNDE0MywiaWF0IjoxNzQ4MzI3NzQzLCJqdGkiOiJhYzY4ODc5MWFmZGM0ZDM5YjQzYjdkYjVlYjk1MWJjNiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.MkkFZk1TnQdhb5JOVTIR0E6Vzj0Rb9JYixZHK90DSQ8', '2025-05-27 06:35:43.117392', '2025-05-28 06:35:43.000000', ************, 'ac688791afdc4d39b43b7db5eb951bc6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (318, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQxNDE0MywiaWF0IjoxNzQ4MzI3NzQzLCJqdGkiOiI4YjA0ZjljODQ0MTU0ODljOTM2ZmUxYjE4MTRmN2UxZSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.8vknj_Y5GQtZOnZnk7KscAkdyzPRy9Fugd9PxmGoGJo', '2025-05-27 06:35:43.122396', '2025-05-28 06:35:43.000000', ************, '8b04f9c84415489c936fe1b1814f7e1e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (319, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQxNDcwMCwiaWF0IjoxNzQ4MzI4MzAwLCJqdGkiOiI3NDQ3MWM4YjdlNjE0YTBiODg3NWY4Y2UyZmMyNDAxYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.UUH4_ernnWY4fDZLh0Pe3JxCENSn8YEeDxcX00Zvmiw', '2025-05-27 06:45:00.892807', '2025-05-28 06:45:00.000000', 541150219354505, '74471c8b7e614a0b8875f8ce2fc2401a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (320, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQxNDcwMCwiaWF0IjoxNzQ4MzI4MzAwLCJqdGkiOiJlNjdmNzNjOTA0OTA0MDg4OWNmZDQyYjlkYjNmYTJlMiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.NiUSW-th8Uw71wHyPm5XMUlxzdfQ58OOdj8G_okz8Wk', '2025-05-27 06:45:00.899807', '2025-05-28 06:45:00.000000', 541150219354505, 'e67f73c9049040889cfd42b9db3fa2e2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (321, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQxNDcxMywiaWF0IjoxNzQ4MzI4MzEzLCJqdGkiOiI2ODk1ZmIyOTcyNjg0NzRhOWJhMjdkZDQxZDg2ZjM1OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.c7fwF0AQbfDbtYgxBXq5nhO3IIpsS7oXyp5v2vzdsgE', '2025-05-27 06:45:13.870865', '2025-05-28 06:45:13.000000', 541150219354505, '6895fb297268474a9ba27dd41d86f358');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (322, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQxNDcxMywiaWF0IjoxNzQ4MzI4MzEzLCJqdGkiOiJhN2FkZGQ1NTNiNDc0ZDZjODA0NjJiZGU3MmViMDYxZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.BKfIrAlSYr1cprY4AWAMm3QqTTFSzst6LBMzvNEBUS4', '2025-05-27 06:45:13.876950', '2025-05-28 06:45:13.000000', 541150219354505, 'a7addd553b474d6c80462bde72eb061f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (323, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQzMjc0OCwiaWF0IjoxNzQ4MzQ2MzQ4LCJqdGkiOiJjOTRjOWQ5MzgzM2I0NDY3YjRkZjEzNmViYmE2ZTZiZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.FoYoj1fiNwLIu4RZPLgVd8xdCfZ3FhOrFNgWISZyIJs', '2025-05-27 11:45:48.931467', '2025-05-28 11:45:48.000000', 541150219354505, 'c94c9d93833b4467b4df136ebba6e6bd');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (324, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQzMjc0OCwiaWF0IjoxNzQ4MzQ2MzQ4LCJqdGkiOiI3OGVmYjZmNDYwMTA0MTA4YjU3YWFlMTU1NTU3YWVkZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.mHcXue0afB5W5tUgoVv7BBgLBZKSiDn2Tfi4v3X0arQ', '2025-05-27 11:45:48.939123', '2025-05-28 11:45:48.000000', 541150219354505, '78efb6f460104108b57aae155557aede');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (325, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4Mjg3MSwiaWF0IjoxNzQ4Mzk2NDcxLCJqdGkiOiJjN2ZhYzc5MTc1Njc0OWU4YThhMTljODYzNWFkMDEyMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.07DBlUgn9pqvsLj1ZHGXYzDmFbqfsbQIaUF8T1nZTys', '2025-05-28 01:41:11.283928', '2025-05-29 01:41:11.000000', 541150219354505, 'c7fac791756749e8a8a19c8635ad0120');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (326, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4Mjg3MSwiaWF0IjoxNzQ4Mzk2NDcxLCJqdGkiOiIzMTY5MjQ4YTE4Y2Q0M2NlOTcyMmUxYjk5ZWRiMWRmMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.OT3GhU9QxAxUv-hx5xf_-baKFZCy5EsDfQ5yP0NInO8', '2025-05-28 01:41:11.294959', '2025-05-29 01:41:11.000000', 541150219354505, '3169248a18cd43ce9722e1b99edb1df3');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (327, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NDc5OCwiaWF0IjoxNzQ4Mzk4Mzk4LCJqdGkiOiI2MDUyMGE5N2EwZTI0MzQ2OTNkNjk2ZjI5MzA3Y2E3OSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.iG3FjF-bdXB2HqpVAUcEqiZulFaLRJMjxaaYSSOWmE8', '2025-05-28 02:13:18.407641', '2025-05-29 02:13:18.000000', ************, '60520a97a0e2434693d696f29307ca79');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (328, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NDc5OCwiaWF0IjoxNzQ4Mzk4Mzk4LCJqdGkiOiJmMDJiNmQyZWY0MWE0OTY5ODcwMDMwMmY1NWZmOWUyNiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.g7A0V9jluGAJNS4FU7WL3tNU7P_4B3u5iaViQssLtXQ', '2025-05-28 02:13:18.417177', '2025-05-29 02:13:18.000000', ************, 'f02b6d2ef41a49698700302f55ff9e26');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (329, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NDkwNCwiaWF0IjoxNzQ4Mzk4NTA0LCJqdGkiOiJhYTk4MjQ1MGMzMGI0ZWVmOGM3ZGJmMGFkOTFiNWY3OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.FcjEhbIR7qH6SO-ZDW-zgsrKWqo9-kmjsvi_UKSUBgE', '2025-05-28 02:15:04.261968', '2025-05-29 02:15:04.000000', 541150219354505, 'aa982450c30b4eef8c7dbf0ad91b5f78');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (330, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NDkwNCwiaWF0IjoxNzQ4Mzk4NTA0LCJqdGkiOiJmOGVlN2FkNmJjMDk0MGZlODg4ODQxMzA3MzAwNmIzOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.SsxSOM2iDR9XKFh38Ts7C3aeLenLnjCr02QrF7QVvlQ', '2025-05-28 02:15:04.271726', '2025-05-29 02:15:04.000000', 541150219354505, 'f8ee7ad6bc0940fe8888413073006b39');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (331, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NDk0MiwiaWF0IjoxNzQ4Mzk4NTQyLCJqdGkiOiJmZGE3NjFkZGU0Y2M0MzJhOTVjMTM5M2JjMzA3MzY0NCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.yAV7JbfNnUYRrl4c-lbRLB6qzHT0nSXXF4hShAp14OU', '2025-05-28 02:15:42.265223', '2025-05-29 02:15:42.000000', ************, 'fda761dde4cc432a95c1393bc3073644');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (332, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NDk0MiwiaWF0IjoxNzQ4Mzk4NTQyLCJqdGkiOiJmOTBhOTBiYmEwZDY0Y2EzODM1NWVlYWIyMDViNjJlYSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.6LL8RrlQp_M43CSbXgC_ptlHm77CLSrA95A7DFDjFQc', '2025-05-28 02:15:42.273316', '2025-05-29 02:15:42.000000', ************, 'f90a90bba0d64ca38355eeab205b62ea');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (333, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************.7Eg04fx7hvYAKeanWvOK-qG9JfcuPiC53j2hyNpnS90', '2025-05-28 02:19:41.533893', '2025-05-29 02:19:41.000000', ************, '2d5bc0bde85848ca8b0c64699c9b2bc2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (334, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************.OEdhMuMLDc5Bb9rS4tVT4pHzj7gJYiRT97dNheRFwdI', '2025-05-28 02:19:41.542778', '2025-05-29 02:19:41.000000', ************, '1d685749cf424abdaa23c9fe4083682a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (335, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************.iWdbiD2x2Z7HOokkM50qF1vAC3F82TWY85knTVGCHu0', '2025-05-28 02:20:31.232697', '2025-05-29 02:20:31.000000', ************, 'fff54130d3bc489dbc570971dd54d070');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (336, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************.x7L2YWdRzNdNjjM59qit05syppAfrOTI2gJCREPjV6I', '2025-05-28 02:20:31.242085', '2025-05-29 02:20:31.000000', ************, '02f8684f19394c61919a9f198804ce14');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (337, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************.khYZjzaQQ-LGL6Uh_RwAxjKMpXlw7iiwUvO3LJPSkDA', '2025-05-28 02:22:16.276342', '2025-05-29 02:22:16.000000', ************, '9b314bb9cfe5473f80561a4d397ba9f7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (338, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NTMzNiwiaWF0IjoxNzQ4Mzk4OTM2LCJqdGkiOiIwOGJlMjY2MTk0NzM0NWZkYjFjNmRlNTI5Y2IyMDZjNSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.1nWkU8mFlmekQCE6eySgsEqykwSXH3w9LDSydDHI_7s', '2025-05-28 02:22:16.283379', '2025-05-29 02:22:16.000000', ************, '08be2661947345fdb1c6de529cb206c5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (339, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NTM2OSwiaWF0IjoxNzQ4Mzk4OTY5LCJqdGkiOiJkNjhlMDU2NDQwNWM0NDhiYTRhM2MxZTIwNGQ4NzVmZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.mjrxMHeJSzJBQBCxDG7RKV2y2Bl1znz77_cKWIu1xT8', '2025-05-28 02:22:49.231026', '2025-05-29 02:22:49.000000', ************, 'd68e0564405c448ba4a3c1e204d875fd');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (340, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NTM2OSwiaWF0IjoxNzQ4Mzk4OTY5LCJqdGkiOiI0ZGMwMmEyODc5MjM0NDAyYTNkYTNjYmJhYjkwOTFiMCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.1OA2tr8689WCjBz_Egteb577ZThoLRZMFn-4zZsTbZ8', '2025-05-28 02:22:49.240149', '2025-05-29 02:22:49.000000', ************, '4dc02a2879234402a3da3cbbab9091b0');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (341, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NTM4MSwiaWF0IjoxNzQ4Mzk4OTgxLCJqdGkiOiIwYzE4YTIyZTAyODc0N2U2ODc2NzlhZjlhMzM2Mjk5ZSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.vmDO31Ayp35S1-gvYJe64ueuuTx1yPa-8WfrVSTtFqE', '2025-05-28 02:23:01.837336', '2025-05-29 02:23:01.000000', ************, '0c18a22e028747e687679af9a336299e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (342, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NTM4MSwiaWF0IjoxNzQ4Mzk4OTgxLCJqdGkiOiI3YTUwZDg4NDVmZDc0NjkyODI3NWM4YjVhOGQ3MWY2MyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.aW2zZz7BsjjhgIBh8GzFEHRPjG6cF4TkoY-09vAYm3Q', '2025-05-28 02:23:01.844728', '2025-05-29 02:23:01.000000', ************, '7a50d8845fd746928275c8b5a8d71f63');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (343, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NTkyNSwiaWF0IjoxNzQ4Mzk5NTI1LCJqdGkiOiI5NjE3YTIwMmE0NmY0NzAwOGE0ZjNmYWM2MTRlMGE1OCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.Q1WlvXT3iOWnwj2r-Y4NIlu0ncVDMeWnL3bFYay3BPU', '2025-05-28 02:32:05.676512', '2025-05-29 02:32:05.000000', ************, '9617a202a46f47008a4f3fac614e0a58');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (344, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NTkyNSwiaWF0IjoxNzQ4Mzk5NTI1LCJqdGkiOiIwNmJlZmQ1OTA5ZGQ0Nzc3YjdjZjZmMzkyNmIwMWVkOSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.B-qmeWY5AjeSnxEAWPbMd4Lixvvma27WChVDZfbiygg', '2025-05-28 02:32:05.682231', '2025-05-29 02:32:05.000000', ************, '06befd5909dd4777b7cf6f3926b01ed9');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (345, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NjU3MywiaWF0IjoxNzQ4NDAwMTczLCJqdGkiOiI1NDM2ZTZkOGIyMjg0NzgzOGVhNTk3NGE3NjQxY2RjYSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.6XEy__1R45xL-m7xp-JdU9MrTfHm_8_HA-e9RDfJx7g', '2025-05-28 02:42:53.031317', '2025-05-29 02:42:53.000000', ************, '5436e6d8b22847838ea5974a7641cdca');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (346, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NjU3MywiaWF0IjoxNzQ4NDAwMTczLCJqdGkiOiJlYjM2Yjc4NmIyMDI0OGU0YmMyODUyM2NjNTMzM2YyYSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.ypa8hbuOMofcFk4jmw1giWVBF_VWYWTOP6TvxsurtaM', '2025-05-28 02:42:53.040351', '2025-05-29 02:42:53.000000', ************, 'eb36b786b20248e4bc28523cc5333f2a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (347, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODUwMzA0MCwiaWF0IjoxNzQ4NDE2NjQwLCJqdGkiOiJlYzMzZTI2YjJhODc0ZDI1OGNkZDUyYzUxNTkwMjY1MiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.yFfBisGcBebeBXyHuzO2nUBPEcqri4IXHYIeNm4UYD8', '2025-05-28 07:17:20.462540', '2025-05-29 07:17:20.000000', ************, 'ec33e26b2a874d258cdd52c515902652');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (348, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODUwMzA0MCwiaWF0IjoxNzQ4NDE2NjQwLCJqdGkiOiI5NWMzMGNmNzRhZDE0ODBmOTUyMDg1MmY3ZjA1YmIzMSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.TqTJ36G96_L1M-lICPA5Psls6BSqOUIDDGXco5QZOkM', '2025-05-28 07:17:20.472175', '2025-05-29 07:17:20.000000', ************, '95c30cf74ad1480f9520852f7f05bb31');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (349, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ3OTQxNiwiaWF0IjoxNzQ4MzkzMDE2LCJqdGkiOiJkODU1N2Q2OTE5ZDI0YTU5YWFhNzE3MDdmMzZkNWY2NCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.5uNvJEeCOyxAGmLEWhVq7dMYh2DpsQm-i4RlFO30128', '2025-05-28 00:43:36.092863', '2025-05-29 00:43:36.000000', ************, 'd8557d6919d24a59aaa71707f36d5f64');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (350, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ3OTQxNiwiaWF0IjoxNzQ4MzkzMDE2LCJqdGkiOiI4YTA5YTM3ZmY2ZmM0MTNiYThlNjE0ZmI2NTk5NWM1NSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.D_pHveO7yeuPD60KTo2thHB3OujYDNjOZvYxtSG0ZBA', '2025-05-28 00:43:36.100862', '2025-05-29 00:43:36.000000', ************, '8a09a37ff6fc413ba8e614fb65995c55');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (351, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ3OTQ0MSwiaWF0IjoxNzQ4MzkzMDQxLCJqdGkiOiIzY2MzNjlhODllNDE0YWM5YTA3MTMyODQyY2I0ODI1YyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.2YIvj0ZuQW5hbQc9nqO9RhWJkM9IrZAXV_dl9cfSilQ', '2025-05-28 00:44:01.566934', '2025-05-29 00:44:01.000000', ************, '3cc369a89e414ac9a07132842cb4825c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (352, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ3OTQ0MSwiaWF0IjoxNzQ4MzkzMDQxLCJqdGkiOiI5OWYzMDEzMmZmMTg0MWNkOWFkMzZmMjIyNmNiOTUzMyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.i7QpiT0Pkfzf29xxWsgYjFpJLdtFubT1-L_TjLqORZw', '2025-05-28 00:44:01.569931', '2025-05-29 00:44:01.000000', ************, '99f30132ff1841cd9ad36f2226cb9533');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (353, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NzEwNCwiaWF0IjoxNzQ4NDAwNzA0LCJqdGkiOiI1ZGJmN2JjZjJkNjE0Yzc0OTJkZmNhNzJmMDdhMDkwZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.WFf-gOw4r7xsFTPj1ZN_ELSUY74dNrxeVCBZDvDILZ8', '2025-05-28 02:51:44.981723', '2025-05-29 02:51:44.000000', ************, '5dbf7bcf2d614c7492dfca72f07a090d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (354, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODQ4NzEwNCwiaWF0IjoxNzQ4NDAwNzA0LCJqdGkiOiJlN2Q3NGY0Yzg3OGM0NjdhYWE2MmRiOTM0YTEzY2FmMSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.wtemp-ep0aurAuqRNxim-O0J9fbITtdhXSK4gaFCQ-U', '2025-05-28 02:51:44.990138', '2025-05-29 02:51:44.000000', ************, 'e7d74f4c878c467aaa62db934a13caf1');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (355, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODUyMzMzMSwiaWF0IjoxNzQ4NDM2OTMxLCJqdGkiOiJmMGFlMDc2MzJlMWU0YmZhODg3Njc3MmRhYjY0MGNmZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.bo08xaWgT4kykRTb5VAe07BXbnrjj6m-CcbAbiIFVwQ', '2025-05-28 12:55:31.693865', '2025-05-29 12:55:31.000000', ************, 'f0ae07632e1e4bfa8876772dab640cfd');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (356, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODUyMzMzMSwiaWF0IjoxNzQ4NDM2OTMxLCJqdGkiOiI3ZDc4NjBiODBiZjc0OWJlYWQ5Y2FiNWM2ZTJlZWQ4MiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.zmMnmQ2s7lm0zS7ofAoWNF_CcrWE6THQm6l28-3i50Q', '2025-05-28 12:55:31.720907', '2025-05-29 12:55:31.000000', ************, '7d7860b80bf749bead9cab5c6e2eed82');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (357, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODYzMDY5MywiaWF0IjoxNzQ4NTQ0MjkzLCJqdGkiOiJiMDEwYjcyNjg3Mzk0YjM4OTRlYTE0YTE1YmRjNzFjNSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.qCnswycKPCJu52vYR41lxb3Sb1iy6wdbPrDfIgoBok4', '2025-05-29 18:44:53.678241', '2025-05-30 18:44:53.000000', ************, 'b010b72687394b3894ea14a15bdc71c5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (358, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODYzMDY5MywiaWF0IjoxNzQ4NTQ0MjkzLCJqdGkiOiIyMTNjMzViNmQyNTc0NjkzYTU1NjEzN2U2NDFhZjZiZiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.R5bb_gvZTmYHdq7MZOkT0H_uKqf_m2Nw1VBUGltmYp0', '2025-05-29 18:44:53.691890', '2025-05-30 18:44:53.000000', ************, '213c35b6d2574693a556137e641af6bf');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (359, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODY2MjQyOSwiaWF0IjoxNzQ4NTc2MDI5LCJqdGkiOiIyZjRlY2ZmNDQ0Y2M0ZmZkYjk3N2RhMDhiMTdkYjM5ZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.D_IYnDLd-9sDyGgzUZ_z6fPBZjw2YpuFEvPfctUpv2o', '2025-05-30 03:33:49.965019', '2025-05-31 03:33:49.000000', 541150219354505, '2f4ecff444cc4ffdb977da08b17db39f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (360, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODY2MjQyOSwiaWF0IjoxNzQ4NTc2MDI5LCJqdGkiOiI3Zjg0NDBlOWIwMDE0MDY0OWFmZmM0MjhjOTQyMTdlZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.5dgLCiBihll1jbuEcG0gPpS6wWYJO2eJ6DhpXMKcKBo', '2025-05-30 03:33:49.969939', '2025-05-31 03:33:49.000000', 541150219354505, '7f8440e9b00140649affc428c94217ef');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (361, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODY2MjQ2MywiaWF0IjoxNzQ4NTc2MDYzLCJqdGkiOiI5MDgyNzVkNDIxY2Q0MDdmYWFiOWFiMmJmNDljNWFmMyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.5p4uEOpuHElH_iAedkvz2YupO-WHUkAaHfJIJuoKOAw', '2025-05-30 03:34:23.140857', '2025-05-31 03:34:23.000000', ************, '908275d421cd407faab9ab2bf49c5af3');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (362, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODY2MjQ2MywiaWF0IjoxNzQ4NTc2MDYzLCJqdGkiOiIzYjcxMWQ4MTkxNmM0YTY0OTNiNzljNmZkYjk2OTlkMSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.UDIUxP4bJSH4r1hpoDydHU3k2msz-b2RhKwI58rhGMU', '2025-05-30 03:34:23.144564', '2025-05-31 03:34:23.000000', ************, '3b711d81916c4a6493b79c6fdb9699d1');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (363, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODY2NjMzMiwiaWF0IjoxNzQ4NTc5OTMyLCJqdGkiOiIyOTQwOTc0MjQyYzI0YWY2ODUwMGZlMTYyNDFhMjBmYiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.EL3W1F4PwiJliiA5e83LeP-JgtWnsj6KF1RtS4TO4bw', '2025-05-30 04:38:52.840325', '2025-05-31 04:38:52.000000', ************, '2940974242c24af68500fe16241a20fb');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (364, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODY2NjMzMiwiaWF0IjoxNzQ4NTc5OTMyLCJqdGkiOiIwNjFkMzBkMTA1Mzc0MDA2OTBmZDNiNjU5NTA2ZWRjZiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.gC5h2mbCdbz5nIUmIUAkOowmfndrZMeSV5tQ_Qa02B4', '2025-05-30 04:38:52.848324', '2025-05-31 04:38:52.000000', ************, '061d30d10537400690fd3b659506edcf');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (365, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODY3MzkxNiwiaWF0IjoxNzQ4NTg3NTE2LCJqdGkiOiIxNmUyMjA4YWRhNGE0OWQ3OGRhYmRhYTA3ODczZTA3NCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.d2tPtgv2KgN46tG0UFWO2GrHL0qAyFLYW7_RjlDvFIs', '2025-05-30 06:45:16.795172', '2025-05-31 06:45:16.000000', ************, '16e2208ada4a49d78dabdaa07873e074');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (366, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODY3MzkxNiwiaWF0IjoxNzQ4NTg3NTE2LCJqdGkiOiJjNzY4MTMxODA1NDY0NTQzOTI3NzY3NDg5ZTJlYzNkNCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.Lh0nmhTTdblpbZrXcKhEa_stPQ5-gkwVkzg7y2Kig-M', '2025-05-30 06:45:16.833563', '2025-05-31 06:45:16.000000', ************, 'c768131805464543927767489e2ec3d4');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (367, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODY5MDI1MywiaWF0IjoxNzQ4NjAzODUzLCJqdGkiOiJjNTFmM2I1YTZjOTk0NWZiYWE2MWEzZWQ0Njk4NWMyNiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.KmXiiGGRT8bCGpnYr7hzI0cBZBlJQk4_UFS9Cl61LX8', '2025-05-30 11:17:33.521596', '2025-05-31 11:17:33.000000', ************, 'c51f3b5a6c9945fbaa61a3ed46985c26');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (368, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODY5MDI1MywiaWF0IjoxNzQ4NjAzODUzLCJqdGkiOiI5NjJjZGQ5ODEwNjA0ZDMzOTY5NDNlNzk4YmEwYmM5NiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.EfuuEwg8P9nri9ucN2p7Z05T8zemlUpRzTo3jnDoVRY', '2025-05-30 11:17:33.573254', '2025-05-31 11:17:33.000000', ************, '962cdd9810604d3396943e798ba0bc96');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (369, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODg0NTU2NywiaWF0IjoxNzQ4NzU5MTY3LCJqdGkiOiIzNDg5YmQ3MmI0NDE0ODRlOGRkMjIwYzk3Yzg2ZjlkYiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.mtmmVXW5wMACN0k92pCShEFMIFmMRSA3dVG0_1c12P8', '2025-06-01 06:26:07.788972', '2025-06-02 06:26:07.000000', ************, '3489bd72b441484e8dd220c97c86f9db');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (370, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0ODg0NTU2NywiaWF0IjoxNzQ4NzU5MTY3LCJqdGkiOiI0MjNlODVlNjYwZTM0YWNlOGEzOTYzYjAwM2VhZDUyNCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.RYUW4oWa4fF08lrOTyF5zOvuo901-Y9Y_uoXmawnuiI', '2025-06-01 06:26:07.795705', '2025-06-02 06:26:07.000000', ************, '423e85e660e34ace8a3963b003ead524');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (371, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTAwODE4MCwiaWF0IjoxNzQ4OTIxNzgwLCJqdGkiOiJlZmRjNTY3YzVlMWQ0NzZhYWI4NmEwM2JhYmZmOGQzZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.dv2EmYrOixOBYfzqdhI8HNuZNolyz5eFVP8qoPx6mDg', '2025-06-03 03:36:20.078477', '2025-06-04 03:36:20.000000', 541150219354505, 'efdc567c5e1d476aab86a03babff8d3d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (372, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTAwODE4MCwiaWF0IjoxNzQ4OTIxNzgwLCJqdGkiOiJmNTAwMmNmMjJiYjg0YzdlYmIyMDcyYzg5NmE4ZTg0NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ns9zTUpMPU4G76QibJ0c0k5IoeksMxhbsG7yvXBxQ2I', '2025-06-03 03:36:20.092369', '2025-06-04 03:36:20.000000', 541150219354505, 'f5002cf22bb84c7ebb2072c896a8e847');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (373, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTAyNjU2NCwiaWF0IjoxNzQ4OTQwMTY0LCJqdGkiOiIyY2MxMzI1YTcwYTQ0OGZlYmRlMjEyNjZkYjQ1ODlkOSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.FXr3H2pJXbcJzI60Ihp8vB823meTPABuziDxrHd6SwM', '2025-06-03 08:42:44.588589', '2025-06-04 08:42:44.000000', ************, '2cc1325a70a448febde21266db4589d9');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (374, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTAyNjU2NCwiaWF0IjoxNzQ4OTQwMTY0LCJqdGkiOiJlNWNhNTM4MzY0Zjk0M2U2ODEzMjViOGIxZWVhYzg1NyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.OHhv60944gXvdosJMUiL7nkwGdS42dcgXvRuZ94MCHk', '2025-06-03 08:42:44.602232', '2025-06-04 08:42:44.000000', ************, 'e5ca538364f943e681325b8b1eeac857');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (375, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTEwNjMwMywiaWF0IjoxNzQ5MDE5OTAzLCJqdGkiOiI2Zjc5NDNkNzU0ZTI0OGJjOWNjMDg3Yzg4MDM5YzVjNCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.mChDcwdfp5abxhS0MCCfDI98NfS6sMdaYwhj0_0VV0Q', '2025-06-04 06:51:43.341860', '2025-06-05 06:51:43.000000', ************, '6f7943d754e248bc9cc087c88039c5c4');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (376, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTEwNjMwMywiaWF0IjoxNzQ5MDE5OTAzLCJqdGkiOiJjZjg3MGY1ZDk1NGQ0MzExOWYxYzBlZGRmOTEwYjdmZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.5j28VYuSuRADtLXM1C0tWrG3kCKvwxfiJqjZoU6N378', '2025-06-04 06:51:43.355023', '2025-06-05 06:51:43.000000', ************, 'cf870f5d954d43119f1c0eddf910b7fd');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (377, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTEwNjM5MCwiaWF0IjoxNzQ5MDE5OTkwLCJqdGkiOiIwZjNkNzM4ODc2ZTE0MzU5YTdlNTA5ZjlhZWZmYWY3YiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.JZICrwsxJ4FUoyLwpKB499cMicqEFLy4v7MXJo0xXJw', '2025-06-04 06:53:10.001495', '2025-06-05 06:53:10.000000', ************, '0f3d738876e14359a7e509f9aeffaf7b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (378, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTEwNjM5MCwiaWF0IjoxNzQ5MDE5OTkwLCJqdGkiOiI4MzdkZjVkNzgzMzc0ZDE1YTkxYzdlNTU1MGU0YWNjMiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.V0y_4OWakw51-bljZc4LhnOxETL4cbml8nm0-_wbMOg', '2025-06-04 06:53:10.008343', '2025-06-05 06:53:10.000000', ************, '837df5d783374d15a91c7e5550e4acc2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (379, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTUzNzU0MCwiaWF0IjoxNzQ5NDUxMTQwLCJqdGkiOiJlN2FhMTVjMzE2ZDg0ODVlYmE1MDNlMGRlZGMyYTc3MiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.SCqcy3vNeOjEg5nITJlKOAIpmOzN-3yS46ivDXTOYf8', '2025-06-09 06:39:00.005262', '2025-06-10 06:39:00.000000', ************, 'e7aa15c316d8485eba503e0dedc2a772');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (380, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTUzNzU0MCwiaWF0IjoxNzQ5NDUxMTQwLCJqdGkiOiI0MDA1MDlkZGNhYmI0YWFjYjU5NmZkNzM5ZjBmOTVkMiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.d9es9o32cIMQVG9Dt5sCzH43CerbsbYjMESJMHN5ntk', '2025-06-09 06:39:00.015242', '2025-06-10 06:39:00.000000', ************, '400509ddcabb4aacb596fd739f0f95d2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (381, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTYyODg5NSwiaWF0IjoxNzQ5NTQyNDk1LCJqdGkiOiI2ZmE2ZjhlZTgxMjI0OTMwOTUzYzRjZTJhMWE0ZDViZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.OTefWFhs_m3WkCT30g074I0DOpnCn94kJ3wi0Rnywlk', '2025-06-10 08:01:35.899115', '2025-06-11 08:01:35.000000', ************, '6fa6f8ee81224930953c4ce2a1a4d5bd');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (382, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTYyODg5NSwiaWF0IjoxNzQ5NTQyNDk1LCJqdGkiOiIyMTAxMWQ4NDM5MTg0NTg1YTVlNTczNTg5ZDFiN2Y4ZSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.oYhFuH4OlZbfhVokiZmS8ZsSRVZLYpEZVft4FSxS3Lc', '2025-06-10 08:01:35.968451', '2025-06-11 08:01:35.000000', ************, '21011d8439184585a5e573589d1b7f8e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (383, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTYzMDc4MiwiaWF0IjoxNzQ5NTQ0MzgyLCJqdGkiOiI4ZWVlMGZhYTE1ZmM0YzU4Yjg3ZjU5NTVlNjMxMzZlNyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.IrFaU95RIpGm1CkDawzuyU9cyqOBK7QFiC9xyrnuv2k', '2025-06-10 08:33:02.062204', '2025-06-11 08:33:02.000000', ************, '8eee0faa15fc4c58b87f5955e63136e7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (384, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTYzMDc4MiwiaWF0IjoxNzQ5NTQ0MzgyLCJqdGkiOiJkNDg5MzllZjgyNTM0YmVlYTIxOGUzYzUwODhkY2IxYiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.Ha8kqY81DsBtWY5hZ3LTtP1oR11kvedn23OqkF4OVnA', '2025-06-10 08:33:02.103485', '2025-06-11 08:33:02.000000', ************, 'd48939ef82534beea218e3c5088dcb1b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (385, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTYzMTE5MSwiaWF0IjoxNzQ5NTQ0NzkxLCJqdGkiOiIwNDk5MGM0NTU4NzU0Y2ZhYjEwOGJmMDdkZjVjMWZhNiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.RNVfxwKrHaSTs_9tp2ltXOizeIQy1N1KWluw_z4X2ig', '2025-06-10 08:39:51.907637', '2025-06-11 08:39:51.000000', ************, '04990c4558754cfab108bf07df5c1fa6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (386, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTYzMTE5MSwiaWF0IjoxNzQ5NTQ0NzkxLCJqdGkiOiIxODk4N2YwN2UxODA0NDNlYTE1NzA5MWE2YWMyMWUyMCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.RE1s4XsydLLw7N5wTE9F33Gl6jtKbf0OVb4q-EOncEw', '2025-06-10 08:39:51.949417', '2025-06-11 08:39:51.000000', ************, '18987f07e180443ea157091a6ac21e20');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (387, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTYzNzc2OCwiaWF0IjoxNzQ5NTUxMzY4LCJqdGkiOiJjMzdiZDU0YTg4Y2M0NThhOWNkMzJjMjkxYzJjNzE3OSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.DNczA7ZQOnM9wGDjfQx5wUKE9wlzO4xU1Ur-YyKRcFg', '2025-06-10 10:29:28.222956', '2025-06-11 10:29:28.000000', ************, 'c37bd54a88cc458a9cd32c291c2c7179');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (388, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTYzNzc2OCwiaWF0IjoxNzQ5NTUxMzY4LCJqdGkiOiJhNTIxMzdjMDk5YjQ0NWRlYjg2M2QxZTFlYzQwZWI2MCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.9OcYQ6s3chPBEV1P0CLtdt3Aub7tWUdtV6gjuNos4Dk', '2025-06-10 10:29:28.239855', '2025-06-11 10:29:28.000000', ************, 'a52137c099b445deb863d1e1ec40eb60');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (389, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxMTM3MiwiaWF0IjoxNzQ5NjI0OTcyLCJqdGkiOiI5ZTRmYTEwMmMxZTA0NjI4OTA3NjllY2M5YzJmMmM1MSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.gsRkJNkEibhonTNtoV6gLXLrUd4TjtHzYm4T-u266CM', '2025-06-11 06:56:12.508029', '2025-06-12 06:56:12.000000', ************, '9e4fa102c1e0462890769ecc9c2f2c51');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (390, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxMTM3MiwiaWF0IjoxNzQ5NjI0OTcyLCJqdGkiOiJjNWY2NGVkYTRlNGY0ZDQwOTgxYzlkOWE5Njg0NzAxNyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.dkvz2J2K6UK7dtVDhqKJR9kF1w9iqLl-8HEVL5p5E8w', '2025-06-11 06:56:12.515038', '2025-06-12 06:56:12.000000', ************, 'c5f64eda4e4f4d40981c9d9a96847017');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (391, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxMTQyMSwiaWF0IjoxNzQ5NjI1MDIxLCJqdGkiOiIyYmU0MDJlZDk1MDY0YmM2OGJkZjhkNzViZDA2M2U3OCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.Ld9EYRg9z0krc0hcvEy_oZQ87ORdG8V67j5ZYg1qwIU', '2025-06-11 06:57:01.899795', '2025-06-12 06:57:01.000000', ************, '2be402ed95064bc68bdf8d75bd063e78');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (392, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxMTQyMSwiaWF0IjoxNzQ5NjI1MDIxLCJqdGkiOiJkMThhZTM3MjhlZTM0MzBhOTY4OTBiNmRkYWJmN2U2NCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.5FupxBgkcBbmTDig-jHOJ4h8L8myAt1tARoIGRFFPyk', '2025-06-11 06:57:01.904713', '2025-06-12 06:57:01.000000', ************, 'd18ae3728ee3430a96890b6ddabf7e64');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (393, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxMTQ0MiwiaWF0IjoxNzQ5NjI1MDQyLCJqdGkiOiJhOThmZGQ4M2RlNTQ0ZGE5OGMxOTA3NDliZmQ2Yzg2ZSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.sH7eYtn7_pgC7n5IqexU4SLn0JFtrvpe86ZVFdQMgJg', '2025-06-11 06:57:22.598118', '2025-06-12 06:57:22.000000', ************, 'a98fdd83de544da98c190749bfd6c86e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (394, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxMTQ0MiwiaWF0IjoxNzQ5NjI1MDQyLCJqdGkiOiJlZDkzZTUzMDk1ZmU0ODI5OTYwZjc0NTNlNmRlMTVkZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.eKoKncks6CW2KtQ8bG62dHHhV0irm6EWdvxz2Jim-cQ', '2025-06-11 06:57:22.602654', '2025-06-12 06:57:22.000000', ************, 'ed93e53095fe4829960f7453e6de15dd');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (395, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxNDA5OCwiaWF0IjoxNzQ5NjI3Njk4LCJqdGkiOiIwMjcxNzY1MmQwMjk0OTdkYTE0Y2YyOWJkZTM1YTJmMSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.JeFs5ZEbOlAjvTfwJv4wFDpzNCSHOO2JvlRL2ld8whU', '2025-06-11 07:41:38.962659', '2025-06-12 07:41:38.000000', ************, '02717652d029497da14cf29bde35a2f1');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (396, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxNDA5OCwiaWF0IjoxNzQ5NjI3Njk4LCJqdGkiOiJiZThiYzg5NTZhOTk0OGJkYTkwZjBjZWMzNzk1OTBlOCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.cLlGXyAQDmAVQ26CAIPv0TNCJlsSUDi--T_aiA-g2jQ', '2025-06-11 07:41:38.967582', '2025-06-12 07:41:38.000000', ************, 'be8bc8956a9948bda90f0cec379590e8');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (397, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxNzE1MCwiaWF0IjoxNzQ5NjMwNzUwLCJqdGkiOiI2OThmZDU2YWJmZTA0YzgzYTg5MzY1Y2IxZDdmMGE4NSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.J7A_qtjuoMjV1ZLhKVikDaqXosov6yMJayrWBQIBJYM', '2025-06-11 08:32:30.844859', '2025-06-12 08:32:30.000000', ************, '698fd56abfe04c83a89365cb1d7f0a85');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (398, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxNzE1MCwiaWF0IjoxNzQ5NjMwNzUwLCJqdGkiOiIzMDMxODkwOTljZjI0YTBhOTJhYWUwNTQ1NGRkMmZmYSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.CAFTXUmCeeHrQIk21kgRIQZipaYRtN_g8o-ehkx6n0k', '2025-06-11 08:32:30.853761', '2025-06-12 08:32:30.000000', ************, '303189099cf24a0a92aae05454dd2ffa');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (399, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxOTk3OSwiaWF0IjoxNzQ5NjMzNTc5LCJqdGkiOiJiNGFkOTZmMDlkMWU0MTdiODAzZWQ5M2UzZDEwYzBkZSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.KN6gC8MPErDSMUwUzWhy_xji56ZZUhT1JrzI_JdrWqs', '2025-06-11 09:19:39.912214', '2025-06-12 09:19:39.000000', ************, 'b4ad96f09d1e417b803ed93e3d10c0de');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (400, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcxOTk3OSwiaWF0IjoxNzQ5NjMzNTc5LCJqdGkiOiI3ZDg2Mzg1NTFhNjQ0NjFiOTA0N2MxNGY5NWU1MjVjZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.PFtqHDv86IyrHA390oDbynE59B-H05DHBsyhMKmbYR8', '2025-06-11 09:19:39.921046', '2025-06-12 09:19:39.000000', ************, '7d8638551a64461b9047c14f95e525cd');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (401, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcyMDAxNSwiaWF0IjoxNzQ5NjMzNjE1LCJqdGkiOiJmYmI1ZTYwZDRjMDQ0NDE0OTY2M2QyYjBmMWEzZTA2NCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.yHe7qWOeiaf0-WkOHz-VAkTxFExj37T45MWiwVWsar0', '2025-06-11 09:20:15.432547', '2025-06-12 09:20:15.000000', ************, 'fbb5e60d4c0444149663d2b0f1a3e064');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (402, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcyMDAxNSwiaWF0IjoxNzQ5NjMzNjE1LCJqdGkiOiIyY2E0YzM5ZGM4MmM0YzljOGE3MmVjMzdjNGUxMjExNSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.VXv8UgY0Q0NTSHPiSdb6FWSz2djibvKHKD05yek1aMc', '2025-06-11 09:20:15.439848', '2025-06-12 09:20:15.000000', ************, '2ca4c39dc82c4c9c8a72ec37c4e12115');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (403, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcyMDAzNywiaWF0IjoxNzQ5NjMzNjM3LCJqdGkiOiI1OTY5MDVjNDJiNTg0OTNhODRlMzdmYzM4ODVjNGY1MSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.Rf78OoH4G97wKX6GCOXo2HYOZhCePuFodOzkF4_uc7Q', '2025-06-11 09:20:37.815981', '2025-06-12 09:20:37.000000', ************, '596905c42b58493a84e37fc3885c4f51');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (404, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTcyMDAzNywiaWF0IjoxNzQ5NjMzNjM3LCJqdGkiOiI5NGUzOWU0ZWQ1OTE0MjMzODUzMmJjZWE5NTA5MWMzNSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.nPDYNqUaXz6xF1tD9WoVyPzFiEiA9xQ2_korgQWJmjg', '2025-06-11 09:20:37.824703', '2025-06-12 09:20:37.000000', ************, '94e39e4ed59142338532bcea95091c35');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (405, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTgwMDI3MCwiaWF0IjoxNzQ5NzEzODcwLCJqdGkiOiJkMGEzZDRhYmVmYWY0ZTVlYWVkMTNlMjE2NTc2MDA0NiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.kYdd0Evou3xjNQAiF5kIxK-4MAbIooA0_rB8v3kde8Y', '2025-06-12 07:37:50.944963', '2025-06-13 07:37:50.000000', ************, 'd0a3d4abefaf4e5eaed13e2165760046');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (406, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTgwMDI3MCwiaWF0IjoxNzQ5NzEzODcwLCJqdGkiOiI0OWNhZmMwMGU5MDg0MTY4OGZkNGQ1Zjg2YThjMWQ2OSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.AVFOCalAjmJByM3JrhuldAuOXpwMY4vdu-dP4ySv1ps', '2025-06-12 07:37:50.965316', '2025-06-13 07:37:50.000000', ************, '49cafc00e90841688fd4d5f86a8c1d69');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (407, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDE2Mjg5MSwiaWF0IjoxNzUwMDc2NDkxLCJqdGkiOiJlNjU0MDY4NzQ3ZGM0MTc3OGRlYWM5MDEzMDI3ZjVhOSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.GTjgjqK53XH2FEz7AhGh49InCSD0BR4XMIvCm4HX5WM', '2025-06-16 12:21:31.669745', '2025-06-17 12:21:31.000000', ************, 'e654068747dc41778deac9013027f5a9');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (408, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDE2Mjg5MSwiaWF0IjoxNzUwMDc2NDkxLCJqdGkiOiIxZjgwMTYzMjg5ZTc0OTkyOGZmN2I2NTIzYzk2NzkwMCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.X-GO1avgUNlFBE8pBVKgWlTYRh8TvhqVzwq-9y46nGw', '2025-06-16 12:21:31.689400', '2025-06-17 12:21:31.000000', ************, '1f80163289e749928ff7b6523c967900');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (409, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDMwNDM2MSwiaWF0IjoxNzUwMjE3OTYxLCJqdGkiOiI2ODliOWVjNmQ4MmY0ZjhmOWZmMmI0MjIxYzZhMGJjZiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.tOqGSEgISmr_Ucsa10ITUQQrmYTAFcYmmjUh8qZu0AQ', '2025-06-18 03:39:21.664625', '2025-06-19 03:39:21.000000', ************, '689b9ec6d82f4f8f9ff2b4221c6a0bcf');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (410, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDMwNDM2MSwiaWF0IjoxNzUwMjE3OTYxLCJqdGkiOiI5ODViMGRmZTQ4MDc0NTJhODhjNjlkZDE4OWQ4NzE4NyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0._oPA7tGVYt-yPBPRkihsMzLNAep873BB7v1eeMM5LdE', '2025-06-18 03:39:21.672132', '2025-06-19 03:39:21.000000', ************, '985b0dfe4807452a88c69dd189d87187');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (411, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDMzODE5MiwiaWF0IjoxNzUwMjUxNzkyLCJqdGkiOiIwMjlhYjBlYzI2ODU0Y2NkYjYzODdlMDg1NDE5OThkMyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.6iK4QjY7x1HREQ3TnS6UXCmF289448g8oXwop7AjEIg', '2025-06-18 13:03:12.474317', '2025-06-19 13:03:12.000000', ************, '029ab0ec26854ccdb6387e08541998d3');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (412, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDMzODE5MiwiaWF0IjoxNzUwMjUxNzkyLCJqdGkiOiIzYTMxNjQ5MmQ1YmM0OTRkOGNhYTA4ZDdlYTc3NDc1YSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.qnQGlN4jyql5abCWnGP9Mql3yK94GbN-HE6YGFeNXE4', '2025-06-18 13:03:12.485330', '2025-06-19 13:03:12.000000', ************, '3a316492d5bc494d8caa08d7ea77475a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (413, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDM4NzUyOSwiaWF0IjoxNzUwMzAxMTI5LCJqdGkiOiIzMWJmNmYzYWExYTg0ZmZiOThiOGNkNTIyNjQ1NWNmOCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.SfUkRAO_qtoyYuIHdWx1O4eo_ZJ5wkIMbYIppCI306I', '2025-06-19 02:45:29.449904', '2025-06-20 02:45:29.000000', ************, '31bf6f3aa1a84ffb98b8cd5226455cf8');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (414, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDM4NzUyOSwiaWF0IjoxNzUwMzAxMTI5LCJqdGkiOiJjMzQzN2JiMGZkNDk0NGMzOGEzYzUxODY0N2EyMjkzZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.Vd3rG3lW0NB8U5kklNjXOtL3AEvfKDFEGgmX0H9zdg4', '2025-06-19 02:45:29.466462', '2025-06-20 02:45:29.000000', ************, 'c3437bb0fd4944c38a3c518647a2293d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (415, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDY1MjA0NSwiaWF0IjoxNzUwNTY1NjQ1LCJqdGkiOiJjN2QwYTA4MjQzNjI0YWY2YTFjZGQ5MTQxNzU0NGNiYiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.Pv4T7g8Lti1w21vC4hZo4PRrhELDGWHJlUEYtbm6uF8', '2025-06-22 04:14:05.781427', '2025-06-23 04:14:05.000000', ************, 'c7d0a08243624af6a1cdd91417544cbb');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (416, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDY1MjA0NSwiaWF0IjoxNzUwNTY1NjQ1LCJqdGkiOiI0MjI0Mjc2YWFjNzM0OWUwYWY3MWJjOWU5NWY5NDcxOSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.k5iUxjv2zsMzcDZqmIDvkxyAGs2FU7biIxWwhr1j-_8', '2025-06-22 04:14:05.794312', '2025-06-23 04:14:05.000000', ************, '4224276aac7349e0af71bc9e95f94719');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (417, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDY4NTAyNCwiaWF0IjoxNzUwNTk4NjI0LCJqdGkiOiI1NzViOWIyNmE1NTc0N2M1YTcwMmE4MjhhZjU5NTI4YyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.cYHE-jTezx_w0nMw1z7Tce0kWrkI0sZO5QOEKCjR9Tc', '2025-06-22 13:23:44.679820', '2025-06-23 13:23:44.000000', ************, '575b9b26a55747c5a702a828af59528c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (418, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDY4NTAyNCwiaWF0IjoxNzUwNTk4NjI0LCJqdGkiOiJhYTcyMTQyNWNlMTk0OTk4OWMxMjMwNWFhNTI3MzMwOSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.8vQ5LyXOnJlx7o1_I0WpFylC19nsF4O5YbkdAgbf2e0', '2025-06-22 13:23:44.688040', '2025-06-23 13:23:44.000000', ************, 'aa721425ce1949989c12305aa5273309');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (419, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDY4NTQzNiwiaWF0IjoxNzUwNTk5MDM2LCJqdGkiOiJmYTVlMjkwZmJlYTE0YmNjYWMyYzA4NmJlODM1OGVkZSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.EhNWuz_B8EB2TMWLwFzXtMd0miFHSiruXy80QOtB_2E', '2025-06-22 13:30:36.080499', '2025-06-23 13:30:36.000000', ************, 'fa5e290fbea14bccac2c086be8358ede');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (420, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDY4NTQzNiwiaWF0IjoxNzUwNTk5MDM2LCJqdGkiOiI5NDRiOGE1ZjMzODc0NTFlOTAyMGQ4YTI3NWE4Mzk5OCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.4cL1E36G0J8OEwHLHYOuy7Jqn1Vdkj66s55WuQmZU-I', '2025-06-22 13:30:36.111212', '2025-06-23 13:30:36.000000', ************, '944b8a5f3387451e9020d8a275a83998');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (421, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDY4NzAxMSwiaWF0IjoxNzUwNjAwNjExLCJqdGkiOiJhODA1N2M3M2QyMjM0ZTk1OWRlNmZkMWUzOWUxZDc2MyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.QyWbEngBRHWTbnZQBImwEKwryMxeaZqUkddQgULeLfI', '2025-06-22 13:56:51.766788', '2025-06-23 13:56:51.000000', ************, 'a8057c73d2234e959de6fd1e39e1d763');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (422, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDY4NzAxMSwiaWF0IjoxNzUwNjAwNjExLCJqdGkiOiJhMDM2ZjgwMjVjZDk0NTc1YjE4MWJhOGFkMDgwYmRmMCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.8jcKdb57lyOXuh7gMTCzQbAVgD-ApW_jdOe3gxRlKNM', '2025-06-22 13:56:51.800919', '2025-06-23 13:56:51.000000', ************, 'a036f8025cd94575b181ba8ad080bdf0');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (423, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDczMTIzOSwiaWF0IjoxNzUwNjQ0ODM5LCJqdGkiOiJiMWM2YmIzZTAwZTg0ZjJhODM3ZGE1NDc1MmYxOTZkZiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.u9XXZQmWkgnkEM_2iCY_fiGlrGfyGulJm2YLMJFxs_M', '2025-06-23 02:13:59.150536', '2025-06-24 02:13:59.000000', ************, 'b1c6bb3e00e84f2a837da54752f196df');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (424, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDczMTIzOSwiaWF0IjoxNzUwNjQ0ODM5LCJqdGkiOiI3MmQ3YzUyNGZjNTc0OTQxYjRmMzgzMGI4NjBiYTRhYyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.nKraNS9Ir2XB94-JhdGnUKd3VcJ9Yq6WlVxpJ1Wutgc', '2025-06-23 02:13:59.198295', '2025-06-24 02:13:59.000000', ************, '72d7c524fc574941b4f3830b860ba4ac');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (425, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDc0MTAyOSwiaWF0IjoxNzUwNjU0NjI5LCJqdGkiOiJiNWQ5ZDE5ZTJkNjE0YTRiOWE5ZDNjZDJjOGVlM2VmNyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.Wv2FyZHvVZg4kc_Qc4QbJTTgKXAL3foEf1UISraYhM8', '2025-06-23 04:57:09.769778', '2025-06-24 04:57:09.000000', ************, 'b5d9d19e2d614a4b9a9d3cd2c8ee3ef7');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (426, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDc0MTAyOSwiaWF0IjoxNzUwNjU0NjI5LCJqdGkiOiI4NmQxOTQ2MzQ0ZTA0ZTEwODQ3N2MxOTg3NjU1MThmYSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.UPtpIh5ODzxT9H_dRPe-JP-nr6FBF9zdhd58LfMwkTc', '2025-06-23 04:57:09.811801', '2025-06-24 04:57:09.000000', ************, '86d1946344e04e108477c198765518fa');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (427, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDc0NTAzMSwiaWF0IjoxNzUwNjU4NjMxLCJqdGkiOiI0Yzg0NzA1MWZkNTA0YmMzYjNkYWUzM2NlODgyYzNkMiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.-qhQRv3CztW1k8mOBzTWwfSziGXbw7QsMYzJIY1UulY', '2025-06-23 06:03:51.779457', '2025-06-24 06:03:51.000000', ************, '4c847051fd504bc3b3dae33ce882c3d2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (428, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDc0NTAzMSwiaWF0IjoxNzUwNjU4NjMxLCJqdGkiOiIxOTM1YjkyYjI2NWE0MWU4YWQxNWE4YmI2M2I3Mzg3ZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.52-ueZAcQPQlPEFQOlYjnjSS5SLen5O5LQnJj7J5Xiw', '2025-06-23 06:03:51.826630', '2025-06-24 06:03:51.000000', ************, '1935b92b265a41e8ad15a8bb63b7387d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (429, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDc0NTA0MywiaWF0IjoxNzUwNjU4NjQzLCJqdGkiOiI4Zjk4MzA5OTU3OGI0ZTg3OWY3M2E5OGQ0YmQxNzc4YiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.T6QYKIJD1OyfmYUH2CoN4zQmM6yxfvJhdGxsG1o5dc4', '2025-06-23 06:04:03.999541', '2025-06-24 06:04:03.000000', ************, '8f983099578b4e879f73a98d4bd1778b');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (430, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDc0NTA0NCwiaWF0IjoxNzUwNjU4NjQ0LCJqdGkiOiI2YzkxYTZjMTY0Nzk0OGI5ODAwYWZhYjk0YjhlNjVjOSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.F4d-hiapsFA1NSp5MTfuHBQXZDHBsGYCAZN5IEu5AWY', '2025-06-23 06:04:04.042972', '2025-06-24 06:04:04.000000', ************, '6c91a6c1647948b9800afab94b8e65c9');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (431, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDkwNDU5OSwiaWF0IjoxNzUwODE4MTk5LCJqdGkiOiI3ZDAxOTNhY2Q0MGQ0Y2QzYjFlNDUzYjExZmQ3ZTk1OSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.jtmaiSVkcIti4esjlB29N3h27yivWA5OQgVX0WW8-qM', '2025-06-25 02:23:19.748034', '2025-06-26 02:23:19.000000', ************, '7d0193acd40d4cd3b1e453b11fd7e959');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (432, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDkwNDU5OSwiaWF0IjoxNzUwODE4MTk5LCJqdGkiOiJjOWY2YTJhZWM1M2U0YmRiYmU5NjM0YzUzOWYxZjUwYSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.lbk7XXETuepdJsgp3-aSQEmQT7xRBXbg0D1LmDFnmGQ', '2025-06-25 02:23:19.807093', '2025-06-26 02:23:19.000000', ************, 'c9f6a2aec53e4bdbbe9634c539f1f50a');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (433, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDkxNTAxMSwiaWF0IjoxNzUwODI4NjExLCJqdGkiOiIzNTI3MjEyNzNjNzI0ZTQyYWFlOGYwZThkYmM3NjhhNiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.KV-k6guliGxEm0mHq-tdWd5Xlctgy-lVY24W91qDVTE', '2025-06-25 05:16:51.269251', '2025-06-26 05:16:51.000000', ************, '352721273c724e42aae8f0e8dbc768a6');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (434, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDkxNTAxMSwiaWF0IjoxNzUwODI4NjExLCJqdGkiOiJjMTRlYjdjOWVmY2I0ZGJjYTZmZDMxMTgwNWQ4MmY5NyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.98FzB-ZFthCKoTPLzPknhdvKbfCunas4RtKUaGvDxg4', '2025-06-25 05:16:51.274720', '2025-06-26 05:16:51.000000', ************, 'c14eb7c9efcb4dbca6fd311805d82f97');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (435, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDkyMDY3MSwiaWF0IjoxNzUwODM0MjcxLCJqdGkiOiJmYzY4MTM3MDg2YzE0MmI3ODUxZDZhN2ZhMjhmYmE4ZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.IyBfqEMcTcWYMZVvRbXM9gp999O2S16JKIBGqX3I6Ng', '2025-06-25 06:51:11.663895', '2025-06-26 06:51:11.000000', ************, 'fc68137086c142b7851d6a7fa28fba8d');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (436, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDkyMDY3MSwiaWF0IjoxNzUwODM0MjcxLCJqdGkiOiI3OTZhMWYxMDNmNDM0ZmRkOTgzYjM5NTRiYTdkNWJlMiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.9EB4A9fGBa9fKLCdgrFQ1LLxm0VFN-nocR0fzsd8sCY', '2025-06-25 06:51:11.678305', '2025-06-26 06:51:11.000000', ************, '796a1f103f434fdd983b3954ba7d5be2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (437, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDk0MjkzNCwiaWF0IjoxNzUwODU2NTM0LCJqdGkiOiIzNDhlNGY5OGQyNDk0ZmEyYmUyZTI1ZDVkNjYyZmJkNSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.DC_4IxU7DAud-jpjNdceeBqHj4t27SQbLAVFUkEK8qU', '2025-06-25 13:02:14.575205', '2025-06-26 13:02:14.000000', ************, '348e4f98d2494fa2be2e25d5d662fbd5');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (438, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MDk0MjkzNCwiaWF0IjoxNzUwODU2NTM0LCJqdGkiOiJkNjM4ZGMwOTc1YjA0NWI4YjU0YmRjY2I1NDUyOTQxMyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.CoElkAt3lEcOM1VEfDsfjQlNrwknA1i6rFEnPqET9wc', '2025-06-25 13:02:14.583713', '2025-06-26 13:02:14.000000', ************, 'd638dc0975b045b8b54bdccb54529413');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (439, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTM3NDgwNSwiaWF0IjoxNzUxMjg4NDA1LCJqdGkiOiJlMzllZjRkOGRiMWE0NWU4YjQ5OWYwMmIyZDFlOWM4OCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.zenD8SpPqPdA1rwv6v8wG2kaFc5c8pGxqaw6S8lttFo', '2025-06-30 13:00:05.797773', '2025-07-01 13:00:05.000000', ************, 'e39ef4d8db1a45e8b499f02b2d1e9c88');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (440, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTM3NDgwNSwiaWF0IjoxNzUxMjg4NDA1LCJqdGkiOiJkNjk3N2QxN2M0MDA0ODIyYjQyYjdhNzNkMWE2MWQ1NCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.ERKl_N3LmxRB8JMjq4hwR3VqCWxy2uldoGEk25_DZwo', '2025-06-30 13:00:05.827385', '2025-07-01 13:00:05.000000', ************, 'd6977d17c4004822b42b7a73d1a61d54');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (441, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTQyMTg3MiwiaWF0IjoxNzUxMzM1NDcyLCJqdGkiOiJmZGVjNGM3NmE4NmM0MWE3YTc3ZGIyNjA0NTE5ODMyZSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.KnnoDK1AfFGFRAKN-O5kOtlF8EN8w5jQZ8TGwHwPV0w', '2025-07-01 02:04:32.905465', '2025-07-02 02:04:32.000000', ************, 'fdec4c76a86c41a7a77db2604519832e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (442, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTQyMTg3MiwiaWF0IjoxNzUxMzM1NDcyLCJqdGkiOiI5ZGNlOTg3ZmUyNmE0ZDFlODJjNmJjZDIzOWYzOWU3YyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.6rdW_t5NdUsbxFi_XfzFHifEagrd8qU2wWC541ySW98', '2025-07-01 02:04:32.941062', '2025-07-02 02:04:32.000000', ************, '9dce987fe26a4d1e82c6bcd239f39e7c');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (443, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTQyMjA0MiwiaWF0IjoxNzUxMzM1NjQyLCJqdGkiOiI2YmNiNTk5NDJhMmY0OTA4YTliMjI2ZDFjOTkzMmMxNSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.Nil35l2BHQuKOh1B1EISZn0kEh6OVaSG_D168Osb8YE', '2025-07-01 02:07:22.260778', '2025-07-02 02:07:22.000000', ************, '6bcb59942a2f4908a9b226d1c9932c15');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (444, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTQyMjA0MiwiaWF0IjoxNzUxMzM1NjQyLCJqdGkiOiI5ZDFjNDZkODIxZDc0ZGZhYTQ5NGIxZjg0NTg1NzM2MiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.faZWfepj2uk9wLfQ--ox_dNmYie73bSxT9AJoSqBrUs', '2025-07-01 02:07:22.268580', '2025-07-02 02:07:22.000000', ************, '9d1c46d821d74dfaa494b1f845857362');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (445, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTQ0ODIyNiwiaWF0IjoxNzUxMzYxODI2LCJqdGkiOiJkNDIxOWQwNTFlOGY0YWEwYjMxNzMzMWQxZjZlMGY0MCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.h5EznwHU4rJwUj-zN-NNxLQL6Ufr3dBWfnS7QJyGFmA', '2025-07-01 09:23:46.151386', '2025-07-02 09:23:46.000000', ************, 'd4219d051e8f4aa0b317331d1f6e0f40');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (446, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTQ0ODIyNiwiaWF0IjoxNzUxMzYxODI2LCJqdGkiOiI1ODllYjI2ODJlMDY0ZjliOWQ1MTRkODI2N2UyNzQwNCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.TUWLE7Bzw9_biN_jeUGVjnL6Ap_AfxajSL7HFh6DRek', '2025-07-01 09:23:46.210425', '2025-07-02 09:23:46.000000', ************, '589eb2682e064f9b9d514d8267e27404');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (447, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTUyMzE4MywiaWF0IjoxNzUxNDM2NzgzLCJqdGkiOiJhYzllN2ZmMDJjZGQ0NTQ5Yjc4M2Q2MGY3ZWM4YzVkYiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.nbZNyQcM-OmQvWWMbe9q5MFVQNCGyoDtsg5ILfR6K3U', '2025-07-02 06:13:03.688556', '2025-07-03 06:13:03.000000', ************, 'ac9e7ff02cdd4549b783d60f7ec8c5db');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (448, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTUyMzE4MywiaWF0IjoxNzUxNDM2NzgzLCJqdGkiOiJkYTljMzVmYzRjMTg0NTgzYWM0MTc1NDFiYzc0ZmIzMiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.xenukt9ETinNoSyvR5yWGEUGLG3pZmqZDiOAx8jhD3E', '2025-07-02 06:13:03.745371', '2025-07-03 06:13:03.000000', ************, 'da9c35fc4c184583ac417541bc74fb32');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (449, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTUyNjQwMiwiaWF0IjoxNzUxNDQwMDAyLCJqdGkiOiI1MmQ5ZmJmNTQzY2I0ODVlODliNGIwNTFlMjYxYmY2ZiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.RM5XWXJl7BMiGx4_xzRgG0y0ai6adryZRilNBwAtglY', '2025-07-02 07:06:42.063456', '2025-07-03 07:06:42.000000', ************, '52d9fbf543cb485e89b4b051e261bf6f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (450, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTUyNjQwMiwiaWF0IjoxNzUxNDQwMDAyLCJqdGkiOiJmMDk4Nzk2Y2NiMGI0ZTZiYWUyMzE0NzVjZTRhZmU2ZSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.gSdxnJlRE2HgDIKXkPGj4FCFCxVohKZ3JJYTDBQMcXg', '2025-07-02 07:06:42.117278', '2025-07-03 07:06:42.000000', ************, 'f098796ccb0b4e6bae231475ce4afe6e');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (451, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTU0MTY5MywiaWF0IjoxNzUxNDU1MjkzLCJqdGkiOiIzN2Q0Nzc1ZmNkNGQ0ZDM0OTBkZTMwMmRmNzg3YzlkMSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.EBZV8Xse62eRe9V0-_SRuIKLdv5q8QZ9vsL7r8qeoxY', '2025-07-02 11:21:33.948805', '2025-07-03 11:21:33.000000', 541150219354505, '37d4775fcd4d4d3490de302df787c9d1');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (452, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTU0MTY5MywiaWF0IjoxNzUxNDU1MjkzLCJqdGkiOiJmNjZmMTZjOWUxOGM0NTI4YmU4NGMxNWE4MGNjNmM0OSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.mUT9Rxtng0MH2qhk-HxjjnvWSziLQ7Q8lbZ9Ow6IIzM', '2025-07-02 11:21:33.954885', '2025-07-03 11:21:33.000000', 541150219354505, 'f66f16c9e18c4528be84c15a80cc6c49');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (453, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTU0MTc3MSwiaWF0IjoxNzUxNDU1MzcxLCJqdGkiOiIxZDljMjdhZjNlMDM0ZGRhOGYxMmMzNGIwNDFhNThhMiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.yOViJpaaE4n7yc64Tw2tDJ7JMjfTGbQ4jWuQB6nbhGI', '2025-07-02 11:22:51.757892', '2025-07-03 11:22:51.000000', ************, '1d9c27af3e034dda8f12c34b041a58a2');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (454, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTU0MTc3MSwiaWF0IjoxNzUxNDU1MzcxLCJqdGkiOiIzOWVhMDdiM2Q3NTk0YTkxYTY2N2U4MmUxMmNiMjlmMCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.JSQwWFd-xarnErsgv1LQOattEn6pH5C9PvmjQSyjF_o', '2025-07-02 11:22:51.765968', '2025-07-03 11:22:51.000000', ************, '39ea07b3d7594a91a667e82e12cb29f0');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (455, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTYxMzU0NiwiaWF0IjoxNzUxNTI3MTQ2LCJqdGkiOiI2N2EyYzRjNTFkMDc0YmFkOGZkY2I3ZjM1ZjcwMjU2ZiIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.tAuA3EA-EtWEpF8OFsr6ODO_C4NWAUJ3A13v-6BUHGA', '2025-07-03 07:19:06.717314', '2025-07-04 07:19:06.000000', ************, '67a2c4c51d074bad8fdcb7f35f70256f');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (456, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTYxMzU0NiwiaWF0IjoxNzUxNTI3MTQ2LCJqdGkiOiIzNTU3NDUyNjczMGE0YjQ0YWM0OGQxOTBlYzMwNTBhZCIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0._GFrkeW17mrlG3DDrhbEYKYI3WvjHKcWlz8qOj7CqsM', '2025-07-03 07:19:06.755546', '2025-07-04 07:19:06.000000', ************, '35574526730a4b44ac48d190ec3050ad');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (457, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTYyNDE0OCwiaWF0IjoxNzUxNTM3NzQ4LCJqdGkiOiI1NDhjMTE4Mzk0MDQ0MGI2OWQzNzk2MjQzYjIxMGQyNyIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.cqgPUTjFvIG2yc3KYMP57yKvm2py4pQoSuMTFtcAAPQ', '2025-07-03 10:15:48.509222', '2025-07-04 10:15:48.000000', ************, '548c1183940440b69d3796243b210d27');
INSERT INTO `token_blacklist_outstandingtoken` VALUES (458, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTYyNDE0OCwiaWF0IjoxNzUxNTM3NzQ4LCJqdGkiOiIxODUzNGYyYzkwNjk0MTEwOTA3YTUzYTRiYjc4YmQyYSIsInVzZXJfaWQiOjIxMzcyNzg1NjgzMn0.4-T8p17sEez6Y9I6tvrGV_uCZkx6ch1CE-12QFT-xCY', '2025-07-03 10:15:48.518392', '2025-07-04 10:15:48.000000', ************, '18534f2c90694110907a53a4bb78bd2a');

SET FOREIGN_KEY_CHECKS = 1;
