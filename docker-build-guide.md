# Docker 构建指南

## 基础镜像与应用镜像分离说明

本项目采用基础镜像与应用镜像分离的方式进行构建，这样可以加快开发过程中的镜像构建速度。

### 镜像结构

1. **基础镜像**：
   - `ai-django-base`: 包含 Python 环境和所有依赖
   - `ai-web-base`: 包含 Node 环境和前端依赖

2. **应用镜像**：
   - `ai-admin`: 基于 `ai-django-base` 构建，包含 Django 应用代码
   - `ai-web`: 基于 `ai-web-base` 构建前端代码，并使用 Nginx 提供服务

### 构建流程

```bash
docker-compose -f docker-compose-build.yml build
docker-compose -f docker-compose-dev.yml up --build -d
docker-compose -f docker-compose-prod.yml up --build -d
```

### 注意事项

1. 当项目依赖发生变化时（如 requirements.txt 或 package.json 更新），需要重新构建基础镜像
2. 基础镜像构建完成后，后续只修改应用代码时，只需重新构建应用镜像即可，大大提高构建速度
3. 基础镜像包含了所有依赖，因此应用镜像构建速度非常快 


2、导入所有镜像： docker load -i all-images.tar

3、一键部署：docker-compose up -d
停止：docker-compose down


常见命令：
删除所有镜像： docker rmi $( docker images -q)
删除没有标签的镜像
docker rmi $( docker images -f "dangling=true" -q)
导出所有镜像： docker images --format "{{.Repository}}:{{.Tag}}" | xargs docker save > all-images.tar
docker save -o images_base.tar ai-django-base:1.2 ai-web-base:1.2
列出docker镜像： docker images
列出docker容器： docker ps -a
删除镜像： docker rmi <镜像id>
查看镜像运行日志： docker logs <镜像id>
停止所有容器
docker stop $( docker ps -q)
删除所有容器
docker rm $( docker ps -aq)


常见问题：
后端连不上mysql服务
将docker_env文件夹下的文件赋权： chmod -R 777 ./docker_env