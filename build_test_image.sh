#!/bin/bash

# YOLO推理服务镜像构建和测试脚本
# 使用方法: ./build_test_image.sh [build|test|run|stop]

set -e

# 配置变量
IMAGE_NAME="yolo-inference"
TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"
CONTAINER_NAME="yolo-test-container"
TEST_PORT="8080"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        exit 1
    fi
}

# 构建镜像
build_image() {
    log_info "开始构建YOLO推理服务镜像..."
    
    TEMPLATE_PATH="backend/app_model_deploy/docker_templates/yolo_inference"
    
    if [ ! -d "$TEMPLATE_PATH" ]; then
        log_error "模板路径不存在: $TEMPLATE_PATH"
        exit 1
    fi
    
    log_info "使用模板路径: $TEMPLATE_PATH"
    log_info "构建镜像: $FULL_IMAGE_NAME"
    
    # 构建镜像
    docker build -t "$FULL_IMAGE_NAME" "$TEMPLATE_PATH"
    
    if [ $? -eq 0 ]; then
        log_info "镜像构建成功!"
        
        # 显示镜像信息
        log_info "镜像信息:"
        docker images "$FULL_IMAGE_NAME"
    else
        log_error "镜像构建失败!"
        exit 1
    fi
}

# 测试镜像
test_image() {
    log_info "测试YOLO推理服务镜像..."
    
    # 检查镜像是否存在
    if ! docker images "$FULL_IMAGE_NAME" | grep -q "$IMAGE_NAME"; then
        log_error "镜像不存在: $FULL_IMAGE_NAME"
        log_info "请先运行: $0 build"
        exit 1
    fi
    
    # 停止已存在的测试容器
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "停止已存在的测试容器..."
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
    fi
    
    # 创建测试权重目录
    TEST_WEIGHTS_DIR="/tmp/yolo_test_weights"
    mkdir -p "$TEST_WEIGHTS_DIR"
    
    log_info "启动测试容器..."
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "$TEST_PORT:8080" \
        -v "$TEST_WEIGHTS_DIR:/app/weights:ro" \
        -e MODEL_NAME="yolo11n" \
        -e MODEL_VERSION="test" \
        -e SERVICE_PORT="8080" \
        -e WEIGHTS_PATH="/app/weights" \
        "$FULL_IMAGE_NAME"
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查容器状态
    if docker ps | grep -q "$CONTAINER_NAME"; then
        log_info "✓ 容器启动成功"
        
        # 测试健康检查
        log_info "测试健康检查接口..."
        if curl -s "http://localhost:$TEST_PORT/health" > /dev/null; then
            log_info "✓ 健康检查通过"
            
            # 显示服务信息
            log_info "服务信息:"
            curl -s "http://localhost:$TEST_PORT/health" | python3 -m json.tool 2>/dev/null || echo "JSON格式化失败"
        else
            log_warn "✗ 健康检查失败"
        fi
        
        # 显示容器日志
        log_info "容器日志 (最后10行):"
        docker logs --tail 10 "$CONTAINER_NAME"
        
        log_info "测试完成!"
        log_info "访问地址: http://localhost:$TEST_PORT"
        log_info "健康检查: http://localhost:$TEST_PORT/health"
        log_info "停止测试: $0 stop"
        
    else
        log_error "✗ 容器启动失败"
        log_info "容器日志:"
        docker logs "$CONTAINER_NAME"
        exit 1
    fi
}

# 运行容器
run_container() {
    log_info "运行YOLO推理服务容器..."
    
    # 检查镜像是否存在
    if ! docker images "$FULL_IMAGE_NAME" | grep -q "$IMAGE_NAME"; then
        log_error "镜像不存在: $FULL_IMAGE_NAME"
        log_info "请先运行: $0 build"
        exit 1
    fi
    
    # 停止已存在的容器
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "停止已存在的容器..."
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
    fi
    
    # 创建权重目录
    WEIGHTS_DIR="/tmp/yolo_weights"
    mkdir -p "$WEIGHTS_DIR"
    
    log_info "启动容器: $CONTAINER_NAME"
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "$TEST_PORT:8080" \
        -v "$WEIGHTS_DIR:/app/weights:ro" \
        -e MODEL_NAME="yolo11n" \
        -e MODEL_VERSION="v1.0" \
        -e SERVICE_PORT="8080" \
        -e WEIGHTS_PATH="/app/weights" \
        --restart unless-stopped \
        "$FULL_IMAGE_NAME"
    
    log_info "容器启动成功!"
    log_info "访问地址: http://localhost:$TEST_PORT"
}

# 停止容器
stop_container() {
    log_info "停止测试容器..."
    
    if docker ps | grep -q "$CONTAINER_NAME"; then
        docker stop "$CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
        log_info "容器已停止并删除"
    else
        log_warn "容器未运行"
    fi
}

# 显示帮助
show_help() {
    echo "YOLO推理服务镜像构建和测试工具"
    echo ""
    echo "使用方法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  build  - 构建Docker镜像"
    echo "  test   - 构建并测试镜像"
    echo "  run    - 运行容器"
    echo "  stop   - 停止容器"
    echo "  help   - 显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 build    # 构建镜像"
    echo "  $0 test     # 测试镜像"
    echo "  $0 run      # 运行容器"
    echo "  $0 stop     # 停止容器"
}

# 主函数
main() {
    check_docker
    
    case "$1" in
        "build")
            build_image
            ;;
        "test")
            build_image
            test_image
            ;;
        "run")
            run_container
            ;;
        "stop")
            stop_container
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
