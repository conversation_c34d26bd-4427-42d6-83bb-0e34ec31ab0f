# 算法库管理系统详细设计文档

## 1. 系统概述

### 1.1 项目背景
算法库管理系统是一个基于Django + Vue3的企业级算法资源管理平台，旨在为算法开发团队提供统一的算法库存储、管理、分享和协作环境。系统支持算法库的全生命周期管理，包括创建、版本控制、部署、监控等功能。

### 1.2 核心功能
- **算法库管理**：创建、编辑、删除算法库，支持版本管理
- **文档管理**：基于Markdown的文档编写和预览，支持实时协作
- **代码管理**：代码文件上传、下载、在线查看，支持语法高亮
- **分类管理**：多级分类体系，支持通用/专用算法库分类

### 1.3 技术架构
- **后端**：Django 4.2.1 + Django REST Framework
- **前端**：Vue 3 + TypeScript + Element Plus + Vite
- **数据库**：MySQL 8.0+
- **文件存储**：MinIO对象存储
- **缓存**：Redis 6.2+
- **任务队列**：Celery + Redis
- **开发环境**：Conda dva环境

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 Vue3     │    │   后端 Django   │    │   MinIO存储     │
│                 │    │                 │    │                 │
│ - 算法库展示    │◄──►│ - REST API      │◄──►│ - 文档文件      │
│ - 算法库维护    │    │ - 权限控制      │    │ - 代码文件      │
│ - 文档编辑      │    │ - 业务逻辑      │    │ - 资源文件      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据库        │
                    │                 │
                    │ - 算法库元数据  │
                    │ - 分类信息      │
                    └─────────────────┘
```

### 2.2 数据流设计
1. **用户请求** → 前端Vue组件
2. **API调用** → 后端Django视图
3. **业务处理** → 数据库操作 + MinIO文件操作
4. **响应返回** → 前端渲染展示

### 2.3 模块划分
- **核心模块**：算法库管理、文档管理
- **基础模块**：用户管理、权限控制
- **扩展模块**：搜索、统计、日志

## 3. 数据库设计

### 3.1 核心数据模型

#### 3.1.1 算法库分类表 (AlgorithmCategory)
```sql
CREATE TABLE algorithm_category (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,           -- 分类名称
    code VARCHAR(100) NOT NULL,           -- 分类编码
    parent_id BIGINT,                     -- 父级分类ID
    order_num INTEGER DEFAULT 1,         -- 排序
    is_active BOOLEAN DEFAULT TRUE,      -- 是否激活
    create_datetime TIMESTAMP,           -- 创建时间
    update_datetime TIMESTAMP            -- 更新时间
);
```

#### 3.1.2 算法库主表 (AIAlgorithm)
```sql
CREATE TABLE ai_algorithm (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,          -- 算法库名称
    `group` VARCHAR(100) DEFAULT 'qianlan', -- 项目组 (group是MySQL关键字，需要反引号)
    description TEXT,                    -- 描述
    type VARCHAR(20) DEFAULT 'general',  -- 类型(general/specialized)
    algorithm_type VARCHAR(10) DEFAULT 'file', -- 实现类型(file)
    minio_path VARCHAR(255),             -- MinIO存储路径
    stars INTEGER DEFAULT 0,            -- 收藏数
    downloads INTEGER DEFAULT 0,        -- 下载次数
    status VARCHAR(20) DEFAULT 'online', -- 状态(online/offline)
    creator_id BIGINT,                   -- 创建者ID
    create_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    is_delete BOOLEAN DEFAULT FALSE,     -- 软删除标记
    FOREIGN KEY (creator_id) REFERENCES users(id) -- 外键约束
);
```

#### 3.1.3 算法库分类关联表
```sql
CREATE TABLE algorithm_category_relation (
    algorithm_id BIGINT,
    category_id BIGINT,
    PRIMARY KEY (algorithm_id, category_id)
);
```

### 3.2 文件存储设计

#### 3.2.1 MinIO存储结构
```
models/
└── algorithms/
    └── {group}/              # 项目组
        └── {name}/           # 算法库名称
            ├── README.md     # 主文档
            └── code/         # 代码目录
                ├── main.py
                ├── config.json
                ├── requirements.txt
                └── ...
```

#### 3.2.2 文件路径规范
- **算法库路径**：`algorithms/{group}/{name}/`
- **文档路径**：`algorithms/{group}/{name}/README.md`
- **代码路径**：`algorithms/{group}/{name}/code/{filename}`

## 4. API接口设计

### 4.1 RESTful API规范

#### 4.1.1 算法库管理接口
```
GET    /algorithm/algorithms/                    # 获取算法库列表
POST   /algorithm/algorithms/                    # 创建算法库
GET    /algorithm/algorithms/{id}/               # 获取算法库详情
PUT    /algorithm/algorithms/{id}/               # 更新算法库
DELETE /algorithm/algorithms/{id}/               # 删除算法库
POST   /algorithm/algorithms/{id}/star/          # 收藏算法库
POST   /algorithm/algorithms/{id}/download/      # 下载算法库
GET    /algorithm/algorithms/{id}/file_url/      # 获取文件URL
```

#### 4.1.2 文档管理接口
```
GET    /algorithm/algorithms/{id}/readme/        # 获取README内容
POST   /algorithm/algorithms/{id}/update_readme/ # 更新README内容
GET    /algorithm/algorithms/{id}/file_tree/     # 获取文件树
GET    /algorithm/algorithms/{id}/main_doc/      # 获取主文档
```

#### 4.1.3 文件管理接口
```
GET    /algorithm/algorithms/{id}/download_file/ # 下载文件
POST   /algorithm/algorithms/get_upload_url/     # 获取预签名上传URL
POST   /algorithm/algorithms/{id}/upload_file/   # 上传文件
POST   /algorithm/algorithms/register_docker/    # 注册Docker镜像
```

#### 4.1.4 分类管理接口
```
GET    /algorithm/categories/                    # 获取分类列表
POST   /algorithm/categories/                    # 创建分类
PUT    /algorithm/categories/{id}/               # 更新分类
DELETE /algorithm/categories/{id}/               # 删除分类
```

### 4.2 请求响应格式

#### 4.2.1 标准响应格式
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据
    },
    "timestamp": "2025-08-05T21:00:00Z"
}
```

#### 4.2.2 分页响应格式
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "count": 100,
        "next": "http://api/algorithms/?page=2",
        "previous": null,
        "results": [
            // 算法库列表
        ]
    }
}
```

## 5. 前端设计

### 5.1 页面结构设计

#### 5.1.1 算法库展示页面
```
┌─────────────────────────────────────────────────────────────┐
│                        页面标题                              │
├─────────────┬───────────────────────────────────────────────┤
│             │                                               │
│   分类树    │              算法库列表                        │
│   (280px)   │            (卡片布局)                         │
│             │                                               │
│  - 通用算法  │  ┌─────┐ ┌─────┐ ┌─────┐                     │
│  - 专用算法  │  │算法1│ │算法2│ │算法3│                     │
│  - 分类A    │  └─────┘ └─────┘ └─────┘                     │
│  - 分类B    │                                               │
│             │  ┌─────┐ ┌─────┐ ┌─────┐                     │
│             │  │算法4│ │算法5│ │算法6│                     │
│             │  └─────┘ └─────┘ └─────┘                     │
└─────────────┴───────────────────────────────────────────────┘
```

#### 5.1.2 算法库详情页面
```
┌─────────────────────────────────────────────────────────────┐
│  返回 | 算法库名称                    收藏  下载             │
├─────────────┬───────────────────────────────────────────────┤
│             │  ┌─────────────────────────────────────────┐  │
│   文件树    │  │              编辑 | 预览                │  │
│   (280px)   │  ├─────────────────────────────────────────┤  │
│             │  │                                         │  │
│ □ README.md │  │                                         │  │
│ □ code/     │  │           Markdown内容                  │  │
│   - main.py │  │                                         │  │
│   - config  │  │                                         │  │
│             │  │                                         │  │
│             │  └─────────────────────────────────────────┘  │
└─────────────┴───────────────────────────────────────────────┘
```

#### 5.1.3 算法库维护页面
```
┌─────────────────────────────────────────────────────────────┐
│  算法库维护                                    + 新建算法库   │
├─────────────────────────────────────────────────────────────┤
│  搜索框 | 项目组筛选 | 状态筛选                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   算法库1   │ │   算法库2   │ │   算法库3   │           │
│  │   qianlan   │ │   apple     │ │   qianlan   │           │
│  │   已上架    │ │   已下架    │ │   已上架    │           │
│  │   ⋮ 更多    │ │   ⋮ 更多    │ │   ⋮ 更多    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 组件设计

#### 5.2.1 核心组件
- **AlgorithmCard**：算法库卡片组件
- **FileTree**：文件树组件
- **MarkdownEditor**：Markdown编辑器组件
- **CategoryTree**：分类树组件
- **UploadDialog**：文件上传对话框

#### 5.2.2 组件通信
- **Props**：父子组件数据传递
- **Events**：子组件向父组件通信
- **Pinia**：全局状态管理
- **Provide/Inject**：跨层级组件通信

### 5.3 状态管理

#### 5.3.1 全局状态
```typescript
interface GlobalState {
  user: UserInfo;           // 用户信息
  categories: Category[];   // 分类列表
  algorithms: Algorithm[];  // 算法库列表
  currentAlgorithm: Algorithm | null; // 当前选中算法库
}
```

#### 5.3.2 页面状态
```typescript
interface PageState {
  loading: boolean;         // 加载状态
  searchKeyword: string;    // 搜索关键词
  selectedCategory: Category | null; // 选中分类
  fileTree: FileNode[];     // 文件树
  documentContent: string;  // 文档内容
}
```

## 6. 核心功能实现

### 6.1 算法库管理

#### 6.1.1 创建算法库
1. **前端表单验证**：名称、项目组、描述等必填项
2. **后端数据验证**：重复性检查、权限验证
3. **MinIO路径创建**：自动生成存储路径
4. **初始化文件**：创建空的README.md文件

#### 6.1.2 算法库列表
1. **分页查询**：支持分页、排序、筛选
2. **搜索功能**：名称、描述全文搜索
3. **分类筛选**：按分类、项目组、状态筛选
4. **卡片展示**：美观的卡片布局

### 6.2 文档管理

#### 6.2.1 Markdown编辑
1. **实时预览**：编辑和预览模式切换
2. **语法高亮**：代码块语法高亮
3. **自动保存**：定时保存草稿
4. **版本控制**：文档修改历史

#### 6.2.2 文档渲染
1. **Markdown解析**：支持标准Markdown语法
2. **代码高亮**：多语言代码高亮
3. **数学公式**：LaTeX数学公式支持
4. **图片显示**：支持图片嵌入

### 6.3 代码管理

#### 6.3.1 文件上传
1. **拖拽上传**：支持拖拽文件上传
2. **批量上传**：多文件同时上传
3. **进度显示**：上传进度实时显示
4. **类型限制**：文件类型和大小限制

#### 6.3.2 代码查看
1. **语法高亮**：根据文件扩展名自动识别
2. **行号显示**：代码行号显示
3. **代码折叠**：支持代码块折叠
4. **搜索功能**：代码内容搜索

## 7. 性能优化

### 7.1 前端优化
- **懒加载**：路由和组件懒加载
- **虚拟滚动**：大列表虚拟滚动
- **缓存策略**：API响应缓存
- **代码分割**：按需加载代码

### 7.2 后端优化
- **数据库索引**：关键字段建立索引
- **查询优化**：减少N+1查询问题
- **缓存机制**：Redis缓存热点数据
- **分页查询**：大数据集分页处理

### 7.3 存储优化
- **文件压缩**：文档和代码文件压缩
- **缓存策略**：MinIO访问缓存
- **预签名URL**：减少服务器中转

## 8. 环境配置

### 8.1 开发环境配置

#### 8.1.1 Conda环境配置
```bash
# 激活dva环境
conda activate dva

# 安装Python依赖
cd backend
pip install -r requirements.txt

# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 初始化数据
python manage.py loaddata db/db518.json

# 下载BGE模型
python manage.py download_bge_model

# 重建向量索引
python manage.py rebuild_vector_index
```

#### 8.1.2 前端环境配置
```bash
cd web
# 安装依赖
pnpm install

# 开发模式启动
pnpm run dev
```

### 8.2 生产环境部署

#### 8.2.1 Docker部署
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f django
```

#### 8.2.2 环境变量配置
- **开发环境**: `.env.development`
- **生产环境**: `.env.production`
- **虚拟机环境**: `.env.vmware.production`

## 9. 安全性设计

### 9.1 权限控制
- **RBAC模型**: 基于Casbin的角色权限控制
- **JWT认证**: 无状态身份验证
- **API权限**: 接口级权限控制
- **数据权限**: 多维度数据权限控制

### 9.2 数据安全
- **数据加密**: 敏感数据加密存储
- **访问日志**: 完整的操作日志记录
- **文件安全**: MinIO访问控制和预签名URL
- **SQL注入防护**: ORM查询防护

### 9.3 网络安全
- **CORS配置**: 跨域请求控制
- **HTTPS支持**: SSL/TLS加密传输
- **防火墙配置**: 端口访问控制
- **容器安全**: Docker容器安全配置

## 10. 监控与运维

### 10.1 系统监控
- **服务监控**: 服务状态实时监控
- **性能监控**: CPU、内存、磁盘使用率
- **容器监控**: Docker容器运行状态
- **日志监控**: 错误日志实时告警

### 10.2 备份策略
- **数据库备份**: 定时数据库备份
- **文件备份**: MinIO数据备份
- **配置备份**: 系统配置文件备份
- **恢复测试**: 定期恢复测试

## 11. 扩展性设计

### 11.1 水平扩展
- **负载均衡**: Nginx负载均衡配置
- **数据库集群**: MySQL主从复制
- **缓存集群**: Redis集群配置
- **存储扩展**: MinIO分布式存储

### 11.2 功能扩展
- **插件机制**: 算法库插件扩展
- **API扩展**: RESTful API版本控制
- **模型扩展**: 支持更多AI模型类型
- **部署扩展**: 支持Kubernetes部署

