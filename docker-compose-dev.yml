version: '3'

services:
  # 前端开发镜像
  web-dev:
    build:
      context: .
      dockerfile: docker_env/web/Dockerfile.dev
      args:
        BASE_IMAGE: ai-web-base:1.0
    image: ai-web-dev:1.0
    privileged: true
    ports:
      - "7789:7789"
    volumes:
      - ./web:/web
      - /web/node_modules  # 避免挂载 node_modules
    depends_on:
      - django
    network_mode: host

  # Django应用镜像
  django:
    build:
      context: .
      dockerfile: docker_env/django/Dockerfile
      args:
        BASE_IMAGE: ai-django-base:1.0
    image: ai-admin:1.0
    privileged: true
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/backend
    depends_on:
      - mysql
      - redis
    network_mode: host

  mysql:
    image: mysql:8.0
    privileged: true
    ports:
      - "3306:3306"
    environment:
      - MYSQL_DATABASE=aidata
      - MYSQL_ROOT_PASSWORD=root
    volumes:
      - ./docker_env/mysql/data:/var/lib/mysql
      - ./docker_env/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./docker_env/mysql/init_73.sql:/docker-entrypoint-initdb.d/init.sql  # 挂载初始化 SQL 脚本
    network_mode: host

  redis:
    image: redis:6.2-alpine
    privileged: true
    ports:
      - "6379:6379"
    volumes:
      - ./docker_env/redis/data:/data
      - ./docker_env/redis/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf
    network_mode: host

  minio:
    image: minio/minio:latest
    privileged: true
    ports:
      - "9000:9000"  # API端口
      - "9001:9001"  # 控制台端口
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - ./docker_env/minio/data:/data
    command: server /data --console-address ":9001"
    network_mode: host