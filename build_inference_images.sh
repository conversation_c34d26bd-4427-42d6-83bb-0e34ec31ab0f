#!/bin/bash

# AI推理服务镜像构建脚本
# 使用方法: ./build_inference_images.sh [base|yolo|all|clean]

set -e

# 配置变量
BASE_IMAGE_NAME="ai-inference-base"
BASE_IMAGE_TAG="latest"
YOLO_IMAGE_NAME="yolo-inference"
YOLO_IMAGE_TAG="latest"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        exit 1
    fi
}

# 构建基础镜像
build_base_image() {
    log_info "开始构建AI推理基础镜像..."
    
    BASE_PATH="backend/app_model_deploy/docker_templates/base_inference"
    
    if [ ! -d "$BASE_PATH" ]; then
        log_error "基础镜像模板路径不存在: $BASE_PATH"
        exit 1
    fi
    
    log_info "使用模板路径: $BASE_PATH"
    log_info "构建镜像: $BASE_IMAGE_NAME:$BASE_IMAGE_TAG"
    
    # 记录构建开始时间
    start_time=$(date +%s)
    
    # 构建镜像
    docker build -t "$BASE_IMAGE_NAME:$BASE_IMAGE_TAG" "$BASE_PATH"
    
    # 计算构建时间
    end_time=$(date +%s)
    build_time=$((end_time - start_time))
    
    if [ $? -eq 0 ]; then
        log_info "基础镜像构建成功! (耗时: ${build_time}秒)"
        
        # 显示镜像信息
        log_info "基础镜像信息:"
        docker images "$BASE_IMAGE_NAME:$BASE_IMAGE_TAG"
        
        # 显示镜像大小
        image_size=$(docker images "$BASE_IMAGE_NAME:$BASE_IMAGE_TAG" --format "{{.Size}}")
        log_info "镜像大小: $image_size"
        
    else
        log_error "基础镜像构建失败!"
        exit 1
    fi
}

# 构建YOLO推理镜像
build_yolo_image() {
    log_info "开始构建YOLO推理服务镜像..."
    
    # 检查基础镜像是否存在
    if ! docker images "$BASE_IMAGE_NAME:$BASE_IMAGE_TAG" | grep -q "$BASE_IMAGE_NAME"; then
        log_error "基础镜像不存在: $BASE_IMAGE_NAME:$BASE_IMAGE_TAG"
        log_info "请先运行: $0 base"
        exit 1
    fi
    
    YOLO_PATH="backend/app_model_deploy/docker_templates/yolo_inference"
    
    if [ ! -d "$YOLO_PATH" ]; then
        log_error "YOLO镜像模板路径不存在: $YOLO_PATH"
        exit 1
    fi
    
    log_info "使用模板路径: $YOLO_PATH"
    log_info "构建镜像: $YOLO_IMAGE_NAME:$YOLO_IMAGE_TAG"
    
    # 记录构建开始时间
    start_time=$(date +%s)
    
    # 构建镜像
    docker build -t "$YOLO_IMAGE_NAME:$YOLO_IMAGE_TAG" "$YOLO_PATH"
    
    # 计算构建时间
    end_time=$(date +%s)
    build_time=$((end_time - start_time))
    
    if [ $? -eq 0 ]; then
        log_info "YOLO推理镜像构建成功! (耗时: ${build_time}秒)"
        
        # 显示镜像信息
        log_info "YOLO推理镜像信息:"
        docker images "$YOLO_IMAGE_NAME:$YOLO_IMAGE_TAG"
        
        # 显示镜像大小
        image_size=$(docker images "$YOLO_IMAGE_NAME:$YOLO_IMAGE_TAG" --format "{{.Size}}")
        log_info "镜像大小: $image_size"
        
    else
        log_error "YOLO推理镜像构建失败!"
        exit 1
    fi
}

# 构建所有镜像
build_all_images() {
    log_info "开始构建所有推理服务镜像..."
    
    # 记录总构建开始时间
    total_start_time=$(date +%s)
    
    # 构建基础镜像
    build_base_image
    
    # 构建YOLO推理镜像
    build_yolo_image
    
    # 计算总构建时间
    total_end_time=$(date +%s)
    total_build_time=$((total_end_time - total_start_time))
    
    log_info "所有镜像构建完成! (总耗时: ${total_build_time}秒)"
    
    # 显示所有镜像
    log_info "构建的镜像列表:"
    docker images | grep -E "(ai-inference-base|yolo-inference)"
}

# 清理镜像
clean_images() {
    log_info "清理推理服务镜像..."
    
    # 停止相关容器
    log_info "停止相关容器..."
    docker ps -a | grep -E "(ai-inference-base|yolo-inference)" | awk '{print $1}' | xargs -r docker stop
    docker ps -a | grep -E "(ai-inference-base|yolo-inference)" | awk '{print $1}' | xargs -r docker rm
    
    # 删除镜像
    log_info "删除镜像..."
    docker images | grep -E "(ai-inference-base|yolo-inference)" | awk '{print $3}' | xargs -r docker rmi -f
    
    # 清理悬空镜像
    log_info "清理悬空镜像..."
    docker image prune -f
    
    log_info "镜像清理完成!"
}

# 显示镜像信息
show_images() {
    log_info "推理服务镜像列表:"
    
    echo ""
    echo "基础镜像:"
    docker images | grep "ai-inference-base" || echo "  未找到基础镜像"
    
    echo ""
    echo "YOLO推理镜像:"
    docker images | grep "yolo-inference" || echo "  未找到YOLO推理镜像"
    
    echo ""
    echo "镜像层级关系:"
    echo "  ai-inference-base:latest (基础镜像)"
    echo "  └── yolo-inference:latest (YOLO推理服务)"
}

# 测试镜像
test_images() {
    log_info "测试推理服务镜像..."
    
    # 测试基础镜像
    if docker images "$BASE_IMAGE_NAME:$BASE_IMAGE_TAG" | grep -q "$BASE_IMAGE_NAME"; then
        log_info "✓ 基础镜像存在"
    else
        log_warn "✗ 基础镜像不存在"
    fi
    
    # 测试YOLO推理镜像
    if docker images "$YOLO_IMAGE_NAME:$YOLO_IMAGE_TAG" | grep -q "$YOLO_IMAGE_NAME"; then
        log_info "✓ YOLO推理镜像存在"
        
        # 快速测试YOLO镜像
        log_info "快速测试YOLO镜像..."
        docker run --rm "$YOLO_IMAGE_NAME:$YOLO_IMAGE_TAG" python -c "
import torch
import cv2
from ultralytics import YOLO
print('✓ 基础库导入成功')
print(f'✓ PyTorch版本: {torch.__version__}')
print(f'✓ OpenCV版本: {cv2.__version__}')
print('✓ YOLO镜像测试通过')
"
    else
        log_warn "✗ YOLO推理镜像不存在"
    fi
}

# 显示帮助
show_help() {
    echo "AI推理服务镜像构建工具"
    echo ""
    echo "使用方法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  base   - 构建基础镜像 (ai-inference-base)"
    echo "  yolo   - 构建YOLO推理镜像 (yolo-inference)"
    echo "  all    - 构建所有镜像"
    echo "  clean  - 清理所有推理镜像"
    echo "  list   - 显示镜像信息"
    echo "  test   - 测试镜像"
    echo "  help   - 显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 base     # 构建基础镜像"
    echo "  $0 yolo     # 构建YOLO推理镜像"
    echo "  $0 all      # 构建所有镜像"
    echo "  $0 clean    # 清理镜像"
    echo ""
    echo "镜像层级:"
    echo "  ai-inference-base:latest (基础镜像，包含PyTorch、OpenCV等)"
    echo "  └── yolo-inference:latest (YOLO推理服务)"
}

# 主函数
main() {
    check_docker
    
    case "$1" in
        "base")
            build_base_image
            ;;
        "yolo")
            build_yolo_image
            ;;
        "all")
            build_all_images
            ;;
        "clean")
            clean_images
            ;;
        "list")
            show_images
            ;;
        "test")
            test_images
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
