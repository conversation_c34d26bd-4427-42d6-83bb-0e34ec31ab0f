#!/bin/bash

# 模型部署系统部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|build|logs|status]

set -e

# 配置变量
COMPOSE_FILE="docker-compose.deploy.yml"
PROJECT_NAME="django-vue-admin"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    log_info "系统要求检查通过"
}

# 创建必要的目录和文件
setup_environment() {
    log_info "设置环境..."
    
    # 创建SSL证书目录
    mkdir -p nginx/ssl
    
    # 生成自签名SSL证书（生产环境请使用正式证书）
    if [ ! -f nginx/ssl/server.crt ]; then
        log_info "生成SSL证书..."
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/server.key \
            -out nginx/ssl/server.crt \
            -subj "/C=CN/ST=Beijing/L=Beijing/O=Company/CN=localhost"
    fi
    
    # 创建MySQL配置目录
    mkdir -p mysql/conf.d
    
    # 创建MySQL配置文件
    if [ ! -f mysql/conf.d/my.cnf ]; then
        cat > mysql/conf.d/my.cnf << EOF
[mysqld]
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
default-time-zone='+8:00'
max_connections=1000
max_allowed_packet=64M
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
EOF
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    log_info "环境设置完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker-compose -f $COMPOSE_FILE build web
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker-compose -f $COMPOSE_FILE build frontend
    
    # 构建推理服务镜像
    log_info "构建YOLO推理服务镜像..."
    cd backend
    python manage.py build_inference_image --image-name yolo-inference --tag latest
    cd ..
    
    log_info "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动基础服务
    log_info "启动基础服务（MySQL, Redis, MinIO）..."
    docker-compose -f $COMPOSE_FILE up -d mysql redis minio
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 运行数据库迁移
    log_info "运行数据库迁移..."
    docker-compose -f $COMPOSE_FILE run --rm web python manage.py migrate
    
    # 创建超级用户（如果不存在）
    log_info "创建管理员用户..."
    docker-compose -f $COMPOSE_FILE run --rm web python manage.py shell -c "
from app_user.models import Users
if not Users.objects.filter(username='admin').exists():
    Users.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('管理员用户创建成功: admin/admin123')
else:
    print('管理员用户已存在')
"
    
    # 启动所有服务
    log_info "启动所有服务..."
    docker-compose -f $COMPOSE_FILE up -d
    
    log_info "服务启动完成"
    show_status
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose -f $COMPOSE_FILE down
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    stop_services
    start_services
}

# 查看日志
show_logs() {
    if [ -n "$2" ]; then
        docker-compose -f $COMPOSE_FILE logs -f "$2"
    else
        docker-compose -f $COMPOSE_FILE logs -f
    fi
}

# 查看状态
show_status() {
    log_info "服务状态:"
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    log_info "访问地址:"
    echo "  前端界面: https://localhost"
    echo "  管理后台: https://localhost/admin"
    echo "  API文档: https://localhost/api/docs"
    echo "  MinIO控制台: http://localhost:9001"
    echo ""
    log_info "默认账号:"
    echo "  管理员: admin/admin123"
    echo "  MinIO: minioadmin/minioadmin"
}

# 清理数据
clean_data() {
    log_warn "这将删除所有数据，包括数据库、文件存储等"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理数据..."
        docker-compose -f $COMPOSE_FILE down -v
        docker system prune -f
        log_info "数据清理完成"
    else
        log_info "操作已取消"
    fi
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份数据库
    log_info "备份数据库..."
    docker-compose -f $COMPOSE_FILE exec -T mysql mysqldump -uroot -p123456 django_vue_admin > "$BACKUP_DIR/database.sql"
    
    # 备份MinIO数据
    log_info "备份MinIO数据..."
    docker-compose -f $COMPOSE_FILE exec -T minio tar czf - /data > "$BACKUP_DIR/minio_data.tar.gz"
    
    log_info "备份完成: $BACKUP_DIR"
}

# 主函数
main() {
    case "$1" in
        "start")
            check_requirements
            setup_environment
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "build")
            check_requirements
            setup_environment
            build_images
            ;;
        "logs")
            show_logs "$@"
            ;;
        "status")
            show_status
            ;;
        "clean")
            clean_data
            ;;
        "backup")
            backup_data
            ;;
        *)
            echo "使用方法: $0 {start|stop|restart|build|logs|status|clean|backup}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动所有服务"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  build   - 构建Docker镜像"
            echo "  logs    - 查看日志 (可指定服务名)"
            echo "  status  - 查看服务状态"
            echo "  clean   - 清理所有数据"
            echo "  backup  - 备份数据"
            echo ""
            echo "示例:"
            echo "  $0 start          # 启动服务"
            echo "  $0 logs web       # 查看web服务日志"
            echo "  $0 status         # 查看状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
