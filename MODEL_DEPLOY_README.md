# 模型部署系统使用指南

## 概述

本系统提供了完整的AI模型在线部署和推理功能，支持从模型管理到容器化部署、在线推理、性能监控的全流程管理。

## 功能特性

### 🚀 核心功能
- **模型部署管理**: 支持Docker容器化部署，自动拉取镜像和权重文件
- **在线推理服务**: 提供RESTful API接口，支持单张和批量图像推理
- **实时监控**: 容器资源监控、服务健康检查、调用统计
- **可视化管理**: Web界面管理部署、查看日志、性能图表

### 🛠 技术架构
- **后端**: Django + DRF + Celery + Docker API
- **前端**: Vue3 + Element Plus + ECharts
- **存储**: MinIO对象存储
- **容器**: Docker容器化部署
- **监控**: 实时性能指标收集

## 快速开始

### 1. 环境准备

确保系统已安装以下组件：
- Docker Engine
- Python 3.9+
- Node.js 16+
- MySQL 8.0+
- Redis
- MinIO

### 2. 后端配置

#### 安装依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 数据库迁移
```bash
python manage.py makemigrations app_model_deploy
python manage.py migrate
```

#### 启动Celery任务队列
```bash
# 启动worker
celery -A application worker -l info

# 启动定时任务
celery -A application beat -l info

# 启动容器监控
python manage.py start_monitor
```

### 3. 前端配置

```bash
cd web
npm install
npm run dev
```

### 4. 构建推理服务镜像

我们采用分层镜像构建策略，先构建包含基础依赖的基础镜像，然后基于基础镜像构建特定的推理服务镜像。

#### 镜像层级结构
```
ai-inference-base:latest (基础镜像)
├── Python 3.9
├── PyTorch 2.0.1
├── OpenCV 4.8.0
├── Flask 2.3.2
├── 其他基础AI库
└── yolo-inference:latest (YOLO推理服务)
    ├── Ultralytics YOLO
    ├── ONNX Runtime
    └── 推理服务代码
```

#### 方法一：使用便捷脚本构建（推荐）
```bash
# 构建所有镜像（基础镜像 + YOLO推理镜像）
./build_inference_images.sh all

# 只构建基础镜像
./build_inference_images.sh base

# 只构建YOLO推理镜像（需要先有基础镜像）
./build_inference_images.sh yolo

# 查看镜像列表
./build_inference_images.sh list

# 测试镜像
./build_inference_images.sh test

# 清理镜像
./build_inference_images.sh clean
```

#### 方法二：使用Django管理命令
```bash
# 构建所有镜像
python manage.py build_inference_images all

# 构建基础镜像
python manage.py build_inference_images base

# 构建YOLO推理镜像
python manage.py build_inference_images yolo --verbose

# 查看镜像列表
python manage.py build_inference_images list

# 测试镜像
python manage.py build_inference_images test
```

#### 方法三：手动构建
```bash
# 1. 构建基础镜像
cd backend/app_model_deploy/docker_templates/base_inference
docker build -t ai-inference-base:latest .

# 2. 构建YOLO推理镜像
cd ../yolo_inference
docker build -t yolo-inference:latest .
```

## 使用指南

### 模型部署流程

#### 1. 准备模型
- 确保模型版本包含Docker镜像信息
- 权重文件已上传到MinIO存储
- 模型状态为"测试通过"或"已上架"

#### 2. 创建部署
1. 进入"模型部署"页面
2. 点击"部署模型"按钮
3. 选择要部署的模型版本
4. 配置部署参数：
   - 部署名称（唯一标识）
   - 服务端口（可自动分配）
   - 资源限制（内存、CPU）
   - 环境变量
   - 自动重启选项

#### 3. 监控部署状态
- 查看部署列表中的状态
- 点击"详情"查看容器日志
- 监控服务健康状态

### 在线推理

#### 1. 选择服务
在"在线推理"页面选择健康的推理服务

#### 2. 配置参数
- 置信度阈值：检测结果的最小置信度
- IoU阈值：非极大值抑制的IoU阈值
- 最大检测数：单张图片最大检测目标数

#### 3. 上传图片
- 支持拖拽上传
- 单张推理：上传一张图片
- 批量推理：最多上传10张图片

#### 4. 查看结果
- 检测框信息
- 类别和置信度
- 推理时间统计

### API接口

#### 部署管理API

```bash
# 获取部署列表
GET /deploy/deployments/

# 创建部署
POST /deploy/deployments/deploy/
{
  "model_version_id": 1,
  "deployment_name": "yolo_v1_deploy",
  "service_port": 8080,
  "deploy_config": {
    "memory_limit": "2g",
    "cpu_limit": 1.0,
    "auto_restart": true
  }
}

# 停止部署
POST /deploy/deployments/{id}/stop/

# 重启部署
POST /deploy/deployments/{id}/restart/

# 删除部署
DELETE /deploy/deployments/{id}/remove/
```

#### 推理API

```bash
# 单张图片推理
POST /deploy/inference/predict/{service_id}/
Content-Type: multipart/form-data

image: [图片文件]
conf: 0.25
iou: 0.45
max_det: 1000

# 批量图片推理
POST /deploy/inference/batch_predict/{service_id}/
Content-Type: multipart/form-data

images: [图片文件1]
images: [图片文件2]
conf: 0.25
iou: 0.45
max_det: 1000

# 健康检查
GET /deploy/inference/health/{service_id}/
```

#### 监控API

```bash
# 获取服务统计
GET /deploy/services/{id}/stats/

# 获取性能图表数据
GET /deploy/metrics/chart_data/?service_id=1&hours=24

# 获取推理日志
GET /deploy/services/{id}/logs/?page=1&page_size=20
```

## 配置说明

### 环境变量配置

在`.env`文件中配置以下参数：

```bash
# MinIO配置
MINIO_ENDPOINT=127.0.0.1:9000
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin
MINIO_MODEL_BUCKET=models

# Docker配置
DOCKER_HOST=unix:///var/run/docker.sock

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

### 推理服务配置

推理服务支持以下环境变量：

```bash
MODEL_NAME=yolo11n          # 模型名称
MODEL_VERSION=v1.0          # 模型版本
SERVICE_PORT=8080           # 服务端口
WEIGHTS_PATH=/app/weights   # 权重文件路径
```

## 监控和维护

### 性能监控

系统自动收集以下指标：
- CPU使用率
- 内存使用率
- GPU使用率（如果可用）
- 每分钟请求数
- 平均响应时间
- 错误率

### 日志管理

- 容器日志：通过Docker API获取
- 推理日志：记录每次推理请求
- 系统日志：Django和Celery日志

### 定时任务

系统包含以下定时任务：
- 每分钟：监控服务状态
- 每5分钟：收集性能指标
- 每10分钟：检查并重启失败服务
- 每小时：更新服务统计
- 每天：生成日报、清理旧日志

## 故障排除

### 常见问题

#### 1. 部署失败
- 检查Docker镜像是否存在
- 确认权重文件路径正确
- 查看容器日志获取详细错误信息

#### 2. 推理服务不健康
- 检查容器是否正常运行
- 确认服务端口是否被占用
- 查看推理服务日志

#### 3. 权重文件下载失败
- 检查MinIO连接配置
- 确认文件路径和权限
- 查看网络连接状态

### 日志查看

```bash
# 查看Django日志
tail -f backend/logs/django.log

# 查看Celery日志
tail -f backend/logs/celery.log

# 查看容器日志
docker logs <container_id>
```

## 开发指南

### 添加新的推理服务类型

1. 在`docker_templates`目录下创建新模板
2. 实现推理服务的Flask应用
3. 配置Dockerfile和依赖文件
4. 更新模型版本的docker_image字段

### 扩展监控指标

1. 在`ServiceMetrics`模型中添加新字段
2. 更新`container_monitor.py`中的指标收集逻辑
3. 在前端图表组件中添加新的图表类型

### 自定义部署配置

1. 扩展`deploy_config`字段的JSON结构
2. 在Docker管理器中处理新的配置参数
3. 更新前端部署对话框的配置选项

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
