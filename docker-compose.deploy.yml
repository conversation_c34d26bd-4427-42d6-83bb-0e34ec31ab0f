version: '3.8'

services:
  # 主应用服务
  web:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: django-vue-admin-web
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /var/run/docker.sock:/var/run/docker.sock  # Docker socket挂载
      - model_weights:/app/model_weights  # 模型权重存储卷
    environment:
      - DEBUG=False
      - DATABASE_URL=mysql://root:123456@mysql:3306/django_vue_admin
      - REDIS_URL=redis://redis:6379/0
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
      - MINIO_MODEL_BUCKET=models
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - mysql
      - redis
      - minio
    networks:
      - app-network
    restart: unless-stopped

  # Celery Worker
  celery-worker:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: django-vue-admin-celery-worker
    command: celery -A application worker -l info
    volumes:
      - ./backend:/app
      - /var/run/docker.sock:/var/run/docker.sock
      - model_weights:/app/model_weights
    environment:
      - DEBUG=False
      - DATABASE_URL=mysql://root:123456@mysql:3306/django_vue_admin
      - REDIS_URL=redis://redis:6379/0
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
      - MINIO_MODEL_BUCKET=models
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - mysql
      - redis
      - minio
    networks:
      - app-network
    restart: unless-stopped

  # Celery Beat (定时任务)
  celery-beat:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: django-vue-admin-celery-beat
    command: celery -A application beat -l info
    volumes:
      - ./backend:/app
    environment:
      - DEBUG=False
      - DATABASE_URL=mysql://root:123456@mysql:3306/django_vue_admin
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - mysql
      - redis
    networks:
      - app-network
    restart: unless-stopped

  # 容器监控服务
  container-monitor:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: django-vue-admin-monitor
    command: python manage.py start_monitor --interval 60
    volumes:
      - ./backend:/app
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - DEBUG=False
      - DATABASE_URL=mysql://root:123456@mysql:3306/django_vue_admin
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - mysql
      - redis
    networks:
      - app-network
    restart: unless-stopped

  # 前端服务
  frontend:
    build:
      context: ./web
      dockerfile: Dockerfile
    container_name: django-vue-admin-frontend
    ports:
      - "80:80"
    depends_on:
      - web
    networks:
      - app-network
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: django-vue-admin-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=django_vue_admin
      - MYSQL_USER=django
      - MYSQL_PASSWORD=django123
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
    networks:
      - app-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: django-vue-admin-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: django-vue-admin-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - app-network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: django-vue-admin-nginx
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - web
      - frontend
    networks:
      - app-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  model_weights:
    driver: local

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
