# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('app_role', '0001_initial'),
        ('app_message', '0001_initial'),
        ('app_dept', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='messagecentertargetuser',
            name='creator',
            field=models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='messagecentertargetuser',
            name='messagecenter',
            field=models.ForeignKey(db_constraint=False, help_text='关联消息中心表', on_delete=django.db.models.deletion.CASCADE, to='app_message.messagecenter', verbose_name='关联消息中心表'),
        ),
        migrations.AddField(
            model_name='messagecentertargetuser',
            name='users',
            field=models.ForeignKey(db_constraint=False, help_text='关联用户表', on_delete=django.db.models.deletion.CASCADE, related_name='target_user', to=settings.AUTH_USER_MODEL, verbose_name='关联用户表'),
        ),
        migrations.AddField(
            model_name='messagecenter',
            name='creator',
            field=models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='messagecenter',
            name='target_dept',
            field=models.ManyToManyField(blank=True, db_constraint=False, help_text='目标部门', to='app_dept.dept', verbose_name='目标部门'),
        ),
        migrations.AddField(
            model_name='messagecenter',
            name='target_role',
            field=models.ManyToManyField(blank=True, db_constraint=False, help_text='目标角色', to='app_role.role', verbose_name='目标角色'),
        ),
        migrations.AddField(
            model_name='messagecenter',
            name='target_user',
            field=models.ManyToManyField(blank=True, help_text='目标用户', related_name='user', through='app_message.MessageCenterTargetUser', to=settings.AUTH_USER_MODEL, verbose_name='目标用户'),
        ),
    ]
