# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.db import migrations, models
import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MessageCenter',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('title', models.CharField(help_text='标题', max_length=100, verbose_name='标题')),
                ('content', models.TextField(help_text='内容', verbose_name='内容')),
                ('target_type', models.<PERSON><PERSON><PERSON><PERSON>(help_text='目标类型', max_length=4, verbose_name='目标类型')),
                ('remark', models.<PERSON>r<PERSON><PERSON>(blank=True, help_text='备注', max_length=255, null=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '消息中心',
                'verbose_name_plural': '消息中心',
                'db_table': 'sys_message_center',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='MessageCenterTargetUser',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('is_read', models.BooleanField(blank=True, default=False, help_text='是否已读', null=True, verbose_name='是否已读')),
            ],
            options={
                'verbose_name': '消息中心目标用户表',
                'verbose_name_plural': '消息中心目标用户表',
                'db_table': 'sys_message_center_target_user',
            },
        ),
    ]
