# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.conf import settings
import django.contrib.auth.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('app_role', '0001_initial'),
        ('app_post', '0001_initial'),
        ('app_dept', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Users',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('status', models.CharField(choices=[('0', '正常'), ('1', '停用')], default='0', help_text='用户状态（0正常 1停用）', max_length=1, verbose_name='用户状态（0正常 1停用）')),
                ('username', models.CharField(db_index=True, help_text='用户账号', max_length=150, unique=True, verbose_name='用户账号')),
                ('nickname', models.CharField(help_text='用户昵称', max_length=150, unique=True, verbose_name='用户昵称')),
                ('employee_no', models.CharField(blank=True, db_index=True, help_text='工号', max_length=150, null=True, unique=True, verbose_name='用户工号')),
                ('email', models.EmailField(blank=True, help_text='邮箱', max_length=255, null=True, verbose_name='邮箱')),
                ('phone', models.CharField(blank=True, help_text='手机号', max_length=255, null=True, verbose_name='手机号')),
                ('avatar', models.CharField(blank=True, help_text='头像', max_length=255, null=True, verbose_name='头像')),
                ('gender', models.CharField(blank=True, choices=[('0', '男'), ('1', '女'), ('2', '未知')], default='2', help_text='性别', max_length=1, null=True, verbose_name='性别')),
                ('last_token', models.CharField(blank=True, help_text='最后一次登录Token', max_length=255, null=True, verbose_name='最后一次登录Token')),
                ('is_delete', models.BooleanField(default=False, help_text='是否逻辑删除', verbose_name='是否逻辑删除')),
                ('remark', models.CharField(blank=True, help_text='备注', max_length=128, null=True, verbose_name='备注')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('dept', models.ForeignKey(blank=True, db_constraint=False, help_text='关联部门', null=True, on_delete=django.db.models.deletion.PROTECT, to='app_dept.dept', verbose_name='所属部门')),
                ('post', models.ManyToManyField(blank=True, db_constraint=False, help_text='关联岗位', to='app_post.post', verbose_name='关联岗位')),
                ('role', models.ManyToManyField(blank=True, db_constraint=False, help_text='关联角色', to='app_role.role', verbose_name='关联角色')),
            ],
            options={
                'verbose_name': '用户表',
                'verbose_name_plural': '用户表',
                'db_table': 'sys_users',
                'ordering': ('-create_datetime',),
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
