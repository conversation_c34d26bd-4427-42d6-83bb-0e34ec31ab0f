# 数据库配置
DATABASE_ENGINE=django.db.backends.mysql
DATABASE_NAME=django_vue_admin
DATABASE_USER=root
DATABASE_PASSWORD=123456
DATABASE_HOST=127.0.0.1
DATABASE_PORT=3306
DATABASE_CONN_MAX_AGE=0
DATABASE_CHARSET=utf8mb4

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 模型部署配置
MODEL_DEPLOY_SERVER_HOST=************
MODEL_DEPLOY_SERVER_PORT=8000

# 其他配置
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=*
