# ================================================= #
# ************** mysql数据库 配置  ************** #
# ================================================= #
# 数据库地址
DATABASE_ENGINE = "django.db.backends.mysql"
# 数据库地址
DATABASE_HOST = "127.0.0.1"
# 数据库端口
DATABASE_PORT = 3306
# 数据库用户名
DATABASE_USER = "root"
# 数据库密码
DATABASE_PASSWORD = "root"
# 数据库名
DATABASE_NAME = "aidata"
#数据库编码
DATABASE_CHARSET = "utf8mb4"
# 数据库长连接时间（默认为0，单位秒）即每次请求都重新连接,debug模式下该值应该写为0 ，mysql默认长连接超时时间为8小时
 #推荐120（2分钟），使用 None 则是无限的持久连接（不推荐）。
DATABASE_CONN_MAX_AGE = 0

# ================================================= #
# ************** 达梦数据库 配置  ************** #
# ================================================= #
DM_DATABASE_ENGINE = "dmDjango"
DM_DATABASE_NAME = "DAMENG"
DM_DATABASE_USER = "AITEST"
DM_DATABASE_PASSWORD = "chenyin.ZZ1024"
DM_DATABASE_HOST = "127.0.0.1"
DM_DATABASE_PORT = "5236"

# ================================================= #
# ************** redis 配置  ************** #
# ================================================= #
REDIS_URL = 'redis://:root@127.0.0.1:6379'

# ================================================= #
# ************** 服务器基本 配置  ************** #
# ================================================= #
DEBUG = True #是否调试模式

# ================================================= #
# ************** minio 配置  ************** #
# ================================================= #
MINIO_ROOT_USER = minioadmin
MINIO_ROOT_PASSWORD = minioadmin
MINIO_ENDPOINT = 127.0.0.1:9000
MINIO_PUBLIC_URL = http://127.0.0.1:9000
MINIO_DATASET_BUCKET = 'datasets'
MINIO_MODEL_BUCKET = 'models'