from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import (
    AIModelViewSet, ModelCategoryViewSet, ModelCommentViewSet,
    ModelVersionViewSet
)

router = DefaultRouter()
router.register(r'models', AIModelViewSet)
router.register(r'categories', ModelCategoryViewSet)
router.register(r'comments', ModelCommentViewSet)
router.register(r'versions', ModelVersionViewSet)

app_name = 'app_model'

urlpatterns = [
    path('', include(router.urls)),
] 