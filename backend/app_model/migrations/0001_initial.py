# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.db import migrations, models
import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AIModel',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('name', models.CharField(max_length=200, verbose_name='模型名称')),
                ('code', models.CharField(blank=True, help_text='模型唯一编码，用于与训练平台和测试平台交互', max_length=100, null=True, unique=True, verbose_name='模型编码')),
                ('group', models.CharField(default='qianlan', help_text='模型所属项目组', max_length=100)),
                ('description', models.TextField(verbose_name='模型描述')),
                ('embedding_vector', models.BinaryField(blank=True, null=True, verbose_name='嵌入特征向量')),
                ('minio_path', models.CharField(blank=True, help_text='MinIO 中的存储路径，例如 apple/DepthPro/', max_length=255, null=True)),
            ],
            options={
                'verbose_name': 'AI模型',
                'verbose_name_plural': 'AI模型',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='ModelCategory',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('name', models.CharField(max_length=100, verbose_name='分类名称')),
                ('code', models.CharField(blank=True, max_length=100, null=True, verbose_name='分类编码')),
                ('order', models.IntegerField(default=1, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
            ],
            options={
                'verbose_name': '模型分类',
                'verbose_name_plural': '模型分类',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='ModelComment',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('content', models.TextField(verbose_name='评论内容')),
            ],
            options={
                'verbose_name': '模型评论',
                'verbose_name_plural': '模型评论',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='ModelVersion',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('code', models.CharField(blank=True, help_text='版本唯一编码，例如resnet_v1，用于与训练平台和测试平台交互', max_length=100, null=True, verbose_name='版本编码')),
                ('version_number', models.CharField(default='v1.0', help_text='模型版本号', max_length=100)),
                ('description', models.TextField(blank=True, null=True, verbose_name='版本描述')),
                ('status', models.CharField(choices=[('dev_done', '开发完毕待训练'), ('train_done', '训练完毕待测试'), ('test_pass', '测试通过'), ('test_fail', '测试未通过'), ('online', '已上架'), ('offline', '已下架')], default='dev_done', max_length=20, verbose_name='版本状态')),
                ('docker_image', models.CharField(blank=True, help_text='Docker 镜像名称，例如 apple/DepthPro:latest', max_length=255, null=True)),
                ('model_weights_path', models.CharField(blank=True, help_text='权重文件路径，例如 apple/DepthPro/weights/model.pth', max_length=255, null=True)),
                ('model_docs_path', models.CharField(blank=True, help_text='文档文件路径，例如 apple/DepthPro/docs/model.md', max_length=255, null=True)),
                ('test_report_path', models.CharField(blank=True, help_text='测试报告文件路径，例如 apple/DepthPro/test/model.md', max_length=255, null=True)),
                ('test_failure_reason', models.TextField(blank=True, help_text='测试不通过原因', null=True)),
            ],
            options={
                'verbose_name': '模型版本',
                'verbose_name_plural': '模型版本',
                'ordering': ['-create_datetime'],
            },
        ),
    ]
