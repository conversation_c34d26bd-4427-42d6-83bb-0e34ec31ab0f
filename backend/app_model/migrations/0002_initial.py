# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('app_model', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='modelversion',
            name='creator',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_versions', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='modelversion',
            name='model',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='versions', to='app_model.aimodel', verbose_name='所属模型'),
        ),
        migrations.AddField(
            model_name='modelcomment',
            name='creator',
            field=models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='modelcomment',
            name='model',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='app_model.aimodel', verbose_name='所属模型'),
        ),
        migrations.AddField(
            model_name='modelcomment',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='app_model.modelcomment', verbose_name='父评论'),
        ),
        migrations.AddField(
            model_name='modelcomment',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to=settings.AUTH_USER_MODEL, verbose_name='发表用户'),
        ),
        migrations.AddField(
            model_name='modelcategory',
            name='creator',
            field=models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='modelcategory',
            name='parent',
            field=models.ForeignKey(blank=True, db_constraint=False, help_text='父级分类', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcat', to='app_model.modelcategory', verbose_name='父级分类'),
        ),
        migrations.AddField(
            model_name='aimodel',
            name='categories',
            field=models.ManyToManyField(help_text='模型分类，可以选择多个类别', related_name='models', to='app_model.modelcategory', verbose_name='模型分类'),
        ),
        migrations.AddField(
            model_name='aimodel',
            name='creator',
            field=models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AlterUniqueTogether(
            name='modelversion',
            unique_together={('model', 'version_number')},
        ),
    ]
