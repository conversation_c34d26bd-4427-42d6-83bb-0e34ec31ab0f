from django.shortcuts import get_object_or_404
from rest_framework.decorators import action
from django.db.models import F
import logging
from django.http import HttpResponse
import zipfile
import tempfile
import os
from rest_framework import serializers
from rest_framework.response import Response

from .models import ModelCategory, AIModel, ModelComment, ModelVersion
from .serializers import (
    ModelCategorySerializer, ModelCategoryTreeSerializer, AIModelSerializer, 
    ModelCommentSerializer, ModelVersionSerializer
)
from utils.viewset import CustomModelViewSet
from utils.json_response import DetailResponse, SuccessResponse, ErrorResponse
from utils.minio_storage import minio_client
from django.conf import settings

logger = logging.getLogger(__name__)

class ModelCategoryViewSet(CustomModelViewSet):
    """
    模型分类管理
    """
    queryset = ModelCategory.objects.all()
    serializer_class = ModelCategorySerializer
    filterset_fields = ['is_active']
    search_fields = ['name', 'code']
    
    @action(methods=["GET"], detail=False)
    def tree(self, request):
        """
        获取模型分类树结构
        """
        queryset = ModelCategory.objects.exclude(is_active=False).filter(parent=None)
        serializer = ModelCategoryTreeSerializer(queryset, many=True)
        return DetailResponse(data=serializer.data, msg="模型分类树获取成功")

class AIModelViewSet(CustomModelViewSet):
    """
    AI模型管理
    """
    queryset = AIModel.objects.all()
    serializer_class = AIModelSerializer
    filterset_fields = ['id', 'name', 'group']
    search_fields = ['name', 'group', 'description']


    def list(self, request, *args, **kwargs):
        """
        获取模型列表
        """
        queryset = self.get_queryset()
        
        # 通过分类ID过滤
        category_ids = request.query_params.getlist('category_ids')
        if category_ids:
            queryset = queryset.filter(categories__id__in=category_ids).distinct()
        
        # 根据当前状态过滤
        current_status = request.query_params.get('current_status')
        if current_status:
            try:
                # 使用子查询找出每个模型的最新版本
                from django.db.models import OuterRef, Subquery, Max, F
                
                # 获取每个模型的最新版本ID
                latest_versions = ModelVersion.objects.filter(
                    model=OuterRef('pk')
                ).order_by('-create_datetime').values('id')[:1]
                
                # 找出最新版本状态匹配的模型
                models_with_matching_status = queryset.filter(
                    versions__id=Subquery(latest_versions),
                    versions__status=current_status
                ).distinct()
                
                queryset = models_with_matching_status
                logger.info(f"按状态 '{current_status}' 过滤后找到 {queryset.count()} 个模型")
            except Exception as e:
                logger.error(f"按状态过滤模型时出错: {str(e)}")
                # 如果过滤出错，继续使用原始查询集
        
        # 其他过滤条件
        queryset = self.filter_queryset(queryset)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)  
        serializer = self.get_serializer(queryset, many=True)
        return DetailResponse(data=serializer.data, msg="获取模型列表成功")

    def retrieve(self, request, *args, **kwargs):
        """
        获取模型详情
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return DetailResponse(data=serializer.data, msg="获取模型详情成功")
    
    def create(self, request, *args, **kwargs):
        """
        创建模型
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return DetailResponse(data=serializer.data, msg="创建模型成功")
        
  


    @action(detail=True, methods=['get'])
    def file_url(self, request, pk=None):
        """
        获取模型文件的下载URL
        """
        model = self.get_object()
        file_path = request.query_params.get('file_path', '')
        if not file_path:
            return ErrorResponse(msg="文件路径不能为空")

        full_path = f"{model.minio_path.rstrip('/')}/{file_path}"
        url = minio_client.get_object_url('models', full_path)
        if url:
            return DetailResponse(data={"url": url}, msg="获取下载链接成功")
        return ErrorResponse(msg="获取文件URL失败")

    @action(methods=["POST"], detail=False)
    def upload(self, request, *args, **kwargs):
        """
        创建模型记录
        现在文件已经通过预签名URL直接上传到MinIO，这个接口只需要创建模型记录
        """
        import re
        import json
        
        # 获取请求中的参数
        name = request.data.get('name')
        group = request.data.get('group', 'default')
        description = request.data.get('description', '')

        # 获取类别ID列表或单一类别ID
        category_ids = request.data.getlist('category_ids')
        if not category_ids:
            # 兼容旧API，尝试获取单一类别ID
            category_id = request.data.get('category_id')
            if category_id:
                category_ids = [category_id]

        # 获取文件信息列表
        try:
            files_info = json.loads(request.data.get('files', '[]'))
        except json.JSONDecodeError:
            return ErrorResponse(msg="文件信息格式错误")
        
        # 参数验证
        if not name or not category_ids or not files_info:
            return ErrorResponse(msg="模型名称、分类和文件不能为空")
        
        # 验证名称符合MinIO命名规则（3-63字符，只能包含小写字母、数字、点和连字符等）
        if not re.match(r'^[a-z0-9][a-z0-9.\-]{1,61}[a-z0-9]$', name):
            return ErrorResponse(msg="模型名称必须是3-63个字符，只能包含小写字母、数字、点和连字符")

        # 解析其他参数
        try:
            parameters_str = request.data.get('parameters', '{}')
            metrics_str = request.data.get('metrics', '{}')
            
            parameters = json.loads(parameters_str) if isinstance(parameters_str, str) else parameters_str
            metrics = json.loads(metrics_str) if isinstance(metrics_str, str) else metrics_str
        except json.JSONDecodeError as e:
            parameters = {}
            metrics = {}
            logger.error(f"参数解析错误: {str(e)}")

        logger.info(f"接收到上传请求: name={name}, group={group}, category_ids={category_ids}, 文件数量={len(files_info)}")
        logger.info(f"请求数据: {request.data}")
        # 不再记录文件列表，因为现在使用预签名URL直接上传
        # logger.info(f"文件: {request.FILES}")
        
        # 确保MinIO存储桶存在
        minio_client.ensure_bucket_exists('models')
        
        # 创建模型对象
        minio_path = f"{group}/{name}"
        try:
            model = AIModel.objects.create(
                name=name,
                group=group,
                description=description,
                minio_path=minio_path,
                parameters=parameters,
                metrics=metrics,
                creator=request.user
            )
            # 设置多个类别
            if category_ids:
                model.categories.set(category_ids)
                
                # 兼容旧系统，设置第一个类别为主类别
                if len(category_ids) > 0:
                    model.category_id = category_ids[0]
                    model.save()
                
            # 返回响应
            return DetailResponse(
                data={
                    'model_id': model.id,
                    'files': files_info
                }, 
                msg="模型创建成功"
            )
        except Exception as e:
            logger.error(f"创建模型失败: {str(e)}")
            return ErrorResponse(msg=f"创建模型失败: {str(e)}")

    @action(detail=True, methods=['get'])
    def download_file(self, request, pk=None):
        """
        下载模型文件(支持流式下载)
        """
        bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
        file_path = request.query_params.get('file_path')
        if not file_path:
            return ErrorResponse(msg="文件路径不能为空")

        model = self.get_object()
        full_path = f"{model.minio_path.rstrip('/')}/{file_path}"
        
        try:
            # 获取文件对象
            response = HttpResponse(content_type='application/octet-stream')
            response['Content-Disposition'] = f'attachment; filename="{file_path}"'
            
            # 从MinIO获取文件数据并流式传输
            data = minio_client.get_object(bucket_name, full_path)
            if data:
                # 设置响应头
                response['Content-Length'] = data.headers.get('Content-Length', 0)
                # 流式传输数据
                for chunk in data.stream(32*1024):
                    response.write(chunk)
                return response
            return ErrorResponse(msg="文件不存在")
        except Exception as e:
            logger.error(f"下载文件失败: {str(e)}")
            return ErrorResponse(msg=f"下载文件失败: {str(e)}")

    @action(methods=["POST"], detail=False)
    def get_upload_url(self, request, *args, **kwargs):
        """
        获取预签名上传URL
        """
        filename = request.data.get('filename')
        group = request.data.get('group', 'default')
        name = request.data.get('name')

        if not filename or not name:
            return ErrorResponse(msg="文件名和模型名称不能为空")

        # 构建对象名称
        object_name = f"{group}/{name}/{filename}"
        bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')

        # 确保存储桶存在
        minio_client.ensure_bucket_exists(bucket_name)

        # 获取预签名上传URL
        url = minio_client.get_presigned_put_url(bucket_name, object_name)
        if url:
            return DetailResponse(data={"url": url, "object_name": object_name}, msg="获取上传URL成功")
        return ErrorResponse(msg="获取上传URL失败")
        
    @action(methods=["POST"], detail=False)
    def version_upload_url(self, request):
        """
        获取模型版本文件上传URL - 训练和测试平台使用
        支持请求格式：
        {
            "version_code": "resnet_v1",
            "version_files": {
                "model_weights": ["model.pth.zip"],
                "model_docs": ["README.md"],
                "test_report": ["test_report.pdf"]
            }
        }
        所有文件统一存放在 group/model/v1.0/ 目录下
        """
        try:
            version_code = request.data.get('version_code')
            version_files = request.data.get('version_files', {})
            
            if not version_code or not version_files:
                return ErrorResponse(msg="版本编码和文件信息不能为空")
                
            # 通过code获取版本
            try:
                version = ModelVersion.objects.get(code=version_code)
                model = version.model
            except ModelVersion.DoesNotExist:
                return ErrorResponse(msg=f"版本编码 {version_code} 不存在")
            
            bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
            # 确保存储桶存在
            minio_client.ensure_bucket_exists(bucket_name)
            
            # 基础路径
            base_path = f"{model.group}/{model.name}/{version.version_number}"
            
            # 用于存储所有文件的上传URL
            upload_urls = {}
            
            # 处理每种文件类型
            for file_type_key, filenames in version_files.items():
                upload_urls[file_type_key] = {}
                
                for filename in filenames:
                    object_name = f"{base_path}/{filename}"
                    
                    # 获取预签名上传URL
                    url = minio_client.get_presigned_put_url(bucket_name, object_name)
                    
                    if url:
                        # 根据文件类型更新模型版本的文件路径
                        # 只在没有设置过的情况下更新
                        if file_type_key == 'model_weights' and not version.model_weights_path:
                            version.model_weights_path = object_name
                        elif file_type_key == 'model_docs' and not version.model_docs_path:
                            version.model_docs_path = object_name
                        elif file_type_key == 'test_report' and not version.test_report_path:
                            version.test_report_path = object_name
                        
                        # 存储URL和对象名称
                        upload_urls[file_type_key][filename] = {
                            "url": url,
                            "object_name": object_name
                        }
                    else:
                        # 如果获取URL失败，添加错误信息
                        upload_urls[file_type_key][filename] = {
                            "error": "获取上传URL失败"
                        }
            
            # 保存版本更新
            version.save()
            
            return DetailResponse(
                data=upload_urls,
                msg="获取上传URL成功"
            )
        except Exception as e:
            logger.error(f"获取预签名URL失败: {str(e)}")
            return ErrorResponse(msg=f"获取预签名URL失败: {str(e)}")

    @action(methods=["POST"], detail=False)
    def semantic_search(self, request, *args, **kwargs):
        """
        任务语义检索
        接收任务描述、输入和输出，返回匹配的模型列表
        """
        # 获取请求参数
        description = request.data.get('description', '')
        input_data = request.data.get('input', '')
        output_data = request.data.get('output', '')
        
        # 验证任务描述必须填写
        if not description:
            return ErrorResponse(msg="任务需求描述不能为空")
        
        # 记录请求日志
        logger.info(f"任务语义检索请求: 描述={description}, 输入={input_data}, 输出={output_data}")
        
        try:
            # 导入向量服务
            from .vector_service import vector_service
            
            # 判断向量服务是否初始化
            if vector_service.model is None:
                logger.warning("向量服务尚未初始化，无法执行语义检索")
                return ErrorResponse(msg="语义检索服务尚未准备就绪，请稍后再试")
            
            # 构建查询文本 (可以结合输入和输出)
            query_text = description
            if input_data:
                query_text += f"\n输入: {input_data}"
            if output_data:
                query_text += f"\n输出: {output_data}"
            
            # 执行向量相似度搜索，返回模型ID和相似度
            search_results = vector_service.search_similar_models(query_text, top_k=10)
            
            if not search_results:
                logger.info("没有找到匹配的模型")
                return DetailResponse(data=[], msg="未找到匹配的模型")
            
            # 获取模型ID列表
            model_ids = [model_id for model_id, _ in search_results]
            similarity_dict = {model_id: score for model_id, score in search_results}
            
            # 查询这些模型的详细信息
            models = AIModel.objects.filter(id__in=model_ids)
            
            # 添加相似度分数，并转换为响应格式
            models_with_score = []
            for model in models:
                # 将模型数据序列化
                serialized_model = self.get_serializer(model).data
                # 添加相似度分数
                similarity_score = similarity_dict.get(model.id, 0)
                serialized_model['similarity_score'] = similarity_score 
                models_with_score.append(serialized_model)
            
            # 按相似度得分排序，得分高的排在前面
            sorted_models = sorted(models_with_score, key=lambda x: x['similarity_score'], reverse=True)
            
            # 返回结果
            return DetailResponse(data=sorted_models, msg="任务语义检索成功")
            
        except Exception as e:
            logger.error(f"执行语义检索失败: {str(e)}")
            
            # 如果向量服务失败，回退到简单实现
            logger.info("回退到简单实现")
            queryset = self.get_queryset()
            
            # 对模型添加相似度分数（示例值）
            models_with_score = []
            for model in queryset:
                # 将模型数据序列化
                serialized_model = self.get_serializer(model).data
                # 添加模拟的相似度分数 (1-5之间的值)
                # 这里简单地基于模型id计算一个伪随机分数
                # 实际实现中应当使用真正的语义相似度计算方法
                similarity_score = (model.id % 4) + 1.5
                serialized_model['similarity_score'] = similarity_score
                models_with_score.append(serialized_model)
            
            # 按相似度得分排序，得分高的排在前面
            sorted_models = sorted(models_with_score, key=lambda x: x['similarity_score'], reverse=True)
            
            # 返回结果
            return DetailResponse(data=sorted_models, msg="任务语义检索成功（简单模式）")

    @action(methods=["POST"], detail=False)
    def delete_model(self, request):
        """
        根据模型ID删除整个模型及其所有版本
        支持请求格式：
        {
            "model_id": 123
        }
        """
        try:
            model_id = request.data.get('model_id')
            if not model_id:
                return ErrorResponse(msg="模型ID不能为空")
                
            # 获取模型
            try:
                model = AIModel.objects.get(id=model_id)
            except AIModel.DoesNotExist:
                return ErrorResponse(msg=f"模型ID {model_id} 不存在")
            
            # 获取模型信息用于日志记录
            model_name = model.name
            model_group = model.group
            
            # 删除MinIO中的所有文件
            bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
            prefix = f"{model_group}/{model_name}/"
            
            try:
                # 列出该模型目录下的所有文件
                objects = list(minio_client.list_objects(bucket_name, prefix=prefix))
                
                # 如果有文件，删除它们
                if objects:
                    logger.info(f"正在删除模型 {model_name} (ID: {model_id}) 的 {len(objects)} 个文件")
                    for obj in objects:
                        minio_client.remove_object(bucket_name, obj.object_name)
                        logger.info(f"已删除文件: {obj.object_name}")
            except Exception as e:
                logger.error(f"删除模型文件失败: {str(e)}")
                # 继续执行，即使文件删除失败也删除数据库记录
            
            # 删除模型(会级联删除所有版本)
            model.delete()
            
            # 记录删除日志
            logger.info(f"已删除模型 {model_name}(ID: {model_id}) 及其所有版本")
            
            return DetailResponse(data=None, msg=f"已删除模型 {model_name} 及其所有版本")
        except Exception as e:
            logger.error(f"删除模型失败: {str(e)}")
            return ErrorResponse(msg=f"删除模型失败: {str(e)}")

    @action(methods=["POST"], detail=False)
    def create_or_update(self, request, *args, **kwargs):
        """
        创建或更新模型及其版本
        每次请求都需要提供model_code
        如果数据库中存在该model_code的模型，则为该模型创建新版本
        如果不存在，则创建新模型和首个版本
        """
        data = request.data
        model_code = data.get('model_code')
        version_code = data.get('version_code')
        
        # model_code是必填参数
        if not model_code:
            return ErrorResponse(msg="model_code不能为空")
        
        if not version_code:
            return ErrorResponse(msg="version_code不能为空")
        
        # 提取版本相关字段
        version_data = {
            'version_number': data.get('version_number'),
            'code': data.get('version_code'),
            'description': data.get('version_description'),
            'status': data.get('version_status', 'dev_done'),
            'docker_image': data.get('docker_image')
        }
        
        # 检查必填参数
        if not version_data['version_number']:
            return ErrorResponse(msg="version_number不能为空")
        
        try:
            # 查询数据库中是否存在该model_code的模型
            model = None
            try:
                model = AIModel.objects.get(code=model_code)
                # 模型存在，检查版本号是否已存在
                if model.versions.filter(version_number=version_data['version_number']).exists():
                    return ErrorResponse(msg=f"版本号 {version_data['version_number']} 已存在")
                
                # 创建新版本
                version = ModelVersion.objects.create(
                    model=model,
                    **version_data
                )
                
                return DetailResponse(
                    data={
                        'model_id': model.id,
                        'model_code': model.code,
                        'version_id': version.id,
                        'version_code': version.code,
                        'message': '新版本创建成功'
                    },
                    msg="模型版本创建成功"
                )
            except AIModel.DoesNotExist:
                # 模型不存在，创建新模型
                # 提取模型相关字段
                model_data = {
                    'name': data.get('model_name'),
                    'code': model_code,
                    'group': data.get('model_group', 'default'),
                    'description': data.get('model_description', ''),
                    'category_ids': data.get('category_ids', []),
                }
                
                # 检查必填参数
                if not model_data['name']:
                    return ErrorResponse(msg="name不能为空")
                
                # 创建模型
                serializer = self.get_serializer(data=model_data)
                serializer.is_valid(raise_exception=True)
                model = serializer.save()
                
                # 创建首个版本
                version = ModelVersion.objects.create(
                    model=model,
                    **version_data
                )
                
                return DetailResponse(
                    data={
                        'model_id': model.id,
                        'model_code': model.code,
                        'version_id': version.id,
                        'version_code': version.code,
                        'message': '模型和首个版本创建成功'
                    },
                    msg="模型创建成功"
                )
                
        except Exception as e:
            logger.error(f"创建/更新模型失败: {str(e)}")
            return ErrorResponse(msg=f"操作失败: {str(e)}")

    @action(methods=["POST"], detail=False)
    def check_code_unique(self, request, *args, **kwargs):
        """
        检查模型编码是否唯一
        """
        code = request.data.get('code')
        model_id = request.data.get('id')  # 如果是编辑模式，需要排除当前模型ID
        
        if not code:
            return DetailResponse(data={"unique": False}, msg="模型编码不能为空")
        
        # 查询是否已存在相同编码的模型
        query = AIModel.objects.filter(code=code)
        if model_id:
            # 如果提供了模型ID，排除该模型（用于编辑模式）
            query = query.exclude(id=model_id)
        
        exists = query.exists()
        
        return DetailResponse(
            data={"unique": not exists},
            msg="模型编码可用" if not exists else "模型编码已存在"
        )

class ModelCommentViewSet(CustomModelViewSet):
    """
    模型评论管理
    """
    queryset = ModelComment.objects.all()
    serializer_class = ModelCommentSerializer

    def perform_create(self, serializer):
        """创建评论时自动关联当前用户"""
        model_id = self.request.data.get('model')
        parent_id = self.request.data.get('parent_id')

        # 获取模型
        model = get_object_or_404(AIModel, id=model_id)

        # 如果有父评论，验证父评论是否存在
        parent = None
        if parent_id:
            parent = get_object_or_404(ModelComment, id=parent_id)

        # 保存评论，关联用户和模型
        serializer.save(user=self.request.user, model=model, parent=parent)

    def get_queryset(self):
        """获取评论列表，按模型ID过滤，只返回顶级评论"""
        queryset = super().get_queryset().select_related('user', 'parent')
        model_id = self.request.query_params.get('model', None)
        # 增加一个参数，可以控制是否只返回顶级评论
        only_top = self.request.query_params.get('only_top', 'true').lower() == 'true'
        
        if model_id:
            filtered = queryset.filter(model_id=model_id)
            if only_top:
                # 只返回顶级评论（没有父评论的评论）
                filtered = filtered.filter(parent__isnull=True)
            return filtered.order_by('create_datetime')
        return queryset.none()

    def list(self, request, *args, **kwargs):
        """获取评论列表"""
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return DetailResponse(data=serializer.data, msg="获取评论列表成功")
        
    def destroy(self, request, *args, **kwargs):
        """删除评论及其所有回复"""
        try:
            # 获取评论 ID
            comment_id = kwargs.get('pk')
            if not comment_id:
                return ErrorResponse(msg="评论ID不能为空")
                
            # 直接查询评论
            comment = ModelComment.objects.filter(id=comment_id).first()
            if not comment:
                return ErrorResponse(msg=f"未找到ID为{comment_id}的评论")
            
            # 递归删除所有子评论
            self.delete_replies(comment_id)
            
            # 删除当前评论
            comment.delete()
            
            return DetailResponse(data=None, msg="评论删除成功")
        except Exception as e:
            logger.error(f"删除评论失败: {str(e)}")
            return ErrorResponse(msg=f"删除评论失败: {str(e)}")
            
    def delete_replies(self, comment_id):
        """递归删除评论的所有回复"""
        # 获取直接回复此评论的评论
        replies = ModelComment.objects.filter(parent_id=comment_id)
        
        # 递归删除每个回复的子回复
        for reply in replies:
            self.delete_replies(reply.id)
            
        # 删除所有回复
        replies.delete()

class ModelVersionViewSet(CustomModelViewSet):
    """
    模型版本管理
    """
    queryset = ModelVersion.objects.all()
    serializer_class = ModelVersionSerializer
    filterset_fields = ['id', 'status', 'code']
    search_fields = ['description', 'code']
    
    def list(self, request, *args, **kwargs):
        """获取版本列表"""
        model_id = request.query_params.get('model_id')
        if model_id:
            queryset = self.filter_queryset(self.get_queryset().filter(model_id=model_id))
        else:
            queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return DetailResponse(data=serializer.data, msg="获取版本列表成功")
    
    def retrieve(self, request, *args, **kwargs):
        """获取版本详情"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return DetailResponse(data=serializer.data, msg="获取版本详情成功")
    
    def create(self, request, *args, **kwargs):
        """创建版本"""
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            # 保存创建者信息
            serializer.save(creator=request.user)
            return DetailResponse(data=serializer.data, msg="创建版本成功")
        except serializers.ValidationError as e:
            # 检查是否是唯一性约束错误
            if 'non_field_errors' in e.detail and '唯一集合' in str(e.detail['non_field_errors'][0]):
                model_id = request.data.get('model')
                version_number = request.data.get('version_number')
                try:
                    model = AIModel.objects.get(id=model_id)
                    return ErrorResponse(msg=f"模型 {model.name} 已存在版本号为 {version_number} 的版本")
                except:
                    return ErrorResponse(msg=f"版本号 {version_number} 已被使用，请更换版本号")
            # 其他验证错误直接抛出
            raise e
        
    def destroy(self, request, *args, **kwargs):
        """删除版本及其所有文件"""
        try:
            version = self.get_object()
            
            # 获取版本信息用于日志记录
            model_name = version.model.name
            model_group = version.model.group
            version_number = version.version_number
            version_code = version.code
            
            # 删除MinIO中的文件
            bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
            prefix = f"{model_group}/{model_name}/{version_number}/"
            
            try:
                # 列出该版本目录下的所有文件
                objects = list(minio_client.list_objects(bucket_name, prefix=prefix))
                
                # 如果有文件，删除它们
                if objects:
                    logger.info(f"正在删除版本 {version_code} (ID: {version.id}) 的 {len(objects)} 个文件")
                    for obj in objects:
                        minio_client.remove_object(bucket_name, obj.object_name)
                        logger.info(f"已删除文件: {obj.object_name}")
            except Exception as e:
                logger.error(f"删除版本文件失败: {str(e)}")
                # 继续执行，即使文件删除失败也删除数据库记录
            
            # 删除版本
            version.delete()
            
            # 记录删除日志
            logger.info(f"已删除模型 {model_name} 的版本 {version_number} (ID: {version.id})")
            
            return DetailResponse(data=None, msg=f"已删除版本")
        except Exception as e:
            logger.error(f"删除版本失败: {str(e)}")
            return ErrorResponse(msg=f"删除版本失败: {str(e)}")
        
    @action(methods=["POST"], detail=False)
    def update_by_code(self, request):
        """
        根据code值修改模型版本状态和文件路径
        支持请求格式：
        {
            "version_code": "resnet_v1",
            "status": "train_done",  # 可选，不传则不修改状态
            "model_weights_path": "group/model/v1.0/weights.pth",  # 可选
            "model_docs_path": "group/model/v1.0/README.md",  # 可选
            "test_report_path": "group/model/v1.0/test_report.pdf",  # 可选
            "test_failure_reason": "测试失败原因"  # 可选，仅在status为test_fail时有效
        }
        """
        try:
            version_code = request.data.get('version_code')
            if not version_code:
                return ErrorResponse(msg="版本编码不能为空")
                
            # 通过code获取版本
            try:
                version = ModelVersion.objects.get(code=version_code)
            except ModelVersion.DoesNotExist:
                return ErrorResponse(msg=f"版本编码 {version_code} 不存在")
            
            # 更新状态
            status = request.data.get('status')
            if status and status in dict(ModelVersion.STATUS_CHOICES).keys():
                version.status = status
                
            # 更新文件路径
            model_weights_path = request.data.get('model_weights_path')
            if model_weights_path:
                version.model_weights_path = model_weights_path
                
            model_docs_path = request.data.get('model_docs_path')
            if model_docs_path:
                version.model_docs_path = model_docs_path
                
            test_report_path = request.data.get('test_report_path')
            if test_report_path:
                version.test_report_path = test_report_path
                
            # 更新测试失败原因
            test_failure_reason = request.data.get('test_failure_reason')
            if test_failure_reason and status == 'test_fail':
                version.test_failure_reason = test_failure_reason
                
            # 保存更新
            version.save()
            
            # 返回更新后的版本信息
            serializer = self.get_serializer(version)
            return DetailResponse(data=serializer.data, msg="更新版本成功")
        except Exception as e:
            logger.error(f"更新版本失败: {str(e)}")
            return ErrorResponse(msg=f"更新版本失败: {str(e)}")
    
    @action(methods=["POST"], detail=False)
    def delete_by_code(self, request):
        """
        根据code值删除模型版本
        支持请求格式：
        {
            "version_code": "resnet_v1"
        }
        """
        try:
            version_code = request.data.get('version_code')
            if not version_code:
                return ErrorResponse(msg="版本编码不能为空")
                
            # 通过code获取版本
            try:
                version = ModelVersion.objects.get(code=version_code)
            except ModelVersion.DoesNotExist:
                return ErrorResponse(msg=f"版本编码 {version_code} 不存在")
            
            # 获取版本信息用于日志记录
            model_name = version.model.name
            model_group = version.model.group
            version_number = version.version_number
            
            # 删除MinIO中的文件
            bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
            prefix = f"{model_group}/{model_name}/{version_number}/"
            
            try:
                # 列出该版本目录下的所有文件
                objects = list(minio_client.list_objects(bucket_name, prefix=prefix))
                
                # 如果有文件，删除它们
                if objects:
                    logger.info(f"正在删除版本 {version_code} 的 {len(objects)} 个文件")
                    for obj in objects:
                        minio_client.remove_object(bucket_name, obj.object_name)
                        logger.info(f"已删除文件: {obj.object_name}")
            except Exception as e:
                logger.error(f"删除版本文件失败: {str(e)}")
                # 继续执行，即使文件删除失败也删除数据库记录
            
            # 删除版本
            version.delete()
            
            # 记录删除日志
            logger.info(f"已删除模型 {model_name} 的版本 {version_number}(code: {version_code})")
            
            return DetailResponse(data=None, msg=f"已删除版本 {version_code}")
        except Exception as e:
            logger.error(f"删除版本失败: {str(e)}")
            return ErrorResponse(msg=f"删除版本失败: {str(e)}")
    
    @action(methods=["POST"], detail=True)
    def get_upload_url(self, request, pk=None):
        """
        获取版本文件上传URL
        """
        version = self.get_object()
        filename = request.data.get('filename')
        content_type = request.data.get('content_type', 'application/octet-stream')
        file_type = request.data.get('file_type', '')  # 文件类型: model_weights, model_docs, test_report
        
        # 检查是否需要分片上传
        use_multipart = request.data.get('use_multipart', False)
        file_size = request.data.get('file_size', 0)
        chunk_size = request.data.get('chunk_size', 5 * 1024 * 1024)  # 默认5MB一片

        if not filename:
            return ErrorResponse(msg="文件名不能为空")
        try:
            # 构建文件路径
            model = version.model
            object_name = f"{model.group}/{model.name}/{version.version_number}/{filename}"
            bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
            
            # 确保存储桶存在
            minio_client.ensure_bucket_exists(bucket_name)
            
            # 如果需要分片上传
            if use_multipart and int(file_size) > chunk_size:
                logger.info(f"需要分片上传，文件大小: {file_size}，分片大小: {chunk_size}")
                # 计算分片数量
                total_parts = (int(file_size) + chunk_size - 1) // chunk_size
                
                # 获取所有分片的上传URL
                result = minio_client.get_multipart_upload_urls(
                    bucket_name, 
                    object_name, 
                    total_parts
                )
                
                if result:
                    # 保存上传ID和分片对象名称到会话中，用于后续合并
                    request.session[f"upload_{result['upload_id']}"] = {
                        'object_name': result['object_name'],
                        'part_object_names': result['part_object_names'],
                        'file_type': file_type,
                        'version_id': version.id
                    }
                    
                    return DetailResponse(
                        data={
                            "upload_id": result['upload_id'],
                            "part_urls": result['part_urls'],
                            "object_name": result['object_name'],
                            "total_parts": total_parts
                        }, 
                        msg="获取分片上传URL成功"
                    )
                return ErrorResponse(msg="获取分片上传URL失败")
            else:
                # 普通上传
                url = minio_client.get_presigned_put_url(bucket_name, object_name)
                if url:
                    return DetailResponse(data={"url": url, "object_name": object_name}, msg="获取上传URL成功")
                return ErrorResponse(msg="获取上传URL失败")
            
        except Exception as e:
            return ErrorResponse(msg=f"获取上传URL失败: {str(e)}")
            
    @action(methods=["POST"], detail=True)
    def complete_multipart_upload(self, request, pk=None):
        """
        完成分片上传
        """
        version = self.get_object()
        upload_id = request.data.get('upload_id')
        object_name = request.data.get('object_name')
        parts = request.data.get('parts', [])  # [{'part_number': number, 'etag': string}, ...]
        file_type = request.data.get('file_type', '')  # 文件类型: model_weights, model_docs, test_report
        
        if not upload_id or not object_name:
            return ErrorResponse(msg="上传ID和对象名称不能为空")
            
        try:
            # 从会话中获取分片信息
            upload_info = request.session.get(f"upload_{upload_id}")
            if not upload_info:
                return ErrorResponse(msg="无法找到上传信息，可能已过期")
            
            # 确保对象名称匹配
            if upload_info['object_name'] != object_name:
                return ErrorResponse(msg="对象名称不匹配")
                
            # 获取分片对象名称
            part_object_names = upload_info['part_object_names']
            
            # 准备分片信息
            formatted_parts = []
            for part in parts:
                part_number = part['part_number']
                if str(part_number) in part_object_names:
                    formatted_parts.append({
                        'part_number': part_number,
                        'part_object_name': part_object_names[str(part_number)]
                    })
            
            bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
            
            # 完成分片上传
            success = minio_client.complete_multipart_upload(
                bucket_name, 
                object_name, 
                upload_id, 
                formatted_parts
            )
            
            if success:
                # 更新版本文件路径
                if file_type == 'model_weights':
                    version.model_weights_path = object_name
                elif file_type == 'model_docs':
                    version.model_docs_path = object_name
                elif file_type == 'test_report':
                    version.test_report_path = object_name
                
                version.save()
                
                # 清理会话中的上传信息
                if f"upload_{upload_id}" in request.session:
                    del request.session[f"upload_{upload_id}"]
                    request.session.modified = True
                
                return DetailResponse(data={"object_name": object_name}, msg="分片上传完成")
            else:
                return ErrorResponse(msg="完成分片上传失败")
                
        except Exception as e:
            return ErrorResponse(msg=f"完成分片上传失败: {str(e)}")
            
    @action(methods=["POST"], detail=True)
    def abort_multipart_upload(self, request, pk=None):
        """
        取消分片上传
        """
        upload_id = request.data.get('upload_id')
        object_name = request.data.get('object_name')
        
        if not upload_id or not object_name:
            return ErrorResponse(msg="上传ID和对象名称不能为空")
            
        try:
            # 从会话中获取分片信息
            upload_info = request.session.get(f"upload_{upload_id}")
            if not upload_info:
                return ErrorResponse(msg="无法找到上传信息，可能已过期")
            
            # 确保对象名称匹配
            if upload_info['object_name'] != object_name:
                return ErrorResponse(msg="对象名称不匹配")
                
            # 获取分片对象名称
            part_object_names = upload_info['part_object_names']
            
            # 准备分片信息
            parts = []
            for part_number, part_object_name in part_object_names.items():
                parts.append({
                    'part_number': int(part_number),
                    'part_object_name': part_object_name
                })
            
            bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
            
            # 取消分片上传
            success = minio_client.abort_multipart_upload(
                bucket_name, 
                object_name, 
                upload_id,
                parts
            )
            
            # 清理会话中的上传信息
            if f"upload_{upload_id}" in request.session:
                del request.session[f"upload_{upload_id}"]
                request.session.modified = True
            
            if success:
                return DetailResponse(msg="已取消分片上传")
            else:
                return ErrorResponse(msg="取消分片上传失败")
                
        except Exception as e:
            return ErrorResponse(msg=f"取消分片上传失败: {str(e)}")
    
    @action(methods=["GET"], detail=True)
    def download_all(self, request, pk=None):
        """
        打包下载版本的所有文件
        """
        version = self.get_object()
        
        # 构建版本文件目录路径
        model = version.model
        bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
        prefix = f"{model.group}/{model.name}/{version.version_number}/"
        
        # 列出该目录下的所有文件
        files = minio_client.list_objects(bucket_name, prefix=prefix)
        files = list(files)
        
        if not files:
            return ErrorResponse(msg="该版本没有可下载的文件")
        
        # 构建ZIP文件名和路径
        zip_filename = f"{model.name}_{version.version_number}.zip"
        zip_object_name = f"{model.group}/{model.name}/packages/{zip_filename}"
        
        # 创建临时文件用于存储ZIP
        with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
            temp_path = temp_file.name
        
        try:
            # 创建ZIP文件
            with zipfile.ZipFile(temp_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file in files:
                    file_path = file.object_name
                    
                    # 从MinIO获取文件
                    file_data = minio_client.get_object(bucket_name, file_path)
                    if file_data:
                        # 获取文件的相对路径
                        if file_path.startswith(prefix):
                            # 提取版本路径后的部分作为相对路径
                            relative_path = file_path[len(prefix):]
                            # 使用相对路径添加到ZIP文件
                            zipf.writestr(relative_path, file_data)
            
            # 上传ZIP文件到MinIO
            with open(temp_path, 'rb') as zip_file:
                minio_client.upload_data(
                    bucket_name, 
                    zip_object_name, 
                    zip_file.read(),
                    content_type='application/zip'
                )
            
            # 删除临时文件
            os.unlink(temp_path)
            
            # 获取下载URL
            download_url = minio_client.get_object_url(bucket_name, zip_object_name)
            if download_url:
                return DetailResponse(
                    data={"url": download_url, "filename": zip_filename}, 
                    msg="获取下载链接成功"
                )
            else:
                return ErrorResponse(msg="获取下载链接失败")
                
        except Exception as e:
            logger.error(f"打包文件失败: {str(e)}")
            return ErrorResponse(msg=f"打包文件失败: {str(e)}")

    @action(methods=["POST"], detail=False)
    def delete_file_from_minio(self, request):
        file_path = request.data.get('file_path')
        if not file_path:
            return ErrorResponse(msg="文件路径不能为空")
        from utils.minio_storage import minio_client
        from django.conf import settings
        bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
        print(bucket_name,file_path)
        try:
            minio_client.remove_object(bucket_name, file_path)
            return SuccessResponse(msg="文件删除成功")
        except Exception as e:
            return ErrorResponse(msg=f"文件删除失败: {str(e)}")
