[{"model": "app_menu.menu", "pk": 100, "fields": {"menu_name": "模型管理", "path": "model", "parent": null, "sort": 10, "is_hide": "0", "component": "Layout", "icon": "Model", "menu_type": "M", "create_datetime": "2023-01-01 00:00:00", "update_datetime": "2023-01-01 00:00:00", "status": "0"}}, {"model": "app_menu.menu", "pk": 101, "fields": {"menu_name": "模型列表", "path": "list", "parent": 100, "sort": 1, "is_hide": "0", "component": "/model/index", "icon": "List", "menu_type": "C", "create_datetime": "2023-01-01 00:00:00", "update_datetime": "2023-01-01 00:00:00", "status": "0"}}, {"model": "app_menu.menu", "pk": 102, "fields": {"menu_name": "模型详情", "path": "detail/:id", "parent": 100, "sort": 2, "is_hide": "1", "component": "/model/detail", "icon": "InfoFilled", "menu_type": "C", "create_datetime": "2023-01-01 00:00:00", "update_datetime": "2023-01-01 00:00:00", "status": "0"}}]