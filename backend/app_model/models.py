from django.db import models
from utils.models import BaseModel
from app_user.models import Users
from app_dataset.models import AIDataset

class ModelCategory(BaseModel):
    """
    模型分类表
    """
    name = models.CharField(null=False, blank=False, max_length=100, verbose_name="分类名称")
    code = models.CharField(null=True, blank=True, max_length=100, verbose_name="分类编码")
    order = models.IntegerField(default=1, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    parent = models.ForeignKey(
        to='self',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        verbose_name='父级分类',
        db_constraint=False,
        related_name='subcat',
        help_text="父级分类",
    )
    
    class Meta:
        verbose_name = '模型分类'
        verbose_name_plural = verbose_name
        ordering = ['order']
    
    def __str__(self):
        return f"{self.name}"

class AIModel(BaseModel):
    """
    模型表
    """
    name = models.CharField(max_length=200, verbose_name="模型名称")
    code = models.Char<PERSON>ield(max_length=100, verbose_name="模型编码", help_text="模型唯一编码，用于与训练平台和测试平台交互", unique=True, null=True, blank=True)
    group = models.CharField(max_length=100, help_text="模型所属项目组", default='qianlan')
    description = models.TextField(verbose_name="模型描述")
    embedding_vector = models.BinaryField(null=True, blank=True, verbose_name="嵌入特征向量")
    datasets = models.ManyToManyField(
        to=AIDataset,
        verbose_name='关联数据集',
        related_name='models',
        help_text="模型使用的数据集，可选择多个",
        blank=True
    )
    categories = models.ManyToManyField(
        to=ModelCategory,
        verbose_name='模型分类',
        related_name='models',
        help_text="模型分类，可以选择多个类别"
    )
    minio_path = models.CharField(
        max_length=255,
        help_text="MinIO 中的存储路径，例如 apple/DepthPro/",
        null=True,
        blank=True
    )
    @property
    def current_status(self):
        """获取模型的当前状态（最新版本的状态）"""
        latest_version = self.versions.order_by('-create_datetime').first()
        if latest_version:
            return latest_version.status
        return None

    @property
    def current_docker_image(self):
        """获取模型的当前Docker镜像（最新版本的Docker镜像）"""
        latest_version = self.versions.order_by('-create_datetime').first()
        if latest_version:
            return latest_version.docker_image
        return None
    
    class Meta:
        verbose_name = 'AI模型'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
    
    def __str__(self):
        return f"{self.group}-{self.name}"

class ModelComment(BaseModel):
    """模型评论"""
    model = models.ForeignKey(AIModel, related_name='comments', on_delete=models.CASCADE, verbose_name='所属模型')
    content = models.TextField(verbose_name='评论内容')
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, verbose_name='父评论')
    user = models.ForeignKey(Users, related_name='comments', on_delete=models.CASCADE, verbose_name='发表用户')
    
    class Meta:
        verbose_name = '模型评论'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
    
    def __str__(self):
        return f"{self.user.username} 的评论: {self.content[:30]}"

class ModelVersion(BaseModel):
    """
    模型版本表
    """
    STATUS_CHOICES = (
        ('dev_done', '开发完毕待训练'),
        ('train_done', '训练完毕待测试'),
        ('test_pass', '测试通过'),
        ('test_fail', '测试未通过'),
        ('online', '已上架'),
        ('offline', '已下架')
    )
    model = models.ForeignKey(
        AIModel, 
        on_delete=models.CASCADE, 
        related_name='versions', 
        verbose_name='所属模型'
    )
    code = models.CharField(max_length=100, verbose_name="版本编码", help_text="版本唯一编码，例如resnet_v1，用于与训练平台和测试平台交互", null=True, blank=True)
    version_number = models.CharField(max_length=100, help_text="模型版本号", default='v1.0')
    description = models.TextField(verbose_name="版本描述", blank=True, null=True)
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='dev_done',
        verbose_name="版本状态"
    )
    docker_image = models.CharField(
        max_length=255,
        help_text="Docker 镜像名称，例如 apple/DepthPro:latest",
        null=True,
        blank=True
    )
    model_weights_path = models.CharField(
        max_length=255,
        help_text="权重文件路径，例如 apple/DepthPro/weights/model.pth",
        null=True,
        blank=True
    )
    model_docs_path = models.CharField(
        max_length=255,
        help_text="文档文件路径，例如 apple/DepthPro/docs/model.md",
        null=True,
        blank=True
    )
    test_report_path = models.CharField(
        max_length=255,
        help_text="测试报告文件路径，例如 apple/DepthPro/test/model.md",
        null=True,
        blank=True
    )
    test_failure_reason = models.TextField(
        help_text="测试不通过原因",
        null=True,
        blank=True
    )
    creator = models.ForeignKey(
        'app_user.Users',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='创建者',
        related_name='created_versions'
    )
    
    class Meta:
        verbose_name = '模型版本'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
        unique_together = ('model', 'version_number')
    
    def __str__(self):
        return f"{self.model.name} {self.version_number}"