import os
from django.db import transaction
from .models import ModelCategory

def init_category_data():
    """
    初始化模型分类数据
    """
    task_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'task.txt')
    
    if not os.path.exists(task_file):
        print(f"File not found: {task_file}")
        return
    
    try:
        with open(task_file, 'r', encoding='utf-8') as f:
            content = f.readlines()
        
        # 清理空行和空格
        content = [line.strip() for line in content if line.strip()]
        
        # 创建顶级分类
        current_parent = None
        current_parent_code = ""
        
        with transaction.atomic():
            for line in content:
                if not line.startswith(" "):
                    # 顶级分类
                    category, created = ModelCategory.objects.update_or_create(
                        name=line,
                        defaults={
                            "code": line,
                            "parent": None,
                            "is_active": True,
                            "order": 1
                        }
                    )
                    current_parent = category
                    current_parent_code = line
                else:
                    # 子分类
                    sub_category_name = line.strip()
                    sub_category_code = f"{current_parent_code}_{sub_category_name}"
                    
                    ModelCategory.objects.update_or_create(
                        name=sub_category_name,
                        defaults={
                            "code": sub_category_code,
                            "parent": current_parent,
                            "is_active": True,
                            "order": 1
                        }
                    )
            
        print("Category data initialization completed!")
        
    except Exception as e:
        print(f"Error initializing category data: {str(e)}")
        raise e 