from django.apps import AppConfig
import threading
import logging
import os

logger = logging.getLogger(__name__)

class AppModelConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'app_model'
    verbose_name = 'AI模型管理'
    
    def ready(self):
        """
        应用就绪时导入信号处理器和初始化向量服务
        """
        from django.conf import settings

        # 检查是否在主进程中运行 (Django runserver 会设置 RUN_MAIN='true')
        # 并且应用配置已完成
        if not (os.environ.get('RUN_MAIN') == 'true' and settings.configured):
            if os.environ.get('RUN_MAIN') != 'true':
                logger.info("Not in main process (RUN_MAIN is not 'true' or not set), skipping vector service initialization in AppConfig.ready().")
            if not settings.configured:
                logger.info("Django settings not fully configured, skipping vector service initialization.")
            return

        # 从配置中获取模型来源和路径
        vector_config = getattr(settings, 'VECTOR_SERVICE', {})

        # 检查语义检索功能是否启用
        if not vector_config.get('ENABLE_SEMANTIC_SEARCH', True):
            logger.info("语义检索功能已在设置中禁用，跳过向量服务初始化")
            return
            
        # 导入信号处理器
        from . import signals
        
        # 延迟加载向量服务，避免阻塞应用启动
        def init_vector_service_task():
            try:
                from .vector_service import vector_service
                
                # 从配置中获取模型来源和路径
                model_source = vector_config.get('MODEL_SOURCE', 'auto')
                local_model_dir = vector_config.get('LOCAL_MODEL_DIR', None)
                hub_model_name = vector_config.get('MODEL_NAME', 'BAAI/bge-m3')
                
                # 设置向量服务参数
                vector_service.model_source = model_source
                if local_model_dir:
                    vector_service.custom_model_path = local_model_dir
                if hub_model_name:
                    vector_service.custom_hub_model = hub_model_name
                
                logger.info(f"开始初始化向量服务 (模型来源: {model_source})...")
                vector_service.initialize()
                
                # 视情况决定是否在启动时重建索引
                if vector_config.get('REBUILD_INDEX_ON_STARTUP', False):
                   logger.info("开始在启动时重建向量索引...")
                   vector_service.rebuild_index()
                   logger.info("启动时重建向量索引完成。")

            except Exception as e:
                logger.error(f"初始化向量服务后台任务失败: {e}", exc_info=True)
        
        logger.info("将在后台线程中启动向量服务初始化...")
        # 在后台线程中初始化
        thread = threading.Thread(target=init_vector_service_task, daemon=True)
        thread.start()
