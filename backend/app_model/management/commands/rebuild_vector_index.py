from django.core.management.base import BaseCommand
import logging
from app_model.vector_service import vector_service
from app_model.models import AIModel
import os
from django.conf import settings

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '重建模型向量索引'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--model-source',
            choices=['local', 'hub'],
            default='auto',
            help='指定模型来源: local(本地), hub(Hugging Face), auto(自动判断)',
        )
        parser.add_argument(
            '--model-path',
            type=str,
            help='指定本地模型路径，仅在使用local源时有效',
        )
        parser.add_argument(
            '--hub-model',
            type=str,
            default=settings.VECTOR_SERVICE.get('MODEL_NAME', 'BAAI/bge-m3'),
            help='指定Hugging Face模型名称，仅在使用hub源时有效',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.WARNING('开始初始化向量服务...'))
        
        model_source = options['model_source']
        model_path = options.get('model_path')
        hub_model = options.get('hub_model')
        
        # 设置模型来源信息到向量服务
        if model_source != 'auto':
            vector_service.model_source = model_source
        if model_path:
            model_path = os.path.abspath(model_path)
            vector_service.custom_model_path = model_path
        if hub_model:
            vector_service.custom_hub_model = hub_model
            
        # 显示配置信息
        self.stdout.write(self.style.SUCCESS(f'模型来源: {model_source}'))
        if model_path:
            self.stdout.write(self.style.SUCCESS(f'本地模型路径: {model_path}'))
        if hub_model:
            self.stdout.write(self.style.SUCCESS(f'Hugging Face模型: {hub_model}'))
        
        # 初始化向量服务
        vector_service.initialize()
        
        if vector_service.model is None:
            self.stdout.write(self.style.ERROR('向量模型初始化失败，无法重建索引'))
            return
            
        self.stdout.write(self.style.SUCCESS('向量模型初始化成功，开始重建索引'))
        
        # 获取所有有描述的模型数量
        total = AIModel.objects.filter(description__isnull=False).exclude(description="").count()
        self.stdout.write(self.style.SUCCESS(f'找到 {total} 个模型需要建立索引'))
        
        # 重建索引
        vector_service.rebuild_index()
        
        # 显示结果
        if vector_service.faiss_index is not None:
            self.stdout.write(self.style.SUCCESS(
                f'向量索引重建完成，共索引了 {vector_service.faiss_index.ntotal} 个模型'
            ))
        else:
            self.stdout.write(self.style.ERROR('向量索引重建失败')) 