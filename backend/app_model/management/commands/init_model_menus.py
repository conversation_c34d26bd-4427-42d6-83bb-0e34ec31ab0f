from django.core.management.base import BaseCommand
import os
from django.core.management import call_command
from pathlib import Path

class Command(BaseCommand):
    help = '初始化模型管理菜单数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始初始化模型管理菜单数据...'))
        
        # 获取当前app的fixtures目录
        app_dir = Path(__file__).resolve().parent.parent.parent
        fixtures_dir = os.path.join(app_dir, 'fixtures')
        
        # 加载菜单数据
        menu_fixture = os.path.join(fixtures_dir, 'menu.json')
        if os.path.exists(menu_fixture):
            call_command('loaddata', menu_fixture)
            self.stdout.write(self.style.SUCCESS('菜单数据初始化完成!'))
        else:
            self.stdout.write(self.style.ERROR(f'菜单数据文件不存在: {menu_fixture}')) 