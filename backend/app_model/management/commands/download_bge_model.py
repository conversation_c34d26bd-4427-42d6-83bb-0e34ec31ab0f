from django.core.management.base import BaseCommand
import os
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '下载BGE-M3模型到本地'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--model-name',
            type=str,
            default=settings.VECTOR_SERVICE.get('MODEL_NAME', 'BAAI/bge-m3'),
            help='要下载的Hugging Face模型名称',
        )
        parser.add_argument(
            '--output-dir',
            type=str,
            default=settings.VECTOR_SERVICE.get('LOCAL_MODEL_DIR'),
            help='模型保存的本地路径',
        )
        
    def handle(self, *args, **options):
        model_name = options['model_name']
        output_dir = options['output_dir']
        
        if not output_dir:
            output_dir = os.path.join(settings.BASE_DIR, 'app_model', 'BGE_Weight', *model_name.split('/'))
        
        output_dir = os.path.abspath(output_dir)
        parent_dir = os.path.dirname(output_dir)
        
        # 确保父目录存在
        if not os.path.exists(parent_dir):
            os.makedirs(parent_dir, exist_ok=True)
            
        self.stdout.write(self.style.SUCCESS(f"开始下载模型: {model_name}"))
        self.stdout.write(self.style.SUCCESS(f"保存路径: {output_dir}"))
        
        try:
            # 从Hugging Face下载模型
            from transformers import AutoModel, AutoTokenizer
            
            # 下载tokenizer
            self.stdout.write("下载tokenizer...")
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            
            # 下载模型
            self.stdout.write("下载模型...")
            model = AutoModel.from_pretrained(model_name)
            
            # 保存到本地
            self.stdout.write("保存模型到本地...")
            model.save_pretrained(output_dir)
            tokenizer.save_pretrained(output_dir)
            
            self.stdout.write(self.style.SUCCESS(f"模型下载完成，保存在: {output_dir}"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"下载模型失败: {str(e)}"))
            
        # 说明如何在设置中使用该模型
        self.stdout.write(self.style.WARNING("如何使用:"))
        self.stdout.write(f"1. 在settings.py中将VECTOR_SERVICE['MODEL_SOURCE']设置为'local'")
        self.stdout.write(f"2. 将VECTOR_SERVICE['LOCAL_MODEL_DIR']设置为'{output_dir}'")
        self.stdout.write(f"3. 重启应用或运行 python manage.py rebuild_vector_index") 