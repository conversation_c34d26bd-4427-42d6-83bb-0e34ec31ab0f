from django.core.management.base import BaseCommand
from app_model.models import AIModel
from django.db import transaction
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '将模型从单一类别迁移到多类别关系'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始迁移模型类别数据...'))
        
        # 计数器
        total_models = AIModel.objects.count()
        migrated_count = 0
        skip_count = 0
        error_count = 0
        
        # 获取所有模型
        models = AIModel.objects.all()
        
        for model in models:
            try:
                with transaction.atomic():
                    # 如果已经有新的categories关系，则跳过
                    if model.categories.exists():
                        skip_count += 1
                        continue
                    
                    # 如果有旧的category关联，则添加到新的categories关系中
                    if model.category:
                        model.categories.add(model.category)
                        self.stdout.write(f'迁移模型 {model.id}: {model.name} 到多类别关系')
                        migrated_count += 1
                    else:
                        self.stdout.write(f'模型 {model.id}: {model.name} 没有旧的类别关联')
                        skip_count += 1
            except Exception as e:
                error_count += 1
                self.stdout.write(self.style.ERROR(f'迁移模型 {model.id}: {model.name} 时出错: {str(e)}'))
        
        # 打印结果
        self.stdout.write(self.style.SUCCESS(f'迁移完成. 总计: {total_models}, 成功: {migrated_count}, 跳过: {skip_count}, 错误: {error_count}')) 