import numpy as np
import faiss
import pickle
import os
import logging
from django.conf import settings
import threading

logger = logging.getLogger(__name__)

class VectorService:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(VectorService, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.model = None
        self.faiss_index = None
        self.model_id_map = []  # 存储模型ID与索引的映射
        self.similarity_threshold = settings.VECTOR_SERVICE.get('SIMILARITY_THRESHOLD', -99999999999)  # 相似度阈值
        
        # 模型加载相关参数
        self.model_source = 'auto'  # 可选: 'auto', 'local', 'hub'
        self.custom_model_path = None  # 自定义本地模型路径
        self.custom_hub_model = None  # 自定义Hugging Face模型名称
        
        self._initialized = True
        
        # 初始化路径
        self.index_path = os.path.join(settings.BASE_DIR, 'app_model', 'vector_data')
        os.makedirs(self.index_path, exist_ok=True)
        self.faiss_index_path = os.path.join(self.index_path, 'faiss_index.bin')
        self.model_map_path = os.path.join(self.index_path, 'model_id_map.pkl')
    
    def initialize(self):
        """初始化向量服务，加载模型和FAISS索引"""
        # 新增：检查模型是否已经加载
        if hasattr(self, 'model') and self.model is not None:
            logger.info("向量模型已成功初始化，跳过重复加载。")
            return

        try:
            # 延迟导入，避免启动时就加载大模型
            from FlagEmbedding import BGEM3FlagModel
            
            # 判断是否有GPU可用
            import torch
            device = 'cuda:0' if torch.cuda.is_available() else 'cpu'
            
            # 构建默认模型路径
            import os
            from django.conf import settings
            default_local_path = os.path.join(settings.BASE_DIR, 'app_model', 'BGE_Weight', 'BAAI', 'bge-m3')
            default_local_path = os.path.abspath(default_local_path)
            
            # 使用自定义路径（如果有）
            local_model_path = self.custom_model_path or default_local_path
            
            # 从配置中获取模型名称
            hub_model_name = self.custom_hub_model or settings.VECTOR_SERVICE.get('MODEL_NAME', 'BAAI/bge-m3')
            
            # 根据模型来源加载模型
            if self.model_source == 'local':
                # 强制使用本地模型
                if not os.path.exists(local_model_path):
                    logger.error(f"本地模型路径不存在: {local_model_path}")
                    raise FileNotFoundError(f"本地模型路径不存在: {local_model_path}")
                    
                logger.info(f"正在加载本地BGE-M3模型，使用设备: {device}，模型路径: {local_model_path}")
                self.model = BGEM3FlagModel(
                    local_model_path,
                    use_fp16=True,
                    pooling_method='cls',
                    devices=[device]
                )
                logger.info("本地BGE-M3模型加载完成")
                
            elif self.model_source == 'hub':
                # 强制使用Hugging Face Hub模型
                logger.info(f"正在从Hugging Face Hub加载模型: {hub_model_name}, 设备: {device}")
                self.model = BGEM3FlagModel(
                    hub_model_name,
                    use_fp16=True,
                    pooling_method='cls',
                    devices=[device]
                )
                logger.info("从Hugging Face Hub加载BGE-M3模型完成")
                
            else:
                # 自动模式：先尝试本地模型，如果失败则使用Hugging Face
                if os.path.exists(local_model_path):
                    try:
                        logger.info(f"尝试加载本地BGE-M3模型，使用设备: {device}，模型路径: {local_model_path}")
                        self.model = BGEM3FlagModel(
                            local_model_path,
                            use_fp16=True,
                            pooling_method='cls',
                            devices=[device]
                        )
                        logger.info("本地BGE-M3模型加载完成")
                    except Exception as e:
                        logger.warning(f"加载本地模型失败: {str(e)}，尝试从Hugging Face Hub加载")
                        self.model = BGEM3FlagModel(
                            hub_model_name,
                            use_fp16=True,
                            pooling_method='cls',
                            devices=[device]
                        )
                        logger.info("从Hugging Face Hub加载BGE-M3模型完成")
                else:
                    # 本地模型不存在，直接从Hugging Face加载
                    logger.info(f"本地模型不存在，从Hugging Face Hub加载模型: {hub_model_name}")
                    self.model = BGEM3FlagModel(
                        hub_model_name,
                        use_fp16=True,
                        pooling_method='cls',
                        devices=[device]
                    )
                    logger.info("从Hugging Face Hub加载BGE-M3模型完成")
            
            # 尝试加载已有的FAISS索引
            self.load_faiss_index()
            
        except Exception as e:
            logger.error(f"初始化向量服务失败: {str(e)}")
            self.model = None
            self.faiss_index = None
    
    def load_faiss_index(self):
        """加载FAISS索引"""
        try:
            if os.path.exists(self.faiss_index_path) and os.path.exists(self.model_map_path):
                logger.info("正在加载FAISS索引和模型映射...")
                self.faiss_index = faiss.read_index(self.faiss_index_path)
                with open(self.model_map_path, 'rb') as f:
                    self.model_id_map = pickle.load(f)
                logger.info(f"FAISS索引加载成功，包含{self.faiss_index.ntotal}个向量")
            else:
                logger.info("未找到现有FAISS索引，将创建新索引")
                # 创建空的FAISS索引
                self.create_empty_index()
        except Exception as e:
            logger.error(f"加载FAISS索引失败: {str(e)}")
            self.create_empty_index()
    
    def create_empty_index(self):
        """创建空的FAISS索引"""
        # 创建一个1024维的L2距离索引
        self.faiss_index = faiss.IndexFlatL2(1024)
        self.model_id_map = []
        
    def rebuild_index(self):
        """重建整个索引"""
        logger.info("正在重建FAISS索引...")
        from .models import AIModel
        
        # 创建空索引
        self.create_empty_index()
        
        # 获取所有有描述的模型
        models = AIModel.objects.filter(description__isnull=False).exclude(description="")
        model_count = models.count()
        logger.info(f"找到{model_count}个模型待索引")
        
        if model_count == 0:
            logger.info("没有模型需要索引")
            return
        
        # 批量生成嵌入向量并更新
        batch_size = 32
        for i in range(0, model_count, batch_size):
            batch_models = models[i:i+batch_size]
            self.update_models_embedding(batch_models, rebuild_index=False)
            logger.info(f"已处理 {min(i+batch_size, model_count)}/{model_count} 个模型")
        
        # 保存索引
        self.save_faiss_index()
        logger.info("FAISS索引重建完成")
    
    def update_models_embedding(self, models, rebuild_index=True):
        """更新模型的嵌入向量"""
        if self.model is None:
            logger.error("BGE-M3模型未初始化，无法生成嵌入向量")
            return
        
        # 构建包含名称、分类和描述的文本               
        texts_to_embed = []
        for model in models:
            # 获取模型分类名称
            categories = [cat.name for cat in model.categories.all()]
            category_text = "，".join(categories) if categories else ""
            
            # 构建完整文本：名称 + 分类 + 描述
            full_text = f"名称：{model.name}\n"
            if category_text:
                full_text += f"分类：{category_text}\n"
            full_text += f"描述：{model.description}"
            
            texts_to_embed.append(full_text)
        
        # 生成嵌入向量
        embeddings = self.model.encode(
            texts_to_embed,
            return_dense=True,
            return_sparse=False,
            return_colbert_vecs=False,
        )
        
        dense_vecs = embeddings["dense_vecs"]
        
        # 更新模型的嵌入向量字段，并添加到FAISS索引
        for i, model in enumerate(models):
            # 将NumPy数组转换为字节数据
            vector_bytes = pickle.dumps(dense_vecs[i])
            
            # 更新数据库中的向量
            model.embedding_vector = vector_bytes
            model.save(update_fields=['embedding_vector'])
            
            # 将向量添加到FAISS索引
            if self.faiss_index is not None:
                # 归一化向量
                normalized_vec = dense_vecs[i].reshape(1, -1).astype(np.float32)
                
                # 添加到索引
                self.faiss_index.add(normalized_vec)
                self.model_id_map.append(model.id)
        
        # 如果需要重建索引，则保存
        if rebuild_index:
            self.save_faiss_index()
    
    def save_faiss_index(self):
        """保存FAISS索引和模型ID映射"""
        if self.faiss_index is None:
            logger.error("FAISS索引未初始化，无法保存")
            return
            
        try:
            # 保存FAISS索引
            faiss.write_index(self.faiss_index, self.faiss_index_path)
            
            # 保存模型ID映射
            with open(self.model_map_path, 'wb') as f:
                pickle.dump(self.model_id_map, f)
                
            logger.info(f"FAISS索引保存成功，包含{self.faiss_index.ntotal}个向量")
        except Exception as e:
            logger.error(f"保存FAISS索引失败: {str(e)}")
    
    def search_similar_models(self, query_text, top_k=10):
        """
        搜索与查询文本相似的模型
        
        参数:
            query_text: 查询文本
            top_k: 返回的结果数量
            
        返回:
            [(model_id, similarity_score), ...]
        """
        if self.model is None or self.faiss_index is None:
            logger.error("向量服务未初始化，无法执行搜索")
            return []
        
        if self.faiss_index.ntotal == 0:
            logger.warning("FAISS索引为空，无法执行搜索")
            return []
        
        try:
            # 处理查询文本，增加提示以提高匹配效果
            enhanced_query = f"查询：{query_text}"
            
            # 生成查询文本的嵌入向量
            query_embedding = self.model.encode(
                [enhanced_query],
                return_dense=True,
                return_sparse=False,
                return_colbert_vecs=False,
            )
            
            query_vector = query_embedding["dense_vecs"][0].reshape(1, -1).astype(np.float32)
            print(query_vector.shape)  # 输出嵌入向量的形状，便于调试
            
            # 在FAISS索引中搜索
            k = min(top_k, self.faiss_index.ntotal)
            distances, indices = self.faiss_index.search(query_vector, k)
            
            # 计算相似度分数 (将L2距离转换为相似度)
            # 我们使用一个简单的转换: similarity = 1 / (1 + distance)
            similarities = 1 / (1 + distances[0])
            
            # 组合模型ID和相似度分数
            results = []
            for i in range(len(indices[0])):
                idx = indices[0][i]
                if idx < len(self.model_id_map):
                    model_id = self.model_id_map[idx]
                    similarity = float(similarities[i])
                    
                    # 只返回相似度超过阈值的结果
                    if similarity >= self.similarity_threshold:
                        results.append((model_id, similarity))
            
            return results
            
        except Exception as e:
            logger.error(f"执行相似度搜索失败: {str(e)}")
            return []

# 创建单例
vector_service = VectorService() 