from django.test import TestCase
import os
import tempfile
from utils.minio_storage import minio_client


# 测试命令
# python manage.py test app_model.tests.MinioStorageTestCase -v 2
class MinioStorageTestCase(TestCase):
    """测试 MinIO 存储功能"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.test_bucket = 'test-bucket'
        self.test_file_content = b'Hello, MinIO!'
        self.test_object_name = 'test-file.txt'
        
        # 确保测试桶存在
        minio_client.ensure_bucket_exists(self.test_bucket)
    
    def tearDown(self):
        """测试后的清理工作"""
        # 清理测试文件
        try:
            minio_client.remove_object(self.test_bucket, self.test_object_name)
        except:
            pass
    
    def test_upload_and_download(self):
        """测试文件上传和下载"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(self.test_file_content)
            temp_file_path = temp_file.name
        
        try:
            # 测试上传文件
            upload_success = minio_client.upload_file(
                self.test_bucket,
                self.test_object_name,
                temp_file_path,
                'text/plain'
            )
            self.assertTrue(upload_success, "文件上传失败")
            
            # 测试下载文件
            download_path = tempfile.mktemp()
            download_success = minio_client.download_file(
                self.test_bucket,
                self.test_object_name,
                download_path
            )
            self.assertTrue(download_success, "文件下载失败")
            
            # 验证下载的文件内容
            with open(download_path, 'rb') as f:
                downloaded_content = f.read()
            self.assertEqual(downloaded_content, self.test_file_content, "下载的文件内容与原文件不匹配")
            
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)
            if os.path.exists(download_path):
                os.unlink(download_path)
    
    def test_list_objects(self):
        """测试列出对象"""
        # 先上传一个测试文件
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(self.test_file_content)
            temp_file_path = temp_file.name
        
        try:
            # 上传文件
            minio_client.upload_file(
                self.test_bucket,
                self.test_object_name,
                temp_file_path,
                'text/plain'
            )
            
            # 测试列出对象
            objects = minio_client.list_objects(self.test_bucket)
            self.assertTrue(len(objects) > 0, "没有找到上传的文件")
            
            # 验证文件名
            found = False
            for obj in objects:
                if obj.object_name == self.test_object_name:
                    found = True
                    break
            self.assertTrue(found, "未能在列表中找到上传的文件")
            
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)
    
    def test_remove_object(self):
        """测试删除对象"""
        # 先上传一个测试文件
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(self.test_file_content)
            temp_file_path = temp_file.name
        
        try:
            # 上传文件
            minio_client.upload_file(
                self.test_bucket,
                self.test_object_name,
                temp_file_path,
                'text/plain'
            )
            
            # 测试删除文件
            delete_success = minio_client.remove_object(self.test_bucket, self.test_object_name)
            self.assertTrue(delete_success, "文件删除失败")
            
            # 验证文件已被删除
            objects = minio_client.list_objects(self.test_bucket)
            found = False
            for obj in objects:
                if obj.object_name == self.test_object_name:
                    found = True
                    break
            self.assertFalse(found, "文件仍然存在，删除失败")
            
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)