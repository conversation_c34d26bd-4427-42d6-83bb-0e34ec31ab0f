from rest_framework import serializers
from .models import ModelCategory, AIModel, ModelComment, ModelVersion
from app_user.serializers import UserSerializer
from utils.serializers import CustomModelSerializer
from django.conf import settings
from utils.basic_serializers import AIDatasetBasicSerializer

class ModelCategorySerializer(CustomModelSerializer):
    """
    模型分类序列化器
    """
    parent_name = serializers.CharField(source='parent.name', read_only=True)

    class Meta:
        model = ModelCategory
        fields = '__all__'
        read_only_fields = ['id', 'create_datetime', 'update_datetime']

class ModelCategoryTreeSerializer(CustomModelSerializer):
    """
    模型分类表的树形序列化器
    """
    children = serializers.SerializerMethodField(read_only=True)

    def get_children(self, instance):
        queryset = ModelCategory.objects.filter(parent=instance.id).exclude(is_active=False)
        if queryset:
            serializer = ModelCategoryTreeSerializer(queryset, many=True)
            return serializer.data
        else:
            return None

    class Meta:
        model = ModelCategory
        fields = "__all__"
        read_only_fields = ["id"]

class ModelCommentSerializer(CustomModelSerializer):
    """
    模型评论序列化器
    """
    user = UserSerializer(read_only=True)
    parent_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    replies = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = ModelComment
        fields = '__all__'
        read_only_fields = ['id', 'user', 'create_datetime', 'update_datetime', 'replies']
    
    def get_replies(self, obj):
        """获取评论的所有回复"""
        # 避免循环序列化导致无限递归
        if hasattr(self.context.get('request', {}), 'depth'):
            if self.context['request'].depth > 3:  # 限制递归深度为3层
                return []
            self.context['request'].depth += 1
        else:
            if self.context.get('request'):
                self.context['request'].depth = 1
        
        # 获取直接回复此评论的评论
        replies = ModelComment.objects.filter(parent=obj.id).order_by('create_datetime')
        if not replies:
            return []
        
        # 递归序列化
        serializer = ModelCommentSerializer(
            replies, 
            many=True, 
            context=self.context
        )
        return serializer.data

class ModelVersionSerializer(CustomModelSerializer):
    """
    模型版本序列化器
    """
    files = serializers.SerializerMethodField()
    model_name = serializers.CharField(source='model.name', read_only=True)
    model_group = serializers.CharField(source='model.group', read_only=True)
    creator_name = serializers.CharField(source='creator.username', read_only=True, default='')
    minio_path = serializers.CharField(source='model.minio_path', read_only=True)
    version_code = serializers.CharField(source='code', read_only=True)
    # 新增三个url字段 （测试服务要求）
    model_weights_url = serializers.SerializerMethodField()
    model_docs_url = serializers.SerializerMethodField()
    test_report_url = serializers.SerializerMethodField()
    
    class Meta:
        model = ModelVersion
        fields = '__all__'
        read_only_fields = ['id', 'create_datetime', 'update_datetime', 'files', 'creator_name']
    
    def get_files(self, obj):
        """
        获取版本目录下的所有文件，并为每个文件添加type字段
        """
        from utils.minio_storage import minio_client
        from django.conf import settings
        
        bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
        prefix = f"{obj.model.group}/{obj.model.name}/{obj.version_number}/"
        files = minio_client.list_objects(bucket_name, prefix=prefix)
        
        # 获取各类文件路径
        weights_path = obj.model_weights_path or ''
        docs_path = obj.model_docs_path or ''
        report_path = obj.test_report_path or ''
        
        files_data = []
        for file in files:
            filename = file.object_name.split('/')[-1]
            # 判断type
            if file.object_name == weights_path:
                file_type = 'model_weights'
            elif file.object_name == docs_path:
                file_type = 'model_docs'
            elif file.object_name == report_path:
                file_type = 'test_report'
            else:
                file_type = 'other'
            file_data = {
                'id': file.object_name,
                'filename': filename,
                'file_path': file.object_name,
                'file_size': file.size,
                'last_modified': file.last_modified.isoformat(),
                'download_url': self._get_download_url(file.object_name),
                'type': file_type
            }
            files_data.append(file_data)
        return files_data
    
    def _get_download_url(self, file_path):
        """获取文件下载链接"""
        from utils.minio_storage import minio_client
        bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
        return minio_client.get_object_url(bucket_name, file_path)
    
    def get_model_weights_url(self, obj):
        if obj.model_weights_path:
            return self._get_download_url(obj.model_weights_path)
        return None
    
    def get_model_docs_url(self, obj):
        if obj.model_docs_path:
            return self._get_download_url(obj.model_docs_path)
        return None
    
    def get_test_report_url(self, obj):
        if obj.test_report_path:
            return self._get_download_url(obj.test_report_path)
        return None
    
    def create(self, validated_data):
        """创建版本"""
        # 检查版本名称是否已存在
        model = validated_data.get('model')
        version_number = validated_data.get('version_number')
        
        # 检查版本名称是否已存在
        if ModelVersion.objects.filter(model=model, version_number=version_number).exists():
            raise serializers.ValidationError(f"版本名称 '{version_number}' 已存在")
            
        return super().create(validated_data)

class AIModelSerializer(CustomModelSerializer):
    """
    AI模型序列化器
    """
    # 新增多类别支持
    categories = ModelCategorySerializer(many=True, read_only=True)
    category_ids = serializers.ListField(
        child=serializers.IntegerField(), 
        write_only=True, 
        required=False
    )
    datasets = AIDatasetBasicSerializer(many=True, read_only=True)
    dataset_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    comments = ModelCommentSerializer(many=True, read_only=True)
    
    # 最新版本
    latest_version = serializers.SerializerMethodField()
    # 版本列表
    versions = ModelVersionSerializer(many=True, read_only=True)
    current_status = serializers.CharField(read_only=True)
    current_docker_image = serializers.CharField(read_only=True)
    
    # 训练平台使用的字段
    version_number = serializers.CharField(write_only=True, required=False)
    version_code = serializers.CharField(write_only=True, required=False)
    version_description = serializers.CharField(write_only=True, required=False)
    version_status = serializers.CharField(write_only=True, required=False)
    docker_image = serializers.CharField(write_only=True, required=False)
    
    # 模型编码字段
    code = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    class Meta:
        model = AIModel
        fields = '__all__'
        read_only_fields = ['id', 'create_datetime', 'update_datetime']

    def get_latest_version(self, obj):
        """获取最新版本信息"""
        latest_version = obj.versions.order_by('-create_datetime').first()
        if latest_version:
            return {
                'id': latest_version.id,
                'version_number': latest_version.version_number,
                'description': latest_version.description,
                'status': latest_version.status,
                'docker_image': latest_version.docker_image,
                'model_weights_path': latest_version.model_weights_path,
                'model_docs_path': latest_version.model_docs_path,
                'test_report_path': latest_version.test_report_path,
                'test_failure_reason': latest_version.test_failure_reason,
                'create_datetime': latest_version.create_datetime,
                'creator': latest_version.creator.username if latest_version.creator else None
            }
        return None

    def create(self, validated_data):
        """创建模型时处理多个类别，同时创建版本"""
        # 提取版本相关数据
        version_data = {}
        for field in ['version_number', 'version_code', 'version_description', 'version_status', 'docker_image']:
            if field in validated_data:
                # 将字段名称转换为模型版本字段名称
                if field == 'version_number':
                    version_data['version_number'] = validated_data.pop(field)
                elif field == 'version_code':
                    version_data['code'] = validated_data.pop(field)
                elif field == 'version_description':
                    version_data['description'] = validated_data.pop(field)
                elif field == 'version_status':
                    version_data['status'] = validated_data.pop(field)
                elif field == 'docker_image':
                    version_data['docker_image'] = validated_data.pop(field)
        
        # 获取分类ID
        category_ids = validated_data.pop('category_ids', None)
        # 兼容旧的单一类别
        category_id = validated_data.pop('category_id', None)

        # 如果没有提供minio_path，则自动生成
        if 'minio_path' not in validated_data:
            group = validated_data.get('group', 'default')
            name = validated_data.get('name')
            if group and name:
                validated_data['minio_path'] = f"{group}/{name}"

        # 如果没有提供code，自动生成唯一的模型编码
        if not validated_data.get('code'):
            import re
            from django.utils.text import slugify
            from pypinyin import lazy_pinyin, Style
            group = validated_data.get('group', 'default')
            name = validated_data.get('name', '')

            # 将中文转为拼音，使用小写风格，无音标
            slug_group = ''.join(lazy_pinyin(group, style=Style.NORMAL))
            slug_name = ''.join(lazy_pinyin(name, style=Style.NORMAL))
            
            # 使用slugify处理名称和分组，移除特殊字符和空格
            # 然后转为小写，并确保只包含英文字母、数字和下划线
            slug_group = re.sub(r'[^a-zA-Z0-9_]', '', slugify(slug_group))
            slug_name = re.sub(r'[^a-zA-Z0-9_]', '', slugify(slug_name))
            
            # 如果处理后的字符串为空，使用默认值
            if not slug_group:
                slug_group = 'default'
            if not slug_name:
                slug_name = 'model'
            
            # 组合生成基础编码
            base_code = f"{slug_group}_{slug_name}"
            
            # 检查编码是否已存在，如果存在则添加后缀
            from django.db.models import Q
            counter = 1
            new_code = base_code
            while AIModel.objects.filter(code=new_code).exists():
                new_code = f"{base_code}_{counter}"
                counter += 1
            
            validated_data['code'] = new_code

        # 获取数据集ID
        dataset_ids = validated_data.pop('dataset_ids', None)
        
        # 创建模型
        instance = super().create(validated_data)

        # 添加关联的数据集
        if dataset_ids:
            instance.datasets.set(dataset_ids)
        
        # 添加类别关系
        if category_ids:
            instance.categories.set(category_ids)
        elif category_id:  # 兼容旧API
            instance.category_id = category_id
            instance.categories.add(category_id)
            instance.save()
        
        # 创建版本(如果有版本数据)
        if version_data:
            # 设置默认值
            if 'version_number' not in version_data:
                version_data['version_number'] = 'v1.0'
            if 'status' not in version_data:
                version_data['status'] = 'dev_done'
            
            ModelVersion.objects.create(model=instance, **version_data)
            
        return instance
        
    def update(self, instance, validated_data):
        """更新模型时处理多个类别和数据集"""
        category_ids = validated_data.pop('category_ids', None)
        dataset_ids = validated_data.pop('dataset_ids', None)
        
        instance = super().update(instance, validated_data)
        
        # 更新类别关系
        if category_ids is not None:
            instance.categories.set(category_ids)

        # 更新关联的数据集
        if dataset_ids is not None:
            instance.datasets.set(dataset_ids)
            
        return instance

