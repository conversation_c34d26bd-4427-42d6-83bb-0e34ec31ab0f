from django.db.models.signals import post_save
from django.dispatch import receiver
import logging
from django.conf import settings

from .models import AIModel
from .vector_service import vector_service

logger = logging.getLogger(__name__)

@receiver(post_save, sender=AIModel)
def update_model_embedding(sender, instance, created, **kwargs):
    """
    当模型创建或更新时，更新嵌入向量
    """
    # 检查语义检索功能是否启用
    if not settings.VECTOR_SERVICE.get('ENABLE_SEMANTIC_SEARCH', True):
        logger.info(f"语义检索功能已禁用，跳过模型 ID: {instance.id} 的嵌入向量更新")
        return
        
    # 检查是否配置了自动更新向量嵌入
    if not settings.VECTOR_SERVICE.get('AUTO_UPDATE_EMBEDDINGS', True):
        logger.info(f"自动更新向量嵌入功能已禁用，跳过模型 ID: {instance.id} 的嵌入向量更新")
        return
        
    # 仅当描述字段被修改时才更新嵌入向量
    if not instance.description:
        return
        
    # 检查是否是描述字段更新或新创建
    if created or kwargs.get('update_fields') is None or 'description' in kwargs.get('update_fields', []):
        try:
            logger.info(f"开始更新模型 ID: {instance.id} 的嵌入向量")
            vector_service.update_models_embedding([instance])
            logger.info(f"模型 ID: {instance.id} 的嵌入向量更新完成")
        except Exception as e:
            logger.error(f"更新模型嵌入向量失败: {str(e)}")