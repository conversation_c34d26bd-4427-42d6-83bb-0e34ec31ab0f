# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.db import migrations, models
import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Post',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('post_name', models.CharField(help_text='岗位名称', max_length=64, verbose_name='岗位名称')),
                ('post_code', models.Char<PERSON><PERSON>(help_text='岗位代码', max_length=32, verbose_name='岗位代码')),
                ('sort', models.Integer<PERSON>ield(default=1, help_text='岗位顺序', verbose_name='岗位顺序')),
                ('status', models.Char<PERSON>ield(choices=[('0', '在职'), ('1', '离职')], default='0', help_text='岗位状态（0在职 1离职）', max_length=1, verbose_name='岗位状态（0在职 1离职）')),
                ('remark', models.CharField(blank=True, help_text='备注', max_length=150, null=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '系统-岗位表',
                'verbose_name_plural': '系统-岗位表',
                'db_table': 'sys_post',
                'ordering': ('sort',),
            },
        ),
    ]
