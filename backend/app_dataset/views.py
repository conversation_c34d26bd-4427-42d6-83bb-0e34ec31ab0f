from django.shortcuts import get_object_or_404
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.db.models import F
import logging
from django.http import HttpResponse
import os
import json
from datetime import datetime

from .models import DatasetCategory, AIDataset, DatasetComment
from .serializers import (
    DatasetCategorySerializer, DatasetCategoryTreeSerializer, AIDatasetSerializer, 
    DatasetCommentSerializer
)
from utils.viewset import CustomModelViewSet
from utils.json_response import DetailResponse, SuccessResponse, ErrorResponse
from utils.minio_storage import minio_client
from django.conf import settings

logger = logging.getLogger(__name__)

class DatasetCategoryViewSet(CustomModelViewSet):
    """
    数据集分类管理
    """
    queryset = DatasetCategory.objects.all()
    serializer_class = DatasetCategorySerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['is_active']
    search_fields = ['name', 'code']
    
    @action(methods=["GET"], detail=False)
    def tree(self, request):
        """
        获取数据集分类树结构
        """
        queryset = DatasetCategory.objects.exclude(is_active=False).filter(parent=None)
        serializer = DatasetCategoryTreeSerializer(queryset, many=True)
        return DetailResponse(data=serializer.data, msg="数据集分类树获取成功")

class AIDatasetViewSet(CustomModelViewSet):
    """
    AI数据集管理
    """
    queryset = AIDataset.objects.all()
    serializer_class = AIDatasetSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        category_id = self.request.query_params.get('category')
        # 支持多个类别ID筛选
        category_ids = self.request.query_params.getlist('category_ids')
        search = self.request.query_params.get('search')

        # 优先使用新的多类别筛选
        if category_ids:
            # 获取所有有效的类别ID，包括子类别
            valid_category_ids = []
            all_subcategory_ids = set()  # 用于存储所有子类别ID
            
            # 递归获取子类别的函数
            def get_all_child_categories(parent_id):
                children = DatasetCategory.objects.filter(parent_id=parent_id)
                child_ids = []
                for child in children:
                    child_ids.append(child.id)
                    # 递归获取子类别的子类别
                    child_ids.extend(get_all_child_categories(child.id))
                return child_ids
            
            # 处理每个传入的类别ID
            for cat_id in category_ids:
                try:
                    # 尝试转换为整数
                    cat_id_int = int(cat_id)
                    # 检查类别是否存在
                    category = DatasetCategory.objects.get(id=cat_id_int)
                    valid_category_ids.append(cat_id_int)
                    
                    # 获取该类别的所有子类别
                    subcategory_ids = get_all_child_categories(cat_id_int)
                    all_subcategory_ids.update(subcategory_ids)
                    
                except (ValueError, DatasetCategory.DoesNotExist):
                    # 如果类别ID不是整数或类别不存在，跳过
                    logger.warning(f"类别ID {cat_id} 无效或不存在")
                    continue
            
            # 将有效的类别ID和所有子类别ID合并
            all_category_ids = set(valid_category_ids) | all_subcategory_ids
            
            if all_category_ids:
                # 使用交集逻辑：数据集必须同时属于所有选中的类别
                # 为每个类别创建一个子查询
                for cat_id in valid_category_ids:
                    # 为每个类别创建一个子查询，包括其所有子类别
                    category_with_children = {cat_id} | {id for id in all_subcategory_ids 
                                                     if self._is_child_of(id, cat_id)}
                    # 使用交集逻辑：数据集必须属于该类别或其子类别
                    queryset = queryset.filter(categories__id__in=category_with_children)
                
                # 确保结果唯一
                queryset = queryset.distinct()
            else:
                # 如果所有类别ID都无效，返回空查询集
                queryset = queryset.none()
        # 兼容旧的单一类别筛选
        elif category_id:
            # 支持查询某个类别下的数据集，包括其子类别
            try:
                # 首先检查是否存在此类别
                category = DatasetCategory.objects.get(id=category_id)
                
                # 获取此类别及其所有子类别的ID列表
                category_ids = [int(category_id)]
                
                # 递归获取子类别
                def get_child_categories(parent_id):
                    children = DatasetCategory.objects.filter(parent_id=parent_id)
                    for child in children:
                        category_ids.append(child.id)
                        get_child_categories(child.id)
                
                get_child_categories(int(category_id))
                
                # 查询与这些类别关联的数据集
                queryset = queryset.filter(categories__id__in=category_ids).distinct()
            except DatasetCategory.DoesNotExist:
                # 如果类别不存在，返回空查询集
                queryset = queryset.none()
                
        if search:
            queryset = queryset.filter(name__icontains=search)
            
        return queryset
    
    def _is_child_of(self, child_id, parent_id):
        """判断一个类别是否是另一个类别的子类别"""
        try:
            child = DatasetCategory.objects.get(id=child_id)
            # 循环向上查找父级
            while child.parent:
                if child.parent.id == parent_id:
                    return True
                child = child.parent
            return False
        except DatasetCategory.DoesNotExist:
            return False

    def list(self, request, *args, **kwargs):
        """
        获取数据集列表
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return DetailResponse(data=serializer.data, msg="获取数据集列表成功")

    def retrieve(self, request, *args, **kwargs):
        """
        获取数据集详情
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return DetailResponse(data=serializer.data, msg="获取数据集详情成功")
    
    def create(self, request, *args, **kwargs):
        """
        创建数据集
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return DetailResponse(data=serializer.data, msg="创建数据集成功")
        
    @action(methods=["POST"], detail=True)
    def star(self, request, *args, **kwargs):
        """收藏数据集"""
        instance = self.get_object()
        instance.stars = F('stars') + 1
        instance.save()
        instance.refresh_from_db()
        return DetailResponse(data={"stars": instance.stars}, msg="收藏成功")
    
    @action(methods=["POST"], detail=True)
    def download(self, request, *args, **kwargs):
        """下载数据集，增加下载次数"""
        instance = self.get_object()
        instance.downloads = F('downloads') + 1
        instance.save()
        instance.refresh_from_db()
        return DetailResponse(data={"downloads": instance.downloads}, msg="下载次数更新成功")

    @action(detail=True, methods=['get'])
    def file_url(self, request, pk=None):
        """
        获取数据集文件的下载URL
        """
        model = self.get_object()
        file_path = request.query_params.get('file_path', '')
        if not file_path:
            return ErrorResponse(msg="文件路径不能为空")

        full_path = f"{model.minio_path.rstrip('/')}/{file_path}"
        url = minio_client.get_object_url('models', full_path)
        if url:
            return DetailResponse(data={"url": url}, msg="获取下载链接成功")
        return ErrorResponse(msg="获取文件URL失败")

    @action(methods=["POST"], detail=False)
    def upload(self, request, *args, **kwargs):
        """
        创建数据集记录
        现在文件已经通过预签名URL直接上传到MinIO，这个接口只需要创建数据集记录
        """
        import re
        import json
        
        # 获取请求中的参数
        name = request.data.get('name')
        group = request.data.get('group', 'default')
        description = request.data.get('description', '')

        # 获取类别ID列表或单一类别ID
        category_ids = request.data.getlist('category_ids')
        if not category_ids:
            # 兼容旧API，尝试获取单一类别ID
            category_id = request.data.get('category_id')
            if category_id:
                category_ids = [category_id]

        # 获取文件信息列表
        try:
            files_info = json.loads(request.data.get('files', '[]'))
        except json.JSONDecodeError:
            return ErrorResponse(msg="文件信息格式错误")
        
        # 参数验证
        if not name or not category_ids or not files_info:
            return ErrorResponse(msg="数据集名称、分类和文件不能为空")
        
        # 验证名称符合MinIO命名规则（3-63字符，只能包含小写字母、数字、点和连字符等）
        if not re.match(r'^[a-z0-9][a-z0-9.\\-]{1,61}[a-z0-9]$', name):
            return ErrorResponse(msg="数据集名称必须是3-63个字符，只能包含小写字母、数字、点和连字符")

        # 解析其他参数
        try:
            parameters_str = request.data.get('parameters', '{}')
            metrics_str = request.data.get('metrics', '{}')
            
            parameters = json.loads(parameters_str) if isinstance(parameters_str, str) else parameters_str
            metrics = json.loads(metrics_str) if isinstance(metrics_str, str) else metrics_str
        except json.JSONDecodeError as e:
            parameters = {}
            metrics = {}
            logger.error(f"参数解析错误: {str(e)}")

        logger.info(f"接收到上传请求: name={name}, group={group}, category_ids={category_ids}, 文件数量={len(files_info)}")
        logger.info(f"请求数据: {request.data}")
        # 不再记录文件列表，因为现在使用预签名URL直接上传
        # logger.info(f"文件: {request.FILES}")

        # 确保MinIO存储桶存在
        minio_client.ensure_bucket_exists('models')
        
        # 创建数据集对象
        minio_path = f"{group}/{name}"
        try:
            model = AIDataset.objects.create(
                name=name,
                group=group,
                description=description,
                minio_path=minio_path,
                parameters=parameters,
                metrics=metrics,
                creator=request.user
            )
            # 设置多个类别
            if category_ids:
                model.categories.set(category_ids)
                
                # 兼容旧系统，设置第一个类别为主类别
                if len(category_ids) > 0:
                    model.category_id = category_ids[0]
                    model.save()
                
            # 返回响应
            return DetailResponse(
                data={
                    'model_id': model.id,
                    'files': files_info
                }, 
                msg="数据集创建成功"
            )
        except Exception as e:
            logger.error(f"创建数据集失败: {str(e)}")
            return ErrorResponse(msg=f"创建数据集失败: {str(e)}")

    @action(detail=True, methods=['get'])
    def download_file(self, request, pk=None):
        """
        下载数据集文件(支持流式下载)
        """
        bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')
        file_path = request.query_params.get('file_path')
        if not file_path:
            return ErrorResponse(msg="文件路径不能为空")

        model = self.get_object()
        full_path = f"{model.minio_path.rstrip('/')}/{file_path}"

        try:
            # 获取文件对象
            response = HttpResponse(content_type='application/octet-stream')
            response['Content-Disposition'] = f'attachment; filename="{file_path}"'
            
            # 从MinIO获取文件数据并流式传输
            data = minio_client.get_object(bucket_name, full_path)
            if data:
                # 设置响应头
                response['Content-Length'] = data.headers.get('Content-Length', 0)
                # 流式传输数据
                for chunk in data.stream(32*1024):
                    response.write(chunk)
                return response
            return ErrorResponse(msg="文件不存在")
        except Exception as e:
            logger.error(f"下载文件失败: {str(e)}")
            return ErrorResponse(msg=f"下载文件失败: {str(e)}")

    @action(methods=["POST"], detail=False)
    def get_upload_url(self, request, *args, **kwargs):
        """
        获取预签名上传URL
        """
        filename = request.data.get('filename')
        group = request.data.get('group', 'default')
        name = request.data.get('name')

        if not filename or not name:
            return ErrorResponse(msg="文件名和数据集名称不能为空")

        # 构建对象名称
        object_name = f"{group}/{name}/{filename}"
        bucket_name = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')

        # 确保存储桶存在
        minio_client.ensure_bucket_exists(bucket_name)

        # 获取预签名上传URL
        url = minio_client.get_presigned_put_url(bucket_name, object_name)
        if url:
            return DetailResponse(data={"url": url, "object_name": object_name}, msg="获取上传URL成功")
        return ErrorResponse(msg="获取上传URL失败")

class DatasetCommentViewSet(CustomModelViewSet):
    """
    数据集评论管理
    """
    queryset = DatasetComment.objects.all()
    serializer_class = DatasetCommentSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        """创建评论时，关联当前用户"""
        # 获取父评论ID
        parent_id = self.request.data.get('parent_id')
        parent_comment = None
        if parent_id:
            try:
                parent_comment = DatasetComment.objects.get(id=parent_id)
            except DatasetComment.DoesNotExist:
                raise serializers.ValidationError("父评论不存在")
        
        serializer.save(user=self.request.user, parent=parent_comment)

    def get_queryset(self):
        """
        重写查询集，支持按数据集ID和父评论ID筛选
        """
        queryset = super().get_queryset()
        model_id = self.request.query_params.get('model_id')
        
        # 按数据集ID筛选
        if model_id:
            queryset = queryset.filter(model_id=model_id)
        
        # 只返回顶级评论（没有父评论的）
        queryset = queryset.filter(parent__isnull=True)
            
        return queryset

    def list(self, request, *args, **kwargs):
        """获取评论列表"""
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return DetailResponse(data=serializer.data, msg="获取评论列表成功")

    def destroy(self, request, *args, **kwargs):
        """
        删除评论及其所有子评论
        """
        try:
            instance = self.get_object()
            
            # 检查权限：只有评论发布者或管理员才能删除
            if not (request.user.is_staff or instance.user == request.user):
                return ErrorResponse(msg="没有权限删除此评论")
            
            # 递归删除所有子评论
            self.delete_replies(instance.id)
            
            # 删除主评论
            instance.delete()
            
            return SuccessResponse(msg="评论删除成功")
        
        except Exception as e:
            logger.error(f"删除评论失败: {str(e)}")
            return ErrorResponse(msg="评论删除失败")

    def delete_replies(self, comment_id):
        """
        递归删除指定评论的所有回复
        """
        replies = DatasetComment.objects.filter(parent_id=comment_id)
        for reply in replies:
            # 递归删除子回复
            self.delete_replies(reply.id)
            # 删除当前回复
            reply.delete()
