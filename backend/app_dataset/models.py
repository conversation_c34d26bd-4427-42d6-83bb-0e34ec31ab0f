from django.db import models
from application.settings import BASE_DIR
from utils.models import BaseModel
from app_user.models import Users

class DatasetCategory(BaseModel):
    """
    数据集分类表
    """
    name = models.CharField(max_length=100, verbose_name="分类名称")
    code = models.CharField(max_length=100, verbose_name="分类编码")
    order = models.IntegerField(default=1, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    parent = models.ForeignKey(
        to='self',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        verbose_name='父级分类',
        db_constraint=False,
        related_name='subcat',
        help_text="父级分类",
    )
    
    class Meta:
        verbose_name = '数据集分类'
        verbose_name_plural = verbose_name
        ordering = ['order']
    
    def __str__(self):
        return f"{self.name}"

class AIDataset(BaseModel):
    """
    数据集表
    """
    STATUS_CHOICES = (
        ('online', '已上架'),
        ('offline', '已下架')
    )
    name = models.CharField(max_length=200, verbose_name="数据集名称")
    group = models.CharField(max_length=100, help_text="数据集所属项目组", default='qianlan')
    description = models.TextField(verbose_name="数据集描述")
    categories = models.ManyToManyField(
        to=DatasetCategory,
        verbose_name='数据集分类',
        related_name='dataset_categories',
        help_text="数据集分类，可以选择多个类别"
    )
    minio_path = models.CharField(
        max_length=255,
        help_text="MinIO 中的存储路径，例如 apple/DepthPro/"
    )
    stars = models.IntegerField(default=0, verbose_name="收藏数")
    downloads = models.IntegerField(default=0, verbose_name="下载次数")
    parameters = models.JSONField(
        default=dict,
        verbose_name="数据集参数",
        help_text="示例：{\"backbone\": \"resnet50\", \"input_size\": 512}"
    )
    metrics = models.JSONField(
        default=dict,
        verbose_name="测试指标",
        help_text="示例：{\"top1_acc\": 0.92, \"f1_score\": 0.88}"
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='online',
        verbose_name="上架状态"
    )
    
    class Meta:
        verbose_name = 'AI数据集'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
    
    def __str__(self):
        return f"{self.group}-{self.name}"

class DatasetComment(BaseModel):
    """数据集评论"""
    model = models.ForeignKey(AIDataset, related_name='comments', on_delete=models.CASCADE, verbose_name='所属数据集')
    content = models.TextField(verbose_name='评论内容')
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, verbose_name='父评论')
    user = models.ForeignKey(Users, related_name='dataset_comments', on_delete=models.CASCADE, verbose_name='发表用户')
    
    class Meta:
        verbose_name = '数据集评论'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
    
    def __str__(self):
        return f"{self.user.username} 的评论: {self.content[:30]}"