from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import AIDatasetViewSet, DatasetCategoryViewSet, DatasetCommentViewSet

router = DefaultRouter()
router.register(r'datasets', AIDatasetViewSet)
router.register(r'categories', DatasetCategoryViewSet)
router.register(r'comments', DatasetCommentViewSet)

app_name = 'app_dataset'

urlpatterns = [
    path('', include(router.urls)),
] 