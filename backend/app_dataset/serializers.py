from rest_framework import serializers
from .models import DatasetCategory, AIDataset, DatasetComment
from app_user.serializers import UserSerializer
from utils.serializers import CustomModelSerializer
from utils.basic_serializers import AIModelBasicSerializer

class DatasetCategorySerializer(CustomModelSerializer):
    """
    数据集分类序列化器
    """
    parent_name = serializers.CharField(source='parent.name', read_only=True)

    class Meta:
        model = DatasetCategory
        fields = '__all__'
        read_only_fields = ['id', 'create_datetime', 'update_datetime']

class DatasetCategoryTreeSerializer(CustomModelSerializer):
    """
    数据集分类表的树形序列化器
    """
    children = serializers.SerializerMethodField(read_only=True)

    def get_children(self, instance):
        queryset = DatasetCategory.objects.filter(parent=instance.id).exclude(is_active=False)
        if queryset:
            serializer = DatasetCategoryTreeSerializer(queryset, many=True)
            return serializer.data
        else:
            return None

    class Meta:
        model = DatasetCategory
        fields = "__all__"
        read_only_fields = ["id"]

class DatasetCommentSerializer(CustomModelSerializer):
    """
    数据集评论序列化器
    """
    user = UserSerializer(read_only=True)
    parent_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    replies = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = DatasetComment
        fields = '__all__'
        read_only_fields = ['id', 'user', 'create_datetime', 'update_datetime', 'replies']
    
    def get_replies(self, obj):
        """获取评论的所有回复"""
        # 避免循环序列化导致无限递归
        if hasattr(self.context.get('request', {}), 'depth'):
            if self.context['request'].depth > 3:  # 限制递归深度为3层
                return []
            self.context['request'].depth += 1
        else:
            if self.context.get('request'):
                self.context['request'].depth = 1
        
        # 获取直接回复此评论的评论
        replies = DatasetComment.objects.filter(parent=obj.id).order_by('create_datetime')
        if not replies:
            return []
        
        # 递归序列化
        serializer = DatasetCommentSerializer(
            replies, 
            many=True, 
            context=self.context
        )
        return serializer.data

class AIDatasetSerializer(CustomModelSerializer):
    """
    AI数据集序列化器
    """
    # 原来的单一类别，保留用于兼容
    category = DatasetCategorySerializer(read_only=True)
    category_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    
    # 新增多类别支持
    categories = DatasetCategorySerializer(many=True, read_only=True)
    category_ids = serializers.ListField(
        child=serializers.IntegerField(), 
        write_only=True, 
        required=False
    )
    
    # 顶级分类下的叶子节点标签，用于前端显示
    leaf_categories = serializers.SerializerMethodField()
    
    comments = DatasetCommentSerializer(many=True, read_only=True)
    file_list = serializers.SerializerMethodField()
    models = AIModelBasicSerializer(many=True, read_only=True)

    class Meta:
        model = AIDataset
        fields = '__all__'
        read_only_fields = ['id', 'create_datetime', 'update_datetime', 'stars', 'downloads']

    @staticmethod
    def get_file_list(obj):
        from utils.minio_storage import minio_client
        if not obj.minio_path:
            return []
        files = minio_client.list_objects('models', prefix=obj.minio_path)
        return [{
            'name': obj.object_name.split('/')[-1],
            'size': obj.size,
            'last_modified': obj.last_modified
        } for obj in files]
    
    def get_leaf_categories(self, obj):
        """
        获取每个顶级分类下的叶子节点类别
        只返回每个顶级分类下最深层级的一个类别
        """
        # 获取所有关联的类别
        categories = obj.categories.all()
        
        # 按根分类分组，保留每组中最深的叶子节点
        root_category_map = {}
        
        for category in categories:
            # 找到此类别的根类别
            current = category
            parent_id = current.parent_id
            
            # 如果是叶子节点（没有子节点）
            is_leaf = not DatasetCategory.objects.filter(parent=current.id).exists()
            
            # 找到根类别
            while parent_id:
                parent = DatasetCategory.objects.get(id=parent_id)
                current = parent
                parent_id = current.parent_id
            
            # 根类别ID
            root_id = current.id
            
            # 如果此类别是叶子节点，并且此根类别下还没有叶子节点，或者比现有的更深层级
            if is_leaf:
                if root_id not in root_category_map or self._get_depth(category) > self._get_depth(root_category_map[root_id]):
                    root_category_map[root_id] = category
        
        # 返回所有叶子节点的序列化数据
        leaf_categories = list(root_category_map.values())
        return DatasetCategorySerializer(leaf_categories, many=True).data
    
    def _get_depth(self, category):
        """获取类别的深度"""
        depth = 0
        current = category
        while current.parent_id:
            depth += 1
            current = current.parent
        return depth
    
    def create(self, validated_data):
        """创建数据集时处理多个类别"""
        category_ids = validated_data.pop('category_ids', None)
        # 兼容旧的单一类别
        category_id = validated_data.pop('category_id', None)
        
        instance = super().create(validated_data)
        
        # 添加类别关系
        if category_ids:
            instance.categories.set(category_ids)
        elif category_id:  # 兼容旧API
            instance.category_id = category_id
            instance.categories.add(category_id)
            instance.save()
            
        return instance
        
    def update(self, instance, validated_data):
        """更新数据集时处理多个类别"""
        category_ids = validated_data.pop('category_ids', None)
        # 兼容旧的单一类别
        category_id = validated_data.pop('category_id', None)
        
        instance = super().update(instance, validated_data)
        
        # 更新类别关系
        if category_ids is not None:
            instance.categories.set(category_ids)
        elif category_id is not None:  # 兼容旧API
            instance.category_id = category_id
            instance.categories.set([category_id])
            
        return instance