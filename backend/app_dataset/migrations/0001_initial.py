# Generated by Django 4.2.1 on 2025-07-08 17:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AIDataset',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('name', models.Char<PERSON>ield(max_length=200, verbose_name='数据集名称')),
                ('group', models.CharField(default='qianlan', help_text='数据集所属项目组', max_length=100)),
                ('description', models.TextField(verbose_name='数据集描述')),
                ('minio_path', models.CharField(help_text='MinIO 中的存储路径，例如 apple/DepthPro/', max_length=255)),
                ('stars', models.IntegerField(default=0, verbose_name='收藏数')),
                ('downloads', models.IntegerField(default=0, verbose_name='下载次数')),
                ('parameters', models.JSONField(default=dict, help_text='示例：{"backbone": "resnet50", "input_size": 512}', verbose_name='数据集参数')),
                ('metrics', models.JSONField(default=dict, help_text='示例：{"top1_acc": 0.92, "f1_score": 0.88}', verbose_name='测试指标')),
                ('status', models.CharField(choices=[('online', '已上架'), ('offline', '已下架')], default='online', max_length=20, verbose_name='上架状态')),
            ],
            options={
                'verbose_name': 'AI数据集',
                'verbose_name_plural': 'AI数据集',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='DatasetComment',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('content', models.TextField(verbose_name='评论内容')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='app_dataset.aidataset', verbose_name='所属数据集')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='app_dataset.datasetcomment', verbose_name='父评论')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dataset_comments', to=settings.AUTH_USER_MODEL, verbose_name='发表用户')),
            ],
            options={
                'verbose_name': '数据集评论',
                'verbose_name_plural': '数据集评论',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='DatasetCategory',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('name', models.CharField(max_length=100, verbose_name='分类名称')),
                ('code', models.CharField(max_length=100, verbose_name='分类编码')),
                ('order', models.IntegerField(default=1, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('parent', models.ForeignKey(blank=True, db_constraint=False, help_text='父级分类', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcat', to='app_dataset.datasetcategory', verbose_name='父级分类')),
            ],
            options={
                'verbose_name': '数据集分类',
                'verbose_name_plural': '数据集分类',
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='aidataset',
            name='categories',
            field=models.ManyToManyField(help_text='数据集分类，可以选择多个类别', related_name='dataset_categories', to='app_dataset.datasetcategory', verbose_name='数据集分类'),
        ),
        migrations.AddField(
            model_name='aidataset',
            name='creator',
            field=models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
    ]
