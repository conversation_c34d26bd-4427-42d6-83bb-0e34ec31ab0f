
## 接口使用说明
baseurl: `68.68.18.120:8000`

- 训练服务开发算法完毕后，可发起请求POST /model/models/create_or_update创建模型，填写模型名称，组别，状态，模型code(唯一id)，版本code(唯一id)，后续可根据code修改相关记录。后续对于同一个模型创建新的版本也请求这个接口，填写model_code和版本相关字段即可。
- 训练完成后，需要上传模型权重和说明文件，需要请求POST /model/models/version_upload_url获取上传url，拿着url和本地文件即可上传文件到minio中，相关代码已给出。
- 对于修改版本字段可请求POST /model/versions/update_by_code



## 新增模型和版本
```
POST /model/models/create_or_update/
Content-Type: application/json

创建首个模型和模型版本
{
    "model_code": "resnet50", 必填，唯一id
    "model_name": "resnet50",
    "model_group": "kmg",
    "model_description": "经典ResNet模型，用于图像分类任务",
    "category_ids": [1，2], #传递类别id，可请求智能管理服务类别接口获取
    "version_code": "resnet50_v4",
    "version_number": "v4.0.0",
    "version_description": "初始版本",
    "version_status": "dev_done",
    "docker_image": "kmg/resnet50:latest"
}


版本状态如下：

('dev_done', '开发完毕待训练'),
('train_done', '训练完毕待测试'),
('test_pass', '测试通过'),
('test_fail', '测试未通过'),
('online', '已上架'),
('offline', '已下架')


响应
{
    "code": 200,
    "data": {
        "model_id": 28638203491561,
        "model_code": "resnet50",
        "version_id": 28638302437099,
        "version_code": "resnet50_v5",
        "message": "新版本创建成功"
    },
    "msg": "模型版本创建成功"
}


创建新的版本
{
    "model_code": "resnet50",
    "description": "经典ResNet模型，用于图像分类任务",
    "category_ids": [1],
    "version_number": "v5.0.0",
    "version_code": "resnet50_v5",
    "version_description": "初始版本",
    "version_status": "dev_done",
    "docker_image": "cv/resnet50:latest"
}


响应
{
    "code": 200,
    "data": {
        "model_id": 28638203491561,
        "model_code": "resnet50",
        "version_id": 28639090719103,
        "version_code": "resnet50_v2",
        "message": "新版本创建成功"
    },
    "msg": "模型版本创建成功"
}
```

## 获取上传url
```
POST /model/models/version_upload_url/
Content-Type: application/json


{
  "version_code": "resnet50_v1",
  "version_files": {
    "model_weights": ["model.pth", "model_config.json"],
    "model_docs": ["README.md", "使用说明.pdf"],
    "test_report": ["测试报告.doc"]
  }
}

响应：

{
    "code": 200,
    "data": {
        "model_weights": {
            "model.pth": {
                "url": "http://localhost:9000/models/cv/resnet50/v1.0.0/model.pth?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250702%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250702T075509Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=5475243adf5a6777d91500663292581f72598551cf58c3c0b7f872e3b9fb02da",
                "object_name": "cv/resnet50/v1.0.0/model.pth"
            },
            "model_config.json": {
                "url": "http://localhost:9000/models/cv/resnet50/v1.0.0/model_config.json?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250702%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250702T075509Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=815b309448327f6656e51f99c0e68f5ea7b7ff7893e15ee4fb10d3638c23c5e1",
                "object_name": "cv/resnet50/v1.0.0/model_config.json"
            }
        },
        "model_docs": {
            "README.md": {
                "url": "http://localhost:9000/models/cv/resnet50/v1.0.0/README.md?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250702%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250702T075509Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=3c979e44c0af83a47c2ae3fc2c23f7b439fdd2219a6666021a4a370d31100c34",
                "object_name": "cv/resnet50/v1.0.0/README.md"
            },
            "使用说明.pdf": {
                "url": "http://localhost:9000/models/cv/resnet50/v1.0.0/%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250702%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250702T075509Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=5dcec558e24bc20f58d0d7e43443900c5b3363423be04445393515aba645b532",
                "object_name": "cv/resnet50/v1.0.0/使用说明.pdf"
            }
        },
        "test_report": {
            "测试报告.doc": {
                "url": "http://localhost:9000/models/cv/resnet50/v1.0.0/%E6%B5%8B%E8%AF%95%E6%8A%A5%E5%91%8A.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250702%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250702T075509Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=1f46ac7f174bc7a11cdf0faae0dc6e7b52ddeed4c38fd0037f940187f368797e",
                "object_name": "cv/resnet50/v1.0.0/测试报告.doc"
            }
        }
    },
    "msg": "获取上传URL成功"
}
```
## 更新版本字段

```
POST /model/versions/update_by_code/
Content-Type: application/json

{
"version_code": "resnet50_v1",
"status": "train_done",
"model_weights_path": "group/model/v1.0/weights.pth",
"model_docs_path": "group/model/v1.0/README.md",
"test_report_path": "group/model/v1.0/test_report.pdf",
"test_failure_reason": "测试失败原因"
}


响应
{
    "code": 200,
    "data": {
        "id": 28622823361819,
        "modifier_name": null,
        "creator_name": "admin",
        "create_datetime": "2025-07-02 15:46:33",
        "update_datetime": "2025-07-02 16:01:37",
        "files": [],
        "model_name": "resnet50",
        "model_group": "cv",
        "minio_path": "cv/resnet50",
        "code": "resnet50_v1",
        "modifier": null,
        "version_number": "v1.0.0",
        "description": "初始版本",
        "status": "train_done",
        "docker_image": "cv/resnet50:latest",
        "model_weights_path": "group/model/v1.0/weights.pth",
        "model_docs_path": "group/model/v1.0/README.md",
        "test_report_path": "group/model/v1.0/test_report.pdf",
        "test_failure_reason": null,
        "model": 28622823311867,
        "creator": 213727856832
    },
    "msg": "更新版本成功"
}
```

## 上传示例代码

### python版本，测试通过
```
import requests

def upload_file_with_presigned_url(presigned_url, file_path):
    """
    使用预签名URL上传文件
    
    Args:
        presigned_url (str): 预签名上传URL
        file_path (str): 要上传的本地文件路径
    
    Returns:
        bool: 上传是否成功
    """
    try:
        # 打开要上传的文件
        with open(file_path, 'rb') as file_data:
            # 上传文件
            # 注意：这里使用PUT方法，直接发送文件内容
            response = requests.put(
                presigned_url,
                data=file_data,
                headers={
                    'Content-Type': 'application/octet-stream'
                }
            )
            
            # 检查上传结果
            if response.status_code == 200:
                print(f"文件 {file_path} 上传成功！")
                return True
            else:
                print(f"文件上传失败，状态码：{response.status_code}")
                print(f"错误信息：{response.text}")
                return False
                
    except Exception as e:
        print(f"上传过程中发生错误：{str(e)}")
        return False

# 使用示例
if __name__ == "__main__":
    # 预签名URL（这个URL是从服务器获取的）
    presigned_url = "http://localhost:9000/models/cv/resnet50/v2.0.0/AlgerMusicPlayer-4.8.2-win-x64.exe?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250702%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250702T090745Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=1c605c6ea4e408c7ea48618302e9d5c3ec58a1ecf9639c487d2dadd8dc0f00e3"
    
    # 要上传的文件路径
    file_path = "D:/Downloads/AlgerMusicPlayer-4.8.2-win-x64.exe"
    
    # 上传文件
    upload_file_with_presigned_url(presigned_url, file_path)

```


### java版本，未测试
```
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.File;

public class SpringMinioUploader {
    
    /**
     * 使用Spring RestTemplate上传文件到预签名URL
     * 
     * @param presignedUrl 预签名上传URL
     * @param filePath 要上传的文件路径
     * @return 上传是否成功
     */
    public static boolean uploadWithRestTemplate(String presignedUrl, String filePath) {
        try {
            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            
            // 创建文件资源
            FileSystemResource fileResource = new FileSystemResource(new File(filePath));
            
            // 创建请求实体
            HttpEntity<FileSystemResource> requestEntity = new HttpEntity<>(fileResource, headers);
            
            // 发送PUT请求
            ResponseEntity<String> response = restTemplate.exchange(
                    presignedUrl,
                    HttpMethod.PUT,
                    requestEntity,
                    String.class
            );
            
            // 检查上传结果
            if (response.getStatusCode().is2xxSuccessful()) {
                System.out.println("文件 " + filePath + " 上传成功！");
                return true;
            } else {
                System.err.println("文件上传失败，状态码：" + response.getStatusCodeValue());
                System.err.println("错误信息：" + response.getBody());
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("上传过程中发生错误：" + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    // 使用示例
    public static void main(String[] args) {
        // 预签名URL
        String presignedUrl = "你的预签名URL";
        
        // 要上传的文件路径
        String filePath = "D:/models/resnet50.pth";
        
        // 上传文件
        boolean success = uploadWithRestTemplate(presignedUrl, filePath);
        if (success) {
            System.out.println("上传成功！");
        } else {
            System.out.println("上传失败！");
        }
    }
}
```

