[{"model": "token_blacklist.outstandingtoken", "pk": 1, "fields": {"user": 541150219354505, "jti": "7de08022847340318db92fe1526a9703", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njg0NzkzOCwiaWF0IjoxNzQ2NzYxNTM4LCJqdGkiOiI3ZGUwODAyMjg0NzM0MDMxOGRiOTJmZTE1MjZhOTcwMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.r5T81hDc2BpwwBItQe0K9cj3qOYAnUkE6vsN3w2GhCc", "created_at": "2025-05-09T03:32:18.853", "expires_at": "2025-05-10T03:32:18"}}, {"model": "token_blacklist.outstandingtoken", "pk": 2, "fields": {"user": 541150219354505, "jti": "2e81581057cf4b71bc5641bd3b6a5988", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njg0NzkzOCwiaWF0IjoxNzQ2NzYxNTM4LCJqdGkiOiIyZTgxNTgxMDU3Y2Y0YjcxYmM1NjQxYmQzYjZhNTk4OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.qXa1Jk9gToZHMsOXMg5L3Q566dCcvtdChlkvUBO4rVc", "created_at": "2025-05-09T03:32:18.860", "expires_at": "2025-05-10T03:32:18"}}, {"model": "token_blacklist.outstandingtoken", "pk": 3, "fields": {"user": 541150219354505, "jti": "cb7502c1b0184ecaa802392279878137", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDM4MiwiaWF0IjoxNzQ2ODYzOTgyLCJqdGkiOiJjYjc1MDJjMWIwMTg0ZWNhYTgwMjM5MjI3OTg3ODEzNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.2-uyroOivktJQE4CT2NIXOec4rol8kjQKxk5uza98qk", "created_at": "2025-05-10T07:59:42.384", "expires_at": "2025-05-11T07:59:42"}}, {"model": "token_blacklist.outstandingtoken", "pk": 4, "fields": {"user": 541150219354505, "jti": "5bc44a5d5eee4b9198de9a6286296fa0", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDM4MiwiaWF0IjoxNzQ2ODYzOTgyLCJqdGkiOiI1YmM0NGE1ZDVlZWU0YjkxOThkZTlhNjI4NjI5NmZhMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.QRy1WhNCw81nf9f3pQpNTTfZFGFOzWGz8W1Z62YZ6xw", "created_at": "2025-05-10T07:59:42.391", "expires_at": "2025-05-11T07:59:42"}}, {"model": "token_blacklist.outstandingtoken", "pk": 5, "fields": {"user": 541150219354505, "jti": "2af62912ec0b4ee8bbffd394639099b9", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDU0OSwiaWF0IjoxNzQ2ODY0MTQ5LCJqdGkiOiIyYWY2MjkxMmVjMGI0ZWU4YmJmZmQzOTQ2MzkwOTliOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.7gqKdI3TQB-k-3x9ojJpzcDal-z7ClCzi4t7DvihrlI", "created_at": "2025-05-10T08:02:29.195", "expires_at": "2025-05-11T08:02:29"}}, {"model": "token_blacklist.outstandingtoken", "pk": 6, "fields": {"user": 541150219354505, "jti": "92e84760bddc41c3a61c40cbc9a571a6", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDU0OSwiaWF0IjoxNzQ2ODY0MTQ5LCJqdGkiOiI5MmU4NDc2MGJkZGM0MWMzYTYxYzQwY2JjOWE1NzFhNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.tceJv_FzmF6X6BL79b-U8f3IvKpwwFe8RstGajIP7lw", "created_at": "2025-05-10T08:02:29.204", "expires_at": "2025-05-11T08:02:29"}}, {"model": "token_blacklist.outstandingtoken", "pk": 7, "fields": {"user": 541150219354505, "jti": "055665893e7f4586bb55ed8d5b914a50", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDc5OCwiaWF0IjoxNzQ2ODY0Mzk4LCJqdGkiOiIwNTU2NjU4OTNlN2Y0NTg2YmI1NWVkOGQ1YjkxNGE1MCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.E3boZE72VzH_D2EsgVMZT_vXbRfUXG_NWdJE4Z8DaQk", "created_at": "2025-05-10T08:06:38.552", "expires_at": "2025-05-11T08:06:38"}}, {"model": "token_blacklist.outstandingtoken", "pk": 8, "fields": {"user": 541150219354505, "jti": "5d8dcb281d8c4a26aec56c0e89103dad", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDc5OCwiaWF0IjoxNzQ2ODY0Mzk4LCJqdGkiOiI1ZDhkY2IyODFkOGM0YTI2YWVjNTZjMGU4OTEwM2RhZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.6ik3eD-Vym0fflORXxcvfU1nZGnsdNQ0H1Az_yaq-qM", "created_at": "2025-05-10T08:06:38.559", "expires_at": "2025-05-11T08:06:38"}}, {"model": "token_blacklist.outstandingtoken", "pk": 9, "fields": {"user": 541150219354505, "jti": "3c6215c6bc4b43e088d043a96d407631", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDgxMSwiaWF0IjoxNzQ2ODY0NDExLCJqdGkiOiIzYzYyMTVjNmJjNGI0M2UwODhkMDQzYTk2ZDQwNzYzMSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.bcM4XFfUHanNowEhW-ANscHbmFOpQhsS4aWgouOXm34", "created_at": "2025-05-10T08:06:51.137", "expires_at": "2025-05-11T08:06:51"}}, {"model": "token_blacklist.outstandingtoken", "pk": 10, "fields": {"user": 541150219354505, "jti": "69611a478d904a59b583dcdf2d3283fa", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MDgxMSwiaWF0IjoxNzQ2ODY0NDExLCJqdGkiOiI2OTYxMWE0NzhkOTA0YTU5YjU4M2RjZGYyZDMyODNmYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.YumUylh43Mdg7bLSKFHfCn6x3L9NBmdMUUtSA-vaZO8", "created_at": "2025-05-10T08:06:51.143", "expires_at": "2025-05-11T08:06:51"}}, {"model": "token_blacklist.outstandingtoken", "pk": 11, "fields": {"user": 541150219354505, "jti": "d2e8cea8870f4334a31e3fa9fd1e4c1c", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MTYwNCwiaWF0IjoxNzQ2ODY1MjA0LCJqdGkiOiJkMmU4Y2VhODg3MGY0MzM0YTMxZTNmYTlmZDFlNGMxYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.FwW1q9oDqupCwWao3cMYVjaDMrZTBK6TnhL9paD8g0M", "created_at": "2025-05-10T08:20:04.830", "expires_at": "2025-05-11T08:20:04"}}, {"model": "token_blacklist.outstandingtoken", "pk": 12, "fields": {"user": 541150219354505, "jti": "ba6309ddd6f94032aa57ac0119836361", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MTYwNCwiaWF0IjoxNzQ2ODY1MjA0LCJqdGkiOiJiYTYzMDlkZGQ2Zjk0MDMyYWE1N2FjMDExOTgzNjM2MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.m-30WtlX3zuJAABoL0h42TuN6jotR1NII74vWafGtl8", "created_at": "2025-05-10T08:20:04.837", "expires_at": "2025-05-11T08:20:04"}}, {"model": "token_blacklist.outstandingtoken", "pk": 13, "fields": {"user": 541150219354505, "jti": "5b8d2904d6d34a0a9a7bd15184f74061", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjA2MywiaWF0IjoxNzQ2ODY1NjYzLCJqdGkiOiI1YjhkMjkwNGQ2ZDM0YTBhOWE3YmQxNTE4NGY3NDA2MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ZnosxiJardo_db9zf1MPkGB9MFjONx8hYMXViS9KfEM", "created_at": "2025-05-10T08:27:43.339", "expires_at": "2025-05-11T08:27:43"}}, {"model": "token_blacklist.outstandingtoken", "pk": 14, "fields": {"user": 541150219354505, "jti": "46fd0cc682ae4ed9aef1afd1f31c89de", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjA2MywiaWF0IjoxNzQ2ODY1NjYzLCJqdGkiOiI0NmZkMGNjNjgyYWU0ZWQ5YWVmMWFmZDFmMzFjODlkZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.x_VoLWGDcjC6FehmPHBd6JvAJsOSPO1ghRhRsdO-O2Q", "created_at": "2025-05-10T08:27:43.375", "expires_at": "2025-05-11T08:27:43"}}, {"model": "token_blacklist.outstandingtoken", "pk": 15, "fields": {"user": 541150219354505, "jti": "01f11fec0a9040e28103dd7238cea466", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjE3OCwiaWF0IjoxNzQ2ODY1Nzc4LCJqdGkiOiIwMWYxMWZlYzBhOTA0MGUyODEwM2RkNzIzOGNlYTQ2NiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.SiwaAWiNMDmwUfr5EzApXlp0CZ9i7FIO0S-8VWafx8Y", "created_at": "2025-05-10T08:29:38.919", "expires_at": "2025-05-11T08:29:38"}}, {"model": "token_blacklist.outstandingtoken", "pk": 16, "fields": {"user": 541150219354505, "jti": "6310bee023664aada3fd9fc5154c1b1e", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjE3OCwiaWF0IjoxNzQ2ODY1Nzc4LCJqdGkiOiI2MzEwYmVlMDIzNjY0YWFkYTNmZDlmYzUxNTRjMWIxZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.zIsZiY8nU0QMVYqyXz8fD0dkUdcf1BqVNXyFc6eQn9o", "created_at": "2025-05-10T08:29:38.956", "expires_at": "2025-05-11T08:29:38"}}, {"model": "token_blacklist.outstandingtoken", "pk": 17, "fields": {"user": 541150219354505, "jti": "c8eb6ddd3df249f9a8a2b2e2e8f31df6", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjI2OSwiaWF0IjoxNzQ2ODY1ODY5LCJqdGkiOiJjOGViNmRkZDNkZjI0OWY5YThhMmIyZTJlOGYzMWRmNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.eVLRrMVVvLwW7wIQtUKTFiAn30rytSC7o0xlDUg9WYk", "created_at": "2025-05-10T08:31:09.737", "expires_at": "2025-05-11T08:31:09"}}, {"model": "token_blacklist.outstandingtoken", "pk": 18, "fields": {"user": 541150219354505, "jti": "86cc7ac2d6f84f8da5154c9ae3ebec13", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjI2OSwiaWF0IjoxNzQ2ODY1ODY5LCJqdGkiOiI4NmNjN2FjMmQ2Zjg0ZjhkYTUxNTRjOWFlM2ViZWMxMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.DuPBPyD3e--QminbEHxVhTLc2DkahCBBSGHMNWYyt7s", "created_at": "2025-05-10T08:31:09.773", "expires_at": "2025-05-11T08:31:09"}}, {"model": "token_blacklist.outstandingtoken", "pk": 19, "fields": {"user": 234457355328, "jti": "c4dae87c1adc4e7ba8c85dbc2ec4755a", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjYzNiwiaWF0IjoxNzQ2ODY2MjM2LCJqdGkiOiJjNGRhZTg3YzFhZGM0ZTdiYThjODVkYmMyZWM0NzU1YSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.GjsOAJp1YUSsAZhX0FvUBT2dSgLkV7gBhIzDMAwpzzU", "created_at": "2025-05-10T08:37:16.207", "expires_at": "2025-05-11T08:37:16"}}, {"model": "token_blacklist.outstandingtoken", "pk": 20, "fields": {"user": 234457355328, "jti": "0d9bbb3ac958498dae37f74e40aea583", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjYzNiwiaWF0IjoxNzQ2ODY2MjM2LCJqdGkiOiIwZDliYmIzYWM5NTg0OThkYWUzN2Y3NGU0MGFlYTU4MyIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.1_NUMKU2AvyLpJWFOfbUG6exBM6Aa84-vDfsaZms6zo", "created_at": "2025-05-10T08:37:16.241", "expires_at": "2025-05-11T08:37:16"}}, {"model": "token_blacklist.outstandingtoken", "pk": 21, "fields": {"user": 541150219354505, "jti": "b7537c6bd6ef4fd79d1321e7468fe457", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjgzOSwiaWF0IjoxNzQ2ODY2NDM5LCJqdGkiOiJiNzUzN2M2YmQ2ZWY0ZmQ3OWQxMzIxZTc0NjhmZTQ1NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.WVV50GRY_0qmIojCBvYN8HGA3to8Ma1KIk4vpZw7x1w", "created_at": "2025-05-10T08:40:39.003", "expires_at": "2025-05-11T08:40:39"}}, {"model": "token_blacklist.outstandingtoken", "pk": 22, "fields": {"user": 541150219354505, "jti": "97d74997b4e3480889a14c69c12be972", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MjgzOSwiaWF0IjoxNzQ2ODY2NDM5LCJqdGkiOiI5N2Q3NDk5N2I0ZTM0ODA4ODlhMTRjNjljMTJiZTk3MiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.GcCXbKetJIEdK-IwVD0nCUp3Ad7mP1rSkvR75w97Dbw", "created_at": "2025-05-10T08:40:39.039", "expires_at": "2025-05-11T08:40:39"}}, {"model": "token_blacklist.outstandingtoken", "pk": 23, "fields": {"user": 541150219354505, "jti": "2b7bdf39cb7f4de4bb78c61a8c1e5970", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MzQwMywiaWF0IjoxNzQ2ODY3MDAzLCJqdGkiOiIyYjdiZGYzOWNiN2Y0ZGU0YmI3OGM2MWE4YzFlNTk3MCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.paTBQLui6nPewcwArgc-X9rVikY1iPCDij1WZl5CaoE", "created_at": "2025-05-10T08:50:03.518", "expires_at": "2025-05-11T08:50:03"}}, {"model": "token_blacklist.outstandingtoken", "pk": 24, "fields": {"user": 541150219354505, "jti": "579acfd185b44ad2a970d38d42599b97", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1MzQwMywiaWF0IjoxNzQ2ODY3MDAzLCJqdGkiOiI1NzlhY2ZkMTg1YjQ0YWQyYTk3MGQzOGQ0MjU5OWI5NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.4adxVIRw88-oB3diAiEfaT05sUrFV0e1oB6Pz6YM-6g", "created_at": "2025-05-10T08:50:03.553", "expires_at": "2025-05-11T08:50:03"}}, {"model": "token_blacklist.outstandingtoken", "pk": 25, "fields": {"user": 234457355328, "jti": "b0766cc024964f52be90a2e1b84571aa", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NDU1NSwiaWF0IjoxNzQ2ODY4MTU1LCJqdGkiOiJiMDc2NmNjMDI0OTY0ZjUyYmU5MGEyZTFiODQ1NzFhYSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.26uKln058V1acP84MVgkmnjsh5zhz4k-sTpls6O35p0", "created_at": "2025-05-10T09:09:15.756", "expires_at": "2025-05-11T09:09:15"}}, {"model": "token_blacklist.outstandingtoken", "pk": 26, "fields": {"user": 234457355328, "jti": "122f282b37004027855bab030fc8a28e", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NDU1NSwiaWF0IjoxNzQ2ODY4MTU1LCJqdGkiOiIxMjJmMjgyYjM3MDA0MDI3ODU1YmFiMDMwZmM4YTI4ZSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.y4GB3lJc2yKFgPmlXb7e1VEKCxnPM61dYOHNYcXMU4o", "created_at": "2025-05-10T09:09:15.791", "expires_at": "2025-05-11T09:09:15"}}, {"model": "token_blacklist.outstandingtoken", "pk": 27, "fields": {"user": 541150219354505, "jti": "3d547a1d85284ccab278e27e121b43b7", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NDkzNiwiaWF0IjoxNzQ2ODY4NTM2LCJqdGkiOiIzZDU0N2ExZDg1Mjg0Y2NhYjI3OGUyN2UxMjFiNDNiNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ETxXFj0b0o2fr7Tc1UJHZRbOVcrlXph-wb-RVrsJrRY", "created_at": "2025-05-10T09:15:36.788", "expires_at": "2025-05-11T09:15:36"}}, {"model": "token_blacklist.outstandingtoken", "pk": 28, "fields": {"user": 541150219354505, "jti": "d1a6ebd8bb8f4eb1857e0fc365f957ca", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NDkzNiwiaWF0IjoxNzQ2ODY4NTM2LCJqdGkiOiJkMWE2ZWJkOGJiOGY0ZWIxODU3ZTBmYzM2NWY5NTdjYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.3XtrNk3UxF1kjX1os5KtdN8mVEgxYtFgMx1_cd8Q1vY", "created_at": "2025-05-10T09:15:36.822", "expires_at": "2025-05-11T09:15:36"}}, {"model": "token_blacklist.outstandingtoken", "pk": 29, "fields": {"user": 234457355328, "jti": "ebe0e2772d804c07b33e175cbe245347", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTI4MCwiaWF0IjoxNzQ2ODY4ODgwLCJqdGkiOiJlYmUwZTI3NzJkODA0YzA3YjMzZTE3NWNiZTI0NTM0NyIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.YKYLsFBjDuAyC1iR9hjv2BjwzS_X9u4hy7HXkHotdsU", "created_at": "2025-05-10T09:21:20.314", "expires_at": "2025-05-11T09:21:20"}}, {"model": "token_blacklist.outstandingtoken", "pk": 30, "fields": {"user": 234457355328, "jti": "fe15291cc1c74eeb8f62e244f262372d", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTI4MCwiaWF0IjoxNzQ2ODY4ODgwLCJqdGkiOiJmZTE1MjkxY2MxYzc0ZWViOGY2MmUyNDRmMjYyMzcyZCIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.zdQgjLMBQLcWH_TPR94nDWX7JfHgS0XPKwBYxCK_vR4", "created_at": "2025-05-10T09:21:20.350", "expires_at": "2025-05-11T09:21:20"}}, {"model": "token_blacklist.outstandingtoken", "pk": 31, "fields": {"user": 541150219354505, "jti": "148fdb416e694324b77263aa44573637", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTYyOCwiaWF0IjoxNzQ2ODY5MjI4LCJqdGkiOiIxNDhmZGI0MTZlNjk0MzI0Yjc3MjYzYWE0NDU3MzYzNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yexRh1EIvJ1AL8tpGjaPnE3UtIsUrz2aFN3UCyD2zXY", "created_at": "2025-05-10T09:27:08.121", "expires_at": "2025-05-11T09:27:08"}}, {"model": "token_blacklist.outstandingtoken", "pk": 32, "fields": {"user": 541150219354505, "jti": "86434ea635074fa1a3c1a89334e2669a", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTYyOCwiaWF0IjoxNzQ2ODY5MjI4LCJqdGkiOiI4NjQzNGVhNjM1MDc0ZmExYTNjMWE4OTMzNGUyNjY5YSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.aLIRoCW7_eZuDgKko0S-KvgTxpuCZUZtPzRfM1ciqQs", "created_at": "2025-05-10T09:27:08.365", "expires_at": "2025-05-11T09:27:08"}}, {"model": "token_blacklist.outstandingtoken", "pk": 33, "fields": {"user": 154559003456, "jti": "04a1ad1311584245b7641eb0bc91b4ae", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTgxMCwiaWF0IjoxNzQ2ODY5NDEwLCJqdGkiOiIwNGExYWQxMzExNTg0MjQ1Yjc2NDFlYjBiYzkxYjRhZSIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.MC0714HqWYKoFUefVBpZb2tbOBrztfhFPhWDPMRArlw", "created_at": "2025-05-10T09:30:10.690", "expires_at": "2025-05-11T09:30:10"}}, {"model": "token_blacklist.outstandingtoken", "pk": 34, "fields": {"user": 154559003456, "jti": "023e6eef6aeb4a528bf6cf6ca730d68e", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTgxMCwiaWF0IjoxNzQ2ODY5NDEwLCJqdGkiOiIwMjNlNmVlZjZhZWI0YTUyOGJmNmNmNmNhNzMwZDY4ZSIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.3ExDJc1laLXrczqQAdCXkLshIv25dowjH36YL-OUyrg", "created_at": "2025-05-10T09:30:10.726", "expires_at": "2025-05-11T09:30:10"}}, {"model": "token_blacklist.outstandingtoken", "pk": 35, "fields": {"user": 154559003456, "jti": "e91d8a24709742b0b18b5b40213bbb22", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTkwMywiaWF0IjoxNzQ2ODY5NTAzLCJqdGkiOiJlOTFkOGEyNDcwOTc0MmIwYjE4YjViNDAyMTNiYmIyMiIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.h_KH7RU_pUiW4z-gZaJhEaILGGTVzb7PfbEzhAJHTwg", "created_at": "2025-05-10T09:31:43.473", "expires_at": "2025-05-11T09:31:43"}}, {"model": "token_blacklist.outstandingtoken", "pk": 36, "fields": {"user": 154559003456, "jti": "4ff4e356ffea4d5f97da6372980a136f", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTkwMywiaWF0IjoxNzQ2ODY5NTAzLCJqdGkiOiI0ZmY0ZTM1NmZmZWE0ZDVmOTdkYTYzNzI5ODBhMTM2ZiIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.S-ctd193zJ6ciNyPlHkLZADZX2vCw9I-JfzRkzFuVQQ", "created_at": "2025-05-10T09:31:43.487", "expires_at": "2025-05-11T09:31:43"}}, {"model": "token_blacklist.outstandingtoken", "pk": 37, "fields": {"user": 541150219354505, "jti": "e0af890bcd1d481eaf1ef9c9bd77e8a8", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTkxNiwiaWF0IjoxNzQ2ODY5NTE2LCJqdGkiOiJlMGFmODkwYmNkMWQ0ODFlYWYxZWY5YzliZDc3ZThhOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.p2WXKEbE9w0Ocw3jvXAHBUauDynvdrohpCu3rQ3K1oo", "created_at": "2025-05-10T09:31:56.446", "expires_at": "2025-05-11T09:31:56"}}, {"model": "token_blacklist.outstandingtoken", "pk": 38, "fields": {"user": 541150219354505, "jti": "a2d2040ffaa24249b812ddd4cf95e80b", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTkxNiwiaWF0IjoxNzQ2ODY5NTE2LCJqdGkiOiJhMmQyMDQwZmZhYTI0MjQ5YjgxMmRkZDRjZjk1ZTgwYiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.TDWvSW4CknQHsOu29dEyNq5nQxEm3qlUdU1Nz7RHe9E", "created_at": "2025-05-10T09:31:56.479", "expires_at": "2025-05-11T09:31:56"}}, {"model": "token_blacklist.outstandingtoken", "pk": 39, "fields": {"user": 154559003456, "jti": "c9280a6dfeb24e49a8b8437760f641fc", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTk0NiwiaWF0IjoxNzQ2ODY5NTQ2LCJqdGkiOiJjOTI4MGE2ZGZlYjI0ZTQ5YThiODQzNzc2MGY2NDFmYyIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.jZ274JWGvwnJqK04-BIhakluHcdxBGAEscmtp6DPXZY", "created_at": "2025-05-10T09:32:26.725", "expires_at": "2025-05-11T09:32:26"}}, {"model": "token_blacklist.outstandingtoken", "pk": 40, "fields": {"user": 154559003456, "jti": "5d09953f8f0844b694106d3e24934af7", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NTk0NiwiaWF0IjoxNzQ2ODY5NTQ2LCJqdGkiOiI1ZDA5OTUzZjhmMDg0NGI2OTQxMDZkM2UyNDkzNGFmNyIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.zhGqE5xGg66oTCi9eIWj9v_zDG1rsnMTuouDF5dgiV0", "created_at": "2025-05-10T09:32:26.761", "expires_at": "2025-05-11T09:32:26"}}, {"model": "token_blacklist.outstandingtoken", "pk": 41, "fields": {"user": 154559003456, "jti": "5bd0e56a667d48d68c55c6979a901ead", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NjAxNywiaWF0IjoxNzQ2ODY5NjE3LCJqdGkiOiI1YmQwZTU2YTY2N2Q0OGQ2OGM1NWM2OTc5YTkwMWVhZCIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.ZExsNUkhTZJfbVirmnppL39DAEgj3Ur2K9t4Hd8N0MA", "created_at": "2025-05-10T09:33:37.820", "expires_at": "2025-05-11T09:33:37"}}, {"model": "token_blacklist.outstandingtoken", "pk": 42, "fields": {"user": 154559003456, "jti": "121667ca463444aca9653120c59551c2", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0Njk1NjAxNywiaWF0IjoxNzQ2ODY5NjE3LCJqdGkiOiIxMjE2NjdjYTQ2MzQ0NGFjYTk2NTMxMjBjNTk1NTFjMiIsInVzZXJfaWQiOjE1NDU1OTAwMzQ1Nn0.E7gVwF28ng04PcRmeMMSz7op3SB6c42vX4b85fKrNe0", "created_at": "2025-05-10T09:33:37.855", "expires_at": "2025-05-11T09:33:37"}}, {"model": "token_blacklist.outstandingtoken", "pk": 43, "fields": {"user": 541150219354505, "jti": "2c84037d2ca54d2e9edfeec26c7c1494", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjI1OSwiaWF0IjoxNzQ2OTQ1ODU5LCJqdGkiOiIyYzg0MDM3ZDJjYTU0ZDJlOWVkZmVlYzI2YzdjMTQ5NCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.7U9-PR_oEDkNOfC8iKCf5VfdGX1SotPh9OpcNlQrW7o", "created_at": "2025-05-11T06:44:19.189", "expires_at": "2025-05-12T06:44:19"}}, {"model": "token_blacklist.outstandingtoken", "pk": 44, "fields": {"user": 541150219354505, "jti": "2f8d3461862f46169048aedad281d465", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjI1OSwiaWF0IjoxNzQ2OTQ1ODU5LCJqdGkiOiIyZjhkMzQ2MTg2MmY0NjE2OTA0OGFlZGFkMjgxZDQ2NSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.KXVPY3fkYggtxSQmex4boddurs-m-iMSWTI6j5NTdic", "created_at": "2025-05-11T06:44:19.199", "expires_at": "2025-05-12T06:44:19"}}, {"model": "token_blacklist.outstandingtoken", "pk": 45, "fields": {"user": 541150219354505, "jti": "6db4ddca3bcf47c4bfedfee15a9775e0", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjg4OSwiaWF0IjoxNzQ2OTQ2NDg5LCJqdGkiOiI2ZGI0ZGRjYTNiY2Y0N2M0YmZlZGZlZTE1YTk3NzVlMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.-9ulCEk0eZV_v5RkCtnocDGQ2cfueM_JUO-2we2fY5s", "created_at": "2025-05-11T06:54:49.056", "expires_at": "2025-05-12T06:54:49"}}, {"model": "token_blacklist.outstandingtoken", "pk": 46, "fields": {"user": 541150219354505, "jti": "556140615128460bae0c1c3491bf7685", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjg4OSwiaWF0IjoxNzQ2OTQ2NDg5LCJqdGkiOiI1NTYxNDA2MTUxMjg0NjBiYWUwYzFjMzQ5MWJmNzY4NSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Kl1Jmm7YPBz7bqv7CzqjOQYNRsJ8sOTgLWZ0VujCgMY", "created_at": "2025-05-11T06:54:49.063", "expires_at": "2025-05-12T06:54:49"}}, {"model": "token_blacklist.outstandingtoken", "pk": 47, "fields": {"user": 541150219354505, "jti": "e5449868ed5448bca98b6e5cc9ba4ebc", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjg5NSwiaWF0IjoxNzQ2OTQ2NDk1LCJqdGkiOiJlNTQ0OTg2OGVkNTQ0OGJjYTk4YjZlNWNjOWJhNGViYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.2XIifY2paE8GNWEwBQefL7TAyoL6H4I3gkVyxGQSf9o", "created_at": "2025-05-11T06:54:55.889", "expires_at": "2025-05-12T06:54:55"}}, {"model": "token_blacklist.outstandingtoken", "pk": 48, "fields": {"user": 541150219354505, "jti": "985a6a8997114947bad6b7f19393c5fa", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjg5NSwiaWF0IjoxNzQ2OTQ2NDk1LCJqdGkiOiI5ODVhNmE4OTk3MTE0OTQ3YmFkNmI3ZjE5MzkzYzVmYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Izi_JNvIOHFR7a8xMkDtRzvCVibbB-FWuHkrLoLuH7E", "created_at": "2025-05-11T06:54:55.894", "expires_at": "2025-05-12T06:54:55"}}, {"model": "token_blacklist.outstandingtoken", "pk": 49, "fields": {"user": 234457355328, "jti": "a2f8372ac8624301b0d7282be7b2ba05", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjk3MSwiaWF0IjoxNzQ2OTQ2NTcxLCJqdGkiOiJhMmY4MzcyYWM4NjI0MzAxYjBkNzI4MmJlN2IyYmEwNSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.5CV87Hjk3WrWKyBJtBXXUNuNl-Q4k1csJfB_HtjRj5U", "created_at": "2025-05-11T06:56:11.345", "expires_at": "2025-05-12T06:56:11"}}, {"model": "token_blacklist.outstandingtoken", "pk": 50, "fields": {"user": 234457355328, "jti": "2f00e993ab3b4f32bf131f021d3e42b8", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMjk3MSwiaWF0IjoxNzQ2OTQ2NTcxLCJqdGkiOiIyZjAwZTk5M2FiM2I0ZjMyYmYxMzFmMDIxZDNlNDJiOCIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.teIwx87IPbGxLleZx18dk5yns12xnSA3Ym4uEAIBa1Y", "created_at": "2025-05-11T06:56:11.351", "expires_at": "2025-05-12T06:56:11"}}, {"model": "token_blacklist.outstandingtoken", "pk": 51, "fields": {"user": 541150219354505, "jti": "7279985437d74577a431095809e43c44", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzAzMCwiaWF0IjoxNzQ2OTQ2NjMwLCJqdGkiOiI3Mjc5OTg1NDM3ZDc0NTc3YTQzMTA5NTgwOWU0M2M0NCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.JPxucD2vGWLkJluUIERBC3JA4tfVZdpJMmVNF8h-4H0", "created_at": "2025-05-11T06:57:10.061", "expires_at": "2025-05-12T06:57:10"}}, {"model": "token_blacklist.outstandingtoken", "pk": 52, "fields": {"user": 541150219354505, "jti": "6fbcf903310a4431ab025c842def4908", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzAzMCwiaWF0IjoxNzQ2OTQ2NjMwLCJqdGkiOiI2ZmJjZjkwMzMxMGE0NDMxYWIwMjVjODQyZGVmNDkwOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Z5BDQ5a9qH6Vd-8TrSOp88lTOok3PqvegcPwoIXX5_g", "created_at": "2025-05-11T06:57:10.068", "expires_at": "2025-05-12T06:57:10"}}, {"model": "token_blacklist.outstandingtoken", "pk": 53, "fields": {"user": 541150219354505, "jti": "975d774a04d545039375a81d2d894a65", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA1MCwiaWF0IjoxNzQ2OTQ2NjUwLCJqdGkiOiI5NzVkNzc0YTA0ZDU0NTAzOTM3NWE4MWQyZDg5NGE2NSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.MJdaYtrMNnmmiGt5yWV-Mjz-PFaykt3ycjwDGqQdC64", "created_at": "2025-05-11T06:57:30.628", "expires_at": "2025-05-12T06:57:30"}}, {"model": "token_blacklist.outstandingtoken", "pk": 54, "fields": {"user": 541150219354505, "jti": "774148067be4411abbca0e3da6b4480a", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA1MCwiaWF0IjoxNzQ2OTQ2NjUwLCJqdGkiOiI3NzQxNDgwNjdiZTQ0MTFhYmJjYTBlM2RhNmI0NDgwYSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Qge8E65Xvn8TPgl8fH4PqhcH5NZEP6fmLwB9UahUrFg", "created_at": "2025-05-11T06:57:30.633", "expires_at": "2025-05-12T06:57:30"}}, {"model": "token_blacklist.outstandingtoken", "pk": 55, "fields": {"user": 541150219354505, "jti": "3e4e53cdd62e4b2f9900793dc59456c3", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA1NiwiaWF0IjoxNzQ2OTQ2NjU2LCJqdGkiOiIzZTRlNTNjZGQ2MmU0YjJmOTkwMDc5M2RjNTk0NTZjMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.1SSanOawGG_OTqEiSJkWTRRPdWyjHn9dUz2ixQBRtoc", "created_at": "2025-05-11T06:57:36.200", "expires_at": "2025-05-12T06:57:36"}}, {"model": "token_blacklist.outstandingtoken", "pk": 56, "fields": {"user": 541150219354505, "jti": "533c210a401c49d0aaaaf92c2bbaa72e", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA1NiwiaWF0IjoxNzQ2OTQ2NjU2LCJqdGkiOiI1MzNjMjEwYTQwMWM0OWQwYWFhYWY5MmMyYmJhYTcyZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.hAm2zqGDzM0nDFos9_nzGC_4dPJXPVyE-h8oWPm1pFY", "created_at": "2025-05-11T06:57:36.206", "expires_at": "2025-05-12T06:57:36"}}, {"model": "token_blacklist.outstandingtoken", "pk": 57, "fields": {"user": 541150219354505, "jti": "4605e7eb3ea041338d1b7fb20934ab29", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA2MywiaWF0IjoxNzQ2OTQ2NjYzLCJqdGkiOiI0NjA1ZTdlYjNlYTA0MTMzOGQxYjdmYjIwOTM0YWIyOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.YuC50LIWy7weAe4oVt024Ylh16Fmzsc0Tg9swm5296Q", "created_at": "2025-05-11T06:57:43.798", "expires_at": "2025-05-12T06:57:43"}}, {"model": "token_blacklist.outstandingtoken", "pk": 58, "fields": {"user": 541150219354505, "jti": "efce050cb6234bc3ac2d227c255a0834", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA2MywiaWF0IjoxNzQ2OTQ2NjYzLCJqdGkiOiJlZmNlMDUwY2I2MjM0YmMzYWMyZDIyN2MyNTVhMDgzNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.TIXDg6BwhBdn2ZD5-FRmvT2RhfdHA7PnU_9ahVX1zgk", "created_at": "2025-05-11T06:57:43.803", "expires_at": "2025-05-12T06:57:43"}}, {"model": "token_blacklist.outstandingtoken", "pk": 59, "fields": {"user": 234457355328, "jti": "04a3fa3ad53840c680a0bb8dba1759f3", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA3NiwiaWF0IjoxNzQ2OTQ2Njc2LCJqdGkiOiIwNGEzZmEzYWQ1Mzg0MGM2ODBhMGJiOGRiYTE3NTlmMyIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.KFD4s6uPN8OwIJLCpHS7MwNHQBBc_7OqeMVd3am4LIo", "created_at": "2025-05-11T06:57:56.432", "expires_at": "2025-05-12T06:57:56"}}, {"model": "token_blacklist.outstandingtoken", "pk": 60, "fields": {"user": 234457355328, "jti": "1988d87a33634dd9b83f2a1ccb1ded56", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA3NiwiaWF0IjoxNzQ2OTQ2Njc2LCJqdGkiOiIxOTg4ZDg3YTMzNjM0ZGQ5YjgzZjJhMWNjYjFkZWQ1NiIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0._HDOmVaBxm4hbJQUMRG1FwYJVYsbu2vuFlgpI_n7Fl0", "created_at": "2025-05-11T06:57:56.438", "expires_at": "2025-05-12T06:57:56"}}, {"model": "token_blacklist.outstandingtoken", "pk": 61, "fields": {"user": 234457355328, "jti": "b92468a5469f4ea68b14a801f897f64f", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA4MSwiaWF0IjoxNzQ2OTQ2NjgxLCJqdGkiOiJiOTI0NjhhNTQ2OWY0ZWE2OGIxNGE4MDFmODk3ZjY0ZiIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.cqo8ZKSzUEOUO2qF5gMVHy3TjRz55dz1LPC03tIBpcA", "created_at": "2025-05-11T06:58:01.132", "expires_at": "2025-05-12T06:58:01"}}, {"model": "token_blacklist.outstandingtoken", "pk": 62, "fields": {"user": 234457355328, "jti": "ec9986d3d3fc40a38996dd6dc7431fae", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzA4MSwiaWF0IjoxNzQ2OTQ2NjgxLCJqdGkiOiJlYzk5ODZkM2QzZmM0MGEzODk5NmRkNmRjNzQzMWZhZSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.WgytmHGXLxBpBGARIJjDrYFuA3BbnVmkOaPpSBpjzOQ", "created_at": "2025-05-11T06:58:01.138", "expires_at": "2025-05-12T06:58:01"}}, {"model": "token_blacklist.outstandingtoken", "pk": 63, "fields": {"user": 541150219354505, "jti": "043ebae35558443689bda28ce2eb0338", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzEzMiwiaWF0IjoxNzQ2OTQ2NzMyLCJqdGkiOiIwNDNlYmFlMzU1NTg0NDM2ODliZGEyOGNlMmViMDMzOCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.QOvz8VkLjExRozaodEWNvLZxCLrUj6wmxY_HM6pT5HQ", "created_at": "2025-05-11T06:58:52.697", "expires_at": "2025-05-12T06:58:52"}}, {"model": "token_blacklist.outstandingtoken", "pk": 64, "fields": {"user": 541150219354505, "jti": "fee5f8042e354f379c14fda37b9649e2", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzEzMiwiaWF0IjoxNzQ2OTQ2NzMyLCJqdGkiOiJmZWU1ZjgwNDJlMzU0ZjM3OWMxNGZkYTM3Yjk2NDllMiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ftB4Pp7_vnCYuTzZnZHNwo3jPEw7Jk5JykQQZOuZHIM", "created_at": "2025-05-11T06:58:52.702", "expires_at": "2025-05-12T06:58:52"}}, {"model": "token_blacklist.outstandingtoken", "pk": 65, "fields": {"user": 541150219354505, "jti": "5239d4e706bf43088a907c748a32e181", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE0OCwiaWF0IjoxNzQ2OTQ2NzQ4LCJqdGkiOiI1MjM5ZDRlNzA2YmY0MzA4OGE5MDdjNzQ4YTMyZTE4MSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.j2zPFBVhc-U9pXuq98AxTNauCAsk6hAQVuQyz9luLz0", "created_at": "2025-05-11T06:59:08.929", "expires_at": "2025-05-12T06:59:08"}}, {"model": "token_blacklist.outstandingtoken", "pk": 66, "fields": {"user": 541150219354505, "jti": "b3e89059968c455f92869b9b14ca2cc4", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE0OCwiaWF0IjoxNzQ2OTQ2NzQ4LCJqdGkiOiJiM2U4OTA1OTk2OGM0NTVmOTI4NjliOWIxNGNhMmNjNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yNPukw0iv7Md6Dlc2FvpnHu8aOrOkaZoA_9NcT19hCs", "created_at": "2025-05-11T06:59:08.936", "expires_at": "2025-05-12T06:59:08"}}, {"model": "token_blacklist.outstandingtoken", "pk": 67, "fields": {"user": 541150219354505, "jti": "81c4e236b06a45fa84c7963cce0fb8d3", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE1NSwiaWF0IjoxNzQ2OTQ2NzU1LCJqdGkiOiI4MWM0ZTIzNmIwNmE0NWZhODRjNzk2M2NjZTBmYjhkMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.G7KjxJtDdB5itSFJzz9rqSFi-DhJfi4ZCER3kiXASys", "created_at": "2025-05-11T06:59:15.292", "expires_at": "2025-05-12T06:59:15"}}, {"model": "token_blacklist.outstandingtoken", "pk": 68, "fields": {"user": 541150219354505, "jti": "2578741c21574836999f2f6c8597954e", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE1NSwiaWF0IjoxNzQ2OTQ2NzU1LCJqdGkiOiIyNTc4NzQxYzIxNTc0ODM2OTk5ZjJmNmM4NTk3OTU0ZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.pnRwbIdes87vZQoMemU_BBW6h7Hhk5PPuCSqcG0fu6I", "created_at": "2025-05-11T06:59:15.298", "expires_at": "2025-05-12T06:59:15"}}, {"model": "token_blacklist.outstandingtoken", "pk": 69, "fields": {"user": 541150219354505, "jti": "c11715e9f02142ab98b8f972e633efb4", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE2NCwiaWF0IjoxNzQ2OTQ2NzY0LCJqdGkiOiJjMTE3MTVlOWYwMjE0MmFiOThiOGY5NzJlNjMzZWZiNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.zhkE5EesfgbtT8LmD8QUuQ7FRAWoiovKqsthSvhFoPo", "created_at": "2025-05-11T06:59:24.731", "expires_at": "2025-05-12T06:59:24"}}, {"model": "token_blacklist.outstandingtoken", "pk": 70, "fields": {"user": 541150219354505, "jti": "f71892c5b6fa46569b8c65c067a0e89d", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE2NCwiaWF0IjoxNzQ2OTQ2NzY0LCJqdGkiOiJmNzE4OTJjNWI2ZmE0NjU2OWI4YzY1YzA2N2EwZTg5ZCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.o3nKrOObcoVp5-EZ7ETBGXsCrZtc_u8TBtfZuHGKVu8", "created_at": "2025-05-11T06:59:24.738", "expires_at": "2025-05-12T06:59:24"}}, {"model": "token_blacklist.outstandingtoken", "pk": 71, "fields": {"user": 541150219354505, "jti": "386c624f211c4d509b10d9d9be173e10", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE4OSwiaWF0IjoxNzQ2OTQ2Nzg5LCJqdGkiOiIzODZjNjI0ZjIxMWM0ZDUwOWIxMGQ5ZDliZTE3M2UxMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ieWjr4cVR4SGKy3Av2UHc1f0h36Qyuf5qumeH6pibxw", "created_at": "2025-05-11T06:59:49.278", "expires_at": "2025-05-12T06:59:49"}}, {"model": "token_blacklist.outstandingtoken", "pk": 72, "fields": {"user": 541150219354505, "jti": "606a8098e9fa488d840a9bd7edfcbf13", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzE4OSwiaWF0IjoxNzQ2OTQ2Nzg5LCJqdGkiOiI2MDZhODA5OGU5ZmE0ODhkODQwYTliZDdlZGZjYmYxMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.nFFrEjVMYkJZyJXKkQ0I7b57OKzEoMCA_AgM2FyNqoo", "created_at": "2025-05-11T06:59:49.283", "expires_at": "2025-05-12T06:59:49"}}, {"model": "token_blacklist.outstandingtoken", "pk": 73, "fields": {"user": 541150219354505, "jti": "872c1d9307504a1fa215a65e83626dc3", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzM0NywiaWF0IjoxNzQ2OTQ2OTQ3LCJqdGkiOiI4NzJjMWQ5MzA3NTA0YTFmYTIxNWE2NWU4MzYyNmRjMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0._e30o-l6ck3Jc_Tx1TR27JVFVtAlG_nuSgoGp9xM2aI", "created_at": "2025-05-11T07:02:27.967", "expires_at": "2025-05-12T07:02:27"}}, {"model": "token_blacklist.outstandingtoken", "pk": 74, "fields": {"user": 541150219354505, "jti": "0f294aae48244039905e7d5887d38acb", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzM0NywiaWF0IjoxNzQ2OTQ2OTQ3LCJqdGkiOiIwZjI5NGFhZTQ4MjQ0MDM5OTA1ZTdkNTg4N2QzOGFjYiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Hl2KbJMwkoG9XRxTzuZ3x7IFsnwjBpv_WWrkYLqY_rs", "created_at": "2025-05-11T07:02:27.974", "expires_at": "2025-05-12T07:02:27"}}, {"model": "token_blacklist.outstandingtoken", "pk": 75, "fields": {"user": 541150219354505, "jti": "4da0d59ea917473f91775dc9fe1224b7", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzM1OSwiaWF0IjoxNzQ2OTQ2OTU5LCJqdGkiOiI0ZGEwZDU5ZWE5MTc0NzNmOTE3NzVkYzlmZTEyMjRiNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.yS9f03Of4RMUcl1Mzhl9GDLILCeICdSf4yHLEnT7t7g", "created_at": "2025-05-11T07:02:39.473", "expires_at": "2025-05-12T07:02:39"}}, {"model": "token_blacklist.outstandingtoken", "pk": 76, "fields": {"user": 541150219354505, "jti": "04e14329f0b640c7abda762024fe4b4f", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzM1OSwiaWF0IjoxNzQ2OTQ2OTU5LCJqdGkiOiIwNGUxNDMyOWYwYjY0MGM3YWJkYTc2MjAyNGZlNGI0ZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.mMKwR_0AETaM8Z8j3G11H6EoK3ukk0DAtCCzVmWhxd0", "created_at": "2025-05-11T07:02:39.479", "expires_at": "2025-05-12T07:02:39"}}, {"model": "token_blacklist.outstandingtoken", "pk": 77, "fields": {"user": 234457355328, "jti": "d787bf5512e54d4d92631756d2fc7851", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQyOSwiaWF0IjoxNzQ2OTQ3MDI5LCJqdGkiOiJkNzg3YmY1NTEyZTU0ZDRkOTI2MzE3NTZkMmZjNzg1MSIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.9IMYlcZKPb4QsM-Bc6UZ-v2CrDnmCDTo0_QGFCKr83o", "created_at": "2025-05-11T07:03:49.661", "expires_at": "2025-05-12T07:03:49"}}, {"model": "token_blacklist.outstandingtoken", "pk": 78, "fields": {"user": 234457355328, "jti": "5ae4a88eb46e48e280e93f7fe59b451d", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQyOSwiaWF0IjoxNzQ2OTQ3MDI5LCJqdGkiOiI1YWU0YTg4ZWI0NmU0OGUyODBlOTNmN2ZlNTliNDUxZCIsInVzZXJfaWQiOjIzNDQ1NzM1NTMyOH0.yitY9gqzoU3xG5ybQyQbYBkMrCicZZ9qaWnqcHbhels", "created_at": "2025-05-11T07:03:49.691", "expires_at": "2025-05-12T07:03:49"}}, {"model": "token_blacklist.outstandingtoken", "pk": 79, "fields": {"user": 541150219354505, "jti": "b18edf65a3844bd885070e11d097eac4", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQ1MSwiaWF0IjoxNzQ2OTQ3MDUxLCJqdGkiOiJiMThlZGY2NWEzODQ0YmQ4ODUwNzBlMTFkMDk3ZWFjNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.3xbSllZyJ0HYduhyJXZWlca_Z0pAtdBwbTWKZhIeo7I", "created_at": "2025-05-11T07:04:11.877", "expires_at": "2025-05-12T07:04:11"}}, {"model": "token_blacklist.outstandingtoken", "pk": 80, "fields": {"user": 541150219354505, "jti": "d4b79d0c35694659ac650e66a53fc15e", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQ1MSwiaWF0IjoxNzQ2OTQ3MDUxLCJqdGkiOiJkNGI3OWQwYzM1Njk0NjU5YWM2NTBlNjZhNTNmYzE1ZSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.YyCkEZDUAgv6o0RmMJ0JO11YAYzZx_euNWb3SRv_CLM", "created_at": "2025-05-11T07:04:11.884", "expires_at": "2025-05-12T07:04:11"}}, {"model": "token_blacklist.outstandingtoken", "pk": 81, "fields": {"user": 541150219354505, "jti": "fb0e7db0171e4670b2b0c6eb096be303", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQ3MiwiaWF0IjoxNzQ2OTQ3MDcyLCJqdGkiOiJmYjBlN2RiMDE3MWU0NjcwYjJiMGM2ZWIwOTZiZTMwMyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Kwl6kjx61Db1CP_4bfNwI5EHBsPFZvhDge9fG-RXPK8", "created_at": "2025-05-11T07:04:32.199", "expires_at": "2025-05-12T07:04:32"}}, {"model": "token_blacklist.outstandingtoken", "pk": 82, "fields": {"user": 541150219354505, "jti": "f4729b6b9ebc41d1800b6f4c0a8b3dc2", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzQ3MiwiaWF0IjoxNzQ2OTQ3MDcyLCJqdGkiOiJmNDcyOWI2YjllYmM0MWQxODAwYjZmNGMwYThiM2RjMiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.FeozOUBZIW55bzhkv_dSWXmddafdtSMMsJFqrKI2cFY", "created_at": "2025-05-11T07:04:32.203", "expires_at": "2025-05-12T07:04:32"}}, {"model": "token_blacklist.outstandingtoken", "pk": 83, "fields": {"user": 541150219354505, "jti": "59c21e63fe87415ebf4df7362ec84377", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzczNywiaWF0IjoxNzQ2OTQ3MzM3LCJqdGkiOiI1OWMyMWU2M2ZlODc0MTVlYmY0ZGY3MzYyZWM4NDM3NyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.uab2Xt18o5tCI5MUaDqjS_kxSjbuCkR5KRaGH7jWFdY", "created_at": "2025-05-11T07:08:57.585", "expires_at": "2025-05-12T07:08:57"}}, {"model": "token_blacklist.outstandingtoken", "pk": 84, "fields": {"user": 541150219354505, "jti": "5fb7e753157b45a9830e2c3c7aeb27e6", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzMzczNywiaWF0IjoxNzQ2OTQ3MzM3LCJqdGkiOiI1ZmI3ZTc1MzE1N2I0NWE5ODMwZTJjM2M3YWViMjdlNiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.y8qJr8AtrB9R9ncCoo6HEVgua9zALgHD6hkEko9kJRc", "created_at": "2025-05-11T07:08:57.592", "expires_at": "2025-05-12T07:08:57"}}, {"model": "token_blacklist.outstandingtoken", "pk": 85, "fields": {"user": 541150219354505, "jti": "3b578334b7a044de8fa4e2b7c23991ec", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDA3NywiaWF0IjoxNzQ2OTQ3Njc3LCJqdGkiOiIzYjU3ODMzNGI3YTA0NGRlOGZhNGUyYjdjMjM5OTFlYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.7_IfDsAQN18Met3kuBr35tv_02nYhrTnaiiRHkYR5TY", "created_at": "2025-05-11T07:14:37.642", "expires_at": "2025-05-12T07:14:37"}}, {"model": "token_blacklist.outstandingtoken", "pk": 86, "fields": {"user": 541150219354505, "jti": "c7a90b3ff6704d31a50e2658327e5f6c", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDA3NywiaWF0IjoxNzQ2OTQ3Njc3LCJqdGkiOiJjN2E5MGIzZmY2NzA0ZDMxYTUwZTI2NTgzMjdlNWY2YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.YNHoBmxwuz4ETk-7-EE6SdsVjg_9RY1yn0Gmk5fswHY", "created_at": "2025-05-11T07:14:37.647", "expires_at": "2025-05-12T07:14:37"}}, {"model": "token_blacklist.outstandingtoken", "pk": 87, "fields": {"user": 541150219354505, "jti": "a1553196451f41c5a5dec10df90f6f9a", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDA4NSwiaWF0IjoxNzQ2OTQ3Njg1LCJqdGkiOiJhMTU1MzE5NjQ1MWY0MWM1YTVkZWMxMGRmOTBmNmY5YSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.e4ay3A-V_bH2rxpejtEYQk1qkIsc5YkKyhJ77wHU1f8", "created_at": "2025-05-11T07:14:45.358", "expires_at": "2025-05-12T07:14:45"}}, {"model": "token_blacklist.outstandingtoken", "pk": 88, "fields": {"user": 541150219354505, "jti": "7f84a8a946db423e905a6a7a35557627", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDA4NSwiaWF0IjoxNzQ2OTQ3Njg1LCJqdGkiOiI3Zjg0YThhOTQ2ZGI0MjNlOTA1YTZhN2EzNTU1NzYyNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.nADk7JhBlfXqT4wOxiwLajPryj5l0U5xpowNvtL4c2E", "created_at": "2025-05-11T07:14:45.364", "expires_at": "2025-05-12T07:14:45"}}, {"model": "token_blacklist.outstandingtoken", "pk": 89, "fields": {"user": 541150219354505, "jti": "4d1516e74f40463b94c38bbb5b3b0f09", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDM4NSwiaWF0IjoxNzQ2OTQ3OTg1LCJqdGkiOiI0ZDE1MTZlNzRmNDA0NjNiOTRjMzhiYmI1YjNiMGYwOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.147nvJR9ixA9zaPKZymc-Ec0en5qrDHZGwdBWSqklS8", "created_at": "2025-05-11T07:19:45.288", "expires_at": "2025-05-12T07:19:45"}}, {"model": "token_blacklist.outstandingtoken", "pk": 90, "fields": {"user": 541150219354505, "jti": "9a1968c3ad2c4d0183cea2c171a1fb8c", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDM4NSwiaWF0IjoxNzQ2OTQ3OTg1LCJqdGkiOiI5YTE5NjhjM2FkMmM0ZDAxODNjZWEyYzE3MWExZmI4YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.o-UVktjEXLvX167RJFjcqyxqmjfkIeo6NqLxS7BJpoM", "created_at": "2025-05-11T07:19:45.293", "expires_at": "2025-05-12T07:19:45"}}, {"model": "token_blacklist.outstandingtoken", "pk": 91, "fields": {"user": 541150219354505, "jti": "c71b08bf500a4148a8116b815426a998", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDM5MiwiaWF0IjoxNzQ2OTQ3OTkyLCJqdGkiOiJjNzFiMDhiZjUwMGE0MTQ4YTgxMTZiODE1NDI2YTk5OCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.M0qhY_KwyBwhkaLtz4YD9EeVby7dHH-h1CsNNSqvymo", "created_at": "2025-05-11T07:19:52.775", "expires_at": "2025-05-12T07:19:52"}}, {"model": "token_blacklist.outstandingtoken", "pk": 92, "fields": {"user": 541150219354505, "jti": "17f4296d9abe4c49bbbd6a2983460920", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzNDM5MiwiaWF0IjoxNzQ2OTQ3OTkyLCJqdGkiOiIxN2Y0Mjk2ZDlhYmU0YzQ5YmJiZDZhMjk4MzQ2MDkyMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.ocYv2uADtkCXetdbjkz7oTjqqZ2wxqRqWgrzdyGmFCg", "created_at": "2025-05-11T07:19:52.781", "expires_at": "2025-05-12T07:19:52"}}, {"model": "token_blacklist.outstandingtoken", "pk": 93, "fields": {"user": 541150219354505, "jti": "9bd2eb7cb055406e9703fd6807ff89b9", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzOTkyNSwiaWF0IjoxNzQ2OTUzNTI1LCJqdGkiOiI5YmQyZWI3Y2IwNTU0MDZlOTcwM2ZkNjgwN2ZmODliOSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Cag7YylpSweXXgXrVa76b1LbOOG9WRtFxMHwqyjKAQo", "created_at": "2025-05-11T08:52:05.976", "expires_at": "2025-05-12T08:52:05"}}, {"model": "token_blacklist.outstandingtoken", "pk": 94, "fields": {"user": 541150219354505, "jti": "8304f9292aea42e2a2f9a0a9eb374134", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzAzOTkyNSwiaWF0IjoxNzQ2OTUzNTI1LCJqdGkiOiI4MzA0ZjkyOTJhZWE0MmUyYTJmOWEwYTllYjM3NDEzNCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.UlPzU5HEqHfhcxXe6XNkxWM9P4oMFmpMjcOmrka-70Y", "created_at": "2025-05-11T08:52:05.999", "expires_at": "2025-05-12T08:52:05"}}, {"model": "token_blacklist.outstandingtoken", "pk": 95, "fields": {"user": 541150219354505, "jti": "6aa3eaf7831f46a7baf230c38d946ad0", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzA0MDI0NCwiaWF0IjoxNzQ2OTUzODQ0LCJqdGkiOiI2YWEzZWFmNzgzMWY0NmE3YmFmMjMwYzM4ZDk0NmFkMCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.hwG2REt5fh9sVS2vT6dvEGYVlGSn7dEViNObOMQhrLE", "created_at": "2025-05-11T08:57:24.487", "expires_at": "2025-05-12T08:57:24"}}, {"model": "token_blacklist.outstandingtoken", "pk": 96, "fields": {"user": 541150219354505, "jti": "8931090168cd4a4f87a65b241cf52c52", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzA0MDI0NCwiaWF0IjoxNzQ2OTUzODQ0LCJqdGkiOiI4OTMxMDkwMTY4Y2Q0YTRmODdhNjViMjQxY2Y1MmM1MiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Xrvm1OHSSrXRbJl3XSNaCOCl2QAkB9nGEO_RyY6w0h8", "created_at": "2025-05-11T08:57:24.494", "expires_at": "2025-05-12T08:57:24"}}, {"model": "token_blacklist.outstandingtoken", "pk": 97, "fields": {"user": 164357145152, "jti": "47ae91ee447d4e02bc4757e2571fb010", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODg5NiwiaWF0IjoxNzQ3MDIyNDk2LCJqdGkiOiI0N2FlOTFlZTQ0N2Q0ZTAyYmM0NzU3ZTI1NzFmYjAxMCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.ePPDzZTBFGfSmG22gYFlS1nzW_R-NYVxJ8jlBk6vqSs", "created_at": "2025-05-12T04:01:36.923", "expires_at": "2025-05-13T04:01:36"}}, {"model": "token_blacklist.outstandingtoken", "pk": 98, "fields": {"user": 164357145152, "jti": "3d833b8b9ccf41c8846b6bbe91a4982b", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODg5NiwiaWF0IjoxNzQ3MDIyNDk2LCJqdGkiOiIzZDgzM2I4YjljY2Y0MWM4ODQ2YjZiYmU5MWE0OTgyYiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.VXnM-aH3wmebPDyruCTUY3nKDFy4546sMarxdRbacr8", "created_at": "2025-05-12T04:01:36.930", "expires_at": "2025-05-13T04:01:36"}}, {"model": "token_blacklist.outstandingtoken", "pk": 99, "fields": {"user": 541150219354505, "jti": "ba677b465b3d4068bf88c9af90d2ff70", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODkwNSwiaWF0IjoxNzQ3MDIyNTA1LCJqdGkiOiJiYTY3N2I0NjViM2Q0MDY4YmY4OGM5YWY5MGQyZmY3MCIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.2kWeBJK1pQfri2lRTgbF8tnlUhdau_wKGPYpxaDUxQQ", "created_at": "2025-05-12T04:01:45.006", "expires_at": "2025-05-13T04:01:45"}}, {"model": "token_blacklist.outstandingtoken", "pk": 100, "fields": {"user": 541150219354505, "jti": "8ed4c515cd0b43fc9d98890ce2cfaf5c", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODkwNSwiaWF0IjoxNzQ3MDIyNTA1LCJqdGkiOiI4ZWQ0YzUxNWNkMGI0M2ZjOWQ5ODg5MGNlMmNmYWY1YyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.PDWO1fFpiX2ho7vkAX9IPIsPq34SuoKMrA17d0IVnjg", "created_at": "2025-05-12T04:01:45.012", "expires_at": "2025-05-13T04:01:45"}}, {"model": "token_blacklist.outstandingtoken", "pk": 101, "fields": {"user": 164357145152, "jti": "91de5ee2bb1e4b82b98e60194c7d3635", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODk0MSwiaWF0IjoxNzQ3MDIyNTQxLCJqdGkiOiI5MWRlNWVlMmJiMWU0YjgyYjk4ZTYwMTk0YzdkMzYzNSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.Jhq_CDHsGyrjA54AWut-FccmBZ27BD4i4bDg5r-CVvM", "created_at": "2025-05-12T04:02:21.967", "expires_at": "2025-05-13T04:02:21"}}, {"model": "token_blacklist.outstandingtoken", "pk": 102, "fields": {"user": 164357145152, "jti": "39c30a8a1e814e3ebd1ad5bd7a5d0c7e", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODk0MSwiaWF0IjoxNzQ3MDIyNTQxLCJqdGkiOiIzOWMzMGE4YTFlODE0ZTNlYmQxYWQ1YmQ3YTVkMGM3ZSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.IJ8y71qlbv0_i0Rvy_9CuulljpjL01k15W9RbcgxGiA", "created_at": "2025-05-12T04:02:21.972", "expires_at": "2025-05-13T04:02:21"}}, {"model": "token_blacklist.outstandingtoken", "pk": 103, "fields": {"user": 164357145152, "jti": "5fc59d38283b4d08b05b3ccd7b22954f", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODk4NiwiaWF0IjoxNzQ3MDIyNTg2LCJqdGkiOiI1ZmM1OWQzODI4M2I0ZDA4YjA1YjNjY2Q3YjIyOTU0ZiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.-8tejxVrE7fpwcON-KCB-1XYWI0mUsIJB6Q8xkyu4Us", "created_at": "2025-05-12T04:03:06.350", "expires_at": "2025-05-13T04:03:06"}}, {"model": "token_blacklist.outstandingtoken", "pk": 104, "fields": {"user": 164357145152, "jti": "5ea453e97fa24025adb58024414ed927", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwODk4NiwiaWF0IjoxNzQ3MDIyNTg2LCJqdGkiOiI1ZWE0NTNlOTdmYTI0MDI1YWRiNTgwMjQ0MTRlZDkyNyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.-4NGSqqEZb0PG3TOfySE2FKDUjlL8IQ_xWCGjiHrA30", "created_at": "2025-05-12T04:03:06.355", "expires_at": "2025-05-13T04:03:06"}}, {"model": "token_blacklist.outstandingtoken", "pk": 105, "fields": {"user": 541150219354505, "jti": "e916d9220d4642179c3236c0be58cec7", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTAwOCwiaWF0IjoxNzQ3MDIyNjA4LCJqdGkiOiJlOTE2ZDkyMjBkNDY0MjE3OWMzMjM2YzBiZTU4Y2VjNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Ctyr2goFnCOklbrOsBtCl8xOCmr6F1SsaOAJbynLtos", "created_at": "2025-05-12T04:03:28.135", "expires_at": "2025-05-13T04:03:28"}}, {"model": "token_blacklist.outstandingtoken", "pk": 106, "fields": {"user": 541150219354505, "jti": "0f17c6285c934bedb2d9e71ff49d553f", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTAwOCwiaWF0IjoxNzQ3MDIyNjA4LCJqdGkiOiIwZjE3YzYyODVjOTM0YmVkYjJkOWU3MWZmNDlkNTUzZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.SxQS4QYCXpAObAX9H2HtxVldrQHwOvO8rdSL2bJJaxc", "created_at": "2025-05-12T04:03:28.140", "expires_at": "2025-05-13T04:03:28"}}, {"model": "token_blacklist.outstandingtoken", "pk": 107, "fields": {"user": 164357145152, "jti": "1f27f5ea49b3439e91cf081158844464", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTA1NiwiaWF0IjoxNzQ3MDIyNjU2LCJqdGkiOiIxZjI3ZjVlYTQ5YjM0MzllOTFjZjA4MTE1ODg0NDQ2NCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.HWgnWOYLwfYcooxkvSMTmhUa4IAPZ0TYX2-tqO4IJio", "created_at": "2025-05-12T04:04:16.065", "expires_at": "2025-05-13T04:04:16"}}, {"model": "token_blacklist.outstandingtoken", "pk": 108, "fields": {"user": 164357145152, "jti": "6e2fdb2112814f618cb0ac213b48f587", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTA1NiwiaWF0IjoxNzQ3MDIyNjU2LCJqdGkiOiI2ZTJmZGIyMTEyODE0ZjYxOGNiMGFjMjEzYjQ4ZjU4NyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.gBdhotEPw4XZyAfW0gYUy1NcBZYTkWxO1knD1qBOqX0", "created_at": "2025-05-12T04:04:16.072", "expires_at": "2025-05-13T04:04:16"}}, {"model": "token_blacklist.outstandingtoken", "pk": 109, "fields": {"user": 164357145152, "jti": "b79b3c9942584f298534e6d278e62d61", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTE1MywiaWF0IjoxNzQ3MDIyNzUzLCJqdGkiOiJiNzliM2M5OTQyNTg0ZjI5ODUzNGU2ZDI3OGU2MmQ2MSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.jW9xMhJOLF-x5EvXemaOH70PsVHu9qaF6aidcQYOTMw", "created_at": "2025-05-12T04:05:53.354", "expires_at": "2025-05-13T04:05:53"}}, {"model": "token_blacklist.outstandingtoken", "pk": 110, "fields": {"user": 164357145152, "jti": "ddbd5de1cf7e42539700ad9258c5f294", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzEwOTE1MywiaWF0IjoxNzQ3MDIyNzUzLCJqdGkiOiJkZGJkNWRlMWNmN2U0MjUzOTcwMGFkOTI1OGM1ZjI5NCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.2S1sqPRof_q3trTPM_xuhsKEAs1aiSz0ObIv_iHoDOE", "created_at": "2025-05-12T04:05:53.359", "expires_at": "2025-05-13T04:05:53"}}, {"model": "token_blacklist.outstandingtoken", "pk": 111, "fields": {"user": 164357145152, "jti": "9f8419233c9042efb055f94a9c80d44b", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE1ODYzMCwiaWF0IjoxNzQ3MDcyMjMwLCJqdGkiOiI5Zjg0MTkyMzNjOTA0MmVmYjA1NWY5NGE5YzgwZDQ0YiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.P3mT3j2z7a4ggVnQCUXSWzFrL4KgP1MzZT5L0Kp5dtM", "created_at": "2025-05-12T17:50:30.555", "expires_at": "2025-05-13T17:50:30"}}, {"model": "token_blacklist.outstandingtoken", "pk": 112, "fields": {"user": 164357145152, "jti": "803edf256e5c4e6abe32ffebb9ff3093", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE1ODYzMCwiaWF0IjoxNzQ3MDcyMjMwLCJqdGkiOiI4MDNlZGYyNTZlNWM0ZTZhYmUzMmZmZWJiOWZmMzA5MyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.Gev17e5EOguGnbnPr4hiRBTYC6b9A3LLhsin4iop6e8", "created_at": "2025-05-12T17:50:30.578", "expires_at": "2025-05-13T17:50:30"}}, {"model": "token_blacklist.outstandingtoken", "pk": 113, "fields": {"user": 164357145152, "jti": "bd85199045cf44e5bb2e94afb706d4ce", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5MjU1NywiaWF0IjoxNzQ3MTA2MTU3LCJqdGkiOiJiZDg1MTk5MDQ1Y2Y0NGU1YmIyZTk0YWZiNzA2ZDRjZSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.Z3NalC6FMOq3ADOuoyg9t7yLT6nw74cVdy5aWcP75IE", "created_at": "2025-05-13T03:15:57.454", "expires_at": "2025-05-14T03:15:57"}}, {"model": "token_blacklist.outstandingtoken", "pk": 114, "fields": {"user": 164357145152, "jti": "d6782fa2a9134afeb5ff0fa9fb55e47a", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5MjU1NywiaWF0IjoxNzQ3MTA2MTU3LCJqdGkiOiJkNjc4MmZhMmE5MTM0YWZlYjVmZjBmYTlmYjU1ZTQ3YSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.4omMyCPAYKGVmHUDtF6DM8ngCN6xoIrnjobyfc_qsqc", "created_at": "2025-05-13T03:15:57.464", "expires_at": "2025-05-14T03:15:57"}}, {"model": "token_blacklist.outstandingtoken", "pk": 115, "fields": {"user": 164357145152, "jti": "6dd83344db604f1ba50ef6b13d49bbde", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5MjY1NywiaWF0IjoxNzQ3MTA2MjU3LCJqdGkiOiI2ZGQ4MzM0NGRiNjA0ZjFiYTUwZWY2YjEzZDQ5YmJkZSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.X63brq4sNaB6-WVwcY3MsBgL3ao9rLmUsy08Td4HumE", "created_at": "2025-05-13T03:17:37.008", "expires_at": "2025-05-14T03:17:37"}}, {"model": "token_blacklist.outstandingtoken", "pk": 116, "fields": {"user": 164357145152, "jti": "45cf28d543c0452aa351c948eb952025", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5MjY1NywiaWF0IjoxNzQ3MTA2MjU3LCJqdGkiOiI0NWNmMjhkNTQzYzA0NTJhYTM1MWM5NDhlYjk1MjAyNSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.a1gl-bSeN_JovrFWSm-E9xd-gyuafpgo9cYESdRcLeg", "created_at": "2025-05-13T03:17:37.013", "expires_at": "2025-05-14T03:17:37"}}, {"model": "token_blacklist.outstandingtoken", "pk": 117, "fields": {"user": 541150219354505, "jti": "ffb7cd28a79c44e997ff7e87d976ae0c", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5MjcwNiwiaWF0IjoxNzQ3MTA2MzA2LCJqdGkiOiJmZmI3Y2QyOGE3OWM0NGU5OTdmZjdlODdkOTc2YWUwYyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.RLnSGtu8-AQ-spU3NMY66CmCQftOIJIqiyIuSZ_IRHc", "created_at": "2025-05-13T03:18:26.317", "expires_at": "2025-05-14T03:18:26"}}, {"model": "token_blacklist.outstandingtoken", "pk": 118, "fields": {"user": 541150219354505, "jti": "a464b20b20724ab2ac1bf883145c0589", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5MjcwNiwiaWF0IjoxNzQ3MTA2MzA2LCJqdGkiOiJhNDY0YjIwYjIwNzI0YWIyYWMxYmY4ODMxNDVjMDU4OSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.2agzcYyGg2eADj94Vwn7T7zmezJHvLT96d7-ebdghzA", "created_at": "2025-05-13T03:18:26.326", "expires_at": "2025-05-14T03:18:26"}}, {"model": "token_blacklist.outstandingtoken", "pk": 119, "fields": {"user": 541150219354505, "jti": "9a63f9f62d654a7fbcb9dffefa4dcb9a", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5Mjc1OSwiaWF0IjoxNzQ3MTA2MzU5LCJqdGkiOiI5YTYzZjlmNjJkNjU0YTdmYmNiOWRmZmVmYTRkY2I5YSIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.xrdfUNORXoHcLj-6v-JBglVICmPuRRUeQyRbMe76-bY", "created_at": "2025-05-13T03:19:19.195", "expires_at": "2025-05-14T03:19:19"}}, {"model": "token_blacklist.outstandingtoken", "pk": 120, "fields": {"user": 541150219354505, "jti": "0ab8815463344c34a7793f2c5b090b27", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5Mjc1OSwiaWF0IjoxNzQ3MTA2MzU5LCJqdGkiOiIwYWI4ODE1NDYzMzQ0YzM0YTc3OTNmMmM1YjA5MGIyNyIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.Q05hhk0m788JCVz32UhXEHv_CVkvqsdnlCRJzFwKofQ", "created_at": "2025-05-13T03:19:19.202", "expires_at": "2025-05-14T03:19:19"}}, {"model": "token_blacklist.outstandingtoken", "pk": 121, "fields": {"user": 164357145152, "jti": "1b500927e35344aebcc69b5a9e84df29", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5Mjc5NCwiaWF0IjoxNzQ3MTA2Mzk0LCJqdGkiOiIxYjUwMDkyN2UzNTM0NGFlYmNjNjliNWE5ZTg0ZGYyOSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.VJX3cdhupcrtRo5qSFmwknZ4-ckyh9TCSgdVdFuS3QI", "created_at": "2025-05-13T03:19:54.233", "expires_at": "2025-05-14T03:19:54"}}, {"model": "token_blacklist.outstandingtoken", "pk": 122, "fields": {"user": 164357145152, "jti": "9c5b336935cc48168ab07166ccc1f3e7", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5Mjc5NCwiaWF0IjoxNzQ3MTA2Mzk0LCJqdGkiOiI5YzViMzM2OTM1Y2M0ODE2OGFiMDcxNjZjY2MxZjNlNyIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.PiKll0aE6cAhREvp7nlSp94xUJxqpZN1k657bN7IXCs", "created_at": "2025-05-13T03:19:54.240", "expires_at": "2025-05-14T03:19:54"}}, {"model": "token_blacklist.outstandingtoken", "pk": 123, "fields": {"user": 164357145152, "jti": "b8b87c8d31844ab695b30499e5d09b91", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5Mjg0MSwiaWF0IjoxNzQ3MTA2NDQxLCJqdGkiOiJiOGI4N2M4ZDMxODQ0YWI2OTViMzA0OTllNWQwOWI5MSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.ZFSilPz4sfCzHWV-RQNSZNBsOqbWBhhGgYpDRzfMSx0", "created_at": "2025-05-13T03:20:41.433", "expires_at": "2025-05-14T03:20:41"}}, {"model": "token_blacklist.outstandingtoken", "pk": 124, "fields": {"user": 164357145152, "jti": "26d30ed938504a37aff7ad077b33ba26", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5Mjg0MSwiaWF0IjoxNzQ3MTA2NDQxLCJqdGkiOiIyNmQzMGVkOTM4NTA0YTM3YWZmN2FkMDc3YjMzYmEyNiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.2WxxqQHiY6pTxdSt7DzQWbM6gRxPmRbDmeBrRc7sBj4", "created_at": "2025-05-13T03:20:41.437", "expires_at": "2025-05-14T03:20:41"}}, {"model": "token_blacklist.outstandingtoken", "pk": 125, "fields": {"user": 164357145152, "jti": "cb3ac741612e49e5bfbf589ea62f4492", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5Mjk0OCwiaWF0IjoxNzQ3MTA2NTQ4LCJqdGkiOiJjYjNhYzc0MTYxMmU0OWU1YmZiZjU4OWVhNjJmNDQ5MiIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.ikb0KSk5oBd2Ew5rIYObWg9tAiUlwPcWIhsAWzVO2m0", "created_at": "2025-05-13T03:22:28.312", "expires_at": "2025-05-14T03:22:28"}}, {"model": "token_blacklist.outstandingtoken", "pk": 126, "fields": {"user": 164357145152, "jti": "e011410279ee45d185eb1953454203fa", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5Mjk0OCwiaWF0IjoxNzQ3MTA2NTQ4LCJqdGkiOiJlMDExNDEwMjc5ZWU0NWQxODVlYjE5NTM0NTQyMDNmYSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0._aWAepVg47xX5m0RJkOgvoDcmCOolio16BQdSJ_P8Zg", "created_at": "2025-05-13T03:22:28.320", "expires_at": "2025-05-14T03:22:28"}}, {"model": "token_blacklist.outstandingtoken", "pk": 127, "fields": {"user": 164357145152, "jti": "412664cdcd5d4a7484d3c1fc62e0b180", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5Mjk4NywiaWF0IjoxNzQ3MTA2NTg3LCJqdGkiOiI0MTI2NjRjZGNkNWQ0YTc0ODRkM2MxZmM2MmUwYjE4MCIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.oPrFkyZyZgj9E4X_9rJ-xNCAts7jNIi2TfLyCSaDicQ", "created_at": "2025-05-13T03:23:07.941", "expires_at": "2025-05-14T03:23:07"}}, {"model": "token_blacklist.outstandingtoken", "pk": 128, "fields": {"user": 164357145152, "jti": "0383341008da4e4fb4e0ac8273225445", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0NzE5Mjk4NywiaWF0IjoxNzQ3MTA2NTg3LCJqdGkiOiIwMzgzMzQxMDA4ZGE0ZTRmYjRlMGFjODI3MzIyNTQ0NSIsInVzZXJfaWQiOjE2NDM1NzE0NTE1Mn0.B9f0RdEKQ4rU713M9X_YsXatQahJXYcr2PVzX6pPJzk", "created_at": "2025-05-13T03:23:07.947", "expires_at": "2025-05-14T03:23:07"}}, {"model": "captcha.captchastore", "pk": 1, "fields": {"challenge": "4+4=", "response": "8", "hashkey": "72bbfa3fb4602a2178870c0407ee1007b78a7516", "expiration": "2025-05-09T11:30:01.904"}}, {"model": "captcha.captchastore", "pk": 3, "fields": {"challenge": "7-6=", "response": "1", "hashkey": "e36b0e8602856547cec04d8df3e93fb33c8b1d8d", "expiration": "2025-05-09T11:30:52.600"}}, {"model": "captcha.captchastore", "pk": 4, "fields": {"challenge": "10-4=", "response": "6", "hashkey": "92db814393f9f23a0bc66c66ad86cebb41c6437a", "expiration": "2025-05-09T11:31:32.296"}}, {"model": "captcha.captchastore", "pk": 5, "fields": {"challenge": "3*5=", "response": "15", "hashkey": "84c3080a84096d1edff65ff5b6044d66dcce5263", "expiration": "2025-05-09T11:31:56.055"}}, {"model": "captcha.captchastore", "pk": 10, "fields": {"challenge": "3*6=", "response": "18", "hashkey": "5cbc64e2f2ab0e35c1d4f98034699af8f877c02a", "expiration": "2025-05-10T16:01:38.138"}}, {"model": "captcha.captchastore", "pk": 11, "fields": {"challenge": "5-4=", "response": "1", "hashkey": "187f1cd32b15e49ab4f90f28741b35c059dd4857", "expiration": "2025-05-10T16:03:03.881"}}, {"model": "captcha.captchastore", "pk": 13, "fields": {"challenge": "9-6=", "response": "3", "hashkey": "ad23f4bf943bc2f81046a4f2ed1128636f1c2f9f", "expiration": "2025-05-10T16:03:56.399"}}, {"model": "captcha.captchastore", "pk": 14, "fields": {"challenge": "8-8=", "response": "0", "hashkey": "ae891229136cc274c9919ce9ad2f6fce8cfbdfce", "expiration": "2025-05-10T16:05:08.453"}}, {"model": "captcha.captchastore", "pk": 15, "fields": {"challenge": "10-6=", "response": "4", "hashkey": "bb73d72a035536240287c5f725788b85c839b65d", "expiration": "2025-05-10T16:11:06.752"}}, {"model": "captcha.captchastore", "pk": 16, "fields": {"challenge": "5*1=", "response": "5", "hashkey": "5514d1545d82eb5546aa58b880408b9730645b03", "expiration": "2025-05-10T16:14:36.626"}}, {"model": "captcha.captchastore", "pk": 17, "fields": {"challenge": "8-4=", "response": "4", "hashkey": "62f56b979a5f23e105beb82a975749c458244319", "expiration": "2025-05-10T16:14:39.095"}}, {"model": "captcha.captchastore", "pk": 18, "fields": {"challenge": "9*7=", "response": "63", "hashkey": "56f79b005c914a4891f66d02a4c3ff07b88b08c4", "expiration": "2025-05-10T16:15:00.696"}}, {"model": "captcha.captchastore", "pk": 19, "fields": {"challenge": "5-4=", "response": "1", "hashkey": "fcffd0e16f93b2970e081a8b5a2c9648aa723d8d", "expiration": "2025-05-10T16:15:03.094"}}, {"model": "captcha.captchastore", "pk": 20, "fields": {"challenge": "4-1=", "response": "3", "hashkey": "1b77fe52fbb70500abb59614137f9d23935b1d71", "expiration": "2025-05-10T16:15:47.577"}}, {"model": "captcha.captchastore", "pk": 21, "fields": {"challenge": "7*7=", "response": "49", "hashkey": "aac35f96f96910a534fde2f54a561c02f9451b25", "expiration": "2025-05-10T16:15:55.184"}}, {"model": "captcha.captchastore", "pk": 22, "fields": {"challenge": "6-3=", "response": "3", "hashkey": "2d6dd47b78540accb03c8fdebb00ce8694d018bf", "expiration": "2025-05-10T16:16:42.303"}}, {"model": "captcha.captchastore", "pk": 23, "fields": {"challenge": "4-2=", "response": "2", "hashkey": "f406dfb0b7a372b095e6168321f9728f82e89007", "expiration": "2025-05-10T16:16:57.724"}}, {"model": "captcha.captchastore", "pk": 24, "fields": {"challenge": "2-1=", "response": "1", "hashkey": "86ab53416b654e0ca3b90830b2ec9b9e53338de3", "expiration": "2025-05-10T16:17:41.052"}}, {"model": "captcha.captchastore", "pk": 25, "fields": {"challenge": "1+10=", "response": "11", "hashkey": "495e9ef27375b2367ce44213d274831cad0782b6", "expiration": "2025-05-10T16:18:27.217"}}, {"model": "captcha.captchastore", "pk": 26, "fields": {"challenge": "10-5=", "response": "5", "hashkey": "79df7107df52f18a9ce8827f2e13a37e882e1c71", "expiration": "2025-05-10T16:19:19.249"}}, {"model": "captcha.captchastore", "pk": 27, "fields": {"challenge": "2+10=", "response": "12", "hashkey": "a25d7c39268ec8aaa65c04754a6fd7397450b0bb", "expiration": "2025-05-10T16:19:22.038"}}, {"model": "captcha.captchastore", "pk": 28, "fields": {"challenge": "4+9=", "response": "13", "hashkey": "0e25e4b646b03ec8db8fe3ca5090fc798770868f", "expiration": "2025-05-10T16:20:02.157"}}, {"model": "captcha.captchastore", "pk": 29, "fields": {"challenge": "8+10=", "response": "18", "hashkey": "e8966246493028ad53531d4e2ed83dfbe451c98b", "expiration": "2025-05-10T16:20:30.079"}}, {"model": "captcha.captchastore", "pk": 30, "fields": {"challenge": "5-1=", "response": "4", "hashkey": "1579cac15c4a1edced53db0959f615c0560bdb73", "expiration": "2025-05-10T16:20:31.829"}}, {"model": "captcha.captchastore", "pk": 34, "fields": {"challenge": "2-1=", "response": "1", "hashkey": "14436cdf682b54313746501a76c52b72cf71d2be", "expiration": "2025-05-10T16:25:04.319"}}, {"model": "captcha.captchastore", "pk": 35, "fields": {"challenge": "7+10=", "response": "17", "hashkey": "9d9172056f30ee71513b04e1a2b9b46a201975dd", "expiration": "2025-05-10T16:26:33.375"}}, {"model": "captcha.captchastore", "pk": 36, "fields": {"challenge": "8-2=", "response": "6", "hashkey": "29397a9b12e84d0e6d66c14e4c11af716c0c76e9", "expiration": "2025-05-10T16:26:37.322"}}, {"model": "captcha.captchastore", "pk": 37, "fields": {"challenge": "6+6=", "response": "12", "hashkey": "0a8dde29c5119247dc6383ec4de88e0715b0f288", "expiration": "2025-05-10T16:27:34.132"}}, {"model": "captcha.captchastore", "pk": 38, "fields": {"challenge": "1+8=", "response": "9", "hashkey": "a212dba3e7e8cebcf95b58d06b4281411605669c", "expiration": "2025-05-10T16:27:58.107"}}, {"model": "captcha.captchastore", "pk": 39, "fields": {"challenge": "5*3=", "response": "15", "hashkey": "f768756d2766f850b0f649838ec36948b9425b3e", "expiration": "2025-05-10T16:28:31.960"}}, {"model": "captcha.captchastore", "pk": 41, "fields": {"challenge": "4*9=", "response": "36", "hashkey": "001bdf0ada739e93926fd5a09bc3428c0e287595", "expiration": "2025-05-10T16:28:51.206"}}, {"model": "captcha.captchastore", "pk": 42, "fields": {"challenge": "8*5=", "response": "40", "hashkey": "ef738b9ace116ad4fccf0ad8204c7ba917b6beaa", "expiration": "2025-05-10T16:29:53.059"}}, {"model": "captcha.captchastore", "pk": 43, "fields": {"challenge": "6-5=", "response": "1", "hashkey": "d418d5f99b57d16e3d8cdcf80c1fb63fa8e44b7f", "expiration": "2025-05-10T16:29:57.151"}}, {"model": "captcha.captchastore", "pk": 44, "fields": {"challenge": "3+9=", "response": "12", "hashkey": "dd05bc56e00c6dc5ed52873799c3f6579df30d25", "expiration": "2025-05-10T16:30:01.959"}}, {"model": "captcha.captchastore", "pk": 45, "fields": {"challenge": "9*10=", "response": "90", "hashkey": "d9b90614d0f3a02f4bb3bfea91a9b9daaacfd3c6", "expiration": "2025-05-10T16:30:24.334"}}, {"model": "captcha.captchastore", "pk": 52, "fields": {"challenge": "10-9=", "response": "1", "hashkey": "c3bb3ec4b3b51d6ed6eaf0b31f42115d01f656c0", "expiration": "2025-05-10T17:06:09.246"}}, {"model": "captcha.captchastore", "pk": 53, "fields": {"challenge": "4-1=", "response": "3", "hashkey": "66704048ea369309a8c7508531d753b9aadf79fb", "expiration": "2025-05-10T17:06:12.252"}}, {"model": "captcha.captchastore", "pk": 54, "fields": {"challenge": "8-7=", "response": "1", "hashkey": "07140364c9d91fd81888ba1e233a1682a2beb066", "expiration": "2025-05-10T17:06:18.683"}}, {"model": "captcha.captchastore", "pk": 55, "fields": {"challenge": "5+6=", "response": "11", "hashkey": "4f156eebefe173ca14e2f23092c42031b4638534", "expiration": "2025-05-10T17:06:30.484"}}, {"model": "captcha.captchastore", "pk": 56, "fields": {"challenge": "4*1=", "response": "4", "hashkey": "c75139cd99f45efd86e3d272868379164dbbe9e8", "expiration": "2025-05-10T17:06:45.605"}}, {"model": "captcha.captchastore", "pk": 57, "fields": {"challenge": "6+5=", "response": "11", "hashkey": "59f4ed407d66a392a98e3aabf0a1fdd97ddf69f9", "expiration": "2025-05-10T17:06:51.209"}}, {"model": "captcha.captchastore", "pk": 58, "fields": {"challenge": "8+5=", "response": "13", "hashkey": "f95050bbd69da3090a7d1c51d2fa4bc20987fa0a", "expiration": "2025-05-10T17:07:06.438"}}, {"model": "captcha.captchastore", "pk": 59, "fields": {"challenge": "5*1=", "response": "5", "hashkey": "cd7c43f09aa9e40b82eedf1ade8b2a45b5ddf717", "expiration": "2025-05-10T17:07:14.302"}}, {"model": "captcha.captchastore", "pk": 66, "fields": {"challenge": "9+10=", "response": "19", "hashkey": "a80aa28d7ddace77308f473bb6ea2c50935527f1", "expiration": "2025-05-10T17:31:10.835"}}, {"model": "captcha.captchastore", "pk": 69, "fields": {"challenge": "2-2=", "response": "0", "hashkey": "81805069e93b45d6555364fd8d3782c41e8f33eb", "expiration": "2025-05-10T17:32:43.583"}}, {"model": "captcha.captchastore", "pk": 73, "fields": {"challenge": "4*4=", "response": "16", "hashkey": "7c4f6cfdd8aa605502e7748ddd98452beefabde7", "expiration": "2025-05-10T17:34:37.969"}}, {"model": "captcha.captchastore", "pk": 77, "fields": {"challenge": "8+5=", "response": "13", "hashkey": "3f219bcabc6740edd4a5f4a56c8f7c903251ee07", "expiration": "2025-05-11T14:55:55.927"}}, {"model": "captcha.captchastore", "pk": 81, "fields": {"challenge": "10*10=", "response": "100", "hashkey": "704d3958b49d6c06fc6a03d40d6dcb0198d5a600", "expiration": "2025-05-11T14:58:10.175"}}, {"model": "captcha.captchastore", "pk": 89, "fields": {"challenge": "5-1=", "response": "4", "hashkey": "183ce265879574fa61d8748ef56bd7c56a7829ea", "expiration": "2025-05-11T14:59:01.169"}}, {"model": "captcha.captchastore", "pk": 90, "fields": {"challenge": "1*5=", "response": "5", "hashkey": "123a58743bd1e204d392a5c67b5f2f60ded34038", "expiration": "2025-05-11T14:59:43.159"}}, {"model": "captcha.captchastore", "pk": 93, "fields": {"challenge": "2*6=", "response": "12", "hashkey": "a1720154a8fed5b8b3f345cb715b667832751f5f", "expiration": "2025-05-11T15:00:08.971"}}, {"model": "captcha.captchastore", "pk": 98, "fields": {"challenge": "7-3=", "response": "4", "hashkey": "dc9cb8357e41c4a98d86832666f21a7a7eb0c4ae", "expiration": "2025-05-11T15:00:49.319"}}, {"model": "captcha.captchastore", "pk": 99, "fields": {"challenge": "10+1=", "response": "11", "hashkey": "8f4fb75b0dd63d9ee00081acce6bbf9081eaf433", "expiration": "2025-05-11T15:01:11.076"}}, {"model": "captcha.captchastore", "pk": 102, "fields": {"challenge": "9+1=", "response": "10", "hashkey": "b0c01b2b86d9854c89ded8ea14f745e0ea742f1d", "expiration": "2025-05-11T15:03:39.512"}}, {"model": "captcha.captchastore", "pk": 107, "fields": {"challenge": "3+2=", "response": "5", "hashkey": "0aa68c982d9b58e2a52b937ae659089b892a917e", "expiration": "2025-05-11T15:05:26.299"}}, {"model": "captcha.captchastore", "pk": 109, "fields": {"challenge": "3+2=", "response": "5", "hashkey": "f388d5bc3fe8497c660e6be65f17e37f12bdc2b6", "expiration": "2025-05-11T15:05:32.234"}}, {"model": "captcha.captchastore", "pk": 115, "fields": {"challenge": "2+1=", "response": "3", "hashkey": "05406c944033550847b7ae78459f7ae534fd122c", "expiration": "2025-05-11T15:20:52.812"}}, {"model": "captcha.captchastore", "pk": 117, "fields": {"challenge": "5+5=", "response": "10", "hashkey": "e4de0f6cfa09bf499bd5be03c0f1b0061bec9cf5", "expiration": "2025-05-11T16:58:17.459"}}, {"model": "captcha.captchastore", "pk": 120, "fields": {"challenge": "10+10=", "response": "20", "hashkey": "eeaf25c4331c3ff1ba2237e9669674e48d7d8e87", "expiration": "2025-05-12T12:02:36.986"}}, {"model": "captcha.captchastore", "pk": 127, "fields": {"challenge": "4+10=", "response": "14", "hashkey": "7b2b5891b7c163cc6279be2f4351919eca06ccf2", "expiration": "2025-05-12T12:06:23.266"}}, {"model": "casbin_adapter.casbinrule", "pk": 128, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/update-user-info/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 129, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dept/:dept_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 130, "fields": {"ptype": "p", "v0": "admin", "v1": "/job/crontab/periodic-task/:job_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 132, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/user/:user_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 133, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/menu/:menu_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 134, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dict-data/:dict-data_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 135, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dept/dept-tree/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 136, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/apis/:api_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 137, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dict-type/:dict-type_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 138, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/post/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 139, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dict-type/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 141, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dept/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 142, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/role/:role_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 143, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/menu/menu-tree-simple/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 144, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/post/:post_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 145, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/menu/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 146, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/apis/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 148, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/role/role-id-to-menu/:role_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 149, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dict-data/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 150, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/post/get-all-posts/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 151, "fields": {"ptype": "p", "v0": "admin", "v1": "/job/crontab/periodic-task/:job_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 152, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/role/get-all-roles/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 153, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/role/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 154, "fields": {"ptype": "p", "v0": "admin", "v1": "/job/crontab/periodic-task/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 155, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/menu/:menu_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 156, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dict-type/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 158, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/menu/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 159, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/apis/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 160, "fields": {"ptype": "p", "v0": "admin", "v1": "/job/crontab/periodic-task/tasklist/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 161, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/user/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 162, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dict-data/:dict-data_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 163, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dict-data/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 164, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dept/:dept_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 165, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dict-type/:dict-type_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 166, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/user/:user_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 167, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/apis/:api_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 168, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/role/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 169, "fields": {"ptype": "p", "v0": "admin", "v1": "/job/crontab/periodic-task/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 170, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/menu/menu-tree/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 171, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dict-data/:dict-data_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 172, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dept/:dept_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 173, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dict-type/:dict-type_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 174, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/user/:user_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 175, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/apis/:api_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 176, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/role/:role_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 177, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/post/:post_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 178, "fields": {"ptype": "p", "v0": "admin", "v1": "/job/crontab/task-result/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 179, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/post/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 180, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/role/:role_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 181, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/user/user-info/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 182, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/user/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 183, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/post/:post_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 184, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/dept/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 185, "fields": {"ptype": "p", "v0": "admin", "v1": "/job/crontab/periodic-task/enabled/:job_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 187, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/menu/:menu_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 188, "fields": {"ptype": "p", "v0": "test", "v1": "/system/post/get-all-posts/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 189, "fields": {"ptype": "p", "v0": "test", "v1": "/system/post/:post_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 190, "fields": {"ptype": "p", "v0": "test", "v1": "/system/post/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 191, "fields": {"ptype": "p", "v0": "test", "v1": "/system/role/get-all-roles/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 192, "fields": {"ptype": "p", "v0": "test", "v1": "/system/role/role-id-to-menu/:role_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 193, "fields": {"ptype": "p", "v0": "test", "v1": "/system/role/:role_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 194, "fields": {"ptype": "p", "v0": "test", "v1": "/system/role/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 195, "fields": {"ptype": "p", "v0": "test", "v1": "/system/user/user-info/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 196, "fields": {"ptype": "p", "v0": "test", "v1": "/system/user/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 197, "fields": {"ptype": "p", "v0": "test", "v1": "/system/user/:user_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 198, "fields": {"ptype": "p", "v0": "test", "v1": "/system/menu/menu-tree-simple/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 199, "fields": {"ptype": "p", "v0": "test", "v1": "/system/menu/menu-tree/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 200, "fields": {"ptype": "p", "v0": "test", "v1": "/system/menu/:menu_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 202, "fields": {"ptype": "p", "v0": "test", "v1": "/system/menu/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 203, "fields": {"ptype": "p", "v0": "test", "v1": "/system/dict-data/:dict-data_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 204, "fields": {"ptype": "p", "v0": "test", "v1": "/system/dict-data/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 206, "fields": {"ptype": "p", "v0": "test", "v1": "/system/dict-type/:dict-type_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 207, "fields": {"ptype": "p", "v0": "test", "v1": "/system/dict-type/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 209, "fields": {"ptype": "p", "v0": "test", "v1": "/system/dept/dept-tree/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 211, "fields": {"ptype": "p", "v0": "test", "v1": "/system/dept/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 212, "fields": {"ptype": "p", "v0": "test", "v1": "/system/dept/:dept_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 213, "fields": {"ptype": "p", "v0": "test", "v1": "/system/apis/:api_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 214, "fields": {"ptype": "p", "v0": "test", "v1": "/system/apis/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 217, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/apis/get-all-api-group/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 218, "fields": {"ptype": "p", "v0": "admin", "v1": "/tool/monitor/get-system-info/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 219, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/operation-log/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 220, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/operation-log/delete-all-logs/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 221, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/operation-log/:log_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 223, "fields": {"ptype": "p", "v0": "test", "v1": "/system/operation-log/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 225, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/message-center/:message_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 226, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/message-center/:message_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 227, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/message-center/get-self-receive/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 228, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/message-center/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 229, "fields": {"ptype": "p", "v0": "admin", "v1": "/system/message-center/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 231, "fields": {"ptype": "p", "v0": "test", "v1": "/system/message-center/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 232, "fields": {"ptype": "p", "v0": "test", "v1": "/system/message-center/get-self-receive/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 234, "fields": {"ptype": "p", "v0": "test", "v1": "/system/message-center/:message_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 235, "fields": {"ptype": "p", "v0": "test", "v1": "/system/apis/get-all-api-group/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 236, "fields": {"ptype": "p", "v0": "test", "v1": "/job/crontab/periodic-task/tasklist/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 237, "fields": {"ptype": "p", "v0": "test", "v1": "/job/crontab/task-result/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 238, "fields": {"ptype": "p", "v0": "test", "v1": "/tool/monitor/get-system-info/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 239, "fields": {"ptype": "p", "v0": "test", "v1": "/job/crontab/periodic-task/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 240, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/message-center/:message_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 241, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/message-center/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 242, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/message-center/get-self-receive/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 243, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/message-center/:message_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 244, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/message-center/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 245, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/operation-log/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 246, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/operation-log/:log_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 247, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/operation-log/delete-all-logs/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 248, "fields": {"ptype": "p", "v0": "dev", "v1": "/tool/monitor/get-system-info/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 249, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/apis/get-all-api-group/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 250, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/apis/:api_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 251, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/apis/:api_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 252, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/apis/:api_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 253, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/apis/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 254, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/apis/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 255, "fields": {"ptype": "p", "v0": "dev", "v1": "/job/crontab/periodic-task/tasklist/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 256, "fields": {"ptype": "p", "v0": "dev", "v1": "/job/crontab/periodic-task/enabled/:job_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 257, "fields": {"ptype": "p", "v0": "dev", "v1": "/job/crontab/periodic-task/:job_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 258, "fields": {"ptype": "p", "v0": "dev", "v1": "/job/crontab/periodic-task/:job_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 259, "fields": {"ptype": "p", "v0": "dev", "v1": "/job/crontab/periodic-task/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 260, "fields": {"ptype": "p", "v0": "dev", "v1": "/job/crontab/task-result/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 261, "fields": {"ptype": "p", "v0": "dev", "v1": "/job/crontab/periodic-task/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 262, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/post/get-all-posts/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 263, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/post/:post_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 264, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/post/:post_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 265, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/post/:post_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 266, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/post/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 267, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/post/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 268, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/role/get-all-roles/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 269, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/role/role-id-to-menu/:role_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 270, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/role/:role_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 271, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/role/:role_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 272, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/role/:role_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 273, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/role/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 274, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/role/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 275, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/update-user-info/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 276, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/user/user-info/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 277, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/user/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 278, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/user/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 279, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/user/:user_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 280, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/user/:user_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 281, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/user/:user_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 282, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/menu/menu-tree-simple/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 283, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/menu/menu-tree/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 284, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/menu/:menu_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 285, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/menu/:menu_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 286, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/menu/:menu_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 287, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/menu/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 288, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/menu/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 289, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dict-data/:dict-data_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 290, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dict-data/:dict-data_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 291, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dict-data/:dict-data_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 292, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dict-data/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 293, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dict-data/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 294, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dict-type/:dict-type_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 295, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dict-type/:dict-type_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 296, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dict-type/:dict-type_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 297, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dict-type/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 298, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dict-type/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 299, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dept/dept-tree/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 300, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dept/", "v2": "POST", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 301, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dept/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 302, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dept/:dept_id/", "v2": "GET", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 303, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dept/:dept_id/", "v2": "DELETE", "v3": "", "v4": "", "v5": ""}}, {"model": "casbin_adapter.casbinrule", "pk": 304, "fields": {"ptype": "p", "v0": "dev", "v1": "/system/dept/:dept_id/", "v2": "PUT", "v3": "", "v4": "", "v5": ""}}, {"model": "app_post.post", "pk": 43274229824, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:22:39.841", "create_datetime": "2024-01-13T15:22:39.841", "post_name": "测试", "post_code": "TEST", "sort": 3, "status": "1", "remark": null}}, {"model": "app_post.post", "pk": 62104692200590, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T15:07:25.255", "create_datetime": "2023-08-23T15:15:37.358", "post_name": "首席执行官", "post_code": "CEO", "sort": 1, "status": "0", "remark": ""}}, {"model": "app_post.post", "pk": 517572581700082, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T15:39:20.600", "create_datetime": "2023-08-23T15:39:20.600", "post_name": "首席技术执行官", "post_code": "CTO", "sort": 2, "status": "0", "remark": "44444"}}, {"model": "app_dept.dept", "pk": 10414461824, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-13T22:58:45.966", "create_datetime": "2023-09-13T22:58:45.966", "dept_name": "产品设计部门", "dept_key": "cpsjbm", "sort": 1, "leader": "pp", "phone": null, "email": null, "status": "0", "parent": null}}, {"model": "app_dept.dept", "pk": 42188673800208, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T17:09:07.102", "create_datetime": "2023-08-24T11:16:50.659", "dept_name": "架构设计四组", "dept_key": "jgsjsz1", "sort": 5, "leader": "pp", "phone": "18888888888", "email": "<EMAIL>", "status": "0", "parent": 376960140500837}}, {"model": "app_dept.dept", "pk": 57407999600609, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T17:08:56.461", "create_datetime": "2023-08-24T10:46:04.785", "dept_name": "架构设计三组", "dept_key": "jgsjsz", "sort": 4, "leader": "pp", "phone": "18888888888", "email": "<EMAIL>", "status": "0", "parent": 376960140500837}}, {"model": "app_dept.dept", "pk": 94609026700594, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T22:38:15.023", "create_datetime": "2023-08-23T16:08:21.698", "dept_name": "架构设计一组", "dept_key": "jgsjyz", "sort": 2, "leader": "pp", "phone": "18888888888", "email": "<EMAIL>", "status": "0", "parent": 376960140500837}}, {"model": "app_dept.dept", "pk": 114667862200894, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T17:08:46.610", "create_datetime": "2023-08-23T16:08:41.757", "dept_name": "架构设计二组", "dept_key": "jgsjrz", "sort": 3, "leader": "pp", "phone": "18888888888", "email": "<EMAIL>", "status": "0", "parent": 376960140500837}}, {"model": "app_dept.dept", "pk": 376960140500837, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T17:08:20.194", "create_datetime": "2023-08-23T16:05:08.097", "dept_name": "架构设计部门", "dept_key": "jgsjbm", "sort": 1, "leader": "pp", "phone": "18888888888", "email": "<EMAIL>", "status": "0", "parent": null}}, {"model": "app_menu.menu", "pk": 43286452544, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:25:50.821", "create_datetime": "2024-01-13T15:25:50.821", "parent": 457773413824, "icon": "elementList", "menu_name": "系统日志", "sort": 2, "path": "/system/system-log/", "component": "/log/system/index", "is_iframe": "1", "is_link": null, "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "log:system:list", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 159568677952, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2025-05-11T15:14:20.593", "create_datetime": "2025-05-11T15:14:20.593", "parent": null, "icon": "iconfont icon-shou<PERSON>_dongtaihui", "menu_name": "数据展示", "sort": 5, "path": "/dataset", "component": "/dataset/index", "is_iframe": "1", "is_link": null, "menu_type": "C", "is_hide": "0", "is_keep_alive": "0", "is_affix": "1", "permission": null, "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 285082180800, "fields": {"creator": null, "modifier": "541150219354505", "update_datetime": "2023-12-12T12:57:15.508", "create_datetime": "2023-11-02T15:06:49.076", "parent": null, "icon": "elementPlace", "menu_name": "个人中心", "sort": 3, "path": "/personal", "component": "/personal/index", "is_iframe": "1", "is_link": null, "menu_type": "M", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": null, "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 384888858368, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-12T14:56:19.181", "create_datetime": "2023-11-20T16:18:08.412", "parent": null, "icon": "iconfont icon-zujian", "menu_name": "系统工具", "sort": 2, "path": "/tool", "component": "Layout", "is_iframe": "1", "is_link": null, "menu_type": "M", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": null, "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 384901100480, "fields": {"creator": 541150219354505, "modifier": "164357145152", "update_datetime": "2025-05-13T11:22:45.499", "create_datetime": "2023-11-20T16:21:19.695", "parent": 457773413824, "icon": "elementCpu", "menu_name": "服务监控", "sort": 1, "path": "/tool/monitor/", "component": "/tool/monitor/index", "is_iframe": "1", "is_link": null, "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "tool:monitor:list", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 384924956608, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-01T11:22:50", "create_datetime": "2023-11-20T16:27:32.447", "parent": 384888858368, "icon": "elementAlarmClock", "menu_name": "定时任务", "sort": 2, "path": "/job/crontab/", "component": "/tool/job/index", "is_iframe": "1", "is_link": null, "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "tool:job:list", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 384928228864, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-20T16:28:23.576", "create_datetime": "2023-11-20T16:28:23.576", "parent": 384924956608, "icon": null, "menu_name": "新增", "sort": 1, "path": null, "component": null, "is_iframe": null, "is_link": null, "menu_type": "F", "is_hide": null, "is_keep_alive": null, "is_affix": null, "permission": "tool:job:add", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 384929917440, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-20T16:28:49.960", "create_datetime": "2023-11-20T16:28:49.960", "parent": 384924956608, "icon": null, "menu_name": "编辑", "sort": 2, "path": null, "component": null, "is_iframe": null, "is_link": null, "menu_type": "F", "is_hide": null, "is_keep_alive": null, "is_affix": null, "permission": "tool:job:edit", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 384931381952, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-20T16:29:12.843", "create_datetime": "2023-11-20T16:29:12.843", "parent": 384924956608, "icon": null, "menu_name": "删除", "sort": 3, "path": null, "component": null, "is_iframe": null, "is_link": null, "menu_type": "F", "is_hide": null, "is_keep_alive": null, "is_affix": null, "permission": "tool:job:delete", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 384934012416, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-20T16:29:53.944", "create_datetime": "2023-11-20T16:29:53.944", "parent": 384924956608, "icon": null, "menu_name": "开关", "sort": 4, "path": null, "component": null, "is_iframe": null, "is_link": null, "menu_type": "F", "is_hide": null, "is_keep_alive": null, "is_affix": null, "permission": "tool:job:run", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 419535754688, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-26T22:40:46.167", "create_datetime": "2023-11-26T22:40:46.167", "parent": 384924956608, "icon": null, "menu_name": "日志", "sort": 5, "path": null, "component": null, "is_iframe": null, "is_link": null, "menu_type": "F", "is_hide": null, "is_keep_alive": null, "is_affix": null, "permission": "tool:job:log", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 457773413824, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-04T14:28:39.814", "create_datetime": "2023-12-03T20:38:29.591", "parent": null, "icon": "elementNotebook", "menu_name": "日志系统", "sort": 3, "path": "/log", "component": "Layout", "is_iframe": "1", "is_link": null, "menu_type": "M", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": null, "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 458000302592, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-12T12:56:49.208", "create_datetime": "2023-12-03T21:37:34.728", "parent": 457773413824, "icon": "elementCalendar", "menu_name": "操作日志", "sort": 1, "path": "/system/operation-log/", "component": "/log/operation/index", "is_iframe": "1", "is_link": null, "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "log:operation:list", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 458003252032, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-03T21:38:20.813", "create_datetime": "2023-12-03T21:38:20.813", "parent": 458000302592, "icon": null, "menu_name": "删除", "sort": 1, "path": null, "component": null, "is_iframe": null, "is_link": null, "menu_type": "F", "is_hide": null, "is_keep_alive": null, "is_affix": null, "permission": "log:operation:delete", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 458004760128, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-03T21:38:44.377", "create_datetime": "2023-12-03T21:38:44.377", "parent": 458000302592, "icon": null, "menu_name": "清空", "sort": 2, "path": null, "component": null, "is_iframe": null, "is_link": null, "menu_type": "F", "is_hide": null, "is_keep_alive": null, "is_affix": null, "permission": "log:operation:clean", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 479138297792, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-12T12:55:38.832", "create_datetime": "2023-12-07T17:22:15.903", "parent": 7298689300002, "icon": "elementDocument", "menu_name": "信息中心", "sort": 9, "path": "/system/message-center", "component": "/system/notice/index", "is_iframe": "1", "is_link": null, "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": null, "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 479144112576, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-07T17:24:29.634", "create_datetime": "2023-12-07T17:23:46.759", "parent": 479138297792, "icon": null, "menu_name": "新增信息", "sort": 1, "path": null, "component": null, "is_iframe": null, "is_link": null, "menu_type": "F", "is_hide": null, "is_keep_alive": null, "is_affix": null, "permission": "system:notice:add", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 479146012288, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-07T17:24:33.728", "create_datetime": "2023-12-07T17:24:16.442", "parent": 479138297792, "icon": null, "menu_name": "删除信息", "sort": 2, "path": null, "component": null, "is_iframe": null, "is_link": null, "menu_type": "F", "is_hide": null, "is_keep_alive": null, "is_affix": null, "permission": "system:notice:delete", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 506472197952, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-12T16:00:28.093", "create_datetime": "2023-12-12T16:00:28.093", "parent": 479138297792, "icon": null, "menu_name": "查看信息", "sort": 3, "path": null, "component": null, "is_iframe": null, "is_link": null, "menu_type": "F", "is_hide": null, "is_keep_alive": null, "is_affix": null, "permission": "system:notice:view", "status": "0", "remark": null}}, {"model": "app_menu.menu", "pk": 7298689300002, "fields": {"creator": null, "modifier": "541150219354505", "update_datetime": "2023-12-04T14:28:20.651", "create_datetime": "2023-08-24T14:31:50.645", "parent": null, "icon": "elementSetting", "menu_name": "系统设置", "sort": 1, "path": "/system", "component": "Layout", "is_iframe": "1", "is_link": "", "menu_type": "M", "is_hide": "0", "is_keep_alive": "0", "is_affix": "1", "permission": "", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 268338419700006, "fields": {"creator": null, "modifier": "541150219354505", "update_datetime": "2023-12-12T14:55:31.286", "create_datetime": "2023-08-24T14:36:11.683", "parent": 7298689300002, "icon": "iconfont icon-siweida<PERSON>u", "menu_name": "API管理", "sort": 2, "path": "/system/api", "component": "/system/api/index", "is_iframe": "1", "is_link": "", "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:api:list", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 324683656700002, "fields": {"creator": null, "modifier": "541150219354505", "update_datetime": "2023-12-12T12:54:42.494", "create_datetime": "2023-08-24T14:37:08.029", "parent": 7298689300002, "icon": "elementMessageBox", "menu_name": "岗位管理", "sort": 3, "path": "/system/post", "component": "/system/post/index", "is_iframe": "1", "is_link": "", "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:post:list", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 357134961700006, "fields": {"creator": null, "modifier": "541150219354505", "update_datetime": "2023-12-12T12:55:08.947", "create_datetime": "2023-08-24T14:37:40.480", "parent": 7298689300002, "icon": "elementPlatform", "menu_name": "部门管理", "sort": 4, "path": "/system/dept", "component": "/system/dept/index", "is_iframe": "1", "is_link": "", "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dept:list", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 538569709516226, "fields": {"creator": null, "modifier": "541150219354505", "update_datetime": "2023-12-12T12:55:23.790", "create_datetime": "2023-08-24T14:43:44.891", "parent": 7298689300002, "icon": "elementFinished", "menu_name": "菜单管理", "sort": 5, "path": "/system/menu", "component": "/system/menu/index", "is_iframe": "1", "is_link": "", "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:menu:list", "status": "0", "remark": "菜单"}}, {"model": "app_menu.menu", "pk": 542593991904854, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:14:48.579", "create_datetime": "2023-09-04T17:14:48.579", "parent": 268338419700006, "icon": "", "menu_name": "添加api", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:api:add", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542594110045814, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:15:16.746", "create_datetime": "2023-09-04T17:15:16.746", "parent": 268338419700006, "icon": "", "menu_name": "编辑api", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:api:edit", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542594206556749, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:15:39.756", "create_datetime": "2023-09-04T17:15:39.756", "parent": 268338419700006, "icon": "", "menu_name": "删除api", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:api:delete", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542594449490837, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:16:37.676", "create_datetime": "2023-09-04T17:16:37.676", "parent": 324683656700002, "icon": "", "menu_name": "添加岗位", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:post:add", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542594511251963, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:16:52.401", "create_datetime": "2023-09-04T17:16:52.401", "parent": 324683656700002, "icon": "", "menu_name": "编辑岗位", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:post:edit", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542594574887944, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:17:07.573", "create_datetime": "2023-09-04T17:17:07.573", "parent": 324683656700002, "icon": "", "menu_name": "删除岗位", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:post:delete", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542594636888145, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:17:22.355", "create_datetime": "2023-09-04T17:17:22.355", "parent": 324683656700002, "icon": "", "menu_name": "导出岗位", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:post:export", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542594825191424, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:18:07.250", "create_datetime": "2023-09-04T17:18:07.250", "parent": 357134961700006, "icon": "", "menu_name": "添加部门", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dept:add", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542594873530777, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:18:18.775", "create_datetime": "2023-09-04T17:18:18.775", "parent": 357134961700006, "icon": "", "menu_name": "编辑部门", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dept:edit", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542594930380374, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:18:32.329", "create_datetime": "2023-09-04T17:18:32.329", "parent": 357134961700006, "icon": "", "menu_name": "删除部门", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dept:delete", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542594993353654, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:18:47.343", "create_datetime": "2023-09-04T17:18:47.343", "parent": 357134961700006, "icon": "", "menu_name": "导出部门", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dept:export", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542595409881595, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:20:26.651", "create_datetime": "2023-09-04T17:20:26.651", "parent": 538569709516226, "icon": "", "menu_name": "添加菜单", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:menu:add", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542595489577566, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:20:45.652", "create_datetime": "2023-09-04T17:20:45.652", "parent": 538569709516226, "icon": "", "menu_name": "修改菜单", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:menu:edit", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542595568258514, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:21:04.411", "create_datetime": "2023-09-04T17:21:04.411", "parent": 538569709516226, "icon": "", "menu_name": "删除菜单", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:menu:delete", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542596157969268, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:23:25.009", "create_datetime": "2023-09-04T17:23:25.009", "parent": 7298689300002, "icon": "elementUserFilled", "menu_name": "角色管理", "sort": 6, "path": "/system/role", "component": "/system/role/index", "is_iframe": "1", "is_link": "", "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:role:list", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542596856497045, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:26:11.551", "create_datetime": "2023-09-04T17:26:11.551", "parent": 542596157969268, "icon": "", "menu_name": "新增角色", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:role:add", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542597283951149, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:27:53.464", "create_datetime": "2023-09-04T17:27:53.464", "parent": 542596157969268, "icon": "", "menu_name": "删除角色", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:role:delete", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542597333875949, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:28:05.367", "create_datetime": "2023-09-04T17:28:05.367", "parent": 542596157969268, "icon": "", "menu_name": "编辑角色", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:role:edit", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542597388884246, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:28:18.482", "create_datetime": "2023-09-04T17:28:18.482", "parent": 542596157969268, "icon": "", "menu_name": "导出角色", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:role:export", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542598150314000, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:31:20.021", "create_datetime": "2023-09-04T17:31:20.021", "parent": 7298689300002, "icon": "elementUser", "menu_name": "用户管理", "sort": 7, "path": "/system/user", "component": "/system/user/index", "is_iframe": "1", "is_link": "", "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:user:list", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542598287434186, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:31:52.713", "create_datetime": "2023-09-04T17:31:52.713", "parent": 542598150314000, "icon": "", "menu_name": "添加用户", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:user:add", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542598338923462, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:32:04.989", "create_datetime": "2023-09-04T17:32:04.989", "parent": 542598150314000, "icon": "", "menu_name": "删除用户", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:user:delete", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542598380740673, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:32:14.959", "create_datetime": "2023-09-04T17:32:14.959", "parent": 542598150314000, "icon": "", "menu_name": "编辑用户", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:user:edit", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 542598541692895, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-04T17:32:53.333", "create_datetime": "2023-09-04T17:32:53.333", "parent": 542598150314000, "icon": "", "menu_name": "导出用户", "sort": 1, "path": "", "component": "", "is_iframe": null, "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:user:export", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 545179689782607, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:29:26.993", "create_datetime": "2023-09-11T20:29:26.993", "parent": 7298689300002, "icon": "elementCellphone", "menu_name": "字典管理", "sort": 8, "path": "/system/dict", "component": "/system/dict/index", "is_iframe": "1", "is_link": "", "menu_type": "C", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dict:list", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 545180315367243, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:31:56.144", "create_datetime": "2023-09-11T20:31:56.144", "parent": 545179689782607, "icon": "", "menu_name": "添加字典类型", "sort": 1, "path": "", "component": "", "is_iframe": "", "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dictT:add", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 545180635585576, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:33:12.490", "create_datetime": "2023-09-11T20:33:12.490", "parent": 545179689782607, "icon": "", "menu_name": "编辑字典类型", "sort": 1, "path": "", "component": "", "is_iframe": "", "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dictT:edit", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 545180720667033, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:33:32.775", "create_datetime": "2023-09-11T20:33:32.775", "parent": 545179689782607, "icon": "", "menu_name": "删除字典类型", "sort": 1, "path": "", "component": "", "is_iframe": "", "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dictT:delete", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 545180793786335, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:33:50.208", "create_datetime": "2023-09-11T20:33:50.208", "parent": 545179689782607, "icon": "", "menu_name": "导出字典类型", "sort": 1, "path": "", "component": "", "is_iframe": "", "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dictT:export", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 545180864145784, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:34:06.983", "create_datetime": "2023-09-11T20:34:06.983", "parent": 545179689782607, "icon": "", "menu_name": "新增字典数据", "sort": 1, "path": "", "component": "", "is_iframe": "", "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dictD:add", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 545180930025717, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:34:22.690", "create_datetime": "2023-09-11T20:34:22.690", "parent": 545179689782607, "icon": "", "menu_name": "修改字典数据", "sort": 1, "path": "", "component": "", "is_iframe": "", "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dictD:edit", "status": "0", "remark": ""}}, {"model": "app_menu.menu", "pk": 545181009289674, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:34:41.588", "create_datetime": "2023-09-11T20:34:41.588", "parent": 545179689782607, "icon": "", "menu_name": "删除字典数据", "sort": 1, "path": "", "component": "", "is_iframe": "", "is_link": "", "menu_type": "F", "is_hide": "0", "is_keep_alive": "1", "is_affix": "1", "permission": "system:dictD:delete", "status": "0", "remark": ""}}, {"model": "app_apis.apis", "pk": 29775558784, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-17T11:00:43.106", "create_datetime": "2023-09-17T11:00:43.106", "path": "/system/role/role-id-to-menu/:role_id/", "description": "获取单个角色API权限", "api_group": "role", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208122656320, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:05:16.505", "create_datetime": "2023-10-19T17:05:16.505", "path": "/system/dict-type/", "description": "添加字典类型信息", "api_group": "dict", "method": "POST", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208129003520, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:06:55.680", "create_datetime": "2023-10-19T17:06:55.680", "path": "/system/dict-type/", "description": "获取字典类型列表", "api_group": "dict", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208132094400, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:07:43.975", "create_datetime": "2023-10-19T17:07:43.975", "path": "/system/dict-type/:dict-type_id/", "description": "删除字典类型", "api_group": "dict", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208133283712, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:08:02.558", "create_datetime": "2023-10-19T17:08:02.558", "path": "/system/dict-type/:dict-type_id/", "description": "更新字典类型", "api_group": "dict", "method": "PUT", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208134697920, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:08:24.655", "create_datetime": "2023-10-19T17:08:24.655", "path": "/system/dict-type/:dict-type_id/", "description": "获取字典类型信息", "api_group": "dict", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208143283520, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:10:38.805", "create_datetime": "2023-10-19T17:10:38.805", "path": "/system/dict-data/", "description": "添加字典数值信息", "api_group": "dict", "method": "POST", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208144666112, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:11:00.408", "create_datetime": "2023-10-19T17:11:00.408", "path": "/system/dict-data/", "description": "获取字典数值列表", "api_group": "dict", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208146358016, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:11:26.844", "create_datetime": "2023-10-19T17:11:26.844", "path": "/system/dict-data/:dict-data_id/", "description": "删除字典数值", "api_group": "dict", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208148071680, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:11:53.620", "create_datetime": "2023-10-19T17:11:53.620", "path": "/system/dict-data/:dict-data_id/", "description": "更新字典数值", "api_group": "dict", "method": "PUT", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208149170880, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:12:10.795", "create_datetime": "2023-10-19T17:12:10.795", "path": "/system/dict-data/:dict-data_id/", "description": "查询字典数值信息", "api_group": "dict", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208158315008, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:14:33.672", "create_datetime": "2023-10-19T17:14:33.672", "path": "/system/menu/menu-tree-simple/", "description": "获取界面菜单树型接口-简单版本", "api_group": "menu", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208182767616, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:20:55.744", "create_datetime": "2023-10-19T17:20:55.744", "path": "/system/user/user-info/", "description": "获取当前用户信息", "api_group": "user", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 208184439552, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:21:21.868", "create_datetime": "2023-10-19T17:21:21.868", "path": "/system/update-user-info/", "description": "修改当前用户信息", "api_group": "user", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 230337122560, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-23T17:30:17.540", "create_datetime": "2023-10-23T17:30:17.540", "path": "/system/role/get-all-roles/", "description": "获取所有角色名称", "api_group": "role", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 230339457856, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-23T17:30:54.029", "create_datetime": "2023-10-23T17:30:54.029", "path": "/system/post/get-all-posts/", "description": "获取所有岗位名称", "api_group": "post", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 423593769024, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-27T16:17:32.641", "create_datetime": "2023-11-27T16:17:32.641", "path": "/job/crontab/periodic-task/", "description": "查询定时任务调度列表", "api_group": "job", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 423598306496, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-27T16:18:43.539", "create_datetime": "2023-11-27T16:18:43.539", "path": "/job/crontab/task-result/", "description": "查询定时任务调度详细", "api_group": "job", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 423599807936, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-27T16:19:06.999", "create_datetime": "2023-11-27T16:19:06.999", "path": "/job/crontab/periodic-task/", "description": "新增定时任务调度", "api_group": "job", "method": "POST", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 423603733440, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-27T16:20:44.394", "create_datetime": "2023-11-27T16:20:08.336", "path": "/job/crontab/periodic-task/:job_id/", "description": "修改定时任务调度", "api_group": "job", "method": "PUT", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 423607226112, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-27T16:21:02.908", "create_datetime": "2023-11-27T16:21:02.908", "path": "/job/crontab/periodic-task/:job_id/", "description": "删除定时任务调度", "api_group": "job", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 423610525504, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-27T16:21:54.461", "create_datetime": "2023-11-27T16:21:54.461", "path": "/job/crontab/periodic-task/enabled/:job_id/", "description": "调度定时任务", "api_group": "job", "method": "PUT", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 423612291584, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-11-27T16:22:22.056", "create_datetime": "2023-11-27T16:22:22.056", "path": "/job/crontab/periodic-task/tasklist/", "description": "获取本地所有的内置定时任务", "api_group": "job", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 444430774784, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-01T10:43:50.856", "create_datetime": "2023-12-01T10:43:50.856", "path": "/system/apis/get-all-api-group/", "description": "获取API所有的分组", "api_group": "apis", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 444583075072, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-01T16:47:00.763", "create_datetime": "2023-12-01T11:23:30.548", "path": "/tool/monitor/get-system-info/", "description": "实时获取本机监控信息", "api_group": "monitor", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 456847716224, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-03T16:37:25.566", "create_datetime": "2023-12-03T16:37:25.566", "path": "/system/operation-log/delete-all-logs/", "description": "操作日志-清空数据", "api_group": "operation_log", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 456852236224, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-03T16:38:36.191", "create_datetime": "2023-12-03T16:38:36.191", "path": "/system/operation-log/:log_id/", "description": "操作日志-删除数据", "api_group": "operation_log", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 456853998848, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-03T16:39:03.732", "create_datetime": "2023-12-03T16:39:03.732", "path": "/system/operation-log/", "description": "操作日志-获取数据列表", "api_group": "operation_log", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 517416997504, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-14T15:30:40.586", "create_datetime": "2023-12-14T15:30:40.586", "path": "/system/message-center/", "description": "查询信息列表", "api_group": "message", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 517420513792, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-14T15:31:44.630", "create_datetime": "2023-12-14T15:31:35.528", "path": "/system/message-center/:message_id/", "description": "查询信息详细", "api_group": "message", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 517429348480, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-14T15:33:53.570", "create_datetime": "2023-12-14T15:33:53.570", "path": "/system/message-center/get-self-receive/", "description": "查询我接收的信息列表", "api_group": "message", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 517430787776, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-14T15:34:16.059", "create_datetime": "2023-12-14T15:34:16.059", "path": "/system/message-center/", "description": "新增信息", "api_group": "message", "method": "POST", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 517432827840, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-14T15:34:47.935", "create_datetime": "2023-12-14T15:34:47.935", "path": "/system/message-center/:message_id/", "description": "删除信息", "api_group": "message", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 637940542400, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-05T10:37:00.975", "create_datetime": "2024-01-05T10:37:00.975", "path": "/system/user/auth/", "description": "用户角色个人权限信息", "api_group": "auth", "method": "GET", "enable_datasource": "1"}}, {"model": "app_apis.apis", "pk": 90743268800150, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:06:14.550", "create_datetime": "2023-08-23T17:06:14.550", "path": "/system/dept/:dept_id/", "description": "更新部门信息", "api_group": "dept", "method": "PUT", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 96577568100591, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:06:20.384", "create_datetime": "2023-08-23T17:06:20.384", "path": "/system/dept/:dept_id/", "description": "删除部门信息", "api_group": "dept", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 141914787100125, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:07:05.722", "create_datetime": "2023-08-23T17:07:05.722", "path": "/system/dept/:dept_id/", "description": "获取部门信息", "api_group": "dept", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 195580817900813, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:07:59.388", "create_datetime": "2023-08-23T17:07:59.388", "path": "/system/dept/", "description": "获取部门列表", "api_group": "dept", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 247035582800744, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:08:50.842", "create_datetime": "2023-08-23T17:08:50.842", "path": "/system/dept/", "description": "添加部门信息", "api_group": "dept", "method": "POST", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 310843027100161, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:22:10.277", "create_datetime": "2023-08-23T17:22:10.277", "path": "/system/apis/", "description": "添加API信息", "api_group": "apis", "method": "POST", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 324866616600411, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:22:24.301", "create_datetime": "2023-08-23T17:22:24.301", "path": "/system/apis/", "description": "获取API列表", "api_group": "apis", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 345536676700546, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:22:44.971", "create_datetime": "2023-08-23T17:22:44.971", "path": "/system/apis/:api_id/", "description": "获取API信息", "api_group": "apis", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 362784584600938, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:23:02.219", "create_datetime": "2023-08-23T17:23:02.219", "path": "/system/apis/:api_id/", "description": "删除API信息", "api_group": "apis", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 371215327200723, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:23:10.650", "create_datetime": "2023-08-23T17:23:10.650", "path": "/system/apis/:api_id/", "description": "更新API信息", "api_group": "apis", "method": "PUT", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 517649286600945, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:01:21.933", "create_datetime": "2023-08-23T17:01:21.933", "path": "/system/post/", "description": "添加岗位信息", "api_group": "post", "method": "POST", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 532031994000284, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:01:36.316", "create_datetime": "2023-08-23T17:01:36.316", "path": "/system/post/", "description": "获取岗位列表", "api_group": "post", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541143409226678, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-31T17:10:42.718", "create_datetime": "2023-08-31T17:10:42.718", "path": "/system/menu/", "description": "获取菜单列表", "api_group": "menu", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541143609139789, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-31T17:11:30.381", "create_datetime": "2023-08-31T17:11:30.381", "path": "/system/menu/", "description": "添加菜单信息", "api_group": "menu", "method": "POST", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541143771157364, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-17T11:01:57.384", "create_datetime": "2023-08-31T17:12:09.009", "path": "/system/menu/:menu_id/", "description": "获取菜单信息", "api_group": "menu", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541143816816558, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-17T11:01:53.455", "create_datetime": "2023-08-31T17:12:19.895", "path": "/system/menu/:menu_id/", "description": "删除菜单信息", "api_group": "menu", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541143897246531, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-17T11:01:47.411", "create_datetime": "2023-08-31T17:12:39.071", "path": "/system/menu/:menu_id/", "description": "更新菜单信息", "api_group": "menu", "method": "PUT", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541144112649207, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-10-19T17:14:50.591", "create_datetime": "2023-08-31T17:13:30.427", "path": "/system/menu/menu-tree/", "description": "获取菜单树形列表", "api_group": "menu", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541144228592353, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-31T17:13:58.070", "create_datetime": "2023-08-31T17:13:58.070", "path": "/system/role/", "description": "获取角色列表", "api_group": "role", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541144299710971, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-31T17:14:15.026", "create_datetime": "2023-08-31T17:14:15.026", "path": "/system/role/", "description": "添加角色信息", "api_group": "role", "method": "POST", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541144399661236, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-17T11:01:11.776", "create_datetime": "2023-08-31T17:14:38.856", "path": "/system/role/:role_id/", "description": "删除角色信息", "api_group": "role", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541144616150237, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-17T11:01:07.455", "create_datetime": "2023-08-31T17:15:30.471", "path": "/system/role/:role_id/", "description": "获取角色信息", "api_group": "role", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541144660706328, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-17T11:01:02.606", "create_datetime": "2023-08-31T17:15:41.094", "path": "/system/role/:role_id/", "description": "更新角色信息", "api_group": "role", "method": "PUT", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541508472194203, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-17T11:01:40.076", "create_datetime": "2023-09-01T17:21:20.512", "path": "/system/user/:user_id/", "description": "更新用户信息", "api_group": "user", "method": "PUT", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541508570034733, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-17T11:01:33.581", "create_datetime": "2023-09-01T17:21:43.839", "path": "/system/user/:user_id/", "description": "获取用户信息", "api_group": "user", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541508653081952, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-17T11:01:30.257", "create_datetime": "2023-09-01T17:22:03.639", "path": "/system/user/:user_id/", "description": "删除用户信息", "api_group": "user", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541508804928339, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-01T17:22:39.842", "create_datetime": "2023-09-01T17:22:39.842", "path": "/system/user/", "description": "添加用户信息", "api_group": "user", "method": "POST", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 541509091344777, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-01T17:23:48.129", "create_datetime": "2023-09-01T17:23:48.129", "path": "/system/user/", "description": "获取用户列表", "api_group": "user", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 569646089000281, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:02:13.931", "create_datetime": "2023-08-23T17:02:13.931", "path": "/system/post/:post_id/", "description": "获取岗位信息", "api_group": "post", "method": "GET", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 581633986900336, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:02:25.918", "create_datetime": "2023-08-23T17:02:25.918", "path": "/system/post/:post_id/", "description": "删除岗位信息", "api_group": "post", "method": "DELETE", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 591806089400182, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-23T17:02:36.091", "create_datetime": "2023-08-23T17:02:36.091", "path": "/system/post/:post_id/", "description": "更新岗位信息", "api_group": "post", "method": "PUT", "enable_datasource": "0"}}, {"model": "app_apis.apis", "pk": 619964488200256, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-08-24T11:27:47.492", "create_datetime": "2023-08-24T11:27:47.492", "path": "/system/dept/dept-tree/", "description": "获取部门树形列表", "api_group": "dept", "method": "GET", "enable_datasource": "0"}}, {"model": "app_role.role", "pk": 154555344320, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2025-05-10T17:28:47.255", "create_datetime": "2025-05-10T17:28:47.255", "role_name": "普通用户", "role_key": "common", "status": "0", "sort": 3, "admin": false, "data_scope": "5", "remark": null, "dept": [], "menu": [384901100480, 458000302592, 458003252032, 479144112576, 7298689300002, 43286452544, 384888858368, 458004760128, 479146012288, 285082180800, 457773413824, 506472197952, 479138297792]}}, {"model": "app_role.role", "pk": 164353133888, "fields": {"creator": 541150219354505, "modifier": "164357145152", "update_datetime": "2025-05-13T11:22:59.017", "create_datetime": "2025-05-12T12:00:17.717", "role_name": "开发人员", "role_key": "dev", "status": "0", "sort": 4, "admin": false, "data_scope": "5", "remark": null, "dept": [], "menu": [542594873530777, 542598541692895, 542598380740673, 542598338923462, 542598287434186, 542597388884246, 542597333875949, 542597283951149, 542596856497045, 542595568258514, 542595489577566, 542595409881595, 542594993353654, 542594930380374, 542594825191424, 542594574887944, 542594511251963, 542594449490837, 384901100480, 458000302592, 542594636888145, 7298689300002, 458003252032, 43286452544, 458004760128, 324683656700002, 457773413824, 357134961700006, 538569709516226, 159568677952, 542596157969268, 542598150314000]}}, {"model": "app_role.role", "pk": 444421914176, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-24T10:43:20.702", "create_datetime": "2023-12-01T10:41:32.409", "role_name": "测试角色", "role_key": "test", "status": "0", "sort": 2, "admin": false, "data_scope": "5", "remark": null, "dept": [], "menu": [542597388884246, 542594511251963, 542594636888145, 542594825191424, 542594873530777, 542594930380374, 542594993353654, 542595409881595, 542595489577566, 542595568258514, 542596856497045, 542597283951149, 542597333875949, 542594574887944, 542598287434186, 542598338923462, 542598380740673, 542598541692895, 545180315367243, 545180635585576, 545180720667033, 545180793786335, 545180864145784, 545180930025717, 545181009289674, 542594206556749, 542594449490837, 542594110045814, 542593991904854, 384901100480, 384928228864, 7298689300002, 458000302592, 458003252032, 479144112576, 384888858368, 384924956608, 384929917440, 458004760128, 479146012288, 268338419700006, 506472197952, 457773413824, 384931381952, 324683656700002, 285082180800, 384934012416, 357134961700006, 419535754688, 538569709516226, 542596157969268, 542598150314000, 545179689782607, 479138297792]}}, {"model": "app_role.role", "pk": 540775921959829, "fields": {"creator": null, "modifier": "541150219354505", "update_datetime": "2023-12-14T15:35:05.564", "create_datetime": "2023-08-30T16:50:26.926", "role_name": "管理员", "role_key": "admin", "status": "0", "sort": 1, "admin": false, "data_scope": "5", "remark": null, "dept": [42188673800208], "menu": [542597388884246, 542594511251963, 542594636888145, 542594825191424, 542594873530777, 542594930380374, 542594993353654, 542595409881595, 542595489577566, 542595568258514, 542596856497045, 542597283951149, 542597333875949, 542594574887944, 542598287434186, 542598338923462, 542598380740673, 542598541692895, 545180315367243, 545180635585576, 545180720667033, 545180793786335, 545180864145784, 545180930025717, 545181009289674, 542594206556749, 542594449490837, 542594110045814, 542593991904854, 384901100480, 384928228864, 7298689300002, 458000302592, 458003252032, 479144112576, 384888858368, 384924956608, 384929917440, 458004760128, 479146012288, 268338419700006, 506472197952, 457773413824, 384931381952, 324683656700002, 285082180800, 384934012416, 357134961700006, 419535754688, 538569709516226, 542596157969268, 542598150314000, 545179689782607, 479138297792]}}, {"model": "app_user.users", "pk": 154559003456, "fields": {"password": "pbkdf2_sha256$600000$Iue4IxP49mwaKQvoxqEGRl$Juc74KcZAgJhTmM30kG0tsymM/TpknDnWjgMRvRU72o=", "last_login": null, "is_superuser": false, "is_staff": true, "is_active": true, "date_joined": "2025-05-10T17:29:44", "creator": 541150219354505, "modifier": "154559003456", "update_datetime": "2025-05-10T17:33:11.309", "create_datetime": "2025-05-10T17:29:44.429", "status": "0", "username": "common", "nickname": "common", "employee_no": null, "email": null, "phone": "13160006317", "avatar": null, "gender": "0", "dept": 10414461824, "last_token": null, "is_delete": false, "remark": null, "post": [], "role": [154555344320]}}, {"model": "app_user.users", "pk": 164357145152, "fields": {"password": "pbkdf2_sha256$600000$eg9IFibrLH18ZYjsBVH3yV$NlFHBAR77a0TO1W7tYucXN1pSZJTimDRb9cyLQzvcAM=", "last_login": null, "is_superuser": false, "is_staff": true, "is_active": true, "date_joined": "2025-05-12T12:01:20", "creator": 541150219354505, "modifier": "164357145152", "update_datetime": "2025-05-12T12:02:55.064", "create_datetime": "2025-05-12T12:01:20.393", "status": "0", "username": "<PERSON><PERSON><PERSON>", "nickname": "<PERSON><PERSON><PERSON>", "employee_no": null, "email": null, "phone": "13145678909", "avatar": null, "gender": "0", "dept": null, "last_token": null, "is_delete": false, "remark": null, "post": [], "role": [164353133888]}}, {"model": "app_user.users", "pk": 234457355328, "fields": {"password": "pbkdf2_sha256$600000$e0jTXC1TrB9PTRKkLTLOL6$JBQDOTiTpuCOvvI7KLT2uWw49waPNnxy+U4iJUj+CdM=", "last_login": null, "is_superuser": false, "is_staff": true, "is_active": true, "date_joined": "2023-10-24T11:23:16", "creator": null, "modifier": "541150219354505", "update_datetime": "2023-12-04T16:44:16.356", "create_datetime": "2023-10-24T11:23:16.177", "status": "0", "username": "test", "nickname": "测试用户", "employee_no": null, "email": "<EMAIL>", "phone": "13229671222", "avatar": null, "gender": "1", "dept": 10414461824, "last_token": null, "is_delete": false, "remark": "测试", "post": [62104692200590, 517572581700082], "role": [444421914176]}}, {"model": "app_user.users", "pk": 252375424640, "fields": {"password": "pbkdf2_sha256$600000$vICG6lABy8yQ0C802zt551$dSfe9kmFUqW07vTPy4HQD+X0BB7/3R9iGyXNtqhgbEQ=", "last_login": null, "is_superuser": false, "is_staff": true, "is_active": true, "date_joined": "2023-10-27T17:09:26", "creator": null, "modifier": "541150219354505", "update_datetime": "2023-12-03T21:45:42.492", "create_datetime": "2023-10-27T17:09:26.010", "status": "0", "username": "nini", "nickname": "妮妮", "employee_no": null, "email": "<EMAIL>", "phone": "13228791221", "avatar": null, "gender": "1", "dept": 10414461824, "last_token": null, "is_delete": false, "remark": null, "post": [517572581700082], "role": [540775921959829]}}, {"model": "app_user.users", "pk": 541150219354505, "fields": {"password": "pbkdf2_sha256$600000$PsjRRnRPVP3HH3iuWIFc3K$P/78Ut0cjzt2OxDWHG1U/0+ZgdEM/N2M79LfmSF+wCo=", "last_login": null, "is_superuser": true, "is_staff": true, "is_active": true, "date_joined": "2023-08-31T17:37:46", "creator": null, "modifier": "541150219354505", "update_datetime": "2023-12-15T16:43:25.930", "create_datetime": "2023-08-31T17:37:46.379", "status": "0", "username": "paopao", "nickname": "泡泡", "employee_no": null, "email": "<EMAIL>", "phone": "13229671229", "avatar": null, "gender": "0", "dept": 94609026700594, "last_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTcwMjcxNjIwNSwiaWF0IjoxNzAyNjI5ODA1LCJqdGkiOiI2MTNhNDAzNWYyOTA0MTk4YjIzOWNjZDhlMGNmY2JmZiIsInVzZXJfaWQiOjU0MTE1MDIxOTM1NDUwNX0.XL5Ksj-9gpYh-ktU3_3mHC3aBQidUzwmy9-Odv2h2Oo", "is_delete": false, "remark": null, "post": [62104692200590], "role": [540775921959829]}}, {"model": "app_dict.dicttype", "pk": 506442600448, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-12T15:52:45.632", "create_datetime": "2023-12-12T15:52:45.632", "dict_name": "信息类型", "dict_type": "sys_notice_type", "status": "0", "remark": null}}, {"model": "app_dict.dicttype", "pk": 545106760873017, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T15:39:39.386", "create_datetime": "2023-09-11T15:39:39.386", "dict_name": "用户性别", "dict_type": "sys_user_sex", "status": "0", "remark": "性别字典"}}, {"model": "app_dict.dicttype", "pk": 545186208771211, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:55:21.241", "create_datetime": "2023-09-11T20:55:21.241", "dict_name": "菜单类型", "dict_type": "sys_menu_type", "status": "0", "remark": "菜单类型"}}, {"model": "app_dict.dicttype", "pk": 545186358604333, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:55:56.964", "create_datetime": "2023-09-11T20:55:56.964", "dict_name": "菜单状态", "dict_type": "sys_show_hide", "status": "0", "remark": "菜单状态"}}, {"model": "app_dict.dicttype", "pk": 545186413918814, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:56:10.152", "create_datetime": "2023-09-11T20:56:10.152", "dict_name": "数字是否", "dict_type": "sys_num_yes_no", "status": "0", "remark": "数字是否"}}, {"model": "app_dict.dicttype", "pk": 545186489282068, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T20:56:28.120", "create_datetime": "2023-09-11T20:56:28.120", "dict_name": "状态是否", "dict_type": "sys_yes_no", "status": "0", "remark": "状态是否"}}, {"model": "app_dict.dicttype", "pk": 545186565144444, "fields": {"creator": null, "modifier": "541150219354505", "update_datetime": "2023-11-09T15:51:02.889", "create_datetime": "2023-09-11T20:56:46.207", "dict_name": "网络请求方法", "dict_type": "sys_method_api", "status": "0", "remark": ""}}, {"model": "app_dict.dictdata", "pk": 2142562432, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:04:37.538", "create_datetime": "2023-09-12T11:04:37.538", "sort": 3, "dict_label": "未知", "dict_value": "2", "dict_type": "sys_user_sex", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2145471872, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:05:22.998", "create_datetime": "2023-09-12T11:05:22.998", "sort": 0, "dict_label": "目录", "dict_value": "M", "dict_type": "sys_menu_type", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2146028928, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:05:31.702", "create_datetime": "2023-09-12T11:05:31.702", "sort": 1, "dict_label": "菜单", "dict_value": "C", "dict_type": "sys_menu_type", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2146541632, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:05:39.713", "create_datetime": "2023-09-12T11:05:39.713", "sort": 2, "dict_label": "按钮", "dict_value": "F", "dict_type": "sys_menu_type", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2148299328, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:06:07.177", "create_datetime": "2023-09-12T11:06:07.177", "sort": 0, "dict_label": "显示", "dict_value": "0", "dict_type": "sys_show_hide", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2148816448, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:06:15.257", "create_datetime": "2023-09-12T11:06:15.257", "sort": 1, "dict_label": "隐藏", "dict_value": "1", "dict_type": "sys_show_hide", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2149911552, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:06:32.368", "create_datetime": "2023-09-12T11:06:32.368", "sort": 1, "dict_label": "是", "dict_value": "0", "dict_type": "sys_num_yes_no", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2150559168, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:06:42.487", "create_datetime": "2023-09-12T11:06:42.487", "sort": 1, "dict_label": "否", "dict_value": "1", "dict_type": "sys_num_yes_no", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2153187072, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T14:56:20.061", "create_datetime": "2023-09-12T11:07:23.548", "sort": 0, "dict_label": "正常", "dict_value": "0", "dict_type": "sys_yes_no", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2153897792, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T14:56:25.959", "create_datetime": "2023-09-12T11:07:34.653", "sort": 1, "dict_label": "停用", "dict_value": "1", "dict_type": "sys_yes_no", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2156130880, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:08:09.545", "create_datetime": "2023-09-12T11:08:09.545", "sort": 1, "dict_label": "创建(POST)", "dict_value": "POST", "dict_type": "sys_method_api", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2156676928, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:08:18.077", "create_datetime": "2023-09-12T11:08:18.077", "sort": 1, "dict_label": "查询(GET)", "dict_value": "GET", "dict_type": "sys_method_api", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2157233856, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:08:26.779", "create_datetime": "2023-09-12T11:08:26.779", "sort": 1, "dict_label": "修改(PUT)", "dict_value": "PUT", "dict_type": "sys_method_api", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 2159468032, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-12T11:09:01.688", "create_datetime": "2023-09-12T11:09:01.688", "sort": 1, "dict_label": "删除(DELETE)", "dict_value": "DELETE", "dict_type": "sys_method_api", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 506443966400, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-12T15:53:06.975", "create_datetime": "2023-12-12T15:53:06.975", "sort": 1, "dict_label": "按用户", "dict_value": "0", "dict_type": "sys_notice_type", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 506444722112, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-12T15:53:18.783", "create_datetime": "2023-12-12T15:53:18.783", "sort": 2, "dict_label": "按角色", "dict_value": "1", "dict_type": "sys_notice_type", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 506446082944, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-12T15:53:40.046", "create_datetime": "2023-12-12T15:53:40.046", "sort": 3, "dict_label": "按部门", "dict_value": "2", "dict_type": "sys_notice_type", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 506446891712, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2023-12-12T15:53:52.683", "create_datetime": "2023-12-12T15:53:52.683", "sort": 4, "dict_label": "通知公告", "dict_value": "3", "dict_type": "sys_notice_type", "status": "0", "remark": null}}, {"model": "app_dict.dictdata", "pk": 545107425737310, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T15:42:17.902", "create_datetime": "2023-09-11T15:42:17.902", "sort": 1, "dict_label": "男", "dict_value": "0", "dict_type": "sys_user_sex", "status": "0", "remark": ""}}, {"model": "app_dict.dictdata", "pk": 545107468653428, "fields": {"creator": null, "modifier": null, "update_datetime": "2023-09-11T15:42:28.134", "create_datetime": "2023-09-11T15:42:28.134", "sort": 2, "dict_label": "女", "dict_value": "1", "dict_type": "sys_user_sex", "status": "0", "remark": ""}}, {"model": "app_operation_log.operationlog", "pk": 43244332928, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:14:53.063", "create_datetime": "2024-01-13T15:14:52.702", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '3', 'captcha<PERSON>ey': 1}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43245469888, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:15:10.486", "create_datetime": "2024-01-13T15:15:10.467", "request_modular": "系统-API接口表", "request_path": "/system/apis/468065838100721,406867697900673,393728528800493,306531874800395,224732935200927/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43255655296, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:17:49.632", "create_datetime": "2024-01-13T15:17:49.614", "request_modular": "系统-菜单表", "request_path": "/system/menu/230490852200003/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '删除失败：该条数据与其他数据有相关绑定,建议将与其绑定的数据解除绑定'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 43256005952, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:17:55.115", "create_datetime": "2024-01-13T15:17:55.093", "request_modular": "系统-菜单表", "request_path": "/system/menu/542592780006850/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43256249280, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:17:58.918", "create_datetime": "2024-01-13T15:17:58.895", "request_modular": "系统-菜单表", "request_path": "/system/menu/542592965080514/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43256447616, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:18:02.019", "create_datetime": "2024-01-13T15:18:01.994", "request_modular": "系统-菜单表", "request_path": "/system/menu/542593010429329/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43256677632, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:18:05.605", "create_datetime": "2024-01-13T15:18:05.588", "request_modular": "系统-菜单表", "request_path": "/system/menu/230490852200003/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43271213376, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:21:53.257", "create_datetime": "2024-01-13T15:21:52.709", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '5', 'captcha<PERSON>ey': 2}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43274229120, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:22:39.847", "create_datetime": "2024-01-13T15:22:39.830", "request_modular": "系统-岗位表", "request_path": "/system/post/", "request_body": "{'post_name': '测试', 'post_code': 'TEST', 'sort': 3, 'status': '1'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43286451904, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:25:50.828", "create_datetime": "2024-01-13T15:25:50.811", "request_modular": "系统-菜单表", "request_path": "/system/menu/", "request_body": "{'parentId': 458000302592, 'menu_type': 'C', 'parent': 457773413824, 'menu_name': '系统日志', 'sort': 2, 'icon': 'elementList', 'permission': 'log:system:list', 'path': '/system/system-log/', 'component': '/log/system/index', 'status': '0', 'is_keep_alive': '1', 'is_iframe': '1', 'is_hide': '0', 'is_affix': '1'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43288007296, "fields": {"creator": null, "modifier": null, "update_datetime": "2024-01-13T15:26:15.135", "create_datetime": "2024-01-13T15:26:15.116", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '3', 'captcha<PERSON>ey': 3}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 43288419648, "fields": {"creator": null, "modifier": null, "update_datetime": "2024-01-13T15:26:21.577", "create_datetime": "2024-01-13T15:26:21.557", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '30', 'captcha<PERSON>ey': 4}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 43288765952, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:26:27.481", "create_datetime": "2024-01-13T15:26:26.968", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '7', 'captcha<PERSON>ey': 5}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43314053760, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:33:02.134", "create_datetime": "2024-01-13T15:33:02.090", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/", "request_body": "{'type': 0, 'name': '间隔任务-加法', 'task': 'app_crontab.tasks.cron_job_add', 'kwargs': {'x': 5, 'y': 6}, 'one_off': False, 'enabled': True, 'interval': {'period': 'seconds', 'every': 10}}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '添加成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43317758912, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:34:00.023", "create_datetime": "2024-01-13T15:33:59.983", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/enabled/2/", "request_body": "{'enabled': True}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '修改成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43321193088, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:34:53.683", "create_datetime": "2024-01-13T15:34:53.642", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/", "request_body": "{'task': 'app_crontab.tasks.cron_job_mul', 'type': 1, 'name': '周期任务-乘法', 'crontab': '* * * * *', 'kwargs': {'x': 5, 'y': 6}, 'one_off': False, 'enabled': True, 'interval': {'period': None, 'every': None}}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '添加成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43321404224, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:34:56.979", "create_datetime": "2024-01-13T15:34:56.941", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/enabled/3/", "request_body": "{'enabled': True}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '修改成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43325527488, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:36:01.404", "create_datetime": "2024-01-13T15:36:01.367", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/", "request_body": "{'task': 'app_crontab.tasks.cron_job_test', 'name': '间隔任务-测试任务', 'type': 1, 'crontab': '37 15 * * *', 'one_off': True, 'enabled': True, 'interval': {'period': None, 'every': None}}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '添加成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43325760960, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:36:05.048", "create_datetime": "2024-01-13T15:36:05.015", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/enabled/4/", "request_body": "{'enabled': True}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '修改成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43330263168, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:37:15.404", "create_datetime": "2024-01-13T15:37:15.362", "request_modular": "消息中心", "request_path": "/system/message-center/", "request_body": "{'target_type': '0', 'target_user': [252375424640, 234457355328, 541150219354505], 'title': '发给用户的', 'content': '<p><strong>测试水水水水水水水水水水水水撒啊啊啊啊啊啊</strong></p>'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43331629824, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:37:36.768", "create_datetime": "2024-01-13T15:37:36.716", "request_modular": "消息中心", "request_path": "/system/message-center/", "request_body": "{'content': '<p>撒啊水水水水水水水水说法伽师</p>', 'target_type': '1', 'target_role': [540775921959829, 444421914176], 'title': '发给角色的'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43332797056, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:37:55.006", "create_datetime": "2024-01-13T15:37:54.954", "request_modular": "消息中心", "request_path": "/system/message-center/", "request_body": "{'content': '<p>啊啊啊啊啊啊啊啊啊啊啊啊啊</p>', 'target_type': '2', 'target_dept': [10414461824, 376960140500837, 94609026700594, 114667862200894, 57407999600609, 42188673800208], 'title': '发给部门的'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43333810624, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:38:10.828", "create_datetime": "2024-01-13T15:38:10.791", "request_modular": "消息中心", "request_path": "/system/message-center/", "request_body": "{'content': '<p>这是一份公告</p>', 'target_type': '3', 'title': '公告'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43335737024, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:38:40.924", "create_datetime": "2024-01-13T15:38:40.891", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/enabled/2/", "request_body": "{'enabled': False}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '修改成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43336094144, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:38:46.508", "create_datetime": "2024-01-13T15:38:46.471", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/enabled/3/", "request_body": "{'enabled': False}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '修改成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 147647524096, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-09T11:29:52.583", "create_datetime": "2025-05-09T11:29:52.564", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'admin', 'password': '******', 'captcha': '0', 'captcha<PERSON>ey': 2}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 135.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '账号/密码不正确'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 147656377792, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-09T11:32:10.924", "create_datetime": "2025-05-09T11:32:10.903", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '6', 'captcha<PERSON>ey': 6}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 135.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 147656863936, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-09T11:32:18.870", "create_datetime": "2025-05-09T11:32:18.499", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '18', 'captcha<PERSON>ey': 7}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 135.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154212804160, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-10T15:59:35.091", "create_datetime": "2025-05-10T15:59:35.065", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '9', 'captcha<PERSON>ey': 8}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154213237504, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T15:59:42.406", "create_datetime": "2025-05-10T15:59:41.836", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '11', 'captcha<PERSON>ey': 9}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154223909312, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T16:02:29.215", "create_datetime": "2025-05-10T16:02:28.583", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '15', 'captcha<PERSON>ey': 12}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154239876032, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T16:06:38.572", "create_datetime": "2025-05-10T16:06:38.063", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154240681408, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T16:06:51.156", "create_datetime": "2025-05-10T16:06:50.647", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154291450368, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T16:20:04.851", "create_datetime": "2025-05-10T16:20:03.912", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '0', 'captcha<PERSON>ey': 31}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154305764352, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-10T16:23:47.601", "create_datetime": "2025-05-10T16:23:47.569", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '9', 'captcha<PERSON>ey': 32}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154306701056, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-10T16:24:02.260", "create_datetime": "2025-05-10T16:24:02.204", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '18', 'captcha<PERSON>ey': 33}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154320791360, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T16:27:43.398", "create_datetime": "2025-05-10T16:27:42.365", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '32', 'captcha<PERSON>ey': 40}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154328189952, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T16:29:38.979", "create_datetime": "2025-05-10T16:29:37.968", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '8', 'captcha<PERSON>ey': 46}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154333997184, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T16:31:09.792", "create_datetime": "2025-05-10T16:31:08.706", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '7', 'captcha<PERSON>ey': 47}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154357443264, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-10T16:37:16.261", "create_datetime": "2025-05-10T16:37:15.051", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '9', 'captcha<PERSON>ey': 48}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154370431360, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T16:40:39.058", "create_datetime": "2025-05-10T16:40:37.990", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '3', 'captcha<PERSON>ey': 49}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154406293760, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-10T16:49:58.400", "create_datetime": "2025-05-10T16:49:58.340", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '45', 'captcha<PERSON>ey': 50}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154406563520, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T16:50:03.572", "create_datetime": "2025-05-10T16:50:02.555", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '11', 'captcha<PERSON>ey': 51}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154480308224, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-10T17:09:15.812", "create_datetime": "2025-05-10T17:09:14.816", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '24', 'captcha<PERSON>ey': 60}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154504694080, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:15:36.843", "create_datetime": "2025-05-10T17:15:35.845", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '27', 'captcha<PERSON>ey': 61}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154509698624, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:16:54.119", "create_datetime": "2025-05-10T17:16:54.042", "request_modular": "系统-部门表", "request_path": "/system/dept/42188673800208/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154509878464, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:16:56.911", "create_datetime": "2025-05-10T17:16:56.851", "request_modular": "系统-部门表", "request_path": "/system/dept/57407999600609/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154520684416, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:19:45.962", "create_datetime": "2025-05-10T17:19:45.694", "request_modular": "系统-菜单表", "request_path": "/system/menu/268338419700006/", "request_body": "{'id': 268338419700006, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-24 14:36:11', 'update_datetime': '2023-12-12 14:55:31', 'modifier': '541150219354505', 'icon': 'iconfont icon-siweidaotu', 'menu_name': 'API管理', 'sort': 2, 'path': '/system/api', 'component': '/system/api/index', 'is_iframe': '1', 'is_link': '', 'menu_type': 'C', 'is_hide': '1', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:list', 'status': '0', 'remark': '', 'creator': None, 'parent': 7298689300002, 'children': [{'id': 542593991904854, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:14:48', 'update_datetime': '2023-09-04 17:14:48', 'modifier': None, 'icon': '', 'menu_name': '添加api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:add', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594110045814, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:16', 'update_datetime': '2023-09-04 17:15:16', 'modifier': None, 'icon': '', 'menu_name': '编辑api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:edit', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594206556749, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:39', 'update_datetime': '2023-09-04 17:15:39', 'modifier': None, 'icon': '', 'menu_name': '删除api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:delete', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154523562944, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:20:30.737", "create_datetime": "2025-05-10T17:20:30.671", "request_modular": "系统-菜单表", "request_path": "/system/menu/268338419700006/", "request_body": "{'id': 268338419700006, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-24 14:36:11', 'update_datetime': '2025-05-10 17:19:45', 'modifier': '541150219354505', 'icon': 'iconfont icon-siweidaotu', 'menu_name': 'API管理', 'sort': 2, 'path': '/system/api', 'component': '/system/api/index', 'is_iframe': '1', 'is_link': '', 'menu_type': 'C', 'is_hide': '1', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:list', 'status': '1', 'remark': '', 'creator': None, 'parent': 7298689300002, 'children': [{'id': 542593991904854, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:14:48', 'update_datetime': '2023-09-04 17:14:48', 'modifier': None, 'icon': '', 'menu_name': '添加api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:add', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594110045814, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:16', 'update_datetime': '2023-09-04 17:15:16', 'modifier': None, 'icon': '', 'menu_name': '编辑api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:edit', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594206556749, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:39', 'update_datetime': '2023-09-04 17:15:39', 'modifier': None, 'icon': '', 'menu_name': '删除api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:delete', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154526002944, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-10T17:21:08.853", "create_datetime": "2025-05-10T17:21:08.796", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '30', 'captcha<PERSON>ey': 62}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154526684608, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-10T17:21:20.374", "create_datetime": "2025-05-10T17:21:19.447", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '4', 'captcha<PERSON>ey': 63}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154528152960, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-10T17:21:42.442", "create_datetime": "2025-05-10T17:21:42.390", "request_modular": "系统-菜单表", "request_path": "/system/menu/268338419700006/", "request_body": "{'id': 268338419700006, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-24 14:36:11', 'update_datetime': '2025-05-10 17:20:30', 'modifier': '541150219354505', 'icon': 'iconfont icon-siweidaotu', 'menu_name': 'API管理', 'sort': 2, 'path': '/system/api', 'component': '/system/api/index', 'is_iframe': '1', 'is_link': '', 'menu_type': 'C', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:list', 'status': '0', 'remark': '', 'creator': None, 'parent': 7298689300002, 'children': [{'id': 542593991904854, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:14:48', 'update_datetime': '2023-09-04 17:14:48', 'modifier': None, 'icon': '', 'menu_name': '添加api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:add', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594110045814, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:16', 'update_datetime': '2023-09-04 17:15:16', 'modifier': None, 'icon': '', 'menu_name': '编辑api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:edit', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594206556749, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:39', 'update_datetime': '2023-09-04 17:15:39', 'modifier': None, 'icon': '', 'menu_name': '删除api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:delete', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '您没有执行该操作的权限。'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154529257344, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-10T17:21:59.701", "create_datetime": "2025-05-10T17:21:59.646", "request_modular": "系统-菜单表", "request_path": "/system/menu/268338419700006/", "request_body": "{'id': 268338419700006, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-24 14:36:11', 'update_datetime': '2025-05-10 17:20:30', 'modifier': '541150219354505', 'icon': 'iconfont icon-siweidaotu', 'menu_name': 'API管理', 'sort': 2, 'path': '/system/api', 'component': '/system/api/index', 'is_iframe': '1', 'is_link': '', 'menu_type': 'C', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:list', 'status': '0', 'remark': '', 'creator': None, 'parent': 7298689300002, 'children': [{'id': 542593991904854, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:14:48', 'update_datetime': '2023-09-04 17:14:48', 'modifier': None, 'icon': '', 'menu_name': '添加api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:add', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594110045814, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:16', 'update_datetime': '2023-09-04 17:15:16', 'modifier': None, 'icon': '', 'menu_name': '编辑api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:edit', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594206556749, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:39', 'update_datetime': '2023-09-04 17:15:39', 'modifier': None, 'icon': '', 'menu_name': '删除api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:delete', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '您没有执行该操作的权限。'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154530477056, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-10T17:22:18.753", "create_datetime": "2025-05-10T17:22:18.704", "request_modular": "系统-菜单表", "request_path": "/system/menu/268338419700006/", "request_body": "{'id': 268338419700006, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-24 14:36:11', 'update_datetime': '2025-05-10 17:20:30', 'modifier': '541150219354505', 'icon': 'iconfont icon-siweidaotu', 'menu_name': 'API管理', 'sort': 2, 'path': '/system/api', 'component': '/system/api/index', 'is_iframe': '1', 'is_link': '', 'menu_type': 'C', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:list', 'status': '0', 'remark': '', 'creator': None, 'parent': 7298689300002, 'children': [{'id': 542593991904854, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:14:48', 'update_datetime': '2023-09-04 17:14:48', 'modifier': None, 'icon': '', 'menu_name': '添加api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:add', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594110045814, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:16', 'update_datetime': '2023-09-04 17:15:16', 'modifier': None, 'icon': '', 'menu_name': '编辑api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:edit', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594206556749, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:39', 'update_datetime': '2023-09-04 17:15:39', 'modifier': None, 'icon': '', 'menu_name': '删除api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:delete', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '您没有执行该操作的权限。'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154531770432, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-10T17:22:38.962", "create_datetime": "2025-05-10T17:22:38.913", "request_modular": "系统-菜单表", "request_path": "/system/menu/268338419700006/", "request_body": "{'id': 268338419700006, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-24 14:36:11', 'update_datetime': '2025-05-10 17:20:30', 'modifier': '541150219354505', 'icon': 'iconfont icon-siweidaotu', 'menu_name': 'API管理', 'sort': 2, 'path': '/system/api', 'component': '/system/api/index', 'is_iframe': '1', 'is_link': '', 'menu_type': 'C', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:list', 'status': '1', 'remark': '', 'creator': None, 'parent': 7298689300002, 'children': [{'id': 542593991904854, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:14:48', 'update_datetime': '2023-09-04 17:14:48', 'modifier': None, 'icon': '', 'menu_name': '添加api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:add', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594110045814, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:16', 'update_datetime': '2023-09-04 17:15:16', 'modifier': None, 'icon': '', 'menu_name': '编辑api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:edit', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594206556749, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:39', 'update_datetime': '2023-09-04 17:15:39', 'modifier': None, 'icon': '', 'menu_name': '删除api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:delete', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '您没有执行该操作的权限。'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154540865344, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-10T17:25:01.072", "create_datetime": "2025-05-10T17:25:01.021", "request_modular": "系统-角色表", "request_path": "/system/role/", "request_body": "{'role_name': '普通用户', 'role_key': 'common', 'sort': 3, 'status': '0', 'menu': [7298689300002, 384888858368, 457773413824, 479138297792, 479144112576, 479146012288, 506472197952, 384901100480, 285082180800, 458000302592, 458003252032, 458004760128], 'api': []}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '您没有执行该操作的权限。'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154541906560, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-10T17:25:17.336", "create_datetime": "2025-05-10T17:25:17.290", "request_modular": "系统-角色表", "request_path": "/system/role/540775921959829/", "request_body": "{'id': 540775921959829, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-30 16:50:26', 'update_datetime': '2023-12-14 15:35:05', 'modifier': '541150219354505', 'role_name': '管理员', 'role_key': 'admin', 'status': '0', 'sort': 1, 'admin': False, 'data_scope': '5', 'remark': None, 'creator': None, 'dept': [], 'menu': [457773413824, 7298689300002, 324683656700002, 542594574887944, 542594449490837, 542594511251963, 542594636888145, 357134961700006, 542594825191424, 542594873530777, 542594930380374, 542594993353654, 538569709516226, 542595409881595, 542595489577566, 542595568258514, 542596157969268, 542596856497045, 542597283951149, 542597333875949, 542597388884246, 542598150314000, 542598287434186, 542598338923462, 542598380740673, 542598541692895, 545179689782607, 545180315367243, 545180635585576, 545180720667033, 545180793786335, 545180864145784, 545180930025717, 545181009289674, 479138297792, 479144112576, 479146012288, 506472197952, 384888858368, 384901100480, 384924956608, 384928228864, 384929917440, 384931381952, 384934012416, 419535754688, 285082180800, 458000302592, 458003252032, 458004760128], 'api': [{'path': '/system/message-center/:message_id/', 'method': 'DELETE'}, {'path': '/system/message-center/', 'method': 'POST'}, {'path': '/system/message-center/get-self-receive/', 'method': 'GET'}, {'path': '/system/message-center/:message_id/', 'method': 'GET'}, {'path': '/system/message-center/', 'method': 'GET'}, {'path': '/system/operation-log/', 'method': 'GET'}, {'path': '/system/operation-log/:log_id/', 'method': 'DELETE'}, {'path': '/system/operation-log/delete-all-logs/', 'method': 'GET'}, {'path': '/tool/monitor/get-system-info/', 'method': 'GET'}, {'path': '/system/apis/get-all-api-group/', 'method': 'GET'}, {'path': '/system/apis/:api_id/', 'method': 'PUT'}, {'path': '/system/apis/:api_id/', 'method': 'DELETE'}, {'path': '/system/apis/:api_id/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'POST'}, {'path': '/job/crontab/periodic-task/tasklist/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/enabled/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'DELETE'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/', 'method': 'POST'}, {'path': '/job/crontab/task-result/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/', 'method': 'GET'}, {'path': '/system/post/get-all-posts/', 'method': 'GET'}, {'path': '/system/post/:post_id/', 'method': 'PUT'}, {'path': '/system/post/:post_id/', 'method': 'DELETE'}, {'path': '/system/post/:post_id/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'POST'}, {'path': '/system/role/get-all-roles/', 'method': 'GET'}, {'path': '/system/role/role-id-to-menu/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'PUT'}, {'path': '/system/role/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'DELETE'}, {'path': '/system/role/', 'method': 'POST'}, {'path': '/system/role/', 'method': 'GET'}, {'path': '/system/update-user-info/', 'method': 'GET'}, {'path': '/system/user/user-info/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'POST'}, {'path': '/system/user/:user_id/', 'method': 'DELETE'}, {'path': '/system/user/:user_id/', 'method': 'GET'}, {'path': '/system/user/:user_id/', 'method': 'PUT'}, {'path': '/system/menu/menu-tree-simple/', 'method': 'GET'}, {'path': '/system/menu/menu-tree/', 'method': 'GET'}, {'path': '/system/menu/:menu_id/', 'method': 'PUT'}, {'path': '/system/menu/:menu_id/', 'method': 'DELETE'}, {'path': '/system/menu/:menu_id/', 'method': 'GET'}, {'path': '/system/menu/', 'method': 'POST'}, {'path': '/system/menu/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'PUT'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'DELETE'}, {'path': '/system/dict-data/', 'method': 'GET'}, {'path': '/system/dict-data/', 'method': 'POST'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'GET'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'PUT'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'DELETE'}, {'path': '/system/dict-type/', 'method': 'GET'}, {'path': '/system/dict-type/', 'method': 'POST'}, {'path': '/system/dept/dept-tree/', 'method': 'GET'}, {'path': '/system/dept/', 'method': 'POST'}, {'path': '/system/dept/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'DELETE'}, {'path': '/system/dept/:dept_id/', 'method': 'PUT'}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '您没有执行该操作的权限。'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154544519104, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-10T17:25:58.165", "create_datetime": "2025-05-10T17:25:58.111", "request_modular": "系统-角色表", "request_path": "/system/role/540775921959829/", "request_body": "{'id': 540775921959829, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-30 16:50:26', 'update_datetime': '2023-12-14 15:35:05', 'modifier': '541150219354505', 'role_name': '管理员', 'role_key': 'admin', 'status': '0', 'sort': 1, 'admin': False, 'data_scope': '5', 'remark': None, 'creator': None, 'dept': [], 'menu': [457773413824, 7298689300002, 324683656700002, 542594574887944, 542594449490837, 542594511251963, 542594636888145, 357134961700006, 542594825191424, 542594873530777, 542594930380374, 542594993353654, 538569709516226, 542595409881595, 542595489577566, 542595568258514, 542596157969268, 542596856497045, 542597283951149, 542597333875949, 542597388884246, 542598150314000, 542598287434186, 542598338923462, 542598380740673, 542598541692895, 545179689782607, 545180315367243, 545180635585576, 545180720667033, 545180793786335, 545180864145784, 545180930025717, 545181009289674, 479138297792, 479144112576, 479146012288, 506472197952, 384888858368, 384901100480, 384924956608, 384928228864, 384929917440, 384931381952, 384934012416, 419535754688, 285082180800, 458000302592, 458003252032, 458004760128], 'api': [{'path': '/system/message-center/:message_id/', 'method': 'DELETE'}, {'path': '/system/message-center/', 'method': 'POST'}, {'path': '/system/message-center/get-self-receive/', 'method': 'GET'}, {'path': '/system/message-center/:message_id/', 'method': 'GET'}, {'path': '/system/message-center/', 'method': 'GET'}, {'path': '/system/operation-log/', 'method': 'GET'}, {'path': '/system/operation-log/:log_id/', 'method': 'DELETE'}, {'path': '/system/operation-log/delete-all-logs/', 'method': 'GET'}, {'path': '/tool/monitor/get-system-info/', 'method': 'GET'}, {'path': '/system/apis/get-all-api-group/', 'method': 'GET'}, {'path': '/system/apis/:api_id/', 'method': 'PUT'}, {'path': '/system/apis/:api_id/', 'method': 'DELETE'}, {'path': '/system/apis/:api_id/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'POST'}, {'path': '/job/crontab/periodic-task/tasklist/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/enabled/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'DELETE'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/', 'method': 'POST'}, {'path': '/job/crontab/task-result/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/', 'method': 'GET'}, {'path': '/system/post/get-all-posts/', 'method': 'GET'}, {'path': '/system/post/:post_id/', 'method': 'PUT'}, {'path': '/system/post/:post_id/', 'method': 'DELETE'}, {'path': '/system/post/:post_id/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'POST'}, {'path': '/system/role/get-all-roles/', 'method': 'GET'}, {'path': '/system/role/role-id-to-menu/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'PUT'}, {'path': '/system/role/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'DELETE'}, {'path': '/system/role/', 'method': 'POST'}, {'path': '/system/role/', 'method': 'GET'}, {'path': '/system/update-user-info/', 'method': 'GET'}, {'path': '/system/user/user-info/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'POST'}, {'path': '/system/user/:user_id/', 'method': 'DELETE'}, {'path': '/system/user/:user_id/', 'method': 'GET'}, {'path': '/system/user/:user_id/', 'method': 'PUT'}, {'path': '/system/menu/menu-tree-simple/', 'method': 'GET'}, {'path': '/system/menu/menu-tree/', 'method': 'GET'}, {'path': '/system/menu/:menu_id/', 'method': 'PUT'}, {'path': '/system/menu/:menu_id/', 'method': 'DELETE'}, {'path': '/system/menu/:menu_id/', 'method': 'GET'}, {'path': '/system/menu/', 'method': 'POST'}, {'path': '/system/menu/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'PUT'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'DELETE'}, {'path': '/system/dict-data/', 'method': 'GET'}, {'path': '/system/dict-data/', 'method': 'POST'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'GET'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'PUT'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'DELETE'}, {'path': '/system/dict-type/', 'method': 'GET'}, {'path': '/system/dict-type/', 'method': 'POST'}, {'path': '/system/dept/dept-tree/', 'method': 'GET'}, {'path': '/system/dept/', 'method': 'POST'}, {'path': '/system/dept/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'DELETE'}, {'path': '/system/dept/:dept_id/', 'method': 'PUT'}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '您没有执行该操作的权限。'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154548940608, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:27:08.389", "create_datetime": "2025-05-10T17:27:07.197", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '21', 'captcha<PERSON>ey': 64}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154549730368, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:27:19.705", "create_datetime": "2025-05-10T17:27:19.537", "request_modular": "系统-角色表", "request_path": "/system/role/540775921959829/", "request_body": "{'id': 540775921959829, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-30 16:50:26', 'update_datetime': '2023-12-14 15:35:05', 'modifier': '541150219354505', 'role_name': '管理员', 'role_key': 'admin', 'status': '0', 'sort': 1, 'admin': False, 'data_scope': '5', 'remark': '1', 'creator': None, 'dept': [], 'menu': [457773413824, 7298689300002, 324683656700002, 542594574887944, 542594449490837, 542594511251963, 542594636888145, 357134961700006, 542594825191424, 542594873530777, 542594930380374, 542594993353654, 538569709516226, 542595409881595, 542595489577566, 542595568258514, 542596157969268, 542596856497045, 542597283951149, 542597333875949, 542597388884246, 542598150314000, 542598287434186, 542598338923462, 542598380740673, 542598541692895, 545179689782607, 545180315367243, 545180635585576, 545180720667033, 545180793786335, 545180864145784, 545180930025717, 545181009289674, 479138297792, 479144112576, 479146012288, 506472197952, 384888858368, 384901100480, 384924956608, 384928228864, 384929917440, 384931381952, 384934012416, 419535754688, 285082180800, 458000302592, 458003252032, 458004760128], 'api': [{'path': '/system/message-center/:message_id/', 'method': 'DELETE'}, {'path': '/system/message-center/', 'method': 'POST'}, {'path': '/system/message-center/get-self-receive/', 'method': 'GET'}, {'path': '/system/message-center/:message_id/', 'method': 'GET'}, {'path': '/system/message-center/', 'method': 'GET'}, {'path': '/system/operation-log/', 'method': 'GET'}, {'path': '/system/operation-log/:log_id/', 'method': 'DELETE'}, {'path': '/system/operation-log/delete-all-logs/', 'method': 'GET'}, {'path': '/tool/monitor/get-system-info/', 'method': 'GET'}, {'path': '/system/apis/get-all-api-group/', 'method': 'GET'}, {'path': '/system/apis/:api_id/', 'method': 'PUT'}, {'path': '/system/apis/:api_id/', 'method': 'DELETE'}, {'path': '/system/apis/:api_id/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'POST'}, {'path': '/job/crontab/periodic-task/tasklist/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/enabled/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'DELETE'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/', 'method': 'POST'}, {'path': '/job/crontab/task-result/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/', 'method': 'GET'}, {'path': '/system/post/get-all-posts/', 'method': 'GET'}, {'path': '/system/post/:post_id/', 'method': 'PUT'}, {'path': '/system/post/:post_id/', 'method': 'DELETE'}, {'path': '/system/post/:post_id/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'POST'}, {'path': '/system/role/get-all-roles/', 'method': 'GET'}, {'path': '/system/role/role-id-to-menu/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'PUT'}, {'path': '/system/role/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'DELETE'}, {'path': '/system/role/', 'method': 'POST'}, {'path': '/system/role/', 'method': 'GET'}, {'path': '/system/update-user-info/', 'method': 'GET'}, {'path': '/system/user/user-info/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'POST'}, {'path': '/system/user/:user_id/', 'method': 'DELETE'}, {'path': '/system/user/:user_id/', 'method': 'GET'}, {'path': '/system/user/:user_id/', 'method': 'PUT'}, {'path': '/system/menu/menu-tree-simple/', 'method': 'GET'}, {'path': '/system/menu/menu-tree/', 'method': 'GET'}, {'path': '/system/menu/:menu_id/', 'method': 'PUT'}, {'path': '/system/menu/:menu_id/', 'method': 'DELETE'}, {'path': '/system/menu/:menu_id/', 'method': 'GET'}, {'path': '/system/menu/', 'method': 'POST'}, {'path': '/system/menu/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'PUT'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'DELETE'}, {'path': '/system/dict-data/', 'method': 'GET'}, {'path': '/system/dict-data/', 'method': 'POST'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'GET'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'PUT'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'DELETE'}, {'path': '/system/dict-type/', 'method': 'GET'}, {'path': '/system/dict-type/', 'method': 'POST'}, {'path': '/system/dept/dept-tree/', 'method': 'GET'}, {'path': '/system/dept/', 'method': 'POST'}, {'path': '/system/dept/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'DELETE'}, {'path': '/system/dept/:dept_id/', 'method': 'PUT'}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154550717248, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:27:35.080", "create_datetime": "2025-05-10T17:27:34.957", "request_modular": "系统-角色表", "request_path": "/system/role/540775921959829/", "request_body": "{'id': 540775921959829, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-30 16:50:26', 'update_datetime': '2025-05-10 17:27:19', 'modifier': '541150219354505', 'role_name': '管理员', 'role_key': 'admin', 'status': '0', 'sort': 1, 'admin': False, 'data_scope': '1', 'remark': '1', 'creator': None, 'dept': [], 'menu': [542597283951149, 542597333875949, 384901100480, 542597388884246, 384928228864, 545180315367243, 542595568258514, 542598287434186, 542598338923462, 542598380740673, 458003252032, 542598541692895, 545180635585576, 479144112576, 545180720667033, 545180793786335, 545180864145784, 542594825191424, 545180930025717, 542594449490837, 542594511251963, 542594574887944, 542594636888145, 545181009289674, 542594873530777, 542594930380374, 542594993353654, 542595409881595, 542595489577566, 542596856497045, 458004760128, 384929917440, 479146012288, 384931381952, 506472197952, 285082180800, 384934012416, 419535754688]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154551903104, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:27:53.679", "create_datetime": "2025-05-10T17:27:53.486", "request_modular": "系统-角色表", "request_path": "/system/role/540775921959829/", "request_body": "{'id': 540775921959829, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-30 16:50:26', 'update_datetime': '2025-05-10 17:27:35', 'modifier': '541150219354505', 'role_name': '管理员', 'role_key': 'admin', 'status': '0', 'sort': 1, 'admin': False, 'data_scope': '1', 'remark': '', 'creator': None, 'dept': [], 'menu': [7298689300002, 324683656700002, 542594574887944, 542594449490837, 542594511251963, 542594636888145, 357134961700006, 542594825191424, 542594873530777, 542594930380374, 542594993353654, 538569709516226, 542595409881595, 542595489577566, 542595568258514, 542596157969268, 542596856497045, 542597283951149, 542597333875949, 542597388884246, 542598150314000, 542598287434186, 542598338923462, 542598380740673, 542598541692895, 545179689782607, 545180315367243, 545180635585576, 545180720667033, 545180793786335, 545180864145784, 545180930025717, 545181009289674, 479138297792, 479144112576, 479146012288, 506472197952, 384888858368, 384901100480, 384924956608, 384928228864, 384929917440, 384931381952, 384934012416, 419535754688, 285082180800, 457773413824, 458000302592, 458003252032, 458004760128, 43286452544], 'api': [{'path': '/system/message-center/:message_id/', 'method': 'DELETE'}, {'path': '/system/message-center/', 'method': 'POST'}, {'path': '/system/message-center/get-self-receive/', 'method': 'GET'}, {'path': '/system/message-center/:message_id/', 'method': 'GET'}, {'path': '/system/message-center/', 'method': 'GET'}, {'path': '/system/operation-log/', 'method': 'GET'}, {'path': '/system/operation-log/:log_id/', 'method': 'DELETE'}, {'path': '/system/operation-log/delete-all-logs/', 'method': 'GET'}, {'path': '/tool/monitor/get-system-info/', 'method': 'GET'}, {'path': '/system/apis/get-all-api-group/', 'method': 'GET'}, {'path': '/system/apis/:api_id/', 'method': 'PUT'}, {'path': '/system/apis/:api_id/', 'method': 'DELETE'}, {'path': '/system/apis/:api_id/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'POST'}, {'path': '/job/crontab/periodic-task/tasklist/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/enabled/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'DELETE'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/', 'method': 'POST'}, {'path': '/job/crontab/task-result/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/', 'method': 'GET'}, {'path': '/system/post/get-all-posts/', 'method': 'GET'}, {'path': '/system/post/:post_id/', 'method': 'PUT'}, {'path': '/system/post/:post_id/', 'method': 'DELETE'}, {'path': '/system/post/:post_id/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'POST'}, {'path': '/system/role/get-all-roles/', 'method': 'GET'}, {'path': '/system/role/role-id-to-menu/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'PUT'}, {'path': '/system/role/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'DELETE'}, {'path': '/system/role/', 'method': 'POST'}, {'path': '/system/role/', 'method': 'GET'}, {'path': '/system/update-user-info/', 'method': 'GET'}, {'path': '/system/user/user-info/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'POST'}, {'path': '/system/user/:user_id/', 'method': 'DELETE'}, {'path': '/system/user/:user_id/', 'method': 'GET'}, {'path': '/system/user/:user_id/', 'method': 'PUT'}, {'path': '/system/menu/menu-tree-simple/', 'method': 'GET'}, {'path': '/system/menu/menu-tree/', 'method': 'GET'}, {'path': '/system/menu/:menu_id/', 'method': 'PUT'}, {'path': '/system/menu/:menu_id/', 'method': 'DELETE'}, {'path': '/system/menu/:menu_id/', 'method': 'GET'}, {'path': '/system/menu/', 'method': 'POST'}, {'path': '/system/menu/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'PUT'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'DELETE'}, {'path': '/system/dict-data/', 'method': 'GET'}, {'path': '/system/dict-data/', 'method': 'POST'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'GET'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'PUT'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'DELETE'}, {'path': '/system/dict-type/', 'method': 'GET'}, {'path': '/system/dict-type/', 'method': 'POST'}, {'path': '/system/dept/dept-tree/', 'method': 'GET'}, {'path': '/system/dept/', 'method': 'POST'}, {'path': '/system/dept/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'DELETE'}, {'path': '/system/dept/:dept_id/', 'method': 'PUT'}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154552818304, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:28:07.841", "create_datetime": "2025-05-10T17:28:07.786", "request_modular": "系统-菜单表", "request_path": "/system/menu/268338419700006/", "request_body": "{'id': 268338419700006, 'modifier_name': '泡泡', 'creator_name': None, 'create_datetime': '2023-08-24 14:36:11', 'update_datetime': '2025-05-10 17:20:30', 'modifier': '541150219354505', 'icon': 'iconfont icon-siweidaotu', 'menu_name': 'API管理', 'sort': 2, 'path': '/system/api', 'component': '/system/api/index', 'is_iframe': '1', 'is_link': '', 'menu_type': 'C', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:list', 'status': '0', 'remark': '', 'creator': None, 'parent': 7298689300002, 'children': [{'id': 542593991904854, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:14:48', 'update_datetime': '2023-09-04 17:14:48', 'modifier': None, 'icon': '', 'menu_name': '添加api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:add', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594110045814, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:16', 'update_datetime': '2023-09-04 17:15:16', 'modifier': None, 'icon': '', 'menu_name': '编辑api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:edit', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}, {'id': 542594206556749, 'modifier_name': None, 'creator_name': None, 'create_datetime': '2023-09-04 17:15:39', 'update_datetime': '2023-09-04 17:15:39', 'modifier': None, 'icon': '', 'menu_name': '删除api', 'sort': 1, 'path': '', 'component': '', 'is_iframe': None, 'is_link': '', 'menu_type': 'F', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'system:api:delete', 'status': '0', 'remark': '', 'creator': None, 'parent': 268338419700006, 'children': []}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154555340416, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:28:47.279", "create_datetime": "2025-05-10T17:28:47.195", "request_modular": "系统-角色表", "request_path": "/system/role/", "request_body": "{'role_name': '普通用户', 'role_key': 'common', 'sort': 3, 'menu': [7298689300002, 384888858368, 479138297792, 479144112576, 479146012288, 506472197952, 384901100480, 285082180800, 457773413824, 458000302592, 458003252032, 458004760128, 43286452544], 'api': []}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154558975936, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:29:44.485", "create_datetime": "2025-05-10T17:29:43.999", "request_modular": "用户表", "request_path": "/system/user/", "request_body": "{'nickname': 'common', 'username': 'common', 'password': '******', 'phone': '13160006317', 'gender': '0', 'dept': 10414461824, 'post': [], 'role': [154555344320], 'is_superuser': False, 'is_staff': True}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154560629312, "fields": {"creator": 154559003456, "modifier": null, "update_datetime": "2025-05-10T17:30:10.745", "create_datetime": "2025-05-10T17:30:09.833", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'common', 'password': '******', 'captcha': '16', 'captcha<PERSON>ey': 65}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154566007360, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-10T17:31:33.918", "create_datetime": "2025-05-10T17:31:33.865", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'common', 'password': '******', 'captcha': '25', 'captcha<PERSON>ey': 67}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 154566568640, "fields": {"creator": 154559003456, "modifier": null, "update_datetime": "2025-05-10T17:31:43.508", "create_datetime": "2025-05-10T17:31:42.635", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'common', 'password': '******', 'captcha': '9', 'captcha<PERSON>ey': 68}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154567397312, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:31:56.497", "create_datetime": "2025-05-10T17:31:55.583", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '6', 'captcha<PERSON>ey': 70}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154568604224, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-10T17:32:14.589", "create_datetime": "2025-05-10T17:32:14.441", "request_modular": "用户表", "request_path": "/system/user/154559003456/", "request_body": "{'id': 154559003456, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-10 17:29:44', 'update_datetime': '2025-05-10 17:29:44', 'dept_name': '产品设计部门', 'last_login': None, 'is_superuser': True, 'is_staff': True, 'is_active': True, 'date_joined': '2025-05-10 17:29:44', 'modifier': '541150219354505', 'status': '0', 'username': 'common', 'nickname': 'common', 'employee_no': None, 'email': None, 'phone': '13160006317', 'avatar': None, 'gender': '0', 'last_token': None, 'is_delete': False, 'remark': None, 'creator': 541150219354505, 'dept': 10414461824, 'post': [], 'role': [154555344320]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154569336640, "fields": {"creator": 154559003456, "modifier": null, "update_datetime": "2025-05-10T17:32:26.779", "create_datetime": "2025-05-10T17:32:25.886", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'common', 'password': '******', 'captcha': '18', 'captcha<PERSON>ey': 71}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154572240256, "fields": {"creator": 154559003456, "modifier": null, "update_datetime": "2025-05-10T17:33:11.328", "create_datetime": "2025-05-10T17:33:11.254", "request_modular": "用户表", "request_path": "/system/user/154559003456/", "request_body": "{'id': 154559003456, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-10 17:29:44', 'update_datetime': '2025-05-10 17:32:14', 'dept_name': '产品设计部门', 'last_login': None, 'is_superuser': False, 'is_staff': True, 'is_active': True, 'date_joined': '2025-05-10 17:29:44', 'modifier': '541150219354505', 'status': '0', 'username': 'common', 'nickname': 'common', 'employee_no': None, 'email': None, 'phone': '13160006317', 'avatar': None, 'gender': '0', 'last_token': None, 'is_delete': False, 'remark': None, 'creator': 541150219354505, 'dept': 10414461824, 'post': [], 'role': [154555344320]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 154573892864, "fields": {"creator": 154559003456, "modifier": null, "update_datetime": "2025-05-10T17:33:37.876", "create_datetime": "2025-05-10T17:33:37.076", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'common', 'password': '******', 'captcha': '14', 'captcha<PERSON>ey': 72}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159453364928, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:44:19.211", "create_datetime": "2025-05-11T14:44:18.827", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '36', 'captcha<PERSON>ey': 74}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159492431232, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:54:29.258", "create_datetime": "2025-05-11T14:54:29.238", "request_modular": "系统-菜单表", "request_path": "/system/menu/", "request_body": "{'menu_type': 'M', 'menu_name': '数据集管理', 'sort': 1, 'icon': 'iconfont icon-crew_feature', 'component': '/dataset', 'status': '0', 'is_hide': '0'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159493663680, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:54:49.076", "create_datetime": "2025-05-11T14:54:48.495", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '18', 'captcha<PERSON>ey': 75}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159494105344, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:54:55.905", "create_datetime": "2025-05-11T14:54:55.396", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '3', 'captcha<PERSON>ey': 76}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159498699008, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-11T14:56:07.188", "create_datetime": "2025-05-11T14:56:07.172", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '20', 'captcha<PERSON>ey': 78}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 159498943232, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-11T14:56:11.362", "create_datetime": "2025-05-11T14:56:10.988", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '11', 'captcha<PERSON>ey': 79}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159501630080, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-11T14:56:52.983", "create_datetime": "2025-05-11T14:56:52.970", "request_modular": "系统-菜单表", "request_path": "/system/menu/159492432000/", "request_body": "{'id': 159492432000, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-11 14:54:29', 'update_datetime': '2025-05-11 14:54:29', 'modifier': '541150219354505', 'icon': 'iconfont icon-crew_feature', 'menu_name': '数据集管理', 'sort': 1, 'path': '/dataset', 'component': 'Layout', 'is_iframe': None, 'is_link': None, 'menu_type': 'M', 'is_hide': '0', 'is_keep_alive': None, 'is_affix': None, 'permission': None, 'status': '0', 'remark': None, 'creator': 541150219354505, 'parent': None, 'children': []}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '您没有执行该操作的权限。'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 159502701248, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:57:10.080", "create_datetime": "2025-05-11T14:57:09.707", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '24', 'captcha<PERSON>ey': 80}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159504017408, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:57:30.643", "create_datetime": "2025-05-11T14:57:30.272", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '4', 'captcha<PERSON>ey': 82}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159504373696, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:57:36.217", "create_datetime": "2025-05-11T14:57:35.839", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '0', 'captcha<PERSON>ey': 83}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159504860416, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:57:43.813", "create_datetime": "2025-05-11T14:57:43.444", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '5', 'captcha<PERSON>ey': 84}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159505152128, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:57:48.021", "create_datetime": "2025-05-11T14:57:48.002", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '', 'captcha<PERSON>ey': 85}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 159505425344, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:57:52.294", "create_datetime": "2025-05-11T14:57:52.273", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '28', 'captcha<PERSON>ey': 86}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 159505668992, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-11T14:57:56.450", "create_datetime": "2025-05-11T14:57:56.078", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '70', 'captcha<PERSON>ey': 87}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159505969472, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-11T14:58:01.149", "create_datetime": "2025-05-11T14:58:00.773", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '10', 'captcha<PERSON>ey': 88}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159509257344, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:58:52.716", "create_datetime": "2025-05-11T14:58:52.146", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '48', 'captcha<PERSON>ey': 91}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159510298496, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:59:08.947", "create_datetime": "2025-05-11T14:59:08.414", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '15', 'captcha<PERSON>ey': 92}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159510705856, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:59:15.310", "create_datetime": "2025-05-11T14:59:14.779", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '16', 'captcha<PERSON>ey': 94}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159511306432, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:59:24.750", "create_datetime": "2025-05-11T14:59:24.163", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '28', 'captcha<PERSON>ey': 95}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159512637440, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:59:44.982", "create_datetime": "2025-05-11T14:59:44.960", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '18', 'captcha<PERSON>ey': 96}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 159512881984, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T14:59:49.294", "create_datetime": "2025-05-11T14:59:48.781", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '80', 'captcha<PERSON>ey': 97}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159523046656, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:02:27.987", "create_datetime": "2025-05-11T15:02:27.604", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '9', 'captcha<PERSON>ey': 100}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159523783040, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:02:39.490", "create_datetime": "2025-05-11T15:02:39.110", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '2', 'captcha<PERSON>ey': 101}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159528275392, "fields": {"creator": 234457355328, "modifier": null, "update_datetime": "2025-05-11T15:03:49.703", "create_datetime": "2025-05-11T15:03:49.303", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'test', 'password': '******', 'captcha': '3', 'captcha<PERSON>ey': 103}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159529696960, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:04:11.893", "create_datetime": "2025-05-11T15:04:11.515", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '8', 'captcha<PERSON>ey': 104}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159530202176, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:04:19.429", "create_datetime": "2025-05-11T15:04:19.409", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '1', 'captcha<PERSON>ey': 105}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 159530640704, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:04:26.278", "create_datetime": "2025-05-11T15:04:26.261", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '1', 'captcha<PERSON>ey': 106}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 159530997952, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:04:32.213", "create_datetime": "2025-05-11T15:04:31.843", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '7', 'captcha<PERSON>ey': 108}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159543071616, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:07:40.518", "create_datetime": "2025-05-11T15:07:40.494", "request_modular": "系统-菜单表", "request_path": "/system/menu/159492432000/", "request_body": "{'id': 159492432000, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-11 14:54:29', 'update_datetime': '2025-05-11 14:54:29', 'modifier': '541150219354505', 'icon': 'iconfont icon-crew_feature', 'menu_name': '数据集管理', 'sort': 1, 'path': '/dataset', 'component': 'Layout', 'is_iframe': None, 'is_link': None, 'menu_type': 'M', 'is_hide': '0', 'is_keep_alive': None, 'is_affix': None, 'permission': None, 'status': '0', 'remark': None, 'creator': 541150219354505, 'parent': None, 'children': []}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159545752832, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:08:22.411", "create_datetime": "2025-05-11T15:08:22.388", "request_modular": "系统-菜单表", "request_path": "/system/menu/159492432000/", "request_body": "{'id': 159492432000, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-11 14:54:29', 'update_datetime': '2025-05-11 15:07:40', 'modifier': '541150219354505', 'icon': 'iconfont icon-crew_feature', 'menu_name': '数据集管理', 'sort': 1, 'path': '/dataset', 'component': 'Layout', 'is_iframe': None, 'is_link': None, 'menu_type': 'M', 'is_hide': '0', 'is_keep_alive': '0', 'is_affix': '1', 'permission': None, 'status': '0', 'remark': None, 'creator': 541150219354505, 'parent': None, 'children': []}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159546449792, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:08:33.299", "create_datetime": "2025-05-11T15:08:33.278", "request_modular": "系统-菜单表", "request_path": "/system/menu/159492432000/", "request_body": "{'id': 159492432000, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-11 14:54:29', 'update_datetime': '2025-05-11 15:08:22', 'modifier': '541150219354505', 'icon': 'iconfont icon-crew_feature', 'menu_name': '数据集管理', 'sort': 1, 'path': '/dataset', 'component': 'Layout', 'is_iframe': '1', 'is_link': None, 'menu_type': 'M', 'is_hide': '0', 'is_keep_alive': '0', 'is_affix': '1', 'permission': None, 'status': '0', 'remark': None, 'creator': 541150219354505, 'parent': None, 'children': []}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159547974208, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:08:57.603", "create_datetime": "2025-05-11T15:08:57.097", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '12', 'captcha<PERSON>ey': 110}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159555224512, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:10:50.402", "create_datetime": "2025-05-11T15:10:50.383", "request_modular": "系统-菜单表", "request_path": "/system/menu/", "request_body": "{'parentId': 159492432000, 'parent': 159492432000, 'menu_name': '数据展示', 'menu_type': 'C', 'sort': 1, 'icon': 'iconfont icon-LoggedinPC', 'component': 'datasets', 'path': '/datasets', 'status': '0'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159564492544, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:13:15.214", "create_datetime": "2025-05-11T15:13:15.196", "request_modular": "系统-菜单表", "request_path": "/system/menu/159555225216/", "request_body": "{'id': 159555225216, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-11 15:10:50', 'update_datetime': '2025-05-11 15:10:50', 'modifier': '541150219354505', 'icon': 'iconfont icon-LoggedinPC', 'menu_name': '数据展示', 'sort': 1, 'path': '/dataset', 'component': '/dataset/index', 'is_iframe': None, 'is_link': None, 'menu_type': 'C', 'is_hide': None, 'is_keep_alive': None, 'is_affix': '1', 'permission': None, 'status': '0', 'remark': None, 'creator': 541150219354505, 'parent': 159492432000, 'children': []}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159568677312, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:14:20.600", "create_datetime": "2025-05-11T15:14:20.583", "request_modular": "系统-菜单表", "request_path": "/system/menu/", "request_body": "{'menu_type': 'C', 'menu_name': '数据展示', 'sort': 5, 'icon': 'iconfont icon-shouye_dongtaihui', 'component': '/dataset/index', 'path': '/dataset', 'status': '0', 'is_hide': '0', 'is_keep_alive': '0', 'is_affix': '1', 'is_iframe': '1'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159569746240, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:14:37.657", "create_datetime": "2025-05-11T15:14:37.285", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '45', 'captcha<PERSON>ey': 111}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159570240192, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:14:45.376", "create_datetime": "2025-05-11T15:14:45.003", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '2', 'captcha<PERSON>ey': 112}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159589424768, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:19:45.306", "create_datetime": "2025-05-11T15:19:44.762", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '8', 'captcha<PERSON>ey': 113}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159589914880, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:19:52.791", "create_datetime": "2025-05-11T15:19:52.420", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '8', 'captcha<PERSON>ey': 114}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159694478016, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T15:47:06.243", "create_datetime": "2025-05-11T15:47:06.219", "request_modular": "系统-岗位表", "request_path": "/system/post/43274229824/", "request_body": "{'id': 43274229824, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2024-01-13 15:22:39', 'update_datetime': '2024-01-13 15:22:39', 'modifier': '541150219354505', 'post_name': '测试', 'post_code': 'TEST', 'sort': 3, 'status': '0', 'remark': None, 'creator': 541150219354505}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159944023360, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T16:52:06.016", "create_datetime": "2025-05-11T16:52:05.365", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '3', 'captcha<PERSON>ey': 116}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 159964415296, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-11T16:57:24.507", "create_datetime": "2025-05-11T16:57:23.989", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '6', 'captcha<PERSON>ey': 118}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164353119808, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-12T12:00:17.735", "create_datetime": "2025-05-12T12:00:17.497", "request_modular": "系统-角色表", "request_path": "/system/role/", "request_body": "{'role_name': '开发人员', 'role_key': 'dev', 'sort': 4, 'status': '0', 'menu': [7298689300002, 384888858368, 324683656700002, 542594511251963, 542594449490837, 542594574887944, 542594636888145, 357134961700006, 542594825191424, 542594873530777, 542594930380374, 542594993353654, 538569709516226, 542595489577566, 542595568258514, 542595409881595, 542596157969268, 542596856497045, 542597283951149, 542597333875949, 542597388884246, 542598150314000, 542598287434186, 542598338923462, 542598380740673, 542598541692895, 384901100480, 285082180800, 457773413824, 458000302592, 458003252032, 458004760128, 43286452544, 159568677952], 'api': [{'path': '/system/message-center/:message_id/', 'method': 'DELETE'}, {'path': '/system/message-center/', 'method': 'POST'}, {'path': '/system/message-center/get-self-receive/', 'method': 'GET'}, {'path': '/system/message-center/:message_id/', 'method': 'GET'}, {'path': '/system/message-center/', 'method': 'GET'}, {'path': '/system/operation-log/', 'method': 'GET'}, {'path': '/system/operation-log/:log_id/', 'method': 'DELETE'}, {'path': '/system/operation-log/delete-all-logs/', 'method': 'GET'}, {'path': '/tool/monitor/get-system-info/', 'method': 'GET'}, {'path': '/system/apis/get-all-api-group/', 'method': 'GET'}, {'path': '/system/apis/:api_id/', 'method': 'PUT'}, {'path': '/system/apis/:api_id/', 'method': 'DELETE'}, {'path': '/system/apis/:api_id/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'POST'}, {'path': '/job/crontab/periodic-task/tasklist/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/enabled/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'DELETE'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/', 'method': 'POST'}, {'path': '/job/crontab/task-result/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/', 'method': 'GET'}, {'path': '/system/post/get-all-posts/', 'method': 'GET'}, {'path': '/system/post/:post_id/', 'method': 'PUT'}, {'path': '/system/post/:post_id/', 'method': 'DELETE'}, {'path': '/system/post/:post_id/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'POST'}, {'path': '/system/role/get-all-roles/', 'method': 'GET'}, {'path': '/system/role/role-id-to-menu/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'PUT'}, {'path': '/system/role/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'DELETE'}, {'path': '/system/role/', 'method': 'POST'}, {'path': '/system/role/', 'method': 'GET'}, {'path': '/system/update-user-info/', 'method': 'GET'}, {'path': '/system/user/user-info/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'POST'}, {'path': '/system/user/:user_id/', 'method': 'DELETE'}, {'path': '/system/user/:user_id/', 'method': 'GET'}, {'path': '/system/user/:user_id/', 'method': 'PUT'}, {'path': '/system/menu/menu-tree-simple/', 'method': 'GET'}, {'path': '/system/menu/menu-tree/', 'method': 'GET'}, {'path': '/system/menu/:menu_id/', 'method': 'PUT'}, {'path': '/system/menu/:menu_id/', 'method': 'DELETE'}, {'path': '/system/menu/:menu_id/', 'method': 'GET'}, {'path': '/system/menu/', 'method': 'POST'}, {'path': '/system/menu/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'PUT'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'DELETE'}, {'path': '/system/dict-data/', 'method': 'GET'}, {'path': '/system/dict-data/', 'method': 'POST'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'GET'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'PUT'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'DELETE'}, {'path': '/system/dict-type/', 'method': 'GET'}, {'path': '/system/dict-type/', 'method': 'POST'}, {'path': '/system/dept/dept-tree/', 'method': 'GET'}, {'path': '/system/dept/', 'method': 'POST'}, {'path': '/system/dept/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'DELETE'}, {'path': '/system/dept/:dept_id/', 'method': 'PUT'}]}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164357129024, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-12T12:01:20.407", "create_datetime": "2025-05-12T12:01:20.141", "request_modular": "用户表", "request_path": "/system/user/", "request_body": "{'nickname': 'qian<PERSON>', 'username': 'qian<PERSON>', 'password': '******', 'phone': '13145678909', 'gender': '0', 'status': '0', 'post': [], 'role': [], 'is_superuser': True, 'is_staff': True}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164358166080, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-12T12:01:36.943", "create_datetime": "2025-05-12T12:01:36.345", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '12', 'captcha<PERSON>ey': 119}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164358689856, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-12T12:01:45.025", "create_datetime": "2025-05-12T12:01:44.529", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '90', 'captcha<PERSON>ey': 121}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164360011776, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-12T12:02:05.219", "create_datetime": "2025-05-12T12:02:05.184", "request_modular": "用户表", "request_path": "/system/user/164357145152/", "request_body": "{'id': 164357145152, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-12 12:01:20', 'update_datetime': '2025-05-12 12:01:20', 'last_login': None, 'is_superuser': True, 'is_staff': True, 'is_active': True, 'date_joined': '2025-05-12 12:01:20', 'modifier': '541150219354505', 'status': '0', 'username': 'qian<PERSON>', 'nickname': 'qian<PERSON>', 'employee_no': None, 'email': None, 'phone': '13145678909', 'avatar': None, 'gender': '0', 'last_token': None, 'is_delete': False, 'remark': None, 'creator': 541150219354505, 'dept': None, 'post': [], 'role': [164353133888]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164360621632, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-12T12:02:14.732", "create_datetime": "2025-05-12T12:02:14.714", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '12', 'captcha<PERSON>ey': 122}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 164361050560, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-12T12:02:21.983", "create_datetime": "2025-05-12T12:02:21.415", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '28', 'captcha<PERSON>ey': 123}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164363203136, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-12T12:02:55.077", "create_datetime": "2025-05-12T12:02:55.049", "request_modular": "用户表", "request_path": "/system/user/164357145152/", "request_body": "{'id': 164357145152, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-12 12:01:20', 'update_datetime': '2025-05-12 12:02:05', 'last_login': None, 'is_superuser': False, 'is_staff': True, 'is_active': True, 'date_joined': '2025-05-12 12:01:20', 'modifier': '541150219354505', 'status': '0', 'username': 'qian<PERSON>', 'nickname': 'qian<PERSON>', 'employee_no': None, 'email': None, 'phone': '13145678909', 'avatar': None, 'gender': '0', 'last_token': None, 'is_delete': False, 'remark': None, 'creator': 541150219354505, 'dept': None, 'post': [], 'role': [164353133888]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164363895872, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-12T12:03:06.365", "create_datetime": "2025-05-12T12:03:05.873", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '1', 'captcha<PERSON>ey': 124}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164365285888, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-12T12:03:28.150", "create_datetime": "2025-05-12T12:03:27.592", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '60', 'captcha<PERSON>ey': 125}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164367315904, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-12T12:03:59.369", "create_datetime": "2025-05-12T12:03:59.311", "request_modular": "系统-角色表", "request_path": "/system/role/164353133888/", "request_body": "{'id': 164353133888, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-12 12:00:17', 'update_datetime': '2025-05-12 12:00:17', 'modifier': '541150219354505', 'role_name': '开发人员', 'role_key': 'dev', 'status': '0', 'sort': 4, 'admin': False, 'data_scope': '5', 'remark': None, 'creator': 541150219354505, 'dept': [], 'menu': [7298689300002, 384888858368, 324683656700002, 542594511251963, 542594449490837, 542594574887944, 542594636888145, 357134961700006, 542594825191424, 542594873530777, 542594930380374, 542594993353654, 538569709516226, 542595489577566, 542595568258514, 542595409881595, 542596157969268, 542596856497045, 542597283951149, 542597333875949, 542597388884246, 542598150314000, 542598287434186, 542598338923462, 542598380740673, 542598541692895, 384901100480, 457773413824, 458000302592, 458003252032, 458004760128, 43286452544, 159568677952], 'api': [{'path': '/system/message-center/:message_id/', 'method': 'DELETE'}, {'path': '/system/message-center/', 'method': 'POST'}, {'path': '/system/message-center/get-self-receive/', 'method': 'GET'}, {'path': '/system/message-center/:message_id/', 'method': 'GET'}, {'path': '/system/message-center/', 'method': 'GET'}, {'path': '/system/operation-log/', 'method': 'GET'}, {'path': '/system/operation-log/:log_id/', 'method': 'DELETE'}, {'path': '/system/operation-log/delete-all-logs/', 'method': 'GET'}, {'path': '/tool/monitor/get-system-info/', 'method': 'GET'}, {'path': '/system/apis/get-all-api-group/', 'method': 'GET'}, {'path': '/system/apis/:api_id/', 'method': 'PUT'}, {'path': '/system/apis/:api_id/', 'method': 'DELETE'}, {'path': '/system/apis/:api_id/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'POST'}, {'path': '/job/crontab/periodic-task/tasklist/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/enabled/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'DELETE'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/', 'method': 'POST'}, {'path': '/job/crontab/task-result/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/', 'method': 'GET'}, {'path': '/system/post/get-all-posts/', 'method': 'GET'}, {'path': '/system/post/:post_id/', 'method': 'PUT'}, {'path': '/system/post/:post_id/', 'method': 'DELETE'}, {'path': '/system/post/:post_id/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'POST'}, {'path': '/system/role/get-all-roles/', 'method': 'GET'}, {'path': '/system/role/role-id-to-menu/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'PUT'}, {'path': '/system/role/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'DELETE'}, {'path': '/system/role/', 'method': 'POST'}, {'path': '/system/role/', 'method': 'GET'}, {'path': '/system/update-user-info/', 'method': 'GET'}, {'path': '/system/user/user-info/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'POST'}, {'path': '/system/user/:user_id/', 'method': 'DELETE'}, {'path': '/system/user/:user_id/', 'method': 'GET'}, {'path': '/system/user/:user_id/', 'method': 'PUT'}, {'path': '/system/menu/menu-tree-simple/', 'method': 'GET'}, {'path': '/system/menu/menu-tree/', 'method': 'GET'}, {'path': '/system/menu/:menu_id/', 'method': 'PUT'}, {'path': '/system/menu/:menu_id/', 'method': 'DELETE'}, {'path': '/system/menu/:menu_id/', 'method': 'GET'}, {'path': '/system/menu/', 'method': 'POST'}, {'path': '/system/menu/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'PUT'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'DELETE'}, {'path': '/system/dict-data/', 'method': 'GET'}, {'path': '/system/dict-data/', 'method': 'POST'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'GET'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'PUT'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'DELETE'}, {'path': '/system/dict-type/', 'method': 'GET'}, {'path': '/system/dict-type/', 'method': 'POST'}, {'path': '/system/dept/dept-tree/', 'method': 'GET'}, {'path': '/system/dept/', 'method': 'POST'}, {'path': '/system/dept/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'DELETE'}, {'path': '/system/dept/:dept_id/', 'method': 'PUT'}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164368353472, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-12T12:04:16.084", "create_datetime": "2025-05-12T12:04:15.523", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '0', 'captcha<PERSON>ey': 126}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 164374591680, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-12T12:05:53.369", "create_datetime": "2025-05-12T12:05:52.995", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '7', 'captcha<PERSON>ey': 128}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 167541131008, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-13T01:50:30.596", "create_datetime": "2025-05-13T01:50:30.172", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '2', 'captcha<PERSON>ey': 129}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169712463168, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-13T11:15:57.475", "create_datetime": "2025-05-13T11:15:57.237", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '5', 'captcha<PERSON>ey': 128}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169718834816, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-13T11:17:37.027", "create_datetime": "2025-05-13T11:17:36.794", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '30', 'captcha<PERSON>ey': 129}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169721990464, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-13T11:18:26.338", "create_datetime": "2025-05-13T11:18:26.101", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '7', 'captcha<PERSON>ey': 130}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169724602624, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-13T11:19:06.989", "create_datetime": "2025-05-13T11:19:06.916", "request_modular": "系统-菜单表", "request_path": "/system/menu/384901100480/", "request_body": "{'id': 384901100480, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2023-11-20 16:21:19', 'update_datetime': '2023-11-20 16:21:19', 'modifier': '541150219354505', 'icon': 'elementCpu', 'menu_name': '服务监控', 'sort': 1, 'path': '/tool/monitor/', 'component': '/tool/monitor/index', 'is_iframe': '1', 'is_link': None, 'menu_type': 'C', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'tool:monitor:list', 'status': '0', 'remark': None, 'creator': 541150219354505, 'parent': 457773413824, 'children': []}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169725370944, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2025-05-13T11:19:19.214", "create_datetime": "2025-05-13T11:19:18.921", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '8', 'captcha<PERSON>ey': 131}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169727401984, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-13T11:19:50.720", "create_datetime": "2025-05-13T11:19:50.656", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '64', 'captcha<PERSON>ey': 132}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "400", "request_os": "Linux", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 169727616384, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-13T11:19:54.253", "create_datetime": "2025-05-13T11:19:54.006", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '80', 'captcha<PERSON>ey': 133}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169730046912, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-13T11:20:32.024", "create_datetime": "2025-05-13T11:20:31.983", "request_modular": "系统-角色表", "request_path": "/system/role/164353133888/", "request_body": "{'id': 164353133888, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-12 12:00:17', 'update_datetime': '2025-05-12 12:03:59', 'modifier': '541150219354505', 'role_name': '开发人员', 'role_key': 'dev', 'status': '0', 'sort': 4, 'admin': False, 'data_scope': '5', 'remark': None, 'creator': 541150219354505, 'dept': [], 'menu': [7298689300002, 324683656700002, 542594449490837, 542594574887944, 542594636888145, 542594511251963, 357134961700006, 542594825191424, 542594873530777, 542594930380374, 542594993353654, 538569709516226, 542595409881595, 542595489577566, 542595568258514, 542596157969268, 542597333875949, 542596856497045, 542597283951149, 542597388884246, 542598150314000, 542598287434186, 542598338923462, 542598380740673, 542598541692895, 457773413824, 384901100480, 458000302592, 458003252032, 458004760128, 43286452544, 159568677952], 'api': [{'path': '/system/message-center/:message_id/', 'method': 'DELETE'}, {'path': '/system/message-center/', 'method': 'POST'}, {'path': '/system/message-center/get-self-receive/', 'method': 'GET'}, {'path': '/system/message-center/:message_id/', 'method': 'GET'}, {'path': '/system/message-center/', 'method': 'GET'}, {'path': '/system/operation-log/', 'method': 'GET'}, {'path': '/system/operation-log/:log_id/', 'method': 'DELETE'}, {'path': '/system/operation-log/delete-all-logs/', 'method': 'GET'}, {'path': '/tool/monitor/get-system-info/', 'method': 'GET'}, {'path': '/system/apis/get-all-api-group/', 'method': 'GET'}, {'path': '/system/apis/:api_id/', 'method': 'PUT'}, {'path': '/system/apis/:api_id/', 'method': 'DELETE'}, {'path': '/system/apis/:api_id/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'POST'}, {'path': '/job/crontab/periodic-task/tasklist/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/enabled/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'DELETE'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/', 'method': 'POST'}, {'path': '/job/crontab/task-result/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/', 'method': 'GET'}, {'path': '/system/post/get-all-posts/', 'method': 'GET'}, {'path': '/system/post/:post_id/', 'method': 'PUT'}, {'path': '/system/post/:post_id/', 'method': 'DELETE'}, {'path': '/system/post/:post_id/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'POST'}, {'path': '/system/role/get-all-roles/', 'method': 'GET'}, {'path': '/system/role/role-id-to-menu/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'PUT'}, {'path': '/system/role/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'DELETE'}, {'path': '/system/role/', 'method': 'POST'}, {'path': '/system/role/', 'method': 'GET'}, {'path': '/system/update-user-info/', 'method': 'GET'}, {'path': '/system/user/user-info/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'POST'}, {'path': '/system/user/:user_id/', 'method': 'DELETE'}, {'path': '/system/user/:user_id/', 'method': 'GET'}, {'path': '/system/user/:user_id/', 'method': 'PUT'}, {'path': '/system/menu/menu-tree-simple/', 'method': 'GET'}, {'path': '/system/menu/menu-tree/', 'method': 'GET'}, {'path': '/system/menu/:menu_id/', 'method': 'PUT'}, {'path': '/system/menu/:menu_id/', 'method': 'DELETE'}, {'path': '/system/menu/:menu_id/', 'method': 'GET'}, {'path': '/system/menu/', 'method': 'POST'}, {'path': '/system/menu/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'PUT'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'DELETE'}, {'path': '/system/dict-data/', 'method': 'GET'}, {'path': '/system/dict-data/', 'method': 'POST'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'GET'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'PUT'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'DELETE'}, {'path': '/system/dict-type/', 'method': 'GET'}, {'path': '/system/dict-type/', 'method': 'POST'}, {'path': '/system/dept/dept-tree/', 'method': 'GET'}, {'path': '/system/dept/', 'method': 'POST'}, {'path': '/system/dept/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'DELETE'}, {'path': '/system/dept/:dept_id/', 'method': 'PUT'}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169730637760, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-13T11:20:41.449", "create_datetime": "2025-05-13T11:20:41.215", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '7', 'captcha<PERSON>ey': 134}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169737478080, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-13T11:22:28.337", "create_datetime": "2025-05-13T11:22:28.095", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '16', 'captcha<PERSON>ey': 135}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169738591168, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-13T11:22:45.505", "create_datetime": "2025-05-13T11:22:45.487", "request_modular": "系统-菜单表", "request_path": "/system/menu/384901100480/", "request_body": "{'id': 384901100480, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2023-11-20 16:21:19', 'update_datetime': '2023-11-20 16:21:19', 'modifier': '541150219354505', 'icon': 'elementCpu', 'menu_name': '服务监控', 'sort': 1, 'path': '/tool/monitor/', 'component': '/tool/monitor/index', 'is_iframe': '1', 'is_link': None, 'menu_type': 'C', 'is_hide': '0', 'is_keep_alive': '1', 'is_affix': '1', 'permission': 'tool:monitor:list', 'status': '0', 'remark': None, 'creator': 541150219354505, 'parent': 457773413824, 'children': []}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169739455296, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-13T11:22:59.033", "create_datetime": "2025-05-13T11:22:58.989", "request_modular": "系统-角色表", "request_path": "/system/role/164353133888/", "request_body": "{'id': 164353133888, 'modifier_name': '泡泡', 'creator_name': '泡泡', 'create_datetime': '2025-05-12 12:00:17', 'update_datetime': '2025-05-12 12:03:59', 'modifier': '541150219354505', 'role_name': '开发人员', 'role_key': 'dev', 'status': '0', 'sort': 4, 'admin': False, 'data_scope': '5', 'remark': None, 'creator': 541150219354505, 'dept': [], 'menu': [7298689300002, 324683656700002, 542594449490837, 542594574887944, 542594636888145, 542594511251963, 357134961700006, 542594825191424, 542594873530777, 542594930380374, 542594993353654, 538569709516226, 542595409881595, 542595489577566, 542595568258514, 542596157969268, 542597333875949, 542596856497045, 542597283951149, 542597388884246, 542598150314000, 542598287434186, 542598338923462, 542598380740673, 542598541692895, 457773413824, 384901100480, 458000302592, 458003252032, 458004760128, 43286452544, 159568677952], 'api': [{'path': '/system/message-center/:message_id/', 'method': 'DELETE'}, {'path': '/system/message-center/', 'method': 'POST'}, {'path': '/system/message-center/get-self-receive/', 'method': 'GET'}, {'path': '/system/message-center/:message_id/', 'method': 'GET'}, {'path': '/system/message-center/', 'method': 'GET'}, {'path': '/system/operation-log/', 'method': 'GET'}, {'path': '/system/operation-log/:log_id/', 'method': 'DELETE'}, {'path': '/system/operation-log/delete-all-logs/', 'method': 'GET'}, {'path': '/tool/monitor/get-system-info/', 'method': 'GET'}, {'path': '/system/apis/get-all-api-group/', 'method': 'GET'}, {'path': '/system/apis/:api_id/', 'method': 'PUT'}, {'path': '/system/apis/:api_id/', 'method': 'DELETE'}, {'path': '/system/apis/:api_id/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'GET'}, {'path': '/system/apis/', 'method': 'POST'}, {'path': '/job/crontab/periodic-task/tasklist/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/enabled/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'DELETE'}, {'path': '/job/crontab/periodic-task/:job_id/', 'method': 'PUT'}, {'path': '/job/crontab/periodic-task/', 'method': 'POST'}, {'path': '/job/crontab/task-result/', 'method': 'GET'}, {'path': '/job/crontab/periodic-task/', 'method': 'GET'}, {'path': '/system/post/get-all-posts/', 'method': 'GET'}, {'path': '/system/post/:post_id/', 'method': 'PUT'}, {'path': '/system/post/:post_id/', 'method': 'DELETE'}, {'path': '/system/post/:post_id/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'GET'}, {'path': '/system/post/', 'method': 'POST'}, {'path': '/system/role/get-all-roles/', 'method': 'GET'}, {'path': '/system/role/role-id-to-menu/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'PUT'}, {'path': '/system/role/:role_id/', 'method': 'GET'}, {'path': '/system/role/:role_id/', 'method': 'DELETE'}, {'path': '/system/role/', 'method': 'POST'}, {'path': '/system/role/', 'method': 'GET'}, {'path': '/system/update-user-info/', 'method': 'GET'}, {'path': '/system/user/user-info/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'GET'}, {'path': '/system/user/', 'method': 'POST'}, {'path': '/system/user/:user_id/', 'method': 'DELETE'}, {'path': '/system/user/:user_id/', 'method': 'GET'}, {'path': '/system/user/:user_id/', 'method': 'PUT'}, {'path': '/system/menu/menu-tree-simple/', 'method': 'GET'}, {'path': '/system/menu/menu-tree/', 'method': 'GET'}, {'path': '/system/menu/:menu_id/', 'method': 'PUT'}, {'path': '/system/menu/:menu_id/', 'method': 'DELETE'}, {'path': '/system/menu/:menu_id/', 'method': 'GET'}, {'path': '/system/menu/', 'method': 'POST'}, {'path': '/system/menu/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'GET'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'PUT'}, {'path': '/system/dict-data/:dict-data_id/', 'method': 'DELETE'}, {'path': '/system/dict-data/', 'method': 'GET'}, {'path': '/system/dict-data/', 'method': 'POST'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'GET'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'PUT'}, {'path': '/system/dict-type/:dict-type_id/', 'method': 'DELETE'}, {'path': '/system/dict-type/', 'method': 'GET'}, {'path': '/system/dict-type/', 'method': 'POST'}, {'path': '/system/dept/dept-tree/', 'method': 'GET'}, {'path': '/system/dept/', 'method': 'POST'}, {'path': '/system/dept/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'GET'}, {'path': '/system/dept/:dept_id/', 'method': 'DELETE'}, {'path': '/system/dept/:dept_id/', 'method': 'PUT'}]}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '更新成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 169740014016, "fields": {"creator": 164357145152, "modifier": null, "update_datetime": "2025-05-13T11:23:07.960", "create_datetime": "2025-05-13T11:23:07.719", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'q<PERSON><PERSON>', 'password': '******', 'captcha': '9', 'captcha<PERSON>ey': 136}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 136.0.0", "response_code": "200", "request_os": "Linux", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_message.messagecenter", "pk": 43330263808, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:15.372", "create_datetime": "2024-01-13T15:37:15.372", "title": "发给用户的", "content": "<p><strong>测试水水水水水水水水水水水水撒啊啊啊啊啊啊</strong></p>", "target_type": "0", "remark": null, "target_dept": [], "target_role": []}}, {"model": "app_message.messagecenter", "pk": 43331630656, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:36.729", "create_datetime": "2024-01-13T15:37:36.729", "title": "发给角色的", "content": "<p>撒啊水水水水水水水水说法伽师</p>", "target_type": "1", "remark": null, "target_dept": [], "target_role": [540775921959829, 444421914176]}}, {"model": "app_message.messagecenter", "pk": 43332798016, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:54.969", "create_datetime": "2024-01-13T15:37:54.970", "title": "发给部门的", "content": "<p>啊啊啊啊啊啊啊啊啊啊啊啊啊</p>", "target_type": "2", "remark": null, "target_dept": [10414461824, 376960140500837, 94609026700594, 114667862200894, 57407999600609, 42188673800208], "target_role": []}}, {"model": "app_message.messagecenter", "pk": 43333811200, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:38:10.800", "create_datetime": "2024-01-13T15:38:10.800", "title": "公告", "content": "<p>这是一份公告</p>", "target_type": "3", "remark": null, "target_dept": [], "target_role": []}}, {"model": "app_message.messagecentertargetuser", "pk": 43330264640, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:15.385", "create_datetime": "2024-01-13T15:37:15.385", "users": 252375424640, "messagecenter": 43330263808, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43330264832, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:15.388", "create_datetime": "2024-01-13T15:37:15.388", "users": 234457355328, "messagecenter": 43330263808, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43330264960, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:15.390", "create_datetime": "2024-01-13T15:37:15.390", "users": 541150219354505, "messagecenter": 43330263808, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43331632000, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:36.750", "create_datetime": "2024-01-13T15:37:36.750", "users": 252375424640, "messagecenter": 43331630656, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43331632320, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:36.755", "create_datetime": "2024-01-13T15:37:36.755", "users": 234457355328, "messagecenter": 43331630656, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43331632448, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:36.757", "create_datetime": "2024-01-13T15:37:36.757", "users": 541150219354505, "messagecenter": 43331630656, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43332799360, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:54.990", "create_datetime": "2024-01-13T15:37:54.990", "users": 252375424640, "messagecenter": 43332798016, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43332799552, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:54.993", "create_datetime": "2024-01-13T15:37:54.993", "users": 234457355328, "messagecenter": 43332798016, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43332799680, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:37:54.995", "create_datetime": "2024-01-13T15:37:54.995", "users": 541150219354505, "messagecenter": 43332798016, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43333811904, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:38:10.811", "create_datetime": "2024-01-13T15:38:10.811", "users": 252375424640, "messagecenter": 43333811200, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43333812160, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:38:10.815", "create_datetime": "2024-01-13T15:38:10.815", "users": 234457355328, "messagecenter": 43333811200, "is_read": false}}, {"model": "app_message.messagecentertargetuser", "pk": 43333812224, "fields": {"creator": 541150219354505, "modifier": "541150219354505", "update_datetime": "2024-01-13T15:38:13.084", "create_datetime": "2024-01-13T15:38:10.816", "users": 541150219354505, "messagecenter": 43333811200, "is_read": true}}, {"model": "app_dataset.datasetcategory", "pk": 1, "fields": {"name": "表格数据", "code": "tabular", "category_type": "data_type", "parent": null, "icon": "el-icon-document", "sort": 0}}, {"model": "app_dataset.datasetcategory", "pk": 2, "fields": {"name": "图像数据", "code": "image", "category_type": "data_type", "parent": null, "icon": "el-icon-picture", "sort": 1}}, {"model": "app_dataset.datasetcategory", "pk": 3, "fields": {"name": "文本数据", "code": "text", "category_type": "data_type", "parent": null, "icon": "el-icon-notebook-1", "sort": 2}}, {"model": "app_dataset.datasetcategory", "pk": 4, "fields": {"name": "音频数据", "code": "audio", "category_type": "data_type", "parent": null, "icon": "el-icon-microphone", "sort": 3}}, {"model": "app_dataset.datasetcategory", "pk": 5, "fields": {"name": "视频数据", "code": "video", "category_type": "data_type", "parent": null, "icon": "el-icon-video-camera", "sort": 4}}, {"model": "app_dataset.datasetcategory", "pk": 6, "fields": {"name": "时序数据", "code": "time_series", "category_type": "data_type", "parent": null, "icon": "el-icon-data-line", "sort": 5}}, {"model": "app_dataset.datasetcategory", "pk": 7, "fields": {"name": "地理数据", "code": "geo", "category_type": "data_type", "parent": null, "icon": "el-icon-location", "sort": 6}}, {"model": "app_dataset.datasetcategory", "pk": 8, "fields": {"name": "分类", "code": "classification", "category_type": "task_type", "parent": null, "icon": "el-icon-collection-tag", "sort": 0}}, {"model": "app_dataset.datasetcategory", "pk": 9, "fields": {"name": "回归", "code": "regression", "category_type": "task_type", "parent": null, "icon": "el-icon-data-analysis", "sort": 1}}, {"model": "app_dataset.datasetcategory", "pk": 10, "fields": {"name": "目标检测", "code": "object_detection", "category_type": "task_type", "parent": null, "icon": "el-icon-aim", "sort": 2}}, {"model": "app_dataset.datasetcategory", "pk": 11, "fields": {"name": "图像分割", "code": "segmentation", "category_type": "task_type", "parent": null, "icon": "el-icon-crop", "sort": 3}}, {"model": "app_dataset.datasetcategory", "pk": 12, "fields": {"name": "文本分类", "code": "text_classification", "category_type": "task_type", "parent": null, "icon": "el-icon-reading", "sort": 4}}, {"model": "app_dataset.datasetcategory", "pk": 13, "fields": {"name": "命名实体识别", "code": "ner", "category_type": "task_type", "parent": null, "icon": "el-icon-price-tag", "sort": 5}}, {"model": "app_dataset.datasetcategory", "pk": 14, "fields": {"name": "机器翻译", "code": "translation", "category_type": "task_type", "parent": null, "icon": "el-icon-chat-dot-round", "sort": 6}}, {"model": "app_dataset.datasetcategory", "pk": 15, "fields": {"name": "语音识别", "code": "speech_recognition", "category_type": "task_type", "parent": null, "icon": "el-icon-headset", "sort": 7}}, {"model": "app_dataset.datasetcategory", "pk": 16, "fields": {"name": "语音合成", "code": "speech_synthesis", "category_type": "task_type", "parent": null, "icon": "el-icon-message", "sort": 8}}, {"model": "app_dataset.datasetcategory", "pk": 17, "fields": {"name": "推荐系统", "code": "recommendation", "category_type": "task_type", "parent": null, "icon": "el-icon-star-off", "sort": 9}}, {"model": "app_dataset.datasetcategory", "pk": 18, "fields": {"name": "聚类", "code": "clustering", "category_type": "task_type", "parent": null, "icon": "el-icon-connection", "sort": 10}}, {"model": "app_dataset.datasetcategory", "pk": 19, "fields": {"name": "异常检测", "code": "anomaly_detection", "category_type": "task_type", "parent": null, "icon": "el-icon-warning", "sort": 11}}, {"model": "app_dataset.datasetcategory", "pk": 20, "fields": {"name": "多模态", "code": "multimodal", "category_type": "data_type", "parent": null, "icon": "el-icon-connection", "sort": 5}}, {"model": "app_dataset.datasetcategory", "pk": 21, "fields": {"name": "其他", "code": "other", "category_type": "data_type", "parent": null, "icon": "el-icon-more", "sort": 6}}, {"model": "app_dataset.datasetcategory", "pk": 22, "fields": {"name": "文本生成", "code": "text_generation", "category_type": "task_type", "parent": 3, "icon": "el-icon-edit", "sort": 2}}, {"model": "app_dataset.datasetcategory", "pk": 23, "fields": {"name": "问答系统", "code": "question_answering", "category_type": "task_type", "parent": 3, "icon": "el-icon-question", "sort": 4}}, {"model": "app_dataset.datasetcategory", "pk": 24, "fields": {"name": "情感分析", "code": "sentiment_analysis", "category_type": "task_type", "parent": 3, "icon": "el-icon-chat-line-round", "sort": 5}}, {"model": "app_dataset.datasetcategory", "pk": 25, "fields": {"name": "图像分类", "code": "image_classification", "category_type": "task_type", "parent": 2, "icon": "el-icon-picture", "sort": 0}}, {"model": "app_dataset.datasetcategory", "pk": 26, "fields": {"name": "图像分割", "code": "image_segmentation", "category_type": "task_type", "parent": 2, "icon": "el-icon-crop", "sort": 2}}, {"model": "app_dataset.datasetcategory", "pk": 27, "fields": {"name": "人脸识别", "code": "face_recognition", "category_type": "task_type", "parent": 2, "icon": "el-icon-user", "sort": 3}}, {"model": "app_dataset.datasetcategory", "pk": 28, "fields": {"name": "图像生成", "code": "image_generation", "category_type": "task_type", "parent": 2, "icon": "el-icon-picture-outline", "sort": 4}}, {"model": "app_dataset.datasetcategory", "pk": 29, "fields": {"name": "超分辨率", "code": "super_resolution", "category_type": "task_type", "parent": 2, "icon": "el-icon-zoom-in", "sort": 5}}, {"model": "app_dataset.datasetcategory", "pk": 30, "fields": {"name": "声音分类", "code": "audio_classification", "category_type": "task_type", "parent": 4, "icon": "el-icon-headset", "sort": 2}}, {"model": "app_dataset.datasetcategory", "pk": 31, "fields": {"name": "音乐生成", "code": "music_generation", "category_type": "task_type", "parent": 4, "icon": "el-icon-service", "sort": 3}}, {"model": "app_dataset.datasetcategory", "pk": 32, "fields": {"name": "视频分类", "code": "video_classification", "category_type": "task_type", "parent": 5, "icon": "el-icon-video-camera", "sort": 0}}, {"model": "app_dataset.datasetcategory", "pk": 33, "fields": {"name": "动作识别", "code": "action_recognition", "category_type": "task_type", "parent": 5, "icon": "el-icon-video-play", "sort": 1}}, {"model": "app_dataset.datasetcategory", "pk": 34, "fields": {"name": "视频生成", "code": "video_generation", "category_type": "task_type", "parent": 5, "icon": "el-icon-film", "sort": 2}}, {"model": "app_dataset.datasetcategory", "pk": 35, "fields": {"name": "视频追踪", "code": "video_tracking", "category_type": "task_type", "parent": 5, "icon": "el-icon-view", "sort": 3}}, {"model": "app_dataset.datasetcategory", "pk": 36, "fields": {"name": "图文匹配", "code": "image_text_matching", "category_type": "task_type", "parent": 20, "icon": "el-icon-document-copy", "sort": 0}}, {"model": "app_dataset.datasetcategory", "pk": 37, "fields": {"name": "文本生成图像", "code": "text_to_image", "category_type": "task_type", "parent": 20, "icon": "el-icon-picture-outline-round", "sort": 1}}, {"model": "app_dataset.datasetcategory", "pk": 38, "fields": {"name": "视觉问答", "code": "visual_qa", "category_type": "task_type", "parent": 20, "icon": "el-icon-question", "sort": 2}}, {"model": "app_dataset.datasetcategory", "pk": 39, "fields": {"name": "多模态检索", "code": "multimodal_retrieval", "category_type": "task_type", "parent": 20, "icon": "el-icon-search", "sort": 3}}, {"model": "app_dataset.datasetcategory", "pk": 40, "fields": {"name": "图数据", "code": "graph", "category_type": "task_type", "parent": 21, "icon": "el-icon-share", "sort": 2}}, {"model": "app_dataset.datasetcategory", "pk": 41, "fields": {"name": "强化学习", "code": "reinforcement_learning", "category_type": "task_type", "parent": 21, "icon": "el-icon-data-analysis", "sort": 3}}, {"model": "app_dataset.dataset", "pk": 1, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": null, "create_datetime": null, "name": "数据集1", "description": "这是一个测试数据集", "owner": 541150219354505, "size": 1048576, "file_count": 10, "status": 1, "tags": "测试, 数据集", "visibility": "private", "downloads": 100, "likes": 50, "license": "MIT", "data_category": null, "task_category": null, "dataset_card": null, "sample_data": null, "current_version": "1.0.0", "minio_bucket": null}}, {"model": "app_dataset.dataset", "pk": 2, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": null, "create_datetime": null, "name": "数据集2", "description": "这是另一个测试数据集", "owner": 541150219354505, "size": 2048576, "file_count": 20, "status": 1, "tags": "机器学习, 数据集", "visibility": "public", "downloads": 500, "likes": 200, "license": "Apache 2.0", "data_category": null, "task_category": null, "dataset_card": null, "sample_data": null, "current_version": "1.0.0", "minio_bucket": null}}, {"model": "app_dataset.dataset", "pk": 160827562752, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-11T20:42:10.668", "create_datetime": "2025-05-11T20:42:10.668", "name": "CIFAR-10图像分类数据集", "description": "CIFAR-10是一个包含60000张32x32彩色图像的数据集，共分为10个类别。每个类别包含6000张图像。数据集分为50000张训练图像和10000张测试图像。", "owner": 541150219354505, "size": 0, "file_count": 0, "status": 1, "tags": "图像分类,深度学习,计算机视觉", "visibility": "public", "downloads": 0, "likes": 0, "license": "MIT", "data_category": 2, "task_category": 8, "dataset_card": "\n# CIFAR-10数据集\n\n## 数据集描述\nCIFAR-10是一个广泛使用的计算机视觉数据集，包含60000张32x32彩色图像。\n\n## 类别信息\n数据集包含以下10个类别：\n- 飞机\n- 汽车\n- 鸟类\n- 猫\n- 鹿\n- 狗\n- 青蛙\n- 马\n- 船\n- 卡车\n\n## 数据格式\n- 图像大小：32x32\n- 颜色通道：RGB（3通道）\n- 文件格式：PNG\n\n## 数据分布\n- 训练集：50000张图像\n- 测试集：10000张图像\n- 每个类别：6000张图像\n\n## 使用许可\n该数据集采用MIT许可证。\n                ", "sample_data": "{\"image_shape\": [32, 32, 3], \"classes\": [\"airplane\", \"automobile\", \"bird\", \"cat\", \"deer\", \"dog\", \"frog\", \"horse\", \"ship\", \"truck\"]}", "current_version": "1.0.0", "minio_bucket": "dataset-cifar-10-9f358175"}}, {"model": "app_dataset.dataset", "pk": 160827564416, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-11T20:42:10.694", "create_datetime": "2025-05-11T20:42:10.694", "name": "ImageNet-Mini示例数据集", "description": "ImageNet-Mini是ImageNet数据集的一个小型子集，包含1000个类别的图像数据，每个类别包含数百张高质量图像。", "owner": 541150219354505, "size": 0, "file_count": 0, "status": 1, "tags": "图像分类,迁移学习,预训练模型", "visibility": "public", "downloads": 0, "likes": 0, "license": "Apache-2.0", "data_category": 2, "task_category": 8, "dataset_card": "\n# ImageNet-Mini数据集\n\n## 数据集描述\nImageNet-Mini是ImageNet数据集的精简版本，保留了原始数据集的核心特征。\n\n## 主要特点\n- 1000个图像类别\n- 每个类别包含500-1000张图像\n- 高质量标注\n- 适合迁移学习和模型预训练\n\n## 数据格式\n- 图像格式：JPEG\n- 分辨率：可变\n- 标注格式：JSON\n\n## 应用场景\n- 计算机视觉研究\n- 深度学习模型训练\n- 迁移学习实验\n                ", "sample_data": "{\"total_classes\": 1000, \"avg_images_per_class\": 750, \"image_format\": \"JPEG\"}", "current_version": "1.0.0", "minio_bucket": "dataset-imagenet-mini-2b92e19f"}}, {"model": "app_dataset.datasetversion", "pk": 160827563200, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-11T20:42:10.675", "create_datetime": "2025-05-11T20:42:10.675", "dataset": 160827562752, "version": "1.0.0", "description": "初始版本 1.0.0", "size": 0, "file_count": 0}}, {"model": "app_dataset.datasetversion", "pk": 160827564736, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-11T20:42:10.699", "create_datetime": "2025-05-11T20:42:10.699", "dataset": 160827564416, "version": "1.0.0", "description": "初始版本 1.0.0", "size": 0, "file_count": 0}}, {"model": "app_dataset.datasetfile", "pk": 1, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": null, "create_datetime": null, "dataset": 1, "version": null, "filename": "file1.csv", "file_path": "/datasets/data1/file1.csv", "file_type": "csv", "file_size": 50000, "rows": 1000, "columns": 10, "preview": "第一行数据, 第二行数据, 第三行数据...", "minio_object": null}}, {"model": "app_dataset.datasetfile", "pk": 2, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": null, "create_datetime": null, "dataset": 2, "version": null, "filename": "file2.json", "file_path": "/datasets/data2/file2.json", "file_type": "json", "file_size": 100000, "rows": 2000, "columns": 15, "preview": "{\"key1\": \"value1\", \"key2\": \"value2\"}", "minio_object": null}}, {"model": "app_dataset.datasetcomment", "pk": 160827563968, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-11T20:42:10.687", "create_datetime": "2025-05-11T20:42:10.687", "dataset": 160827562752, "user": 541150219354505, "parent": null, "content": "这是一个非常有用的数据集，非常适合深度学习入门和研究。"}}, {"model": "app_dataset.datasetcomment", "pk": 160827565248, "fields": {"creator": null, "modifier": null, "update_datetime": "2025-05-11T20:42:10.707", "create_datetime": "2025-05-11T20:42:10.707", "dataset": 160827564416, "user": 541150219354505, "parent": null, "content": "这是一个非常有用的数据集，非常适合深度学习入门和研究。"}}]