aioredis==1.3.1
amqp==5.2.0
asgiref==3.7.2
async-timeout==4.0.3
attrs==23.1.0
autobahn==23.6.2
Automat==22.10.0
bcrypt==4.1.2
billiard==4.2.0
casbin==1.23.1
casbin-django-orm-adapter==1.1.2
celery==5.3.5
certifi==2023.7.22
cffi==1.15.1
channels==3.0.5
channels-redis==3.4.1
charset-normalizer==3.2.0
click==8.1.7
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
constantly==15.1.0
cron-descriptor==1.4.0
cryptography==41.0.3
daphne==3.0.2
distro==1.8.0
Django==4.2.1
django-celery-beat==2.5.0
django-celery-results==2.5.1
django-comment-migrate==0.1.7
django-cors-headers==4.1.0
django-filter==23.2
django-ranged-response==0.2.0
django-redis==5.4.0
django-restql==0.15.3
django-simple-captcha==0.5.18
django-timezone-field==6.0.1
djangorestframework==3.14.0
djangorestframework-simplejwt==5.2.2
dnspython==2.4.2
docopt==0.6.2
filelock==3.12.2
fsspec==2023.6.0
future==0.18.3
greenlet==3.0.1
hiredis==2.2.3
huggingface-hub==0.23.2
hyperlink==21.0.0
idna==3.4
incremental==22.10.0
jsonpath==0.82
jsonschema==3.2.0
kombu==5.3.3
msgpack==1.0.7
mysqlclient==2.2.1
numpy==1.25.2
packaging==23.1
paramiko==3.4.0
Pillow==10.0.0
prompt-toolkit==3.0.40
psutil==5.9.6
pyasn1==0.5.0
pyasn1-modules==0.3.0
pycparser==2.21
PyJWT==2.7.0
PyMySQL==1.1.0
pyOpenSSL==23.2.0
pyPEG2==2.15.2
pypinyin==0.49.0
python-crontab==3.0.0
python-dateutil==2.8.2
python-decouple==3.3
pytz==2023.3
redis==5.0.1
regex==2023.8.8
requests==2.31.0
safetensors==0.4.1
serializers==0.2.4
service-identity==23.1.0
simpleeval==0.9.13
six==1.16.0
sqlparse==0.4.4
tokenizers==0.19
tqdm==4.66.1
transformers==4.44.2
txaio==23.1.1
typing_extensions==4.7.0
tzdata==2023.3
ua-parser==0.18.0
urllib3==2.0.4
user-agents==2.2.0
vine==5.1.0
wcwidth==0.2.10
websocket-client==0.59.0
websockets==10.4
zope.interface==6.0
eventlet==0.33.3
django-import-export
pandas
minio
coreapi
faiss-cpu==1.7.4
drf-yasg
torch==2.1.2
torchvision==0.16.2
torchaudio==2.1.2
openai
opencv-python
docker
tabulate
FlagEmbedding==1.3.4

# =======================AI推理服务基础依赖包========================
# 图像处理
imageio==2.31.1
# 数值计算
scipy==1.11.1
# Web框架
Flask==2.3.2
Flask-CORS==4.0.0
Werkzeug==2.3.6
# # 序列化
# pydantic==2.1.1
# 日志
loguru==0.7.0
# 配置管理
python-dotenv==1.0.0
# 文件处理
pathlib2==2.3.7
# 异步支持
asyncio-mqtt==0.13.0
# 数据验证
marshmallow==3.20.1
# 缓存
cachetools==5.3.1
# 测试工具（开发用）
pytest==7.4.0
pytest-asyncio==0.21.1


# ==========================YOLO特定依赖包（基础镜像已包含torch、torchvision、pillow、numpy、flask等）===================
ultralytics==8.0.196
# 模型优化和导出
onnx==1.14.1
onnxruntime==1.15.1
# 可视化（如果需要）
matplotlib==3.7.2
seaborn==0.12.2
# Web服务器
gunicorn==21.2.0