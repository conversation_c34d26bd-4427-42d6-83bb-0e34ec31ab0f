#!/usr/bin/env python3
"""
YOLO模型推理服务
"""
import os
import io
import json
import time
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
from PIL import Image
import numpy as np
from ultralytics import YOLO
from utils import setup_logging, validate_image, format_predictions

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 全局变量
model = None
model_info = {}

def load_model():
    """加载YOLO模型"""
    global model, model_info
    
    try:
        # 获取环境变量
        model_name = os.getenv('MODEL_NAME', 'yolo11n')
        model_version = os.getenv('MODEL_VERSION', 'v1.0')
        weights_path = os.getenv('WEIGHTS_PATH', '/app/weights')
        
        # 查找权重文件（支持多种命名方式）
        weight_files = []

        logger.info(f"开始查找权重文件，权重路径: {weights_path}")

        # 首先检查权重目录是否存在
        if not os.path.exists(weights_path):
            logger.warning(f"权重目录不存在: {weights_path}")
        else:
            logger.info(f"权重目录存在，列出目录内容:")
            try:
                files_in_dir = os.listdir(weights_path)
                for f in files_in_dir:
                    full_path = os.path.join(weights_path, f)
                    is_file = os.path.isfile(full_path)
                    file_size = os.path.getsize(full_path) if is_file else 0
                    logger.info(f"  - {f} ({'文件' if is_file else '目录'}, {file_size} bytes)")
            except Exception as e:
                logger.error(f"无法列出目录内容: {str(e)}")

        # 方法1: 查找特定名称的权重文件
        for ext in ['.pt', '.onnx', '.engine']:
            weight_file = os.path.join(weights_path, f"{model_name}{ext}")
            if os.path.exists(weight_file):
                weight_files.append(weight_file)
                logger.info(f"找到特定名称权重文件: {weight_file}")

        # 方法2: 如果没找到，查找目录中所有权重文件
        if not weight_files and os.path.exists(weights_path):
            logger.info("查找目录中所有权重文件...")
            for filename in os.listdir(weights_path):
                if filename.endswith(('.pt', '.onnx', '.engine')):
                    weight_file = os.path.join(weights_path, filename)
                    if os.path.isfile(weight_file):
                        weight_files.append(weight_file)
                        logger.info(f"找到权重文件: {filename}")

        # 方法3: 查找固定名称的权重文件
        if not weight_files:
            logger.info("查找固定名称的权重文件...")
            for filename in ['weights.pt', 'model.pt', 'best.pt']:
                weight_file = os.path.join(weights_path, filename)
                if os.path.exists(weight_file):
                    weight_files.append(weight_file)
                    logger.info(f"找到固定名称权重文件: {filename}")
                    break
        
        if not weight_files:
            # 如果没有找到权重文件，尝试使用预训练模型
            logger.warning(f"未找到权重文件，尝试使用预训练模型: {model_name}")
            try:
                # 尝试使用预训练模型（会自动下载）
                model = YOLO(f"{model_name}.pt")
                logger.info(f"成功加载预训练模型: {model_name}.pt")
            except Exception as e:
                logger.error(f"加载预训练模型失败: {str(e)}")
                # 尝试使用最基础的YOLOv8n模型
                try:
                    logger.info("尝试使用基础的yolov8n模型...")
                    model = YOLO("yolov8n.pt")
                    logger.info("成功加载yolov8n模型")
                except Exception as e2:
                    logger.error(f"加载基础模型也失败: {str(e2)}")
                    raise Exception(f"无法加载任何模型: 权重文件不存在，预训练模型下载失败: {str(e)}")
        else:
            # 使用找到的第一个权重文件
            weight_file = weight_files[0]
            logger.info(f"加载权重文件: {weight_file}")
            try:
                model = YOLO(weight_file)
                logger.info(f"成功加载权重文件: {weight_file}")
            except Exception as e:
                logger.error(f"加载权重文件失败: {str(e)}")
                # 如果权重文件损坏，回退到预训练模型
                logger.info("权重文件可能损坏，回退到预训练模型...")
                try:
                    model = YOLO(f"{model_name}.pt")
                    logger.info(f"成功回退到预训练模型: {model_name}.pt")
                except Exception as e2:
                    logger.error(f"回退到预训练模型也失败: {str(e2)}")
                    raise Exception(f"权重文件损坏且无法下载预训练模型: {str(e)}")
        
        # 设置模型信息
        model_info = {
            'name': model_name,
            'version': model_version,
            'weights_path': weight_files[0] if weight_files else f"{model_name}.pt",
            'loaded_at': datetime.now().isoformat(),
            'input_size': 640,  # YOLO默认输入尺寸
            'classes': model.names if hasattr(model, 'names') else {},
        }
        
        logger.info(f"模型加载成功: {model_info}")
        return True
        
    except Exception as e:
        logger.error(f"模型加载失败: {str(e)}")
        return False

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    if model is None:
        return jsonify({
            'status': 'unhealthy',
            'message': '模型未加载',
            'timestamp': datetime.now().isoformat()
        }), 503
    
    return jsonify({
        'status': 'healthy',
        'model_info': model_info,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/info', methods=['GET'])
def model_info_endpoint():
    """模型信息接口"""
    return jsonify({
        'model_info': model_info,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/predict', methods=['POST'])
def predict():
    """推理接口"""
    start_time = time.time()
    
    try:
        # 检查模型是否已加载
        if model is None:
            return jsonify({
                'error': '模型未加载',
                'timestamp': datetime.now().isoformat()
            }), 503
        
        # 获取请求数据
        if 'image' not in request.files:
            return jsonify({
                'error': '缺少图像文件',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        image_file = request.files['image']
        if image_file.filename == '':
            return jsonify({
                'error': '未选择文件',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        # 获取推理参数
        conf_threshold = float(request.form.get('conf', 0.25))
        iou_threshold = float(request.form.get('iou', 0.45))
        max_det = int(request.form.get('max_det', 1000))
        
        # 验证和加载图像
        try:
            image = Image.open(image_file.stream)
            if not validate_image(image):
                return jsonify({
                    'error': '无效的图像格式',
                    'timestamp': datetime.now().isoformat()
                }), 400
        except Exception as e:
            return jsonify({
                'error': f'图像加载失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        # 执行推理
        try:
            results = model(
                image,
                conf=conf_threshold,
                iou=iou_threshold,
                max_det=max_det,
                verbose=False
            )
            
            # 格式化预测结果
            predictions = format_predictions(results[0])
            
            # 计算推理时间
            inference_time = time.time() - start_time
            
            return jsonify({
                'predictions': predictions,
                'model_info': {
                    'name': model_info['name'],
                    'version': model_info['version']
                },
                'parameters': {
                    'conf_threshold': conf_threshold,
                    'iou_threshold': iou_threshold,
                    'max_det': max_det
                },
                'inference_time': round(inference_time, 3),
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"推理执行失败: {str(e)}")
            return jsonify({
                'error': f'推理失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }), 500
            
    except Exception as e:
        logger.error(f"推理接口异常: {str(e)}")
        return jsonify({
            'error': f'服务异常: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/batch_predict', methods=['POST'])
def batch_predict():
    """批量推理接口"""
    start_time = time.time()
    
    try:
        # 检查模型是否已加载
        if model is None:
            return jsonify({
                'error': '模型未加载',
                'timestamp': datetime.now().isoformat()
            }), 503
        
        # 获取上传的文件列表
        if 'images' not in request.files:
            return jsonify({
                'error': '缺少图像文件',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        image_files = request.files.getlist('images')
        if not image_files:
            return jsonify({
                'error': '未选择文件',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        # 获取推理参数
        conf_threshold = float(request.form.get('conf', 0.25))
        iou_threshold = float(request.form.get('iou', 0.45))
        max_det = int(request.form.get('max_det', 1000))
        
        # 批量处理图像
        results = []
        for i, image_file in enumerate(image_files):
            try:
                # 加载图像
                image = Image.open(image_file.stream)
                if not validate_image(image):
                    results.append({
                        'filename': image_file.filename,
                        'error': '无效的图像格式'
                    })
                    continue
                
                # 执行推理
                model_results = model(
                    image,
                    conf=conf_threshold,
                    iou=iou_threshold,
                    max_det=max_det,
                    verbose=False
                )
                
                # 格式化结果
                predictions = format_predictions(model_results[0])
                
                results.append({
                    'filename': image_file.filename,
                    'predictions': predictions
                })
                
            except Exception as e:
                results.append({
                    'filename': image_file.filename,
                    'error': str(e)
                })
        
        # 计算总处理时间
        total_time = time.time() - start_time
        
        return jsonify({
            'results': results,
            'model_info': {
                'name': model_info['name'],
                'version': model_info['version']
            },
            'parameters': {
                'conf_threshold': conf_threshold,
                'iou_threshold': iou_threshold,
                'max_det': max_det
            },
            'total_images': len(image_files),
            'successful_predictions': len([r for r in results if 'predictions' in r]),
            'total_time': round(total_time, 3),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"批量推理接口异常: {str(e)}")
        return jsonify({
            'error': f'服务异常: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    # 启动时加载模型
    if not load_model():
        logger.error("模型加载失败，服务无法启动")
        exit(1)
    
    # 获取服务端口
    port = int(os.getenv('SERVICE_PORT', 8080))
    
    # 启动服务
    logger.info(f"启动YOLO推理服务，端口: {port}")
    app.run(host='0.0.0.0', port=port, debug=False, threaded=True)
