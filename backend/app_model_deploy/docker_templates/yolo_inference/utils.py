#!/usr/bin/env python3
"""
YOLO推理服务工具函数
"""
import logging
import sys
from PIL import Image
import numpy as np


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
        ]
    )


def validate_image(image):
    """
    验证图像是否有效
    
    Args:
        image: PIL Image对象
        
    Returns:
        bool: 是否有效
    """
    try:
        # 检查图像模式
        if image.mode not in ['RGB', 'RGBA', 'L']:
            return False
        
        # 检查图像尺寸
        width, height = image.size
        if width < 32 or height < 32 or width > 4096 or height > 4096:
            return False
        
        # 尝试转换为RGB
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        return True
        
    except Exception:
        return False


def format_predictions(results):
    """
    格式化YOLO预测结果
    
    Args:
        results: YOLO模型输出结果
        
    Returns:
        dict: 格式化后的预测结果
    """
    try:
        predictions = {
            'detections': [],
            'summary': {
                'total_detections': 0,
                'classes_detected': set(),
                'confidence_scores': []
            }
        }
        
        # 检查是否有检测结果
        if results.boxes is None or len(results.boxes) == 0:
            predictions['summary']['total_detections'] = 0
            predictions['summary']['classes_detected'] = list(predictions['summary']['classes_detected'])
            return predictions
        
        # 获取检测框、置信度和类别
        boxes = results.boxes.xyxy.cpu().numpy()  # 边界框坐标
        confidences = results.boxes.conf.cpu().numpy()  # 置信度
        classes = results.boxes.cls.cpu().numpy()  # 类别ID
        
        # 获取类别名称映射
        class_names = results.names if hasattr(results, 'names') else {}
        
        # 处理每个检测结果
        for i in range(len(boxes)):
            x1, y1, x2, y2 = boxes[i]
            confidence = float(confidences[i])
            class_id = int(classes[i])
            class_name = class_names.get(class_id, f'class_{class_id}')
            
            detection = {
                'bbox': {
                    'x1': float(x1),
                    'y1': float(y1),
                    'x2': float(x2),
                    'y2': float(y2),
                    'width': float(x2 - x1),
                    'height': float(y2 - y1)
                },
                'confidence': round(confidence, 4),
                'class_id': class_id,
                'class_name': class_name
            }
            
            predictions['detections'].append(detection)
            predictions['summary']['classes_detected'].add(class_name)
            predictions['summary']['confidence_scores'].append(confidence)
        
        # 更新汇总信息
        predictions['summary']['total_detections'] = len(predictions['detections'])
        predictions['summary']['classes_detected'] = list(predictions['summary']['classes_detected'])
        
        if predictions['summary']['confidence_scores']:
            predictions['summary']['avg_confidence'] = round(
                np.mean(predictions['summary']['confidence_scores']), 4
            )
            predictions['summary']['max_confidence'] = round(
                np.max(predictions['summary']['confidence_scores']), 4
            )
            predictions['summary']['min_confidence'] = round(
                np.min(predictions['summary']['confidence_scores']), 4
            )
        
        # 移除原始置信度分数列表（太长了）
        del predictions['summary']['confidence_scores']
        
        return predictions
        
    except Exception as e:
        return {
            'detections': [],
            'summary': {
                'total_detections': 0,
                'classes_detected': [],
                'error': str(e)
            }
        }


def preprocess_image(image, target_size=640):
    """
    预处理图像
    
    Args:
        image: PIL Image对象
        target_size: 目标尺寸
        
    Returns:
        PIL Image: 预处理后的图像
    """
    try:
        # 转换为RGB
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 获取原始尺寸
        original_width, original_height = image.size
        
        # 计算缩放比例（保持宽高比）
        scale = min(target_size / original_width, target_size / original_height)
        
        # 计算新尺寸
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)
        
        # 缩放图像
        image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 创建目标尺寸的画布（填充黑色）
        canvas = Image.new('RGB', (target_size, target_size), (0, 0, 0))
        
        # 计算粘贴位置（居中）
        paste_x = (target_size - new_width) // 2
        paste_y = (target_size - new_height) // 2
        
        # 粘贴缩放后的图像到画布中心
        canvas.paste(image, (paste_x, paste_y))
        
        return canvas
        
    except Exception as e:
        raise ValueError(f"图像预处理失败: {str(e)}")


def calculate_iou(box1, box2):
    """
    计算两个边界框的IoU
    
    Args:
        box1: [x1, y1, x2, y2]
        box2: [x1, y1, x2, y2]
        
    Returns:
        float: IoU值
    """
    try:
        # 计算交集区域
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        # 检查是否有交集
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        # 计算交集面积
        intersection = (x2 - x1) * (y2 - y1)
        
        # 计算并集面积
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection
        
        # 计算IoU
        if union == 0:
            return 0.0
        
        return intersection / union
        
    except Exception:
        return 0.0


def filter_predictions_by_area(predictions, min_area=100, max_area=None):
    """
    根据检测框面积过滤预测结果
    
    Args:
        predictions: 预测结果字典
        min_area: 最小面积
        max_area: 最大面积
        
    Returns:
        dict: 过滤后的预测结果
    """
    try:
        filtered_detections = []
        
        for detection in predictions['detections']:
            bbox = detection['bbox']
            area = bbox['width'] * bbox['height']
            
            # 检查面积范围
            if area < min_area:
                continue
            if max_area is not None and area > max_area:
                continue
            
            filtered_detections.append(detection)
        
        # 更新预测结果
        predictions['detections'] = filtered_detections
        predictions['summary']['total_detections'] = len(filtered_detections)
        
        # 重新计算类别统计
        classes_detected = set()
        for detection in filtered_detections:
            classes_detected.add(detection['class_name'])
        predictions['summary']['classes_detected'] = list(classes_detected)
        
        return predictions
        
    except Exception as e:
        predictions['summary']['filter_error'] = str(e)
        return predictions
