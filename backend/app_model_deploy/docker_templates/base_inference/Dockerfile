# AI推理服务基础镜像
# 包含Python、PyTorch、常用AI库等基础环境
FROM python:3.9-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # 基础工具
    curl \
    wget \
    git \
    unzip \
    # 图像处理依赖
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    # OpenCV依赖
    libglib2.0-0 \
    libgtk-3-0 \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    # 其他依赖
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 升级pip
RUN pip install --upgrade pip setuptools wheel

# 安装基础Python包
COPY requirements.txt /tmp/requirements.txt
RUN pip install -r /tmp/requirements.txt && rm /tmp/requirements.txt

# 创建常用目录
RUN mkdir -p /app/weights \
    && mkdir -p /app/models \
    && mkdir -p /app/logs \
    && mkdir -p /app/temp \
    && mkdir -p /app/.cache

# 设置权限
RUN chmod -R 755 /app

# 创建非root用户（安全最佳实践）
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 健康检查基础配置
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 默认端口
EXPOSE 8080

# 默认命令（子镜像会覆盖）
CMD ["python", "-c", "print('Base inference image ready')"]
