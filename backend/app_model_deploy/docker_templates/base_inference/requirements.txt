# AI推理服务基础依赖包

# 深度学习框架
torch==2.0.1
torchvision==0.15.2
torchaudio==2.0.2

# 图像处理
Pillow==10.0.0
opencv-python==********
imageio==2.31.1

# 数值计算
numpy==1.25.2
scipy==1.11.1

# Web框架
Flask==2.3.2
Flask-CORS==4.0.0
Werkzeug==2.3.6

# HTTP客户端
requests==2.31.0
urllib3==2.0.4

# 数据处理
pandas==2.0.3

# 序列化
pydantic==2.1.1

# 日志
loguru==0.7.0

# 配置管理
python-dotenv==1.0.0

# 文件处理
pathlib2==2.3.7

# 时间处理
python-dateutil==2.8.2

# 系统监控
psutil==5.9.5

# 异步支持
asyncio-mqtt==0.13.0

# 数据验证
marshmallow==3.20.1

# 缓存
cachetools==5.3.1

# 工具库
tqdm==4.65.0
click==8.1.6

# 测试工具（开发用）
pytest==7.4.0
pytest-asyncio==0.21.1



# YOLO特定依赖包（基础镜像已包含torch、torchvision、pillow、numpy、flask等）
ultralytics==8.0.196

# 模型优化和导出
onnx==1.14.1
onnxruntime==1.15.1

# 可视化（如果需要）
matplotlib==3.7.2
seaborn==0.12.2

# Web服务器
gunicorn==21.2.0
