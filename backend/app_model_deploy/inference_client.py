import requests
import logging
import time
import uuid
from typing import Dict, Any, Optional, List
from django.conf import settings
from django.utils import timezone
from .models import ModelService, InferenceLog

logger = logging.getLogger(__name__)


class InferenceClient:
    """
    推理客户端 - 用于与部署的模型服务进行通信
    """
    
    def __init__(self, service: ModelService):
        self.service = service
        self.base_url = service.api_endpoint.rstrip('/')
        self.timeout = 30
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            dict: 健康检查结果
        """
        try:
            response = requests.get(
                f"{self.base_url.replace('/predict', '')}/health",
                timeout=10
            )
            
            if response.status_code == 200:
                return {
                    'status': 'healthy',
                    'data': response.json(),
                    'response_time': response.elapsed.total_seconds()
                }
            else:
                return {
                    'status': 'unhealthy',
                    'error': f'HTTP {response.status_code}',
                    'response_time': response.elapsed.total_seconds()
                }
                
        except requests.exceptions.Timeout:
            return {
                'status': 'timeout',
                'error': '健康检查超时'
            }
        except requests.exceptions.RequestException as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def predict_image(self, 
                     image_file, 
                     parameters: Optional[Dict[str, Any]] = None,
                     client_ip: Optional[str] = None,
                     user_agent: Optional[str] = None) -> Dict[str, Any]:
        """
        图像推理
        
        Args:
            image_file: 图像文件对象
            parameters: 推理参数
            client_ip: 客户端IP
            user_agent: 用户代理
            
        Returns:
            dict: 推理结果
        """
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # 准备请求数据
            files = {'image': image_file}
            data = parameters or {}
            
            # 发送推理请求
            response = requests.post(
                self.base_url,
                files=files,
                data=data,
                timeout=self.timeout
            )
            
            # 计算响应时间
            response_time = time.time() - start_time
            
            # 更新服务统计
            self.service.total_requests += 1
            
            if response.status_code == 200:
                result_data = response.json()
                self.service.successful_requests += 1
                
                # 更新平均响应时间
                self._update_average_response_time(response_time)
                self.service.save()
                
                # 记录成功日志
                self._log_inference(
                    request_id=request_id,
                    input_data={'parameters': parameters, 'image_info': 'uploaded'},
                    output_data=result_data,
                    response_time=response_time,
                    status='success',
                    client_ip=client_ip,
                    user_agent=user_agent
                )
                
                return {
                    'success': True,
                    'request_id': request_id,
                    'data': result_data,
                    'response_time': response_time
                }
            else:
                self.service.failed_requests += 1
                self.service.save()
                
                error_msg = f'推理服务返回错误: HTTP {response.status_code}'
                
                # 记录失败日志
                self._log_inference(
                    request_id=request_id,
                    input_data={'parameters': parameters, 'image_info': 'uploaded'},
                    response_time=response_time,
                    status='failed',
                    error_message=error_msg,
                    client_ip=client_ip,
                    user_agent=user_agent
                )
                
                return {
                    'success': False,
                    'request_id': request_id,
                    'error': error_msg,
                    'response_time': response_time
                }
                
        except requests.exceptions.Timeout:
            self.service.failed_requests += 1
            self.service.save()
            
            error_msg = '推理请求超时'
            
            # 记录超时日志
            self._log_inference(
                request_id=request_id,
                input_data={'parameters': parameters, 'image_info': 'uploaded'},
                response_time=self.timeout,
                status='timeout',
                error_message=error_msg,
                client_ip=client_ip,
                user_agent=user_agent
            )
            
            return {
                'success': False,
                'request_id': request_id,
                'error': error_msg,
                'response_time': self.timeout
            }
            
        except requests.exceptions.RequestException as e:
            self.service.failed_requests += 1
            self.service.save()
            
            error_msg = f'推理请求失败: {str(e)}'
            response_time = time.time() - start_time
            
            # 记录错误日志
            self._log_inference(
                request_id=request_id,
                input_data={'parameters': parameters, 'image_info': 'uploaded'},
                response_time=response_time,
                status='failed',
                error_message=error_msg,
                client_ip=client_ip,
                user_agent=user_agent
            )
            
            return {
                'success': False,
                'request_id': request_id,
                'error': error_msg,
                'response_time': response_time
            }
    
    def predict_batch(self, 
                     image_files: List,
                     parameters: Optional[Dict[str, Any]] = None,
                     client_ip: Optional[str] = None,
                     user_agent: Optional[str] = None) -> Dict[str, Any]:
        """
        批量图像推理
        
        Args:
            image_files: 图像文件列表
            parameters: 推理参数
            client_ip: 客户端IP
            user_agent: 用户代理
            
        Returns:
            dict: 批量推理结果
        """
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # 准备请求数据
            files = [('images', image_file) for image_file in image_files]
            data = parameters or {}
            
            # 发送批量推理请求
            response = requests.post(
                f"{self.base_url.replace('/predict', '/batch_predict')}",
                files=files,
                data=data,
                timeout=self.timeout * 2  # 批量处理需要更长时间
            )
            
            # 计算响应时间
            response_time = time.time() - start_time
            
            # 更新服务统计
            self.service.total_requests += len(image_files)
            
            if response.status_code == 200:
                result_data = response.json()
                
                # 统计成功和失败的数量
                successful_count = len([r for r in result_data.get('results', []) if 'predictions' in r])
                failed_count = len(image_files) - successful_count
                
                self.service.successful_requests += successful_count
                self.service.failed_requests += failed_count
                
                # 更新平均响应时间
                self._update_average_response_time(response_time)
                self.service.save()
                
                # 记录批量推理日志
                self._log_inference(
                    request_id=request_id,
                    input_data={
                        'parameters': parameters,
                        'batch_size': len(image_files),
                        'image_info': 'batch_uploaded'
                    },
                    output_data=result_data,
                    response_time=response_time,
                    status='success',
                    client_ip=client_ip,
                    user_agent=user_agent
                )
                
                return {
                    'success': True,
                    'request_id': request_id,
                    'data': result_data,
                    'response_time': response_time,
                    'batch_size': len(image_files),
                    'successful_count': successful_count,
                    'failed_count': failed_count
                }
            else:
                self.service.failed_requests += len(image_files)
                self.service.save()
                
                error_msg = f'批量推理服务返回错误: HTTP {response.status_code}'
                
                # 记录失败日志
                self._log_inference(
                    request_id=request_id,
                    input_data={
                        'parameters': parameters,
                        'batch_size': len(image_files),
                        'image_info': 'batch_uploaded'
                    },
                    response_time=response_time,
                    status='failed',
                    error_message=error_msg,
                    client_ip=client_ip,
                    user_agent=user_agent
                )
                
                return {
                    'success': False,
                    'request_id': request_id,
                    'error': error_msg,
                    'response_time': response_time,
                    'batch_size': len(image_files)
                }
                
        except Exception as e:
            self.service.failed_requests += len(image_files)
            self.service.save()
            
            error_msg = f'批量推理异常: {str(e)}'
            response_time = time.time() - start_time
            
            # 记录错误日志
            self._log_inference(
                request_id=request_id,
                input_data={
                    'parameters': parameters,
                    'batch_size': len(image_files),
                    'image_info': 'batch_uploaded'
                },
                response_time=response_time,
                status='failed',
                error_message=error_msg,
                client_ip=client_ip,
                user_agent=user_agent
            )
            
            return {
                'success': False,
                'request_id': request_id,
                'error': error_msg,
                'response_time': response_time,
                'batch_size': len(image_files)
            }
    
    def _update_average_response_time(self, response_time: float):
        """更新平均响应时间"""
        if self.service.total_requests == 1:
            self.service.average_response_time = response_time
        else:
            # 计算加权平均
            total_time = self.service.average_response_time * (self.service.total_requests - 1)
            self.service.average_response_time = (total_time + response_time) / self.service.total_requests
    
    def _log_inference(self, 
                      request_id: str,
                      input_data: Dict[str, Any],
                      response_time: float,
                      status: str,
                      output_data: Optional[Dict[str, Any]] = None,
                      error_message: Optional[str] = None,
                      client_ip: Optional[str] = None,
                      user_agent: Optional[str] = None):
        """记录推理日志"""
        try:
            InferenceLog.objects.create(
                service=self.service,
                request_id=request_id,
                input_data=input_data,
                output_data=output_data,
                response_time=response_time,
                status=status,
                error_message=error_message,
                client_ip=client_ip,
                user_agent=user_agent
            )
        except Exception as e:
            logger.error(f"记录推理日志失败: {str(e)}")


def get_inference_client(service_id: int) -> Optional[InferenceClient]:
    """
    获取推理客户端
    
    Args:
        service_id: 服务ID
        
    Returns:
        InferenceClient: 推理客户端实例
    """
    try:
        service = ModelService.objects.get(id=service_id)
        return InferenceClient(service)
    except ModelService.DoesNotExist:
        logger.error(f"服务不存在: {service_id}")
        return None
    except Exception as e:
        logger.error(f"创建推理客户端失败: {str(e)}")
        return None
