"""
性能监控系统配置
"""
import os
from django.conf import settings

# 监控配置
MONITORING_CONFIG = {
    # 数据收集配置
    'COLLECTION': {
        'INTERVAL': int(os.getenv('MONITORING_INTERVAL', 60)),  # 收集间隔（秒）
        'ENABLED': os.getenv('MONITORING_ENABLED', 'true').lower() == 'true',  # 是否启用监控
        'AUTO_START': os.getenv('MONITORING_AUTO_START', 'false').lower() == 'true',  # 是否自动启动
    },
    
    # 数据保留配置
    'RETENTION': {
        'METRICS_DAYS': int(os.getenv('METRICS_RETENTION_DAYS', 30)),  # 监控数据保留天数
        'LOGS_DAYS': int(os.getenv('LOGS_RETENTION_DAYS', 7)),  # 推理日志保留天数
        'AUTO_CLEANUP': os.getenv('AUTO_CLEANUP_ENABLED', 'true').lower() == 'true',  # 自动清理
    },
    
    # 告警配置
    'ALERTS': {
        'ENABLED': os.getenv('ALERTS_ENABLED', 'false').lower() == 'true',  # 是否启用告警
        'CPU_THRESHOLD': float(os.getenv('CPU_ALERT_THRESHOLD', 80.0)),  # CPU告警阈值
        'MEMORY_THRESHOLD': float(os.getenv('MEMORY_ALERT_THRESHOLD', 90.0)),  # 内存告警阈值
        'ERROR_RATE_THRESHOLD': float(os.getenv('ERROR_RATE_THRESHOLD', 5.0)),  # 错误率告警阈值
        'RESPONSE_TIME_THRESHOLD': float(os.getenv('RESPONSE_TIME_THRESHOLD', 5.0)),  # 响应时间告警阈值
    },
    
    # 性能优化配置
    'PERFORMANCE': {
        'BATCH_SIZE': int(os.getenv('METRICS_BATCH_SIZE', 100)),  # 批量处理大小
        'CACHE_ENABLED': os.getenv('METRICS_CACHE_ENABLED', 'true').lower() == 'true',  # 缓存启用
        'CACHE_TTL': int(os.getenv('METRICS_CACHE_TTL', 300)),  # 缓存过期时间（秒）
    },
    
    # Docker配置
    'DOCKER': {
        'SOCKET_PATH': os.getenv('DOCKER_SOCKET_PATH', 'unix://var/run/docker.sock'),
        'API_VERSION': os.getenv('DOCKER_API_VERSION', 'auto'),
        'TIMEOUT': int(os.getenv('DOCKER_TIMEOUT', 10)),
    }
}

# 告警阈值配置
ALERT_THRESHOLDS = {
    'cpu_usage': {
        'warning': MONITORING_CONFIG['ALERTS']['CPU_THRESHOLD'] * 0.8,
        'critical': MONITORING_CONFIG['ALERTS']['CPU_THRESHOLD']
    },
    'memory_usage': {
        'warning': MONITORING_CONFIG['ALERTS']['MEMORY_THRESHOLD'] * 0.8,
        'critical': MONITORING_CONFIG['ALERTS']['MEMORY_THRESHOLD']
    },
    'error_rate': {
        'warning': MONITORING_CONFIG['ALERTS']['ERROR_RATE_THRESHOLD'] * 0.6,
        'critical': MONITORING_CONFIG['ALERTS']['ERROR_RATE_THRESHOLD']
    },
    'response_time': {
        'warning': MONITORING_CONFIG['ALERTS']['RESPONSE_TIME_THRESHOLD'] * 0.8,
        'critical': MONITORING_CONFIG['ALERTS']['RESPONSE_TIME_THRESHOLD']
    }
}

# 图表配置
CHART_CONFIG = {
    'DEFAULT_TIME_RANGE': 24,  # 默认时间范围（小时）
    'MAX_DATA_POINTS': 1000,   # 最大数据点数
    'REFRESH_INTERVAL': 30,    # 前端刷新间隔（秒）
    'COLORS': {
        'cpu': '#409EFF',
        'memory': '#67C23A',
        'gpu': '#E6A23C',
        'requests': '#F56C6C',
        'response_time': '#909399',
        'error_rate': '#F56C6C'
    }
}

def get_monitoring_config():
    """获取监控配置"""
    return MONITORING_CONFIG

def get_alert_thresholds():
    """获取告警阈值配置"""
    return ALERT_THRESHOLDS

def get_chart_config():
    """获取图表配置"""
    return CHART_CONFIG

def is_monitoring_enabled():
    """检查监控是否启用"""
    return MONITORING_CONFIG['COLLECTION']['ENABLED']

def get_collection_interval():
    """获取数据收集间隔"""
    return MONITORING_CONFIG['COLLECTION']['INTERVAL']

def should_auto_start():
    """检查是否应该自动启动监控"""
    return MONITORING_CONFIG['COLLECTION']['AUTO_START']
