from rest_framework import serializers
from .models import ModelDeployment, ModelService, InferenceLog, ServiceMetrics
from app_model.serializers import ModelVersionSerializer
from app_user.serializers import UserSerializer
from utils.serializers import CustomModelSerializer


class ModelDeploymentSerializer(CustomModelSerializer):
    """
    模型部署序列化器
    """
    model_version_info = ModelVersionSerializer(source='model_version', read_only=True)
    deployed_by_info = UserSerializer(source='deployed_by', read_only=True)
    model_name = serializers.CharField(source='model_version.model.name', read_only=True)
    version_number = serializers.CharField(source='model_version.version_number', read_only=True)
    docker_image = serializers.CharField(source='model_version.docker_image', read_only=True)
    service_info = serializers.SerializerMethodField()

    class Meta:
        model = ModelDeployment
        fields = '__all__'
        read_only_fields = ['id', 'create_datetime', 'update_datetime', 'container_id', 'container_name']

    def get_service_info(self, obj):
        """获取关联的服务信息（简化版，避免循环引用）"""
        try:
            if hasattr(obj, 'service') and obj.service:
                service = obj.service
                return {
                    'id': service.id,
                    'service_name': service.service_name,
                    'api_endpoint': service.api_endpoint,
                    'health_check_url': service.health_check_url,
                    'is_healthy': service.is_healthy,
                    'last_health_check': service.last_health_check,
                    'total_requests': service.total_requests,
                    'successful_requests': service.successful_requests,
                    'failed_requests': service.failed_requests,
                    'success_rate': (service.successful_requests / service.total_requests * 100) if service.total_requests > 0 else 0
                }
            return None
        except Exception:
            return None


class ModelServiceSerializer(CustomModelSerializer):
    """
    模型服务序列化器
    """
    deployment_info = ModelDeploymentSerializer(source='deployment', read_only=True)
    model_name = serializers.CharField(source='deployment.model_version.model.name', read_only=True)
    version_number = serializers.CharField(source='deployment.model_version.version_number', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = ModelService
        fields = '__all__'
        read_only_fields = ['id', 'create_datetime', 'update_datetime']
    
    def get_success_rate(self, obj):
        """计算成功率"""
        if obj.total_requests == 0:
            return 0.0
        return round((obj.successful_requests / obj.total_requests) * 100, 2)


class InferenceLogSerializer(CustomModelSerializer):
    """
    推理日志序列化器
    """
    service_name = serializers.CharField(source='service.service_name', read_only=True)
    model_name = serializers.CharField(source='service.deployment.model_version.model.name', read_only=True)
    
    class Meta:
        model = InferenceLog
        fields = '__all__'
        read_only_fields = ['id', 'create_datetime', 'update_datetime']


class ServiceMetricsSerializer(CustomModelSerializer):
    """
    服务指标序列化器
    """
    service_name = serializers.CharField(source='service.service_name', read_only=True)
    
    class Meta:
        model = ServiceMetrics
        fields = '__all__'
        read_only_fields = ['id', 'create_datetime', 'update_datetime']


class DeploymentCreateSerializer(serializers.Serializer):
    """
    部署创建序列化器
    """
    model_version_id = serializers.IntegerField(help_text='模型版本ID')
    deployment_name = serializers.CharField(max_length=100, help_text='部署名称')
    service_port = serializers.IntegerField(required=False, help_text='服务端口，不指定则自动分配')
    deploy_config = serializers.JSONField(required=False, default=dict, help_text='部署配置')
    
    def validate_deployment_name(self, value):
        """验证部署名称唯一性"""
        if ModelDeployment.objects.filter(deployment_name=value).exists():
            raise serializers.ValidationError('部署名称已存在')
        return value


class InferenceRequestSerializer(serializers.Serializer):
    """
    推理请求序列化器
    """
    input_data = serializers.JSONField(help_text='输入数据')
    
    def validate_input_data(self, value):
        """验证输入数据格式"""
        if not isinstance(value, dict):
            raise serializers.ValidationError('输入数据必须是JSON对象')
        return value


class InferenceResponseSerializer(serializers.Serializer):
    """
    推理响应序列化器
    """
    request_id = serializers.CharField()
    status = serializers.CharField()
    output_data = serializers.JSONField(required=False)
    response_time = serializers.FloatField()
    error_message = serializers.CharField(required=False)


class ServiceStatsSerializer(serializers.Serializer):
    """
    服务统计序列化器
    """
    total_requests = serializers.IntegerField()
    successful_requests = serializers.IntegerField()
    failed_requests = serializers.IntegerField()
    success_rate = serializers.FloatField()
    average_response_time = serializers.FloatField()
    requests_per_hour = serializers.IntegerField()
    
    
class DeploymentStatsSerializer(serializers.Serializer):
    """
    部署统计序列化器
    """
    total_deployments = serializers.IntegerField()
    running_deployments = serializers.IntegerField()
    stopped_deployments = serializers.IntegerField()
    failed_deployments = serializers.IntegerField()
    total_services = serializers.IntegerField()
    healthy_services = serializers.IntegerField()
