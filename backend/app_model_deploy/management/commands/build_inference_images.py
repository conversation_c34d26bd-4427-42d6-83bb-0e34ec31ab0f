from django.core.management.base import BaseCommand
import subprocess
import os
import time
from pathlib import Path


class Command(BaseCommand):
    help = '构建AI推理服务镜像'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['base', 'yolo', 'all', 'clean', 'list', 'test'],
            help='操作类型: base(基础镜像), yolo(YOLO镜像), all(所有镜像), clean(清理), list(列表), test(测试)'
        )
        parser.add_argument(
            '--tag',
            type=str,
            default='latest',
            help='镜像标签，默认为latest'
        )
        parser.add_argument(
            '--no-cache',
            action='store_true',
            help='不使用缓存构建镜像'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细构建日志'
        )
    
    def handle(self, *args, **options):
        action = options['action']
        tag = options['tag']
        no_cache = options['no_cache']
        verbose = options['verbose']
        
        self.stdout.write(
            self.style.SUCCESS(f'开始执行操作: {action}')
        )
        
        if action == 'base':
            self.build_base_image(tag, no_cache, verbose)
        elif action == 'yolo':
            self.build_yolo_image(tag, no_cache, verbose)
        elif action == 'all':
            self.build_all_images(tag, no_cache, verbose)
        elif action == 'clean':
            self.clean_images()
        elif action == 'list':
            self.list_images()
        elif action == 'test':
            self.test_images()
    
    def build_base_image(self, tag='latest', no_cache=False, verbose=False):
        """构建基础镜像"""
        self.stdout.write('构建AI推理基础镜像...')
        
        base_path = Path('backend/app_model_deploy/docker_templates/base_inference')
        if not base_path.exists():
            self.stdout.write(
                self.style.ERROR(f'基础镜像模板路径不存在: {base_path}')
            )
            return False
        
        image_name = f'ai-inference-base:{tag}'
        
        # 构建Docker命令
        cmd = ['docker', 'build', '-t', image_name]
        if no_cache:
            cmd.append('--no-cache')
        cmd.append(str(base_path))
        
        return self._run_docker_build(cmd, image_name, verbose)
    
    def build_yolo_image(self, tag='latest', no_cache=False, verbose=False):
        """构建YOLO推理镜像"""
        self.stdout.write('构建YOLO推理服务镜像...')
        
        # 检查基础镜像是否存在
        if not self._check_image_exists('ai-inference-base:latest'):
            self.stdout.write(
                self.style.ERROR('基础镜像不存在，请先构建基础镜像')
            )
            self.stdout.write('运行: python manage.py build_inference_images base')
            return False
        
        yolo_path = Path('backend/app_model_deploy/docker_templates/yolo_inference')
        if not yolo_path.exists():
            self.stdout.write(
                self.style.ERROR(f'YOLO镜像模板路径不存在: {yolo_path}')
            )
            return False
        
        image_name = f'yolo-inference:{tag}'
        
        # 构建Docker命令
        cmd = ['docker', 'build', '-t', image_name]
        if no_cache:
            cmd.append('--no-cache')
        cmd.append(str(yolo_path))
        
        return self._run_docker_build(cmd, image_name, verbose)
    
    def build_all_images(self, tag='latest', no_cache=False, verbose=False):
        """构建所有镜像"""
        self.stdout.write('构建所有推理服务镜像...')
        
        start_time = time.time()
        
        # 构建基础镜像
        if not self.build_base_image(tag, no_cache, verbose):
            return False
        
        # 构建YOLO推理镜像
        if not self.build_yolo_image(tag, no_cache, verbose):
            return False
        
        end_time = time.time()
        total_time = int(end_time - start_time)
        
        self.stdout.write(
            self.style.SUCCESS(f'所有镜像构建完成! (总耗时: {total_time}秒)')
        )
        
        # 显示镜像列表
        self.list_images()
        
        return True
    
    def clean_images(self):
        """清理推理镜像"""
        self.stdout.write('清理推理服务镜像...')
        
        try:
            # 停止相关容器
            self.stdout.write('停止相关容器...')
            subprocess.run([
                'docker', 'ps', '-a', '--filter', 'ancestor=ai-inference-base',
                '--filter', 'ancestor=yolo-inference', '-q'
            ], capture_output=True, text=True, check=False)
            
            # 删除镜像
            images_to_remove = []
            
            # 获取要删除的镜像
            result = subprocess.run([
                'docker', 'images', '--filter', 'reference=ai-inference-base',
                '--filter', 'reference=yolo-inference', '-q'
            ], capture_output=True, text=True, check=False)
            
            if result.stdout.strip():
                images_to_remove = result.stdout.strip().split('\n')
            
            if images_to_remove:
                self.stdout.write(f'删除 {len(images_to_remove)} 个镜像...')
                subprocess.run(['docker', 'rmi', '-f'] + images_to_remove, 
                             check=False)
            
            # 清理悬空镜像
            self.stdout.write('清理悬空镜像...')
            subprocess.run(['docker', 'image', 'prune', '-f'], check=False)
            
            self.stdout.write(
                self.style.SUCCESS('镜像清理完成!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'清理镜像时出错: {str(e)}')
            )
    
    def list_images(self):
        """列出推理镜像"""
        self.stdout.write('推理服务镜像列表:')
        
        try:
            # 获取基础镜像
            result = subprocess.run([
                'docker', 'images', '--filter', 'reference=ai-inference-base',
                '--format', 'table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}'
            ], capture_output=True, text=True, check=True)
            
            if result.stdout.strip():
                self.stdout.write('\n基础镜像:')
                self.stdout.write(result.stdout)
            else:
                self.stdout.write('  未找到基础镜像')
            
            # 获取YOLO推理镜像
            result = subprocess.run([
                'docker', 'images', '--filter', 'reference=yolo-inference',
                '--format', 'table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}'
            ], capture_output=True, text=True, check=True)
            
            if result.stdout.strip():
                self.stdout.write('\nYOLO推理镜像:')
                self.stdout.write(result.stdout)
            else:
                self.stdout.write('  未找到YOLO推理镜像')
            
            self.stdout.write('\n镜像层级关系:')
            self.stdout.write('  ai-inference-base:latest (基础镜像)')
            self.stdout.write('  └── yolo-inference:latest (YOLO推理服务)')
            
        except subprocess.CalledProcessError as e:
            self.stdout.write(
                self.style.ERROR(f'获取镜像列表失败: {str(e)}')
            )
    
    def test_images(self):
        """测试推理镜像"""
        self.stdout.write('测试推理服务镜像...')
        
        # 测试基础镜像
        if self._check_image_exists('ai-inference-base:latest'):
            self.stdout.write('✓ 基础镜像存在')
        else:
            self.stdout.write('✗ 基础镜像不存在')
        
        # 测试YOLO推理镜像
        if self._check_image_exists('yolo-inference:latest'):
            self.stdout.write('✓ YOLO推理镜像存在')
            
            # 快速测试YOLO镜像
            self.stdout.write('快速测试YOLO镜像...')
            try:
                result = subprocess.run([
                    'docker', 'run', '--rm', 'yolo-inference:latest',
                    'python', '-c', '''
import torch
import cv2
from ultralytics import YOLO
print("✓ 基础库导入成功")
print(f"✓ PyTorch版本: {torch.__version__}")
print(f"✓ OpenCV版本: {cv2.__version__}")
print("✓ YOLO镜像测试通过")
'''
                ], capture_output=True, text=True, check=True, timeout=60)
                
                self.stdout.write(result.stdout)
                
            except subprocess.TimeoutExpired:
                self.stdout.write('✗ YOLO镜像测试超时')
            except subprocess.CalledProcessError as e:
                self.stdout.write(f'✗ YOLO镜像测试失败: {e.stderr}')
        else:
            self.stdout.write('✗ YOLO推理镜像不存在')
    
    def _run_docker_build(self, cmd, image_name, verbose=False):
        """运行Docker构建命令"""
        start_time = time.time()
        
        try:
            if verbose:
                # 实时显示构建日志
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                         stderr=subprocess.STDOUT, 
                                         universal_newlines=True)
                
                for line in process.stdout:
                    self.stdout.write(line.rstrip())
                
                process.wait()
                if process.returncode != 0:
                    raise subprocess.CalledProcessError(process.returncode, cmd)
            else:
                # 静默构建
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            end_time = time.time()
            build_time = int(end_time - start_time)
            
            self.stdout.write(
                self.style.SUCCESS(f'镜像构建成功: {image_name} (耗时: {build_time}秒)')
            )
            
            # 显示镜像大小
            try:
                result = subprocess.run([
                    'docker', 'images', image_name, '--format', '{{.Size}}'
                ], capture_output=True, text=True, check=True)
                
                if result.stdout.strip():
                    self.stdout.write(f'镜像大小: {result.stdout.strip()}')
            except:
                pass
            
            return True
            
        except subprocess.CalledProcessError as e:
            self.stdout.write(
                self.style.ERROR(f'镜像构建失败: {image_name}')
            )
            if e.stderr:
                self.stdout.write(e.stderr)
            return False
    
    def _check_image_exists(self, image_name):
        """检查镜像是否存在"""
        try:
            result = subprocess.run([
                'docker', 'images', image_name, '-q'
            ], capture_output=True, text=True, check=True)
            
            return bool(result.stdout.strip())
        except:
            return False
