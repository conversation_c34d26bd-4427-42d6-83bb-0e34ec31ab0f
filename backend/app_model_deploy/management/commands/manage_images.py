from django.core.management.base import BaseCommand
from app_model_deploy.docker_manager import docker_manager
from tabulate import tabulate


class Command(BaseCommand):
    help = '管理Docker镜像'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['list', 'check', 'pull'],
            help='操作类型: list(列出本地镜像), check(检查镜像), pull(拉取镜像)'
        )
        parser.add_argument(
            '--image',
            type=str,
            help='镜像名称（用于check和pull操作）'
        )
        parser.add_argument(
            '--filter',
            type=str,
            help='过滤条件（用于list操作）'
        )
    
    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'list':
            self.list_images(options.get('filter'))
        elif action == 'check':
            self.check_image(options.get('image'))
        elif action == 'pull':
            self.pull_image(options.get('image'))
    
    def list_images(self, filter_pattern=None):
        """列出本地镜像"""
        self.stdout.write(self.style.SUCCESS('本地Docker镜像列表:'))
        
        try:
            images = docker_manager.list_local_images(filter_pattern)
            
            if not images:
                if filter_pattern:
                    self.stdout.write(f'未找到匹配 "{filter_pattern}" 的镜像')
                else:
                    self.stdout.write('未找到任何本地镜像')
                return
            
            # 准备表格数据
            headers = ['镜像ID', '标签', '大小(MB)', '创建时间']
            table_data = []
            
            for image in images:
                table_data.append([
                    image['id'],
                    image['tag'],
                    image['size_mb'],
                    image['created'][:19]  # 只显示日期时间部分
                ])
            
            # 显示表格
            table = tabulate(table_data, headers=headers, tablefmt='grid')
            self.stdout.write(table)
            
            self.stdout.write(f'\n总计: {len(images)} 个镜像')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'列出镜像失败: {str(e)}')
            )
    
    def check_image(self, image_name):
        """检查镜像是否存在"""
        if not image_name:
            self.stdout.write(
                self.style.ERROR('请指定镜像名称: --image <镜像名称>')
            )
            return
        
        try:
            exists = docker_manager.check_image_exists(image_name)
            
            if exists:
                self.stdout.write(
                    self.style.SUCCESS(f'✓ 镜像存在: {image_name}')
                )
                
                # 显示镜像详细信息
                try:
                    image = docker_manager.client.images.get(image_name)
                    self.stdout.write(f'镜像ID: {image.id}')
                    self.stdout.write(f'标签: {", ".join(image.tags)}')
                    self.stdout.write(f'大小: {round(image.attrs["Size"] / 1024 / 1024, 2)} MB')
                    self.stdout.write(f'创建时间: {image.attrs["Created"][:19]}')
                except Exception:
                    pass
            else:
                self.stdout.write(
                    self.style.WARNING(f'✗ 镜像不存在: {image_name}')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'检查镜像失败: {str(e)}')
            )
    
    def pull_image(self, image_name):
        """拉取镜像"""
        if not image_name:
            self.stdout.write(
                self.style.ERROR('请指定镜像名称: --image <镜像名称>')
            )
            return
        
        try:
            self.stdout.write(f'正在获取镜像: {image_name}')
            
            success = docker_manager.pull_image(image_name)
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f'✓ 镜像获取成功: {image_name}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'✗ 镜像获取失败: {image_name}')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'拉取镜像异常: {str(e)}')
            )
