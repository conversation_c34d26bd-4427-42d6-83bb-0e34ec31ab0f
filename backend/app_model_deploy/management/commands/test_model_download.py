from django.core.management.base import BaseCommand
from app_model.models import ModelVersion
from utils.minio_storage import model_storage_manager
import tempfile
import os


class Command(BaseCommand):
    help = '测试模型文件下载功能'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--model-version-id',
            type=int,
            help='模型版本ID'
        )
        parser.add_argument(
            '--list-versions',
            action='store_true',
            help='列出所有模型版本'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示路径，不实际下载'
        )
    
    def handle(self, *args, **options):
        if options['list_versions']:
            self.list_model_versions()
            return
        
        model_version_id = options.get('model_version_id')
        if not model_version_id:
            self.stdout.write(
                self.style.ERROR('请指定模型版本ID: --model-version-id <ID>')
            )
            self.stdout.write('或使用 --list-versions 查看所有版本')
            return
        
        self.test_download(model_version_id, options.get('dry_run', False))
    
    def list_model_versions(self):
        """列出所有模型版本"""
        self.stdout.write(self.style.SUCCESS('模型版本列表:'))
        
        versions = ModelVersion.objects.select_related('model').all()
        
        if not versions:
            self.stdout.write('未找到任何模型版本')
            return
        
        for version in versions:
            model = version.model
            self.stdout.write(f'ID: {version.id}')
            self.stdout.write(f'  模型: {model.name}')
            self.stdout.write(f'  版本: {version.version_number}')
            self.stdout.write(f'  状态: {version.status}')
            
            if hasattr(model, 'minio_path'):
                self.stdout.write(f'  MinIO路径: {model.minio_path}')
            
            if version.model_weights_path:
                self.stdout.write(f'  权重文件: {version.model_weights_path}')
            
            if version.model_docs_path:
                self.stdout.write(f'  文档文件: {version.model_docs_path}')
            
            if version.test_report_path:
                self.stdout.write(f'  测试报告: {version.test_report_path}')
            
            self.stdout.write('---')
    
    def test_download(self, model_version_id, dry_run=False):
        """测试下载功能"""
        try:
            version = ModelVersion.objects.select_related('model').get(id=model_version_id)
        except ModelVersion.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'模型版本不存在: {model_version_id}')
            )
            return
        
        self.stdout.write(
            self.style.SUCCESS(f'测试模型版本: {version.model.name} v{version.version_number}')
        )
        
        model = version.model
        
        # 显示路径信息
        self.stdout.write('\n路径信息:')
        if hasattr(model, 'minio_path') and model.minio_path:
            self.stdout.write(f'  模型MinIO路径: {model.minio_path}')
        else:
            self.stdout.write('  模型MinIO路径: 未设置')
        
        # 显示各文件的完整路径
        if version.model_weights_path:
            if hasattr(model, 'minio_path') and model.minio_path:
                full_weights_path = f"{model.minio_path.rstrip('/')}/{version.model_weights_path}"
            else:
                full_weights_path = version.model_weights_path
            self.stdout.write(f'  权重文件完整路径: {full_weights_path}')
        
        if version.model_docs_path:
            if hasattr(model, 'minio_path') and model.minio_path:
                full_docs_path = f"{model.minio_path.rstrip('/')}/{version.model_docs_path}"
            else:
                full_docs_path = version.model_docs_path
            self.stdout.write(f'  文档文件完整路径: {full_docs_path}')
        
        if version.test_report_path:
            if hasattr(model, 'minio_path') and model.minio_path:
                full_report_path = f"{model.minio_path.rstrip('/')}/{version.test_report_path}"
            else:
                full_report_path = version.test_report_path
            self.stdout.write(f'  测试报告完整路径: {full_report_path}')
        
        if dry_run:
            self.stdout.write('\n[DRY RUN] 只显示路径，不实际下载')
            return
        
        # 实际下载测试
        self.stdout.write('\n开始下载测试...')
        
        with tempfile.TemporaryDirectory() as temp_dir:
            self.stdout.write(f'临时目录: {temp_dir}')
            
            # 测试下载所有文件
            result = model_storage_manager.download_model_files(version, temp_dir)
            
            if result['success']:
                self.stdout.write(self.style.SUCCESS('✓ 下载成功!'))
                
                self.stdout.write('\n下载的文件:')
                for file_info in result['files']:
                    file_type = file_info['type']
                    local_path = file_info['local_path']
                    remote_path = file_info['remote_path']
                    
                    if os.path.exists(local_path):
                        file_size = os.path.getsize(local_path)
                        self.stdout.write(f'  ✓ {file_type}: {remote_path} ({file_size} bytes)')
                    else:
                        self.stdout.write(f'  ✗ {file_type}: 文件不存在')
                
            else:
                self.stdout.write(self.style.ERROR('✗ 下载失败!'))
                
                if result['errors']:
                    self.stdout.write('\n错误信息:')
                    for error in result['errors']:
                        self.stdout.write(f'  - {error}')
            
            # 测试单独下载权重文件
            if version.model_weights_path:
                self.stdout.write('\n测试单独下载权重文件...')
                weights_path = os.path.join(temp_dir, 'test_weights.pt')
                
                success = model_storage_manager.download_model_weights(version, weights_path)
                
                if success and os.path.exists(weights_path):
                    file_size = os.path.getsize(weights_path)
                    self.stdout.write(f'  ✓ 权重文件下载成功: {file_size} bytes')
                else:
                    self.stdout.write('  ✗ 权重文件下载失败')
        
        self.stdout.write('\n测试完成!')
