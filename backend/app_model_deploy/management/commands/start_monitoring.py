from django.core.management.base import BaseCommand
from django.conf import settings
import logging
import signal
import sys
import time

from app_model_deploy.container_monitor import container_monitor

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '启动模型部署性能监控系统'

    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='监控数据收集间隔（秒），默认60秒'
        )
        parser.add_argument(
            '--daemon',
            action='store_true',
            help='以守护进程模式运行'
        )

    def handle(self, *args, **options):
        interval = options['interval']
        daemon = options['daemon']
        
        self.stdout.write(
            self.style.SUCCESS(f'启动性能监控系统，收集间隔: {interval}秒')
        )
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        try:
            # 启动监控
            container_monitor.start_monitoring(interval=interval)
            
            if daemon:
                self.stdout.write('监控系统已在后台启动')
            else:
                self.stdout.write('监控系统运行中，按 Ctrl+C 停止...')
                # 保持主进程运行
                while container_monitor.monitoring:
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            self.stdout.write('\n收到停止信号，正在关闭监控系统...')
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'监控系统启动失败: {str(e)}')
            )
        finally:
            container_monitor.stop_monitoring()
            self.stdout.write(
                self.style.SUCCESS('监控系统已停止')
            )
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.stdout.write(f'\n收到信号 {signum}，正在停止监控系统...')
        container_monitor.stop_monitoring()
        sys.exit(0)
