from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from app_model_deploy.models import ServiceMetrics, InferenceLog

class Command(BaseCommand):
    help = '清理过期的监控数据和推理日志'

    def add_arguments(self, parser):
        parser.add_argument(
            '--metrics-days',
            type=int,
            default=30,
            help='保留监控数据的天数，默认30天'
        )
        parser.add_argument(
            '--logs-days',
            type=int,
            default=7,
            help='保留推理日志的天数，默认7天'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要删除的数据数量，不实际删除'
        )

    def handle(self, *args, **options):
        metrics_days = options['metrics_days']
        logs_days = options['logs_days']
        dry_run = options['dry_run']
        
        # 计算截止时间
        metrics_cutoff = timezone.now() - timedelta(days=metrics_days)
        logs_cutoff = timezone.now() - timedelta(days=logs_days)
        
        # 查询过期数据
        old_metrics = ServiceMetrics.objects.filter(timestamp__lt=metrics_cutoff)
        old_logs = InferenceLog.objects.filter(create_datetime__lt=logs_cutoff)
        
        metrics_count = old_metrics.count()
        logs_count = old_logs.count()
        
        self.stdout.write(f'找到 {metrics_count} 条过期监控数据（{metrics_days}天前）')
        self.stdout.write(f'找到 {logs_count} 条过期推理日志（{logs_days}天前）')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('这是试运行模式，不会实际删除数据')
            )
            return
        
        if metrics_count == 0 and logs_count == 0:
            self.stdout.write(
                self.style.SUCCESS('没有需要清理的过期数据')
            )
            return
        
        # 确认删除
        confirm = input('确认删除这些数据吗？(y/N): ')
        if confirm.lower() != 'y':
            self.stdout.write('操作已取消')
            return
        
        # 删除过期数据
        deleted_metrics = 0
        deleted_logs = 0
        
        if metrics_count > 0:
            deleted_metrics, _ = old_metrics.delete()
            self.stdout.write(
                self.style.SUCCESS(f'已删除 {deleted_metrics} 条监控数据')
            )
        
        if logs_count > 0:
            deleted_logs, _ = old_logs.delete()
            self.stdout.write(
                self.style.SUCCESS(f'已删除 {deleted_logs} 条推理日志')
            )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'数据清理完成，共删除 {deleted_metrics + deleted_logs} 条记录'
            )
        )
