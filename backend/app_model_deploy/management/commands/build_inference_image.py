from django.core.management.base import BaseCommand
from django.conf import settings
import docker
import os
import tempfile
import shutil
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '构建推理服务Docker镜像'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--image-name',
            type=str,
            default='yolo-inference',
            help='Docker镜像名称，默认为yolo-inference'
        )
        parser.add_argument(
            '--tag',
            type=str,
            default='latest',
            help='Docker镜像标签，默认为latest'
        )
        parser.add_argument(
            '--template',
            type=str,
            default='yolo_inference',
            help='使用的模板名称，默认为yolo_inference'
        )
    
    def handle(self, *args, **options):
        image_name = options['image_name']
        tag = options['tag']
        template = options['template']
        full_image_name = f"{image_name}:{tag}"
        
        self.stdout.write(
            self.style.SUCCESS(f'开始构建Docker镜像: {full_image_name}')
        )
        
        try:
            # 获取Docker客户端
            client = docker.from_env()
            
            # 获取模板路径
            template_path = os.path.join(
                settings.BASE_DIR,
                'app_model_deploy',
                'docker_templates',
                template
            )
            
            if not os.path.exists(template_path):
                self.stdout.write(
                    self.style.ERROR(f'模板路径不存在: {template_path}')
                )
                return
            
            # 构建镜像
            self.stdout.write('正在构建镜像...')
            
            # 使用生成器来实时显示构建日志
            build_logs = client.api.build(
                path=template_path,
                tag=full_image_name,
                rm=True,
                decode=True
            )
            
            for log in build_logs:
                if 'stream' in log:
                    self.stdout.write(log['stream'].strip())
                elif 'error' in log:
                    self.stdout.write(
                        self.style.ERROR(f"构建错误: {log['error']}")
                    )
                    return
            
            self.stdout.write(
                self.style.SUCCESS(f'Docker镜像构建成功: {full_image_name}')
            )
            
            # 显示镜像信息
            try:
                image = client.images.get(full_image_name)
                self.stdout.write(f'镜像ID: {image.id}')
                self.stdout.write(f'镜像大小: {image.attrs["Size"] / 1024 / 1024:.2f} MB')
                self.stdout.write(f'创建时间: {image.attrs["Created"]}')
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'获取镜像信息失败: {str(e)}')
                )
            
        except docker.errors.BuildError as e:
            self.stdout.write(
                self.style.ERROR(f'Docker构建失败: {str(e)}')
            )
            for log in e.build_log:
                if 'stream' in log:
                    self.stdout.write(log['stream'].strip())
        except docker.errors.APIError as e:
            self.stdout.write(
                self.style.ERROR(f'Docker API错误: {str(e)}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'构建异常: {str(e)}')
            )
