from django.core.management.base import BaseCommand
from utils.json_response import DetailResponse, SuccessResponse, ErrorResponse
import json


class Command(BaseCommand):
    help = '测试响应格式是否正确'
    
    def handle(self, *args, **options):
        self.stdout.write('测试响应格式...')
        
        # 测试 DetailResponse
        self.test_detail_response()
        
        # 测试 SuccessResponse
        self.test_success_response()
        
        # 测试 ErrorResponse
        self.test_error_response()
        
        self.stdout.write(
            self.style.SUCCESS('所有响应格式测试通过!')
        )
    
    def test_detail_response(self):
        """测试 DetailResponse"""
        self.stdout.write('测试 DetailResponse...')
        
        # 基本用法
        response = DetailResponse(
            data={'test': 'data'},
            msg='测试成功'
        )
        
        self.stdout.write(f'基本响应: {response.data}')
        
        # 模拟推理响应（合并额外数据）
        inference_data = {
            'predictions': {'detections': []},
            'inference_time': 0.5
        }
        
        # 合并额外数据
        response_data = inference_data.copy()
        response_data.update({
            'request_id': 'test-123',
            'response_time': 0.8
        })
        
        response = DetailResponse(
            data=response_data,
            msg='推理成功'
        )
        
        self.stdout.write(f'推理响应: {json.dumps(response.data, indent=2, ensure_ascii=False)}')
        
        # 验证数据结构
        assert response.data['code'] == 200
        assert response.data['msg'] == '推理成功'
        assert 'predictions' in response.data['data']
        assert 'request_id' in response.data['data']
        assert 'response_time' in response.data['data']
        
        self.stdout.write('✓ DetailResponse 测试通过')
    
    def test_success_response(self):
        """测试 SuccessResponse"""
        self.stdout.write('测试 SuccessResponse...')
        
        response = SuccessResponse(msg='操作成功')
        
        self.stdout.write(f'成功响应: {response.data}')
        
        # 验证数据结构
        assert response.data['code'] == 200
        assert response.data['msg'] == '操作成功'
        
        self.stdout.write('✓ SuccessResponse 测试通过')
    
    def test_error_response(self):
        """测试 ErrorResponse"""
        self.stdout.write('测试 ErrorResponse...')
        
        response = ErrorResponse(msg='操作失败')
        
        self.stdout.write(f'错误响应: {response.data}')
        
        # 验证数据结构
        assert response.data['code'] == 400
        assert response.data['msg'] == '操作失败'
        
        self.stdout.write('✓ ErrorResponse 测试通过')
    
    def test_batch_inference_response(self):
        """测试批量推理响应格式"""
        self.stdout.write('测试批量推理响应格式...')
        
        # 模拟批量推理数据
        batch_data = {
            'results': [
                {'filename': 'image1.jpg', 'predictions': {'detections': []}},
                {'filename': 'image2.jpg', 'predictions': {'detections': []}}
            ],
            'summary': {
                'total_images': 2,
                'successful_images': 2,
                'failed_images': 0
            }
        }
        
        # 合并额外数据
        response_data = batch_data.copy()
        response_data.update({
            'request_id': 'batch-456',
            'response_time': 2.5,
            'batch_size': 2,
            'successful_count': 2,
            'failed_count': 0
        })
        
        response = DetailResponse(
            data=response_data,
            msg='批量推理成功'
        )
        
        self.stdout.write(f'批量推理响应: {json.dumps(response.data, indent=2, ensure_ascii=False)}')
        
        # 验证数据结构
        assert response.data['code'] == 200
        assert response.data['msg'] == '批量推理成功'
        assert 'results' in response.data['data']
        assert 'request_id' in response.data['data']
        assert 'batch_size' in response.data['data']
        
        self.stdout.write('✓ 批量推理响应格式测试通过')
    
    def test_health_check_response(self):
        """测试健康检查响应格式"""
        self.stdout.write('测试健康检查响应格式...')
        
        # 模拟健康检查数据
        health_data = {
            'status': 'healthy',
            'model_loaded': True,
            'service_info': {
                'model_name': 'yolo11n',
                'version': 'v1.0'
            }
        }
        
        # 合并额外数据
        response_data = health_data.copy()
        response_data.update({
            'response_time': 0.1
        })
        
        response = DetailResponse(
            data=response_data,
            msg='服务健康'
        )
        
        self.stdout.write(f'健康检查响应: {json.dumps(response.data, indent=2, ensure_ascii=False)}')
        
        # 验证数据结构
        assert response.data['code'] == 200
        assert response.data['msg'] == '服务健康'
        assert 'status' in response.data['data']
        assert 'response_time' in response.data['data']
        
        self.stdout.write('✓ 健康检查响应格式测试通过')
