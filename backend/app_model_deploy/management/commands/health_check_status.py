from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import requests
from app_model_deploy.models import ModelService, ModelDeployment
from app_model_deploy.docker_manager import docker_manager

class Command(BaseCommand):
    help = '检查和显示所有服务的健康状态'

    def add_arguments(self, parser):
        parser.add_argument(
            '--service-id',
            type=int,
            help='检查指定服务ID的健康状态'
        )
        parser.add_argument(
            '--update',
            action='store_true',
            help='执行健康检查并更新数据库状态'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细信息'
        )
        parser.add_argument(
            '--format',
            choices=['table', 'json', 'simple'],
            default='table',
            help='输出格式'
        )

    def handle(self, *args, **options):
        service_id = options.get('service_id')
        update = options.get('update')
        verbose = options.get('verbose')
        output_format = options.get('format')

        if service_id:
            # 检查单个服务
            try:
                service = ModelService.objects.get(id=service_id)
                self.check_single_service(service, update, verbose)
            except ModelService.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'服务ID {service_id} 不存在')
                )
        else:
            # 检查所有服务
            self.check_all_services(update, verbose, output_format)

    def check_single_service(self, service, update=False, verbose=False):
        """检查单个服务的健康状态"""
        self.stdout.write(f'\n=== 检查服务: {service.service_name} ===')
        
        # 1. 检查容器状态
        container_status = self.check_container_status(service.deployment)
        
        # 2. 检查HTTP健康状态
        http_status = self.check_http_health(service)
        
        # 3. 显示结果
        self.display_service_status(service, container_status, http_status, verbose)
        
        # 4. 更新数据库（如果需要）
        if update:
            self.update_service_status(service, container_status, http_status)

    def check_all_services(self, update=False, verbose=False, output_format='table'):
        """检查所有服务的健康状态"""
        services = ModelService.objects.all().select_related('deployment')
        
        if not services.exists():
            self.stdout.write(
                self.style.WARNING('没有找到任何服务')
            )
            return

        results = []
        
        for service in services:
            # 检查容器状态
            container_status = self.check_container_status(service.deployment)
            
            # 检查HTTP健康状态
            http_status = self.check_http_health(service)
            
            # 收集结果
            result = {
                'service': service,
                'container_status': container_status,
                'http_status': http_status,
                'overall_healthy': container_status['is_running'] and http_status['is_healthy']
            }
            results.append(result)
            
            # 更新数据库（如果需要）
            if update:
                self.update_service_status(service, container_status, http_status)

        # 显示结果
        self.display_all_results(results, verbose, output_format)

    def check_container_status(self, deployment):
        """检查容器状态"""
        if not deployment.container_id:
            return {
                'is_running': False,
                'status': 'no_container',
                'error': '没有关联的容器'
            }

        try:
            success, error_msg, status_info = docker_manager.get_container_status(
                deployment.container_id
            )
            
            if success and status_info:
                return {
                    'is_running': status_info['status'] == 'running',
                    'status': status_info['status'],
                    'error': None
                }
            else:
                return {
                    'is_running': False,
                    'status': 'error',
                    'error': error_msg or '获取容器状态失败'
                }
        except Exception as e:
            return {
                'is_running': False,
                'status': 'error',
                'error': str(e)
            }

    def check_http_health(self, service):
        """检查HTTP健康状态"""
        try:
            response = requests.get(
                service.health_check_url,
                timeout=10
            )
            
            return {
                'is_healthy': response.status_code == 200,
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds(),
                'error': None if response.status_code == 200 else f'HTTP {response.status_code}'
            }
            
        except requests.exceptions.Timeout:
            return {
                'is_healthy': False,
                'status_code': None,
                'response_time': None,
                'error': '请求超时'
            }
        except requests.exceptions.RequestException as e:
            return {
                'is_healthy': False,
                'status_code': None,
                'response_time': None,
                'error': str(e)
            }

    def update_service_status(self, service, container_status, http_status):
        """更新服务状态到数据库"""
        # 判断整体健康状态
        is_healthy = container_status['is_running'] and http_status['is_healthy']
        
        # 更新服务状态
        service.is_healthy = is_healthy
        service.last_health_check = timezone.now()
        service.save()
        
        # 更新部署状态
        deployment = service.deployment
        if not container_status['is_running']:
            deployment.status = 'stopped' if container_status['status'] == 'exited' else 'error'
            deployment.error_message = container_status.get('error', '')
            deployment.save()

    def display_service_status(self, service, container_status, http_status, verbose=False):
        """显示单个服务的状态"""
        # 基本信息
        self.stdout.write(f'服务名称: {service.service_name}')
        self.stdout.write(f'服务ID: {service.id}')
        self.stdout.write(f'健康检查URL: {service.health_check_url}')
        
        # 容器状态
        container_icon = '✅' if container_status['is_running'] else '❌'
        self.stdout.write(f'容器状态: {container_icon} {container_status["status"]}')
        if container_status['error']:
            self.stdout.write(f'  错误: {container_status["error"]}')
        
        # HTTP健康状态
        http_icon = '✅' if http_status['is_healthy'] else '❌'
        self.stdout.write(f'HTTP健康: {http_icon}')
        if http_status['status_code']:
            self.stdout.write(f'  状态码: {http_status["status_code"]}')
        if http_status['response_time']:
            self.stdout.write(f'  响应时间: {http_status["response_time"]:.3f}s')
        if http_status['error']:
            self.stdout.write(f'  错误: {http_status["error"]}')
        
        # 整体状态
        overall_healthy = container_status['is_running'] and http_status['is_healthy']
        overall_icon = '✅' if overall_healthy else '❌'
        self.stdout.write(f'整体状态: {overall_icon} {"健康" if overall_healthy else "异常"}')
        
        # 数据库状态
        self.stdout.write(f'数据库记录: {"健康" if service.is_healthy else "异常"}')
        if service.last_health_check:
            self.stdout.write(f'最后检查: {service.last_health_check}')
        
        if verbose:
            # 显示详细统计信息
            self.stdout.write(f'\n统计信息:')
            self.stdout.write(f'  总请求数: {service.total_requests}')
            self.stdout.write(f'  成功请求: {service.successful_requests}')
            self.stdout.write(f'  失败请求: {service.failed_requests}')
            if service.total_requests > 0:
                success_rate = service.successful_requests / service.total_requests * 100
                self.stdout.write(f'  成功率: {success_rate:.1f}%')

    def display_all_results(self, results, verbose=False, output_format='table'):
        """显示所有服务的检查结果"""
        if output_format == 'table':
            self.display_table_format(results, verbose)
        elif output_format == 'json':
            self.display_json_format(results)
        else:
            self.display_simple_format(results)

    def display_table_format(self, results, verbose=False):
        """表格格式显示"""
        self.stdout.write('\n' + '='*80)
        self.stdout.write('服务健康状态检查结果')
        self.stdout.write('='*80)
        
        # 表头
        header = f"{'ID':<4} {'服务名称':<20} {'容器':<8} {'HTTP':<8} {'整体':<8} {'数据库':<8}"
        if verbose:
            header += f" {'成功率':<8} {'最后检查':<20}"
        
        self.stdout.write(header)
        self.stdout.write('-'*80)
        
        # 数据行
        healthy_count = 0
        for result in results:
            service = result['service']
            container_icon = '✅' if result['container_status']['is_running'] else '❌'
            http_icon = '✅' if result['http_status']['is_healthy'] else '❌'
            overall_icon = '✅' if result['overall_healthy'] else '❌'
            db_icon = '✅' if service.is_healthy else '❌'
            
            if result['overall_healthy']:
                healthy_count += 1
            
            row = f"{service.id:<4} {service.service_name[:18]:<20} {container_icon:<8} {http_icon:<8} {overall_icon:<8} {db_icon:<8}"
            
            if verbose:
                success_rate = 0
                if service.total_requests > 0:
                    success_rate = service.successful_requests / service.total_requests * 100
                
                last_check = service.last_health_check.strftime('%m-%d %H:%M') if service.last_health_check else 'Never'
                row += f" {success_rate:>6.1f}% {last_check:<20}"
            
            self.stdout.write(row)
        
        # 统计信息
        self.stdout.write('-'*80)
        total_count = len(results)
        self.stdout.write(f'总计: {total_count} 个服务, {healthy_count} 个健康, {total_count - healthy_count} 个异常')

    def display_json_format(self, results):
        """JSON格式显示"""
        import json
        
        json_results = []
        for result in results:
            service = result['service']
            json_results.append({
                'id': service.id,
                'service_name': service.service_name,
                'container_running': result['container_status']['is_running'],
                'http_healthy': result['http_status']['is_healthy'],
                'overall_healthy': result['overall_healthy'],
                'db_healthy': service.is_healthy,
                'last_health_check': service.last_health_check.isoformat() if service.last_health_check else None,
                'total_requests': service.total_requests,
                'successful_requests': service.successful_requests,
                'failed_requests': service.failed_requests
            })
        
        self.stdout.write(json.dumps(json_results, indent=2, ensure_ascii=False))

    def display_simple_format(self, results):
        """简单格式显示"""
        for result in results:
            service = result['service']
            status = "健康" if result['overall_healthy'] else "异常"
            self.stdout.write(f"{service.id}: {service.service_name} - {status}")
