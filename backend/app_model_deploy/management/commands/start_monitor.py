from django.core.management.base import BaseCommand
from app_model_deploy.container_monitor import container_monitor
import signal
import sys


class Command(BaseCommand):
    help = '启动容器监控服务'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='监控间隔（秒），默认60秒'
        )
    
    def handle(self, *args, **options):
        interval = options['interval']
        
        self.stdout.write(
            self.style.SUCCESS(f'启动容器监控服务，监控间隔: {interval}秒')
        )
        
        # 设置信号处理器
        def signal_handler(sig, frame):
            self.stdout.write(self.style.WARNING('收到停止信号，正在关闭监控服务...'))
            container_monitor.stop_monitoring()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 启动监控
            container_monitor.start_monitoring(interval=interval)
            
            # 保持程序运行
            self.stdout.write(
                self.style.SUCCESS('容器监控服务已启动，按 Ctrl+C 停止')
            )
            
            # 等待信号
            signal.pause()
            
        except KeyboardInterrupt:
            self.stdout.write(self.style.WARNING('收到键盘中断，正在关闭监控服务...'))
            container_monitor.stop_monitoring()
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'监控服务异常: {str(e)}')
            )
            container_monitor.stop_monitoring()
            raise
