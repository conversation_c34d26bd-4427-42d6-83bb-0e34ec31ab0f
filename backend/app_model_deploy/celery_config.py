from celery.schedules import crontab

# 模型部署相关的定时任务配置
CELERY_BEAT_SCHEDULE = {
    # 每分钟监控服务状态
    'monitor-services': {
        'task': 'app_model_deploy.tasks.monitor_services',
        'schedule': 60.0,  # 每60秒执行一次
    },
    
    # 每5分钟收集性能指标
    'collect-metrics': {
        'task': 'app_model_deploy.tasks.collect_metrics',
        'schedule': 300.0,  # 每300秒执行一次
    },
    
    # 每天凌晨2点清理旧日志
    'cleanup-old-logs': {
        'task': 'app_model_deploy.tasks.cleanup_old_logs',
        'schedule': crontab(hour=2, minute=0),
    },
    
    # 每天早上8点生成日报
    'generate-daily-report': {
        'task': 'app_model_deploy.tasks.generate_daily_report',
        'schedule': crontab(hour=8, minute=0),
    },
    
    # 每10分钟检查并重启失败的服务
    'auto-restart-failed-services': {
        'task': 'app_model_deploy.tasks.auto_restart_failed_services',
        'schedule': 600.0,  # 每600秒执行一次
    },
    
    # 每小时更新服务统计
    'update-service-statistics': {
        'task': 'app_model_deploy.tasks.update_service_statistics',
        'schedule': crontab(minute=0),  # 每小时的0分执行
    },
}
