import docker
import logging
import os
import tempfile
import shutil
from typing import Dict, List, Optional, Tuple
from django.conf import settings
from utils.minio_storage import model_storage_manager

logger = logging.getLogger(__name__)


class ModelPathManager:
    """
    模型路径管理器 - 优雅地处理Docker环境中的路径问题
    """
    
    def __init__(self):
        self.is_docker_env = self._detect_docker_environment()
        self.shared_dir = '/shared/model_weights'
        
    def _detect_docker_environment(self) -> bool:
        """自动检测是否在Docker环境中"""
        docker_indicators = [
            os.path.exists('/.dockerenv'),
            os.path.exists('/proc/1/cgroup') and 'docker' in open('/proc/1/cgroup', 'r').read(),
            os.environ.get('DOCKER_CONTAINER') == 'true'
        ]
        return any(docker_indicators)
    
    def create_model_directory(self, model_version_id: int) -> Tuple[str, str]:
        """
        创建模型目录
        
        Returns:
            Tuple[str, str]: (容器内路径, 宿主机路径)
        """
        dir_name = f"model_{model_version_id}_{os.getpid()}"
        
        if self.is_docker_env:
            # Docker环境：使用共享目录
            container_path = os.path.join(self.shared_dir, dir_name)
            host_path = os.path.join('/shared/model_weights', dir_name)  # 宿主机对应路径
            os.makedirs(container_path, exist_ok=True)
            logger.info(f"🐳 Docker环境 - 模型目录: {container_path}")
            return container_path, host_path
        else:
            # 本地环境：使用临时目录
            local_path = tempfile.mkdtemp(prefix=f"model_{model_version_id}_")
            logger.info(f"💻 本地环境 - 模型目录: {local_path}")
            return local_path, local_path
    
    def cleanup_directory(self, path: str):
        """清理目录"""
        try:
            if os.path.exists(path):
                shutil.rmtree(path)
                logger.info(f"🧹 目录清理成功: {path}")
        except Exception as e:
            logger.error(f"❌ 清理目录失败: {str(e)}")


class DockerManager:
    """
    Docker容器管理器 - 重构版本
    """
    
    def __init__(self):
        try:
            self.client = docker.from_env()
            self.client.ping()
            self.path_manager = ModelPathManager()
            logger.info("✅ Docker客户端连接成功")
        except Exception as e:
            logger.error(f"❌ Docker客户端连接失败: {str(e)}")
            raise Exception(f"无法连接到Docker服务: {str(e)}")
    
    def pull_image(self, image_name: str) -> bool:
        """拉取Docker镜像"""
        try:
            try:
                local_image = self.client.images.get(image_name)
                logger.info(f"📦 使用本地镜像: {image_name}")
                return True
            except docker.errors.ImageNotFound:
                logger.info(f"⬇️ 拉取镜像: {image_name}")
                self.client.images.pull(image_name)
                return True
        except Exception as e:
            logger.error(f"❌ 镜像拉取失败: {str(e)}")
            return False
    
    def _cleanup_existing_container(self, container_name: str):
        """清理已存在的同名容器"""
        try:
            existing_container = self.client.containers.get(container_name)
            if existing_container.status == 'running':
                existing_container.stop(timeout=10)
            existing_container.remove(force=True)
            logger.info(f"🗑️ 清理已存在容器: {container_name}")
        except docker.errors.NotFound:
            pass
        except Exception as e:
            logger.warning(f"⚠️ 清理容器异常: {str(e)}")
    
    def create_container(self, 
                        image_name: str,
                        container_name: str,
                        model_version,
                        service_port: int,
                        deploy_config: Dict = None) -> Tuple[bool, Optional[str], Optional[str], Optional[str]]:
        """
        创建并启动容器 - 简化版本
        """
        container_path = None
        try:
            # 1. 清理已存在的容器
            self._cleanup_existing_container(container_name)
            
            # 2. 创建模型目录（自动处理Docker/本地环境）
            container_path, host_path = self.path_manager.create_model_directory(model_version.id)
            
            # 3. 下载模型文件
            download_result = model_storage_manager.download_model_files(model_version, container_path)
            if not download_result['success']:
                raise Exception(f"模型文件下载失败: {'; '.join(download_result['errors'])}")
            
            # 4. 准备容器配置
            environment = {
                'MODEL_NAME': model_version.model.name,
                'MODEL_VERSION': model_version.version_number,
                'SERVICE_PORT': str(service_port),
                'WEIGHTS_PATH': '/app/weights',
            }
            
            if deploy_config and 'environment' in deploy_config:
                environment.update(deploy_config['environment'])
            
            # 5. 卷挂载（使用宿主机路径）
            volumes = {host_path: {'bind': '/app/weights', 'mode': 'ro'}}
            ports = {f'{service_port}/tcp': service_port}
            
            # 6. 资源限制
            mem_limit = deploy_config.get('memory_limit', '2g') if deploy_config else '2g'
            cpu_limit = deploy_config.get('cpu_limit', 1.0) if deploy_config else 1.0
            
            # 7. 创建并启动容器
            container = self.client.containers.run(
                image=image_name,
                name=container_name,
                environment=environment,
                volumes=volumes,
                ports=ports,
                mem_limit=mem_limit,
                nano_cpus=int(cpu_limit * 1e9),
                detach=True,
                remove=False,
                restart_policy={"Name": "unless-stopped"}
            )
            
            logger.info(f"🚀 容器创建成功: {container.id[:12]}")
            logger.info(f"📁 权重挂载: {host_path} -> /app/weights")
            
            return True, container.id, None, container_path
            
        except Exception as e:
            error_msg = f"容器创建失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            
            # 清理资源
            if container_path:
                self.path_manager.cleanup_directory(container_path)
            
            return False, None, error_msg, None
    
    def stop_container(self, container_id: str) -> Tuple[bool, Optional[str]]:
        """停止容器"""
        try:
            container = self.client.containers.get(container_id)
            container.stop(timeout=30)
            logger.info(f"⏹️ 容器停止成功: {container_id[:12]}")
            return True, None
        except docker.errors.NotFound:
            return False, f"容器不存在: {container_id}"
        except Exception as e:
            return False, f"停止容器失败: {str(e)}"
    
    def remove_container(self, container_id: str) -> Tuple[bool, Optional[str]]:
        """删除容器"""
        try:
            container = self.client.containers.get(container_id)
            container.remove(force=True)
            logger.info(f"🗑️ 容器删除成功: {container_id[:12]}")
            return True, None
        except docker.errors.NotFound:
            return False, f"容器不存在: {container_id}"
        except Exception as e:
            return False, f"删除容器失败: {str(e)}"
    
    def get_container_status(self, container_id: str) -> Tuple[bool, Optional[str], Optional[Dict]]:
        """获取容器状态"""
        try:
            container = self.client.containers.get(container_id)
            container.reload()
            
            status_info = {
                'status': container.status,
                'created': container.attrs['Created'],
                'started_at': container.attrs['State'].get('StartedAt'),
                'ports': container.attrs['NetworkSettings']['Ports'],
            }
            
            return True, None, status_info
        except docker.errors.NotFound:
            return False, f"容器不存在: {container_id}", None
        except Exception as e:
            return False, f"获取状态失败: {str(e)}", None
    
    def get_container_logs(self, container_id: str, tail: int = 100) -> Tuple[bool, Optional[str], Optional[str]]:
        """获取容器日志"""
        try:
            container = self.client.containers.get(container_id)
            logs = container.logs(tail=tail, timestamps=True).decode('utf-8')
            return True, None, logs
        except docker.errors.NotFound:
            return False, f"容器不存在: {container_id}", None
        except Exception as e:
            return False, f"获取日志失败: {str(e)}", None
    
    def cleanup_model_directory(self, temp_dir: str):
        """清理模型目录"""
        self.path_manager.cleanup_directory(temp_dir)


# 全局Docker管理器实例
docker_manager = DockerManager()
