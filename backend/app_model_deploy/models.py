from django.db import models
from utils.models import BaseModel
from app_model.models import ModelVersion
from app_user.models import Users
import json


class ModelDeployment(BaseModel):
    """
    模型部署记录表
    """
    STATUS_CHOICES = (
        ('pending', '待部署'),
        ('deploying', '部署中'),
        ('running', '运行中'),
        ('stopped', '已停止'),
        ('failed', '部署失败'),
        ('error', '运行异常'),
    )
    
    model_version = models.ForeignKey(
        ModelVersion,
        on_delete=models.CASCADE,
        related_name='deployments',
        verbose_name='模型版本'
    )
    
    deployment_name = models.CharField(
        max_length=100,
        verbose_name='部署名称',
        help_text='部署实例的名称，用于标识不同的部署'
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='部署状态'
    )
    
    container_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='容器ID',
        help_text='Docker容器的ID'
    )
    
    container_name = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='容器名称',
        help_text='Docker容器的名称'
    )

    temp_weights_dir = models.CharField(
        max_length=500,
        null=True,
        blank=True,
        verbose_name='临时权重目录',
        help_text='存储权重文件的临时目录路径，用于容器挂载'
    )
    
    service_port = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='服务端口',
        help_text='推理服务监听的端口'
    )
    
    service_url = models.URLField(
        null=True,
        blank=True,
        verbose_name='服务URL',
        help_text='推理服务的访问地址'
    )
    
    deploy_config = models.JSONField(
        default=dict,
        verbose_name='部署配置',
        help_text='部署时的配置参数，如资源限制、环境变量等'
    )
    
    error_message = models.TextField(
        null=True,
        blank=True,
        verbose_name='错误信息',
        help_text='部署或运行时的错误信息'
    )
    
    deployed_by = models.ForeignKey(
        Users,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='部署人',
        related_name='deployed_models'
    )
    
    deployed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='部署时间'
    )
    
    stopped_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='停止时间'
    )
    
    class Meta:
        verbose_name = '模型部署'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
        unique_together = ['deployment_name']
    
    def __str__(self):
        return f"{self.deployment_name} - {self.model_version.model.name}"


class ModelService(BaseModel):
    """
    模型服务表 - 记录运行中的推理服务
    """
    deployment = models.OneToOneField(
        ModelDeployment,
        on_delete=models.CASCADE,
        related_name='service',
        verbose_name='关联部署'
    )
    
    service_name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='服务名称'
    )
    
    api_endpoint = models.URLField(
        verbose_name='API端点',
        help_text='推理服务的API地址'
    )
    
    health_check_url = models.URLField(
        verbose_name='健康检查URL',
        help_text='服务健康检查的地址'
    )
    
    is_healthy = models.BooleanField(
        default=False,
        verbose_name='服务健康状态'
    )
    
    last_health_check = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='最后健康检查时间'
    )
    
    total_requests = models.IntegerField(
        default=0,
        verbose_name='总请求次数'
    )
    
    successful_requests = models.IntegerField(
        default=0,
        verbose_name='成功请求次数'
    )
    
    failed_requests = models.IntegerField(
        default=0,
        verbose_name='失败请求次数'
    )
    
    average_response_time = models.FloatField(
        default=0.0,
        verbose_name='平均响应时间(秒)'
    )
    
    class Meta:
        verbose_name = '模型服务'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
    
    def __str__(self):
        return f"{self.service_name} - {self.deployment.model_version.model.name}"


class InferenceLog(BaseModel):
    """
    推理日志表 - 记录每次推理请求
    """
    service = models.ForeignKey(
        ModelService,
        on_delete=models.CASCADE,
        related_name='inference_logs',
        verbose_name='关联服务'
    )
    
    request_id = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='请求ID'
    )
    
    input_data = models.JSONField(
        verbose_name='输入数据',
        help_text='推理请求的输入数据'
    )
    
    output_data = models.JSONField(
        null=True,
        blank=True,
        verbose_name='输出数据',
        help_text='推理结果数据'
    )
    
    response_time = models.FloatField(
        verbose_name='响应时间(秒)'
    )
    
    status = models.CharField(
        max_length=20,
        choices=[
            ('success', '成功'),
            ('failed', '失败'),
            ('timeout', '超时'),
        ],
        verbose_name='请求状态'
    )
    
    error_message = models.TextField(
        null=True,
        blank=True,
        verbose_name='错误信息'
    )
    
    client_ip = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name='客户端IP'
    )
    
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name='用户代理'
    )
    
    class Meta:
        verbose_name = '推理日志'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
        indexes = [
            models.Index(fields=['service', '-create_datetime']),
            models.Index(fields=['status', '-create_datetime']),
        ]
    
    def __str__(self):
        return f"{self.request_id} - {self.service.service_name}"


class ServiceMetrics(BaseModel):
    """
    服务指标表 - 记录服务的性能指标
    """
    service = models.ForeignKey(
        ModelService,
        on_delete=models.CASCADE,
        related_name='metrics',
        verbose_name='关联服务'
    )
    
    timestamp = models.DateTimeField(
        verbose_name='时间戳'
    )
    
    cpu_usage = models.FloatField(
        null=True,
        blank=True,
        verbose_name='CPU使用率(%)'
    )
    
    memory_usage = models.FloatField(
        null=True,
        blank=True,
        verbose_name='内存使用率(%)'
    )
    
    gpu_usage = models.FloatField(
        null=True,
        blank=True,
        verbose_name='GPU使用率(%)'
    )
    
    requests_per_minute = models.IntegerField(
        default=0,
        verbose_name='每分钟请求数'
    )
    
    average_response_time = models.FloatField(
        default=0.0,
        verbose_name='平均响应时间(秒)'
    )
    
    error_rate = models.FloatField(
        default=0.0,
        verbose_name='错误率(%)'
    )
    
    class Meta:
        verbose_name = '服务指标'
        verbose_name_plural = verbose_name
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['service', '-timestamp']),
        ]
    
    def __str__(self):
        return f"{self.service.service_name} - {self.timestamp}"
