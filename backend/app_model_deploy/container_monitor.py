import docker
import psutil
import logging
import time
import threading
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import models
from .models import ModelService, ServiceMetrics

logger = logging.getLogger(__name__)


class ContainerMonitor:
    """
    容器监控器 - 监控容器资源使用情况
    """
    
    def __init__(self):
        try:
            self.client = docker.from_env()
            self.monitoring = False
            self.monitor_thread = None
        except Exception as e:
            logger.error(f"Docker客户端初始化失败: {str(e)}")
            self.client = None
    
    def start_monitoring(self, interval=60):
        """
        开始监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if not self.client:
            logger.error("Docker客户端未初始化，无法开始监控")
            return
        
        if self.monitoring:
            logger.warning("监控已在运行中")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"容器监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """
        停止监控
        """
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("容器监控已停止")
    
    def _monitor_loop(self, interval):
        """
        监控循环
        """
        while self.monitoring:
            try:
                self._collect_metrics()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"监控循环异常: {str(e)}")
                time.sleep(interval)
    
    def _collect_metrics(self):
        """
        收集指标数据
        """
        try:
            # 获取所有运行中的服务
            services = ModelService.objects.filter(
                deployment__status='running',
                is_healthy=True
            )
            for service in services:
                try:
                    metrics = self._get_container_metrics(service)
                    if metrics:
                        # 保存指标数据
                        ServiceMetrics.objects.create(
                            service=service,
                            timestamp=timezone.now(),
                            **metrics
                        )
                        logger.debug(f"收集服务 {service.service_name} 指标成功，指标{metrics}")
                except Exception as e:
                    logger.error(f"收集服务 {service.service_name} 指标失败: {str(e)}")
                    
        except Exception as e:
            logger.error(f"收集指标数据异常: {str(e)}")
    
    def _get_container_metrics(self, service):
        """
        获取容器指标
        
        Args:
            service: 模型服务对象
            
        Returns:
            dict: 指标数据
        """
        try:
            container_id = service.deployment.container_id
            if not container_id:
                return None
            
            container = self.client.containers.get(container_id)
            
            # 获取容器统计信息
            stats = container.stats(stream=False)
            
            # 计算CPU使用率
            cpu_usage = self._calculate_cpu_usage(stats)
            
            # 计算内存使用率
            memory_usage = self._calculate_memory_usage(stats)
            
            # 获取GPU使用率（如果有）
            gpu_usage = self._get_gpu_usage(container_id)
            
            # 计算请求统计
            request_stats = self._calculate_request_stats(service)
            
            return {
                'cpu_usage': cpu_usage,
                'memory_usage': memory_usage,
                'gpu_usage': gpu_usage,
                'requests_per_minute': request_stats['requests_per_minute'],
                'average_response_time': request_stats['average_response_time'],
                'error_rate': request_stats['error_rate']
            }
            
        except docker.errors.NotFound:
            logger.warning(f"容器不存在: {container_id}")
            return None
        except Exception as e:
            logger.error(f"获取容器指标异常: {str(e)}")
            return None
    
    def _calculate_cpu_usage(self, stats):
        """
        计算CPU使用率
        """
        try:
            cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                       stats['precpu_stats']['cpu_usage']['total_usage']
            system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                          stats['precpu_stats']['system_cpu_usage']
            
            if system_delta > 0:
                cpu_usage = (cpu_delta / system_delta) * \
                           len(stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100
                return round(cpu_usage, 2)
            return 0.0
        except (KeyError, ZeroDivisionError):
            return 0.0
    
    def _calculate_memory_usage(self, stats):
        """
        计算内存使用率
        """
        try:
            memory_usage = stats['memory_stats']['usage']
            memory_limit = stats['memory_stats']['limit']
            
            if memory_limit > 0:
                usage_percent = (memory_usage / memory_limit) * 100
                return round(usage_percent, 2)
            return 0.0
        except KeyError:
            return 0.0
    
    def _get_gpu_usage(self, container_id):
        """
        获取GPU使用率（简化实现）
        """
        try:
            # 这里可以集成nvidia-ml-py或其他GPU监控工具
            # 目前返回None表示暂不支持GPU监控
            return None
        except Exception:
            return None
    
    def _calculate_request_stats(self, service):
        """
        计算请求统计信息
        """
        try:
            # 获取最近一分钟的请求日志
            one_minute_ago = timezone.now() - timedelta(minutes=1)
            recent_logs = service.inference_logs.filter(
                create_datetime__gte=one_minute_ago
            )
            
            requests_per_minute = recent_logs.count()
            
            # 计算平均响应时间
            if recent_logs.exists():
                avg_response_time = recent_logs.aggregate(
                    avg_time=models.Avg('response_time')
                )['avg_time'] or 0.0
            else:
                avg_response_time = 0.0
            
            # 计算错误率
            failed_requests = recent_logs.filter(status='failed').count()
            error_rate = 0.0
            if requests_per_minute > 0:
                error_rate = (failed_requests / requests_per_minute) * 100
            
            return {
                'requests_per_minute': requests_per_minute,
                'average_response_time': round(avg_response_time, 3),
                'error_rate': round(error_rate, 2)
            }
            
        except Exception as e:
            logger.error(f"计算请求统计异常: {str(e)}")
            return {
                'requests_per_minute': 0,
                'average_response_time': 0.0,
                'error_rate': 0.0
            }
    
    def get_container_info(self, container_id):
        """
        获取容器详细信息
        
        Args:
            container_id: 容器ID
            
        Returns:
            dict: 容器信息
        """
        try:
            container = self.client.containers.get(container_id)
            container.reload()
            
            return {
                'id': container.id,
                'name': container.name,
                'status': container.status,
                'image': container.image.tags[0] if container.image.tags else container.image.id,
                'created': container.attrs['Created'],
                'started_at': container.attrs['State'].get('StartedAt'),
                'ports': container.attrs['NetworkSettings']['Ports'],
                'environment': container.attrs['Config']['Env'],
                'mounts': container.attrs['Mounts'],
                'network_settings': container.attrs['NetworkSettings'],
            }
            
        except docker.errors.NotFound:
            return None
        except Exception as e:
            logger.error(f"获取容器信息异常: {str(e)}")
            return None
    
    def health_check_container(self, container_id):
        """
        容器健康检查
        
        Args:
            container_id: 容器ID
            
        Returns:
            dict: 健康检查结果
        """
        try:
            container = self.client.containers.get(container_id)
            container.reload()
            
            # 检查容器状态
            is_running = container.status == 'running'
            
            # 检查容器健康状态（如果有健康检查配置）
            health_status = container.attrs['State'].get('Health', {}).get('Status')
            
            # 获取最近的日志
            logs = container.logs(tail=10).decode('utf-8', errors='ignore')
            
            return {
                'is_running': is_running,
                'health_status': health_status,
                'status': container.status,
                'recent_logs': logs,
                'last_check': timezone.now().isoformat()
            }
            
        except docker.errors.NotFound:
            return {
                'is_running': False,
                'health_status': 'not_found',
                'status': 'not_found',
                'recent_logs': '',
                'last_check': timezone.now().isoformat()
            }
        except Exception as e:
            logger.error(f"容器健康检查异常: {str(e)}")
            return {
                'is_running': False,
                'health_status': 'error',
                'status': 'error',
                'recent_logs': str(e),
                'last_check': timezone.now().isoformat()
            }


# 全局容器监控器实例
container_monitor = ContainerMonitor()
