from celery import shared_task
import logging
import requests
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Count, Avg, Q
from .models import ModelDeployment, ModelService, InferenceLog, ServiceMetrics
from .container_monitor import container_monitor
from .docker_manager import docker_manager

logger = logging.getLogger(__name__)


@shared_task
def cron_job_monitor_services():
    """
    监控所有运行中的服务
    """
    try:
        logger.info("开始监控服务状态")
        
        # 获取所有运行中的部署
        deployments = ModelDeployment.objects.filter(status='running')
        
        for deployment in deployments:
            try:
                # 检查容器状态
                if deployment.container_id:
                    success, error_msg, status_info = docker_manager.get_container_status(
                        deployment.container_id
                    )
                    
                    if success and status_info:
                        container_status = status_info['status']
                        
                        # 如果容器不是运行状态，更新部署状态
                        if container_status != 'running':
                            deployment.status = 'stopped' if container_status == 'exited' else 'error'
                            deployment.error_message = f"容器状态: {container_status}"
                            deployment.save()
                            
                            # 更新服务健康状态
                            if hasattr(deployment, 'service'):
                                deployment.service.is_healthy = False
                                deployment.service.save()
                            
                            logger.warning(f"部署 {deployment.deployment_name} 容器状态异常: {container_status}")
                            continue
                
                # 检查服务健康状态
                if hasattr(deployment, 'service'):
                    service = deployment.service
                    
                    try:
                        # 发送健康检查请求
                        response = requests.get(
                            service.health_check_url,
                            timeout=10
                        )
                        
                        if response.status_code == 200:
                            service.is_healthy = True
                            service.last_health_check = timezone.now()
                            service.save()
                        else:
                            service.is_healthy = False
                            service.last_health_check = timezone.now()
                            service.save()
                            logger.warning(f"服务 {service.service_name} 健康检查失败: HTTP {response.status_code}")
                            
                    except requests.exceptions.RequestException as e:
                        service.is_healthy = False
                        service.last_health_check = timezone.now()
                        service.save()
                        logger.warning(f"服务 {service.service_name} 健康检查异常: {str(e)}")
                
            except Exception as e:
                logger.error(f"监控部署 {deployment.deployment_name} 异常: {str(e)}")
        
        logger.info("服务状态监控完成")
        
    except Exception as e:
        logger.error(f"监控服务任务异常: {str(e)}")


@shared_task
def cron_job_collect_metrics():
    """
    收集服务性能指标
    """
    try:
        logger.info("开始收集服务指标")
        
        # 获取所有健康的服务
        services = ModelService.objects.filter(
            is_healthy=True,
            deployment__status='running'
        )
        
        for service in services:
            try:
                # 收集容器指标
                if service.deployment.container_id:
                    metrics = container_monitor._get_container_metrics(service)
                    
                    if metrics:
                        # 保存指标数据
                        ServiceMetrics.objects.create(
                            service=service,
                            timestamp=timezone.now(),
                            **metrics
                        )
                        logger.debug(f"收集服务 {service.service_name} 指标成功")
                
            except Exception as e:
                logger.error(f"收集服务 {service.service_name} 指标异常: {str(e)}")
        
        logger.info("服务指标收集完成")
        
    except Exception as e:
        logger.error(f"收集指标任务异常: {str(e)}")


@shared_task
def cron_job_cleanup_old_logs():
    """
    清理旧的推理日志
    """
    try:
        logger.info("开始清理旧日志")
        
        # 删除30天前的推理日志
        cutoff_date = timezone.now() - timedelta(days=30)
        deleted_count = InferenceLog.objects.filter(
            create_datetime__lt=cutoff_date
        ).delete()[0]
        
        logger.info(f"清理推理日志完成，删除 {deleted_count} 条记录")
        
        # 删除7天前的服务指标
        cutoff_date = timezone.now() - timedelta(days=7)
        deleted_count = ServiceMetrics.objects.filter(
            timestamp__lt=cutoff_date
        ).delete()[0]
        
        logger.info(f"清理服务指标完成，删除 {deleted_count} 条记录")
        
    except Exception as e:
        logger.error(f"清理旧日志任务异常: {str(e)}")


@shared_task
def cron_job_generate_daily_report():
    """
    生成每日服务报告
    """
    try:
        logger.info("开始生成每日报告")
        
        # 获取昨天的日期范围
        yesterday = timezone.now().date() - timedelta(days=1)
        start_time = timezone.make_aware(datetime.combine(yesterday, datetime.min.time()))
        end_time = timezone.make_aware(datetime.combine(yesterday, datetime.max.time()))
        
        # 统计昨天的推理请求
        daily_logs = InferenceLog.objects.filter(
            create_datetime__range=(start_time, end_time)
        )
        
        total_requests = daily_logs.count()
        successful_requests = daily_logs.filter(status='success').count()
        failed_requests = daily_logs.filter(status='failed').count()
        timeout_requests = daily_logs.filter(status='timeout').count()
        
        # 计算平均响应时间
        avg_response_time = daily_logs.aggregate(
            avg_time=Avg('response_time')
        )['avg_time'] or 0.0
        
        # 按服务统计
        service_stats = daily_logs.values('service__service_name').annotate(
            request_count=Count('id'),
            success_count=Count('id', filter=Q(status='success')),
            avg_response_time=Avg('response_time')
        ).order_by('-request_count')
        
        # 构建报告数据
        report_data = {
            'date': yesterday.isoformat(),
            'summary': {
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'timeout_requests': timeout_requests,
                'success_rate': round((successful_requests / total_requests * 100) if total_requests > 0 else 0, 2),
                'average_response_time': round(avg_response_time, 3)
            },
            'service_stats': list(service_stats),
            'generated_at': timezone.now().isoformat()
        }
        
        logger.info(f"每日报告生成完成: {report_data['summary']}")
        
        # 这里可以将报告发送到邮件、消息队列或保存到文件
        # 目前只记录到日志
        
    except Exception as e:
        logger.error(f"生成每日报告任务异常: {str(e)}")


@shared_task
def cron_job_cleanup_orphaned_temp_files():
    """
    清理临时文件
    """
    try:
        logger.info("开始清理临时文件")

        import tempfile
        import glob
        import os

        # 获取系统临时目录
        temp_base_dir = tempfile.gettempdir()

        # 查找所有model_*开头的临时目录
        pattern = os.path.join(temp_base_dir, "model_*")
        temp_dirs = glob.glob(pattern)

        cleaned_count = 0
        for temp_dir in temp_dirs:
            try:
                # 检查是否还有对应的部署记录在使用这个目录
                if not ModelDeployment.objects.filter(
                    temp_weights_dir=temp_dir,
                    status__in=['running', 'deploying']
                ).exists():
                    # 没有活跃的部署使用这个目录，可以清理
                    docker_manager.cleanup_model_directory(temp_dir)
                    cleaned_count += 1
                    logger.info(f"清理孤儿临时目录: {temp_dir}")

            except Exception as e:
                logger.warning(f"清理临时目录 {temp_dir} 失败: {str(e)}")

        logger.info(f"孤儿临时文件清理完成，共清理 {cleaned_count} 个目录")

    except Exception as e:
        logger.error(f"清理孤儿临时文件任务异常: {str(e)}")


@shared_task
def cron_job_auto_restart_failed_services():
    """
    自动重启失败的服务
    """
    try:
        logger.info("开始检查失败的服务")

        # 获取失败的部署（最近1小时内失败的）
        one_hour_ago = timezone.now() - timedelta(hours=1)
        failed_deployments = ModelDeployment.objects.filter(
            status='failed',
            update_datetime__gte=one_hour_ago
        )

        for deployment in failed_deployments:
            try:
                # 检查是否应该自动重启（可以根据配置决定）
                auto_restart = deployment.deploy_config.get('auto_restart', False)

                if auto_restart:
                    logger.info(f"尝试自动重启部署: {deployment.deployment_name}")

                    # 重新部署
                    deployment.status = 'deploying'
                    deployment.error_message = None
                    deployment.save()

                    # 这里可以调用部署逻辑
                    # 为了简化，这里只是更新状态
                    # 实际应该调用 docker_manager 重新创建容器
                    
            except Exception as e:
                logger.error(f"自动重启部署 {deployment.deployment_name} 异常: {str(e)}")
        
        logger.info("失败服务检查完成")
        
    except Exception as e:
        logger.error(f"自动重启失败服务任务异常: {str(e)}")


@shared_task
def cron_job_update_service_statistics():
    """
    更新服务统计信息
    """
    try:
        logger.info("开始更新服务统计")
        
        services = ModelService.objects.all()
        
        for service in services:
            try:
                # 重新计算统计信息
                logs = service.inference_logs.all()
                
                total_requests = logs.count()
                successful_requests = logs.filter(status='success').count()
                failed_requests = logs.filter(status='failed').count()
                
                # 计算平均响应时间
                avg_response_time = logs.aggregate(
                    avg_time=Avg('response_time')
                )['avg_time'] or 0.0
                
                # 更新服务统计
                service.total_requests = total_requests
                service.successful_requests = successful_requests
                service.failed_requests = failed_requests
                service.average_response_time = avg_response_time
                service.save()
                
            except Exception as e:
                logger.error(f"更新服务 {service.service_name} 统计异常: {str(e)}")
        
        logger.info("服务统计更新完成")
        
    except Exception as e:
        logger.error(f"更新服务统计任务异常: {str(e)}")
