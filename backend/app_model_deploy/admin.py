from django.contrib import admin
from .models import ModelDeployment, ModelService, InferenceLog, ServiceMetrics


@admin.register(ModelDeployment)
class ModelDeploymentAdmin(admin.ModelAdmin):
    list_display = ['deployment_name', 'model_version', 'status', 'service_port', 'deployed_by', 'deployed_at']
    list_filter = ['status', 'deployed_at', 'create_datetime']
    search_fields = ['deployment_name', 'model_version__model__name', 'container_name']
    readonly_fields = ['container_id', 'container_name', 'create_datetime', 'update_datetime']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('model_version', 'deployment_name', 'status', 'deployed_by')
        }),
        ('容器信息', {
            'fields': ('container_id', 'container_name', 'service_port', 'service_url')
        }),
        ('配置信息', {
            'fields': ('deploy_config', 'error_message')
        }),
        ('时间信息', {
            'fields': ('deployed_at', 'stopped_at', 'create_datetime', 'update_datetime')
        }),
    )


@admin.register(ModelService)
class ModelServiceAdmin(admin.ModelAdmin):
    list_display = ['service_name', 'deployment', 'is_healthy', 'total_requests', 'successful_requests', 'average_response_time']
    list_filter = ['is_healthy', 'create_datetime', 'last_health_check']
    search_fields = ['service_name', 'deployment__deployment_name']
    readonly_fields = ['create_datetime', 'update_datetime']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('deployment', 'service_name', 'is_healthy')
        }),
        ('服务配置', {
            'fields': ('api_endpoint', 'health_check_url', 'last_health_check')
        }),
        ('统计信息', {
            'fields': ('total_requests', 'successful_requests', 'failed_requests', 'average_response_time')
        }),
        ('时间信息', {
            'fields': ('create_datetime', 'update_datetime')
        }),
    )


@admin.register(InferenceLog)
class InferenceLogAdmin(admin.ModelAdmin):
    list_display = ['request_id', 'service', 'status', 'response_time', 'client_ip', 'create_datetime']
    list_filter = ['status', 'create_datetime', 'service']
    search_fields = ['request_id', 'service__service_name', 'client_ip']
    readonly_fields = ['create_datetime', 'update_datetime']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('service', 'request_id', 'status', 'response_time')
        }),
        ('请求信息', {
            'fields': ('input_data', 'output_data', 'error_message')
        }),
        ('客户端信息', {
            'fields': ('client_ip', 'user_agent')
        }),
        ('时间信息', {
            'fields': ('create_datetime', 'update_datetime')
        }),
    )


@admin.register(ServiceMetrics)
class ServiceMetricsAdmin(admin.ModelAdmin):
    list_display = ['service', 'timestamp', 'cpu_usage', 'memory_usage', 'requests_per_minute', 'error_rate']
    list_filter = ['timestamp', 'service']
    search_fields = ['service__service_name']
    readonly_fields = ['create_datetime', 'update_datetime']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('service', 'timestamp')
        }),
        ('资源使用', {
            'fields': ('cpu_usage', 'memory_usage', 'gpu_usage')
        }),
        ('性能指标', {
            'fields': ('requests_per_minute', 'average_response_time', 'error_rate')
        }),
        ('时间信息', {
            'fields': ('create_datetime', 'update_datetime')
        }),
    )
