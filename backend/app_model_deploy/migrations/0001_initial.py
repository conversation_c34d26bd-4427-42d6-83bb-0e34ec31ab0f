# Generated by Django 4.2.1 on 2025-07-23 19:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app_model', '0003_aimodel_datasets'),
    ]

    operations = [
        migrations.CreateModel(
            name='ModelDeployment',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('deployment_name', models.CharField(help_text='部署实例的名称，用于标识不同的部署', max_length=100, verbose_name='部署名称')),
                ('status', models.CharField(choices=[('pending', '待部署'), ('deploying', '部署中'), ('running', '运行中'), ('stopped', '已停止'), ('failed', '部署失败'), ('error', '运行异常')], default='pending', max_length=20, verbose_name='部署状态')),
                ('container_id', models.CharField(blank=True, help_text='Docker容器的ID', max_length=100, null=True, verbose_name='容器ID')),
                ('container_name', models.CharField(blank=True, help_text='Docker容器的名称', max_length=100, null=True, verbose_name='容器名称')),
                ('service_port', models.IntegerField(blank=True, help_text='推理服务监听的端口', null=True, verbose_name='服务端口')),
                ('service_url', models.URLField(blank=True, help_text='推理服务的访问地址', null=True, verbose_name='服务URL')),
                ('deploy_config', models.JSONField(default=dict, help_text='部署时的配置参数，如资源限制、环境变量等', verbose_name='部署配置')),
                ('error_message', models.TextField(blank=True, help_text='部署或运行时的错误信息', null=True, verbose_name='错误信息')),
                ('deployed_at', models.DateTimeField(blank=True, null=True, verbose_name='部署时间')),
                ('stopped_at', models.DateTimeField(blank=True, null=True, verbose_name='停止时间')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('deployed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deployed_models', to=settings.AUTH_USER_MODEL, verbose_name='部署人')),
                ('model_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deployments', to='app_model.modelversion', verbose_name='模型版本')),
            ],
            options={
                'verbose_name': '模型部署',
                'verbose_name_plural': '模型部署',
                'ordering': ['-create_datetime'],
                'unique_together': {('deployment_name',)},
            },
        ),
        migrations.CreateModel(
            name='ModelService',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('service_name', models.CharField(max_length=100, unique=True, verbose_name='服务名称')),
                ('api_endpoint', models.URLField(help_text='推理服务的API地址', verbose_name='API端点')),
                ('health_check_url', models.URLField(help_text='服务健康检查的地址', verbose_name='健康检查URL')),
                ('is_healthy', models.BooleanField(default=False, verbose_name='服务健康状态')),
                ('last_health_check', models.DateTimeField(blank=True, null=True, verbose_name='最后健康检查时间')),
                ('total_requests', models.IntegerField(default=0, verbose_name='总请求次数')),
                ('successful_requests', models.IntegerField(default=0, verbose_name='成功请求次数')),
                ('failed_requests', models.IntegerField(default=0, verbose_name='失败请求次数')),
                ('average_response_time', models.FloatField(default=0.0, verbose_name='平均响应时间(秒)')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('deployment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='service', to='app_model_deploy.modeldeployment', verbose_name='关联部署')),
            ],
            options={
                'verbose_name': '模型服务',
                'verbose_name_plural': '模型服务',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='ServiceMetrics',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('timestamp', models.DateTimeField(verbose_name='时间戳')),
                ('cpu_usage', models.FloatField(blank=True, null=True, verbose_name='CPU使用率(%)')),
                ('memory_usage', models.FloatField(blank=True, null=True, verbose_name='内存使用率(%)')),
                ('gpu_usage', models.FloatField(blank=True, null=True, verbose_name='GPU使用率(%)')),
                ('requests_per_minute', models.IntegerField(default=0, verbose_name='每分钟请求数')),
                ('average_response_time', models.FloatField(default=0.0, verbose_name='平均响应时间(秒)')),
                ('error_rate', models.FloatField(default=0.0, verbose_name='错误率(%)')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='app_model_deploy.modelservice', verbose_name='关联服务')),
            ],
            options={
                'verbose_name': '服务指标',
                'verbose_name_plural': '服务指标',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['service', '-timestamp'], name='app_model_d_service_2b1dc1_idx')],
            },
        ),
        migrations.CreateModel(
            name='InferenceLog',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('request_id', models.CharField(max_length=100, unique=True, verbose_name='请求ID')),
                ('input_data', models.JSONField(help_text='推理请求的输入数据', verbose_name='输入数据')),
                ('output_data', models.JSONField(blank=True, help_text='推理结果数据', null=True, verbose_name='输出数据')),
                ('response_time', models.FloatField(verbose_name='响应时间(秒)')),
                ('status', models.CharField(choices=[('success', '成功'), ('failed', '失败'), ('timeout', '超时')], max_length=20, verbose_name='请求状态')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('client_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='客户端IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='用户代理')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inference_logs', to='app_model_deploy.modelservice', verbose_name='关联服务')),
            ],
            options={
                'verbose_name': '推理日志',
                'verbose_name_plural': '推理日志',
                'ordering': ['-create_datetime'],
                'indexes': [models.Index(fields=['service', '-create_datetime'], name='app_model_d_service_c503b5_idx'), models.Index(fields=['status', '-create_datetime'], name='app_model_d_status_abce13_idx')],
            },
        ),
    ]
