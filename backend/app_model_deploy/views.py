import uuid
import logging
import requests
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.conf import settings
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from .models import ModelDeployment, ModelService, InferenceLog, ServiceMetrics
from .serializers import (
    ModelDeploymentSerializer, ModelServiceSerializer, InferenceLogSerializer,
    ServiceMetricsSerializer, DeploymentCreateSerializer, InferenceRequestSerializer,
    InferenceResponseSerializer, ServiceStatsSerializer, DeploymentStatsSerializer
)
from .docker_manager import docker_manager
from .inference_client import get_inference_client
from app_model.models import ModelVersion
from utils.viewset import CustomModelViewSet
from rest_framework.viewsets import ViewSet
from utils.json_response import DetailResponse, SuccessResponse, ErrorResponse

logger = logging.getLogger(__name__)


class ModelDeploymentViewSet(CustomModelViewSet):
    """
    模型部署管理视图集
    """
    queryset = ModelDeployment.objects.all()
    serializer_class = ModelDeploymentSerializer
    filterset_fields = ['status', 'model_version', 'deployed_by']
    search_fields = ['deployment_name', 'model_version__model__name']
    ordering_fields = ['create_datetime', 'deployed_at']

    def get_queryset(self):
        """优化查询，预加载关联对象"""
        return ModelDeployment.objects.select_related(
            'model_version',
            'model_version__model',
            'deployed_by',
            'service'
        ).prefetch_related(
            'service__metrics'
        ).order_by('-create_datetime')
    
    @action(methods=['POST'], detail=False)
    def deploy(self, request):
        """
        部署模型
        """
        serializer = DeploymentCreateSerializer(data=request.data)
        if not serializer.is_valid():
            return ErrorResponse(msg="参数验证失败", data=serializer.errors)
        
        data = serializer.validated_data
        model_version_id = data['model_version_id']
        deployment_name = data['deployment_name']
        service_port = data.get('service_port')
        deploy_config = data.get('deploy_config', {})
        
        try:
            # 获取模型版本
            model_version = get_object_or_404(ModelVersion, id=model_version_id)
            
            # 检查模型版本是否有Docker镜像
            if not model_version.docker_image:
                return ErrorResponse(msg="模型版本缺少Docker镜像信息")
            
            # 自动分配端口（如果未指定）
            if not service_port:
                service_port = self._get_available_port()
            
            # 创建部署记录
            deployment = ModelDeployment.objects.create(
                model_version=model_version,
                deployment_name=deployment_name,
                status='deploying',
                service_port=service_port,
                deploy_config=deploy_config,
                deployed_by=request.user,
                deployed_at=timezone.now()
            )
            
            # 异步部署（这里简化为同步）
            success, container_id, error_msg, container_name, temp_dir = self._deploy_model(deployment)

            if success:
                deployment.status = 'running'
                deployment.container_id = container_id
                deployment.container_name = container_name
                deployment.temp_weights_dir = temp_dir
                deployment.service_url = f"http://{settings.MODEL_DEPLOY_CONFIG['SERVER_HOST']}:{service_port}"
                deployment.save()

                # 创建服务记录
                service = ModelService.objects.create(
                    deployment=deployment,
                    service_name=deployment.container_name,
                    api_endpoint=f"{deployment.service_url}/predict",
                    health_check_url=f"{deployment.service_url}/health"
                )
                
                return DetailResponse(
                    data=ModelDeploymentSerializer(deployment).data,
                    msg="模型部署成功"
                )
            else:
                deployment.status = 'failed'
                deployment.error_message = error_msg
                deployment.save()
                return ErrorResponse(msg=f"模型部署失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"部署模型异常: {str(e)}")
            return ErrorResponse(msg=f"部署异常: {str(e)}")
    
    @action(methods=['POST'], detail=True)
    def stop(self, request, pk=None):
        """
        停止部署
        """
        deployment = self.get_object()
        
        if deployment.status != 'running':
            return ErrorResponse(msg="只能停止运行中的部署")
        
        try:
            # 停止容器
            if deployment.container_id:
                success, error_msg = docker_manager.stop_container(deployment.container_id)
                if not success:
                    return ErrorResponse(msg=f"停止容器失败: {error_msg}")

            # 清理临时权重文件
            if deployment.temp_weights_dir:
                try:
                    docker_manager.cleanup_model_directory(deployment.temp_weights_dir)
                    logger.info(f"清理临时权重目录: {deployment.temp_weights_dir}")
                except Exception as e:
                    logger.warning(f"清理临时权重目录失败: {str(e)}")

            # 更新状态
            deployment.status = 'stopped'
            deployment.temp_weights_dir = None  # 清空临时目录记录
            deployment.stopped_at = timezone.now()
            deployment.save()
            
            # 更新服务状态
            if hasattr(deployment, 'service'):
                deployment.service.is_healthy = False
                deployment.service.save()
            
            return SuccessResponse(msg="部署停止成功")
            
        except Exception as e:
            logger.error(f"停止部署异常: {str(e)}")
            return ErrorResponse(msg=f"停止异常: {str(e)}")
    
    @action(methods=['POST'], detail=True)
    def restart(self, request, pk=None):
        """
        重启部署
        """
        deployment = self.get_object()
        
        if deployment.status not in ['stopped', 'failed', 'error']:
            return ErrorResponse(msg="只能重启已停止或失败的部署")
        
        try:
            # 重新部署
            deployment.status = 'deploying'
            deployment.error_message = None
            deployment.save()
            
            success, container_id, error_msg, container_name, temp_dir = self._deploy_model(deployment)

            if success:
                deployment.status = 'running'
                deployment.container_id = container_id
                deployment.container_name = container_name
                deployment.temp_weights_dir = temp_dir
                deployment.deployed_at = timezone.now()
                deployment.save()
                
                # 更新服务状态
                if hasattr(deployment, 'service'):
                    deployment.service.is_healthy = True
                    deployment.service.save()
                
                return SuccessResponse(msg="部署重启成功")
            else:
                deployment.status = 'failed'
                deployment.error_message = error_msg
                deployment.save()
                return ErrorResponse(msg=f"重启失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"重启部署异常: {str(e)}")
            return ErrorResponse(msg=f"重启异常: {str(e)}")
    
    @action(methods=['DELETE'], detail=True)
    def remove(self, request, pk=None):
        """
        删除部署
        """
        deployment = self.get_object()
        
        try:
            # 先停止容器
            if deployment.container_id and deployment.status == 'running':
                docker_manager.stop_container(deployment.container_id)

            # 删除容器
            if deployment.container_id:
                docker_manager.remove_container(deployment.container_id)

            # 清理临时权重文件
            if deployment.temp_weights_dir:
                try:
                    docker_manager.cleanup_model_directory(deployment.temp_weights_dir)
                    logger.info(f"清理临时权重目录: {deployment.temp_weights_dir}")
                except Exception as e:
                    logger.warning(f"清理临时权重目录失败: {str(e)}")
            
            # 删除部署记录
            deployment.delete()
            
            return SuccessResponse(msg="部署删除成功")
            
        except Exception as e:
            logger.error(f"删除部署异常: {str(e)}")
            return ErrorResponse(msg=f"删除异常: {str(e)}")
    
    @action(methods=['GET'], detail=True)
    def logs(self, request, pk=None):
        """
        获取部署日志
        """
        deployment = self.get_object()
        
        if not deployment.container_id:
            return ErrorResponse(msg="容器不存在")
        
        try:
            tail = int(request.query_params.get('tail', 100))
            success, error_msg, logs = docker_manager.get_container_logs(
                deployment.container_id, tail=tail
            )
            
            if success:
                return DetailResponse(data={'logs': logs}, msg="获取日志成功")
            else:
                return ErrorResponse(msg=f"获取日志失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"获取日志异常: {str(e)}")
            return ErrorResponse(msg=f"获取日志异常: {str(e)}")
    
    @action(methods=['GET'], detail=True)
    def status(self, request, pk=None):
        """
        获取部署状态
        """
        deployment = self.get_object()
        
        if not deployment.container_id:
            return DetailResponse(
                data={'status': deployment.status, 'container_status': None},
                msg="获取状态成功"
            )
        
        try:
            success, error_msg, status_info = docker_manager.get_container_status(
                deployment.container_id
            )
            
            if success:
                return DetailResponse(
                    data={
                        'status': deployment.status,
                        'container_status': status_info
                    },
                    msg="获取状态成功"
                )
            else:
                return ErrorResponse(msg=f"获取状态失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"获取状态异常: {str(e)}")
            return ErrorResponse(msg=f"获取状态异常: {str(e)}")
    
    @action(methods=['GET'], detail=False)
    def stats(self, request):
        """
        获取部署统计信息
        """
        try:
            total_deployments = ModelDeployment.objects.count()
            running_deployments = ModelDeployment.objects.filter(status='running').count()
            stopped_deployments = ModelDeployment.objects.filter(status='stopped').count()
            failed_deployments = ModelDeployment.objects.filter(status='failed').count()
            total_services = ModelService.objects.count()
            healthy_services = ModelService.objects.filter(is_healthy=True).count()
            
            stats_data = {
                'total_deployments': total_deployments,
                'running_deployments': running_deployments,
                'stopped_deployments': stopped_deployments,
                'failed_deployments': failed_deployments,
                'total_services': total_services,
                'healthy_services': healthy_services,
            }
            
            serializer = DeploymentStatsSerializer(data=stats_data)
            serializer.is_valid(raise_exception=True)
            
            return DetailResponse(data=serializer.data, msg="获取统计信息成功")
            
        except Exception as e:
            logger.error(f"获取统计信息异常: {str(e)}")
            return ErrorResponse(msg=f"获取统计信息异常: {str(e)}")
    
    def _deploy_model(self, deployment):
        """
        执行模型部署
        """
        try:
            # 拉取镜像
            if not docker_manager.pull_image(deployment.model_version.docker_image):
                return False, None, "拉取Docker镜像失败"
            
            # 创建容器（添加时间戳确保唯一性）
            import time
            timestamp = int(time.time())
            container_name = f"model_{deployment.id}_{deployment.deployment_name}_{timestamp}"
            success, container_id, error_msg, temp_dir = docker_manager.create_container(
                image_name=deployment.model_version.docker_image,
                container_name=container_name,
                model_version=deployment.model_version,
                service_port=deployment.service_port,
                deploy_config=deployment.deploy_config
            )

            return success, container_id, error_msg, container_name, temp_dir
            
        except Exception as e:
            return False, None, str(e), None
    
    def _get_available_port(self):
        """
        获取可用端口
        """
        import socket

        # 从8000开始查找可用端口
        for port in range(8000, 9000):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('0.0.0.0', port))
                    return port
            except OSError:
                continue

        # 如果没有找到可用端口，返回随机端口
        return 8888


class ModelServiceViewSet(CustomModelViewSet):
    """
    模型服务管理视图集
    """
    queryset = ModelService.objects.all()
    serializer_class = ModelServiceSerializer
    filterset_fields = ['is_healthy', 'deployment__status']
    search_fields = ['service_name', 'deployment__model_version__model__name']
    ordering_fields = ['create_datetime', 'last_health_check']

    @action(methods=['POST'], detail=True)
    def health_check(self, request, pk=None):
        """
        健康检查
        """
        service = self.get_object()

        try:
            # 发送健康检查请求
            response = requests.get(
                service.health_check_url,
                timeout=10
            )

            if response.status_code == 200:
                service.is_healthy = True
                service.last_health_check = timezone.now()
                service.save()
                return SuccessResponse(msg="服务健康")
            else:
                service.is_healthy = False
                service.last_health_check = timezone.now()
                service.save()
                return ErrorResponse(msg=f"服务不健康，状态码: {response.status_code}")

        except requests.exceptions.RequestException as e:
            service.is_healthy = False
            service.last_health_check = timezone.now()
            service.save()
            return ErrorResponse(msg=f"健康检查失败: {str(e)}")

    @action(methods=['POST'], detail=True)
    def predict(self, request, pk=None):
        """
        模型推理
        """
        service = self.get_object()

        # 验证输入数据
        serializer = InferenceRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return ErrorResponse(msg="输入数据验证失败", data=serializer.errors)

        input_data = serializer.validated_data['input_data']
        request_id = str(uuid.uuid4())

        # 记录开始时间
        start_time = timezone.now()

        try:
            # 发送推理请求
            response = requests.post(
                service.api_endpoint,
                json=input_data,
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )

            # 计算响应时间
            end_time = timezone.now()
            response_time = (end_time - start_time).total_seconds()

            # 更新服务统计
            service.total_requests += 1

            if response.status_code == 200:
                output_data = response.json()
                service.successful_requests += 1

                # 更新平均响应时间
                service.average_response_time = (
                    (service.average_response_time * (service.total_requests - 1) + response_time) /
                    service.total_requests
                )

                service.save()

                # 记录推理日志
                InferenceLog.objects.create(
                    service=service,
                    request_id=request_id,
                    input_data=input_data,
                    output_data=output_data,
                    response_time=response_time,
                    status='success',
                    client_ip=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )

                # 返回推理结果
                response_data = {
                    'request_id': request_id,
                    'status': 'success',
                    'output_data': output_data,
                    'response_time': response_time
                }

                response_serializer = InferenceResponseSerializer(data=response_data)
                response_serializer.is_valid(raise_exception=True)

                return DetailResponse(data=response_serializer.data, msg="推理成功")
            else:
                service.failed_requests += 1
                service.save()

                error_message = f"推理服务返回错误，状态码: {response.status_code}"

                # 记录失败日志
                InferenceLog.objects.create(
                    service=service,
                    request_id=request_id,
                    input_data=input_data,
                    response_time=response_time,
                    status='failed',
                    error_message=error_message,
                    client_ip=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )

                return ErrorResponse(msg=error_message)

        except requests.exceptions.Timeout:
            service.failed_requests += 1
            service.save()

            error_message = "推理请求超时"

            # 记录超时日志
            InferenceLog.objects.create(
                service=service,
                request_id=request_id,
                input_data=input_data,
                response_time=30.0,
                status='timeout',
                error_message=error_message,
                client_ip=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            return ErrorResponse(msg=error_message)

        except requests.exceptions.RequestException as e:
            service.failed_requests += 1
            service.save()

            error_message = f"推理请求失败: {str(e)}"

            # 记录错误日志
            InferenceLog.objects.create(
                service=service,
                request_id=request_id,
                input_data=input_data,
                response_time=(timezone.now() - start_time).total_seconds(),
                status='failed',
                error_message=error_message,
                client_ip=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            return ErrorResponse(msg=error_message)

    @action(methods=['GET'], detail=True)
    def stats(self, request, pk=None):
        """
        获取服务统计信息
        """
        service = self.get_object()

        try:
            # 计算每小时请求数（最近24小时）
            last_24_hours = timezone.now() - timedelta(hours=24)
            recent_logs = InferenceLog.objects.filter(
                service=service,
                create_datetime__gte=last_24_hours
            )
            requests_per_hour = recent_logs.count()

            # 计算成功率
            success_rate = 0.0
            if service.total_requests > 0:
                success_rate = (service.successful_requests / service.total_requests) * 100

            stats_data = {
                'total_requests': service.total_requests,
                'successful_requests': service.successful_requests,
                'failed_requests': service.failed_requests,
                'success_rate': round(success_rate, 2),
                'average_response_time': round(service.average_response_time, 3),
                'requests_per_hour': requests_per_hour,
            }

            serializer = ServiceStatsSerializer(data=stats_data)
            serializer.is_valid(raise_exception=True)

            return DetailResponse(data=serializer.data, msg="获取统计信息成功")

        except Exception as e:
            logger.error(f"获取服务统计信息异常: {str(e)}")
            return ErrorResponse(msg=f"获取统计信息异常: {str(e)}")

    @action(methods=['GET'], detail=True)
    def logs(self, request, pk=None):
        """
        获取推理日志
        """
        service = self.get_object()

        try:
            # 获取查询参数
            page_size = int(request.query_params.get('page_size', 20))
            page = int(request.query_params.get('page', 1))
            status_filter = request.query_params.get('status')

            # 构建查询
            queryset = InferenceLog.objects.filter(service=service)
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # 分页
            start = (page - 1) * page_size
            end = start + page_size
            logs = queryset.order_by('-create_datetime')[start:end]
            total = queryset.count()

            serializer = InferenceLogSerializer(logs, many=True)

            return DetailResponse(
                data={
                    'logs': serializer.data,
                    'total': total,
                    'page': page,
                    'page_size': page_size
                },
                msg="获取推理日志成功"
            )

        except Exception as e:
            logger.error(f"获取推理日志异常: {str(e)}")
            return ErrorResponse(msg=f"获取推理日志异常: {str(e)}")

    def _get_client_ip(self, request):
        """
        获取客户端IP地址
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class InferenceLogViewSet(CustomModelViewSet):
    """
    推理日志视图集
    """
    queryset = InferenceLog.objects.all()
    serializer_class = InferenceLogSerializer
    filterset_fields = ['status', 'service']
    search_fields = ['request_id', 'service__service_name']
    ordering_fields = ['create_datetime', 'response_time']


class ServiceMetricsViewSet(CustomModelViewSet):
    """
    服务指标视图集
    """
    queryset = ServiceMetrics.objects.all()
    serializer_class = ServiceMetricsSerializer
    filterset_fields = ['service']
    ordering_fields = ['timestamp']

    @action(methods=['GET'], detail=False)
    def chart_data(self, request):
        """
        获取图表数据
        """
        try:
            service_id = request.query_params.get('service_id')
            hours = int(request.query_params.get('hours', 24))

            if not service_id:
                return ErrorResponse(msg="缺少service_id参数")

            # 获取指定时间范围内的指标数据
            start_time = timezone.now() - timedelta(hours=hours)
            metrics = ServiceMetrics.objects.filter(
                service_id=service_id,
                timestamp__gte=start_time
            ).order_by('timestamp')

            # 构建图表数据
            chart_data = {
                'timestamps': [],
                'cpu_usage': [],
                'memory_usage': [],
                'gpu_usage': [],
                'requests_per_minute': [],
                'average_response_time': [],
                'error_rate': []
            }

            for metric in metrics:
                chart_data['timestamps'].append(metric.timestamp.isoformat())
                chart_data['cpu_usage'].append(metric.cpu_usage)
                chart_data['memory_usage'].append(metric.memory_usage)
                chart_data['gpu_usage'].append(metric.gpu_usage)
                chart_data['requests_per_minute'].append(metric.requests_per_minute)
                chart_data['average_response_time'].append(metric.average_response_time)
                chart_data['error_rate'].append(metric.error_rate)

            return DetailResponse(data=chart_data, msg="获取图表数据成功")

        except Exception as e:
            logger.error(f"获取图表数据异常: {str(e)}")
            return ErrorResponse(msg=f"获取图表数据异常: {str(e)}")


class InferenceViewSet(ViewSet):
    """
    通用推理接口视图集
    """

    @action(methods=['POST'], detail=False, url_path='predict/(?P<service_id>[^/.]+)')
    def predict(self, request, service_id=None):
        """
        通用推理接口
        """
        try:
            # 获取推理客户端
            client = get_inference_client(int(service_id))
            if not client:
                return ErrorResponse(msg="推理服务不存在或不可用")

            # 检查是否上传了图像文件
            if 'image' not in request.FILES:
                return ErrorResponse(msg="缺少图像文件")

            image_file = request.FILES['image']
            if image_file.size == 0:
                return ErrorResponse(msg="图像文件为空")

            # 获取推理参数
            parameters = {}
            for key, value in request.POST.items():
                if key != 'image':
                    try:
                        # 尝试转换为数字
                        if '.' in value:
                            parameters[key] = float(value)
                        else:
                            parameters[key] = int(value)
                    except ValueError:
                        parameters[key] = value

            # 获取客户端信息
            client_ip = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            # 执行推理
            result = client.predict_image(
                image_file=image_file,
                parameters=parameters,
                client_ip=client_ip,
                user_agent=user_agent
            )

            if result['success']:
                # 将额外数据合并到主数据中
                response_data = result['data'].copy()
                response_data.update({
                    'request_id': result['request_id'],
                    'response_time': result['response_time']
                })

                return DetailResponse(
                    data=response_data,
                    msg="推理成功"
                )
            else:
                return ErrorResponse(
                    msg=result['error'],
                    data={
                        'request_id': result['request_id'],
                        'response_time': result['response_time']
                    }
                )

        except Exception as e:
            logger.error(f"推理接口异常: {str(e)}")
            return ErrorResponse(msg=f"推理异常: {str(e)}")

    @action(methods=['POST'], detail=False, url_path='batch_predict/(?P<service_id>[^/.]+)')
    def batch_predict(self, request, service_id=None):
        """
        批量推理接口
        """
        try:
            # 获取推理客户端
            client = get_inference_client(int(service_id))
            if not client:
                return ErrorResponse(msg="推理服务不存在或不可用")

            # 检查是否上传了图像文件
            if 'images' not in request.FILES:
                return ErrorResponse(msg="缺少图像文件")

            image_files = request.FILES.getlist('images')
            if not image_files:
                return ErrorResponse(msg="未选择图像文件")

            # 限制批量大小
            max_batch_size = 10
            if len(image_files) > max_batch_size:
                return ErrorResponse(msg=f"批量大小不能超过{max_batch_size}个文件")

            # 获取推理参数
            parameters = {}
            for key, value in request.POST.items():
                if key != 'images':
                    try:
                        # 尝试转换为数字
                        if '.' in value:
                            parameters[key] = float(value)
                        else:
                            parameters[key] = int(value)
                    except ValueError:
                        parameters[key] = value

            # 获取客户端信息
            client_ip = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            # 执行批量推理
            result = client.predict_batch(
                image_files=image_files,
                parameters=parameters,
                client_ip=client_ip,
                user_agent=user_agent
            )

            if result['success']:
                # 将额外数据合并到主数据中
                response_data = result['data'].copy()
                response_data.update({
                    'request_id': result['request_id'],
                    'response_time': result['response_time'],
                    'batch_size': result['batch_size'],
                    'successful_count': result.get('successful_count', 0),
                    'failed_count': result.get('failed_count', 0)
                })

                return DetailResponse(
                    data=response_data,
                    msg="批量推理成功"
                )
            else:
                return ErrorResponse(
                    msg=result['error'],
                    data={
                        'request_id': result['request_id'],
                        'response_time': result['response_time'],
                        'batch_size': result['batch_size']
                    }
                )

        except Exception as e:
            logger.error(f"批量推理接口异常: {str(e)}")
            return ErrorResponse(msg=f"批量推理异常: {str(e)}")

    @action(methods=['GET'], detail=False, url_path='health/(?P<service_id>[^/.]+)')
    def health_check(self, request, service_id=None):
        """
        服务健康检查接口
        """
        try:
            # 获取推理客户端
            client = get_inference_client(int(service_id))
            if not client:
                return ErrorResponse(msg="推理服务不存在或不可用")

            # 执行健康检查
            result = client.health_check()

            if result['status'] == 'healthy':
                # 将额外数据合并到主数据中
                response_data = result['data'].copy()
                response_data.update({
                    'response_time': result['response_time']
                })

                return DetailResponse(
                    data=response_data,
                    msg="服务健康"
                )
            else:
                return ErrorResponse(
                    msg=f"服务不健康: {result.get('error', '未知错误')}",
                    data=result
                )

        except Exception as e:
            logger.error(f"健康检查异常: {str(e)}")
            return ErrorResponse(msg=f"健康检查异常: {str(e)}")

    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
