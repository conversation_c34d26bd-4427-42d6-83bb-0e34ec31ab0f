from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import (
    ModelDeploymentViewSet, ModelServiceViewSet,
    InferenceLogViewSet, ServiceMetricsViewSet, InferenceViewSet
)

router = DefaultRouter()
router.register(r'deployments', ModelDeploymentViewSet)
router.register(r'services', ModelServiceViewSet)
router.register(r'logs', InferenceLogViewSet)
router.register(r'metrics', ServiceMetricsViewSet)
router.register(r'inference', InferenceViewSet, basename='inference')

app_name = 'app_model_deploy'

urlpatterns = [
    path('', include(router.urls)),
]
