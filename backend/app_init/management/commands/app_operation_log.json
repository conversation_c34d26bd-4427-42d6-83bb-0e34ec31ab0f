[{"model": "app_operation_log.operationlog", "pk": 43244332928, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:14:53.063", "create_datetime": "2024-01-13T15:14:52.702", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '3', 'captcha<PERSON>ey': 1}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43245469888, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:15:10.486", "create_datetime": "2024-01-13T15:15:10.467", "request_modular": "系统-API接口表", "request_path": "/system/apis/468065838100721,406867697900673,393728528800493,306531874800395,224732935200927/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43255655296, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:17:49.632", "create_datetime": "2024-01-13T15:17:49.614", "request_modular": "系统-菜单表", "request_path": "/system/menu/230490852200003/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '删除失败：该条数据与其他数据有相关绑定,建议将与其绑定的数据解除绑定'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 43256005952, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:17:55.115", "create_datetime": "2024-01-13T15:17:55.093", "request_modular": "系统-菜单表", "request_path": "/system/menu/542592780006850/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43256249280, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:17:58.918", "create_datetime": "2024-01-13T15:17:58.895", "request_modular": "系统-菜单表", "request_path": "/system/menu/542592965080514/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43256447616, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:18:02.019", "create_datetime": "2024-01-13T15:18:01.994", "request_modular": "系统-菜单表", "request_path": "/system/menu/542593010429329/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43256677632, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:18:05.605", "create_datetime": "2024-01-13T15:18:05.588", "request_modular": "系统-菜单表", "request_path": "/system/menu/230490852200003/", "request_body": "{}", "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '删除成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43271213376, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:21:53.257", "create_datetime": "2024-01-13T15:21:52.709", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '5', 'captcha<PERSON>ey': 2}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43274229120, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:22:39.847", "create_datetime": "2024-01-13T15:22:39.830", "request_modular": "系统-岗位表", "request_path": "/system/post/", "request_body": "{'post_name': '测试', 'post_code': 'TEST', 'sort': 3, 'status': '1'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43286451904, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:25:50.828", "create_datetime": "2024-01-13T15:25:50.811", "request_modular": "系统-菜单表", "request_path": "/system/menu/", "request_body": "{'parentId': 458000302592, 'menu_type': 'C', 'parent': 457773413824, 'menu_name': '系统日志', 'sort': 2, 'icon': 'elementList', 'permission': 'log:system:list', 'path': '/system/system-log/', 'component': '/log/system/index', 'status': '0', 'is_keep_alive': '1', 'is_iframe': '1', 'is_hide': '0', 'is_affix': '1'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43288007296, "fields": {"creator": null, "modifier": null, "update_datetime": "2024-01-13T15:26:15.135", "create_datetime": "2024-01-13T15:26:15.116", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '3', 'captcha<PERSON>ey': 3}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 43288419648, "fields": {"creator": null, "modifier": null, "update_datetime": "2024-01-13T15:26:21.577", "create_datetime": "2024-01-13T15:26:21.557", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '30', 'captcha<PERSON>ey': 4}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "400", "request_os": "Windows 10", "json_result": "{'code': 400, 'msg': '图片验证码错误'}", "status": false}}, {"model": "app_operation_log.operationlog", "pk": 43288765952, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:26:27.481", "create_datetime": "2024-01-13T15:26:26.968", "request_modular": "登录模块", "request_path": "/login/", "request_body": "{'username': 'paopao', 'password': '******', 'captcha': '7', 'captcha<PERSON>ey': 5}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '请求成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43314053760, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:33:02.134", "create_datetime": "2024-01-13T15:33:02.090", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/", "request_body": "{'type': 0, 'name': '间隔任务-加法', 'task': 'app_crontab.tasks.cron_job_add', 'kwargs': {'x': 5, 'y': 6}, 'one_off': False, 'enabled': True, 'interval': {'period': 'seconds', 'every': 10}}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '添加成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43317758912, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:34:00.023", "create_datetime": "2024-01-13T15:33:59.983", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/enabled/2/", "request_body": "{'enabled': True}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '修改成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43321193088, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:34:53.683", "create_datetime": "2024-01-13T15:34:53.642", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/", "request_body": "{'task': 'app_crontab.tasks.cron_job_mul', 'type': 1, 'name': '周期任务-乘法', 'crontab': '* * * * *', 'kwargs': {'x': 5, 'y': 6}, 'one_off': False, 'enabled': True, 'interval': {'period': None, 'every': None}}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '添加成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43321404224, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:34:56.979", "create_datetime": "2024-01-13T15:34:56.941", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/enabled/3/", "request_body": "{'enabled': True}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '修改成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43325527488, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:36:01.404", "create_datetime": "2024-01-13T15:36:01.367", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/", "request_body": "{'task': 'app_crontab.tasks.cron_job_test', 'name': '间隔任务-测试任务', 'type': 1, 'crontab': '37 15 * * *', 'one_off': True, 'enabled': True, 'interval': {'period': None, 'every': None}}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '添加成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43325760960, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:36:05.048", "create_datetime": "2024-01-13T15:36:05.015", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/enabled/4/", "request_body": "{'enabled': True}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '修改成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43330263168, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:37:15.404", "create_datetime": "2024-01-13T15:37:15.362", "request_modular": "消息中心", "request_path": "/system/message-center/", "request_body": "{'target_type': '0', 'target_user': [252375424640, 234457355328, 541150219354505], 'title': '发给用户的', 'content': '<p><strong>测试水水水水水水水水水水水水撒啊啊啊啊啊啊</strong></p>'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43331629824, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:37:36.768", "create_datetime": "2024-01-13T15:37:36.716", "request_modular": "消息中心", "request_path": "/system/message-center/", "request_body": "{'content': '<p>撒啊水水水水水水水水说法伽师</p>', 'target_type': '1', 'target_role': [540775921959829, 444421914176], 'title': '发给角色的'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43332797056, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:37:55.006", "create_datetime": "2024-01-13T15:37:54.954", "request_modular": "消息中心", "request_path": "/system/message-center/", "request_body": "{'content': '<p>啊啊啊啊啊啊啊啊啊啊啊啊啊</p>', 'target_type': '2', 'target_dept': [10414461824, 376960140500837, 94609026700594, 114667862200894, 57407999600609, 42188673800208], 'title': '发给部门的'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43333810624, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:38:10.828", "create_datetime": "2024-01-13T15:38:10.791", "request_modular": "消息中心", "request_path": "/system/message-center/", "request_body": "{'content': '<p>这是一份公告</p>', 'target_type': '3', 'title': '公告'}", "request_method": "POST", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '新增成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43335737024, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:38:40.924", "create_datetime": "2024-01-13T15:38:40.891", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/enabled/2/", "request_body": "{'enabled': False}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '修改成功'}", "status": true}}, {"model": "app_operation_log.operationlog", "pk": 43336094144, "fields": {"creator": 541150219354505, "modifier": null, "update_datetime": "2024-01-13T15:38:46.508", "create_datetime": "2024-01-13T15:38:46.471", "request_modular": "周期性任务", "request_path": "/job/crontab/periodic-task/enabled/3/", "request_body": "{'enabled': False}", "request_method": "PUT", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 119.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": "{'code': 200, 'msg': '修改成功'}", "status": true}}]