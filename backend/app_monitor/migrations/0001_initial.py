# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.db import migrations, models
import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MonitorManage',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('ip', models.CharField(blank=True, help_text='服务器IP', max_length=50, null=True, verbose_name='服务器IP')),
                ('name', models.CharField(blank=True, help_text='名称', max_length=50, null=True, verbose_name='名称')),
                ('os', models.CharField(blank=True, help_text='系统名称', max_length=50, null=True, verbose_name='系统名称')),
                ('online', models.BooleanField(default=False, help_text='在线状态', verbose_name='在线状态')),
                ('status', models.BooleanField(default=True, help_text='监控状态', verbose_name='监控状态')),
                ('days', models.SmallIntegerField(default=30, help_text='日志保留天数', verbose_name='日志保留天数')),
                ('interval', models.SmallIntegerField(default=5, help_text='监控日志刷新间隔', verbose_name='监控日志刷新间隔')),
                ('islocal', models.BooleanField(default=False, help_text='是否是本机监控', verbose_name='是否是本机监控')),
            ],
            options={
                'verbose_name': '服务监控',
                'verbose_name_plural': '服务监控',
                'db_table': 'sys_monitor',
            },
        ),
    ]
