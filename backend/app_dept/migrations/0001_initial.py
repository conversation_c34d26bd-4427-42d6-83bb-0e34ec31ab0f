# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.db import migrations, models
import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Dept',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('dept_name', models.CharField(help_text='部门名称', max_length=64, verbose_name='部门名称')),
                ('dept_key', models.Char<PERSON><PERSON>(blank=True, help_text='关联字符', max_length=64, null=True, unique=True, verbose_name='关联字符')),
                ('sort', models.IntegerField(default=1, help_text='显示排序', verbose_name='显示排序')),
                ('leader', models.CharField(blank=True, help_text='负责人', max_length=32, null=True, verbose_name='负责人')),
                ('phone', models.CharField(blank=True, help_text='联系电话', max_length=32, null=True, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, help_text='邮箱', max_length=32, null=True, verbose_name='邮箱')),
                ('status', models.CharField(choices=[('0', '正常'), ('1', '停用')], default='0', help_text='部门状态（0正常 1停用）', max_length=1, verbose_name='部门状态（0正常 1停用）')),
            ],
            options={
                'verbose_name': '系统-部门表',
                'verbose_name_plural': '系统-部门表',
                'db_table': 'sys_dept',
                'ordering': ('sort',),
            },
        ),
    ]
