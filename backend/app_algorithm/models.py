from django.db import models
from application.settings import BASE_DIR
from utils.models import BaseModel
from app_user.models import Users

class AlgorithmCategory(BaseModel):
    """
    算法库分类表
    """
    name = models.CharField(max_length=100, verbose_name="分类名称")
    code = models.CharField(max_length=100, verbose_name="分类编码")
    order = models.IntegerField(default=1, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    parent = models.ForeignKey(
        to='self',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        verbose_name='父级分类',
        db_constraint=False,
        related_name='subcat',
        help_text="父级分类",
    )
    
    class Meta:
        verbose_name = '算法库分类'
        verbose_name_plural = verbose_name
        ordering = ['order']
    
    def __str__(self):
        return f"{self.name}"

class AIAlgorithm(BaseModel):
    """
    算法库表
    """
    STATUS_CHOICES = (
        ('online', '已上架'),
        ('offline', '已下架')
    )

    # 算法库实现类型
    ALGORITHM_TYPE_CHOICES = (
        ('docker', 'Docker镜像'),
        ('file', '文件包')
    )

    # 算法库分类类型：通用算法 vs 专用算法
    TYPE_CHOICES = (
        ('general', '通用算法'),
        ('specialized', '专用算法')
    )

    name = models.CharField(max_length=200, verbose_name="算法库名称", db_index=True)
    group = models.CharField(max_length=100, help_text="算法库所属项目组", default='qianlan', db_index=True)
    description = models.TextField(verbose_name="算法库描述")
    categories = models.ManyToManyField(
        to=AlgorithmCategory,
        verbose_name='算法库分类',
        related_name='algorithm_categories',
        help_text="算法库分类，可以选择多个类别"
    )

    # 算法库分类类型：通用算法(运筹优化、深度学习、强化学习) vs 专用算法
    type = models.CharField(
        max_length=20,
        choices=TYPE_CHOICES,
        default='general',
        verbose_name="算法库分类类型",
        help_text="算法库分类类型：通用算法或专用算法",
        db_index=True
    )

    # 算法库实现类型
    algorithm_type = models.CharField(
        max_length=10,
        choices=ALGORITHM_TYPE_CHOICES,
        default='file',
        verbose_name="算法库实现类型",
        help_text="算法库实现类型：Docker镜像或文件包"
    )
    
    # MinIO存储路径，适用于文件包类型
    minio_path = models.CharField(
        max_length=255,
        help_text="MinIO 中的存储路径，例如 apple/DepthPro/",
        blank=True,
        null=True
    )
    
    # Docker相关配置，适用于Docker类型
    docker_image = models.CharField(
        max_length=255, 
        verbose_name="Docker镜像", 
        help_text="Docker镜像地址，例如 registry.example.com/image:tag",
        blank=True,
        null=True
    )
    
    docker_registry = models.CharField(
        max_length=255, 
        verbose_name="Docker镜像仓库", 
        help_text="Docker镜像仓库地址",
        blank=True,
        null=True
    )
    
    docker_tag = models.CharField(
        max_length=100, 
        verbose_name="Docker标签", 
        help_text="Docker镜像标签，如latest",
        blank=True,
        null=True
    )
    
    # 运行配置
    run_command = models.TextField(
        verbose_name="运行命令", 
        help_text="算法运行命令",
        blank=True,
        null=True
    )
    
    stars = models.IntegerField(default=0, verbose_name="收藏数")
    downloads = models.IntegerField(default=0, verbose_name="下载次数")
    parameters = models.JSONField(
        default=dict,
        verbose_name="算法库参数",
        help_text="示例：{\"backbone\": \"resnet50\", \"input_size\": 512}"
    )
    metrics = models.JSONField(
        default=dict,
        verbose_name="测试指标",
        help_text="示例：{\"top1_acc\": 0.92, \"f1_score\": 0.88}"
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='online',
        verbose_name="上架状态",
        db_index=True
    )

    class Meta:
        verbose_name = 'AI算法库'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
        indexes = [
            models.Index(fields=['name', 'group']),
            models.Index(fields=['type', 'status']),
            models.Index(fields=['algorithm_type']),
            models.Index(fields=['create_datetime']),
        ]
    
    def __str__(self):
        return f"{self.group}-{self.name}"

class AlgorithmComment(BaseModel):
    """算法库评论"""
    model = models.ForeignKey(AIAlgorithm, related_name='comments', on_delete=models.CASCADE, verbose_name='所属算法库')
    content = models.TextField(verbose_name='评论内容')
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, verbose_name='父评论')
    user = models.ForeignKey(Users, related_name='algorithm_comments', on_delete=models.CASCADE, verbose_name='发表用户')
    
    class Meta:
        verbose_name = '算法库评论'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
    
    def __str__(self):
        return f"{self.user.username} 的评论: {self.content[:30]}"