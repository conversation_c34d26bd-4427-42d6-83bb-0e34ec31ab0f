from django.shortcuts import get_object_or_404
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.db.models import F
import logging
from django.http import HttpResponse
import os
import json
from datetime import datetime

from .models import AlgorithmCategory, AIAlgorithm, AlgorithmComment
from .serializers import (
    AlgorithmCategorySerializer, AlgorithmCategoryTreeSerializer, AIAlgorithmSerializer, 
    AlgorithmCommentSerializer
)
from utils.viewset import CustomModelViewSet
from utils.json_response import DetailResponse, SuccessResponse, ErrorResponse
from utils.minio_storage import minio_client
from django.conf import settings

logger = logging.getLogger(__name__)

class AlgorithmCategoryViewSet(CustomModelViewSet):
    """
    算法库分类管理
    """
    queryset = AlgorithmCategory.objects.all()
    serializer_class = AlgorithmCategorySerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['is_active']
    search_fields = ['name', 'code']
    
    @action(methods=["GET"], detail=False)
    def tree(self, request):
        """
        获取算法库分类树结构
        """
        queryset = AlgorithmCategory.objects.exclude(is_active=False).filter(parent=None)
        serializer = AlgorithmCategoryTreeSerializer(queryset, many=True)
        return DetailResponse(data=serializer.data, msg="算法库分类树获取成功")

class AIAlgorithmViewSet(CustomModelViewSet):
    """
    AI算法库管理
    """
    queryset = AIAlgorithm.objects.all()
    serializer_class = AIAlgorithmSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['type', 'status', 'group']  # 添加type字段过滤

    def get_queryset(self):
        queryset = super().get_queryset()
        category_id = self.request.query_params.get('category')
        # 支持多个类别ID筛选
        category_ids = self.request.query_params.getlist('category_ids')
        search = self.request.query_params.get('search')
        algorithm_type = self.request.query_params.get('type')  # 添加type过滤

        # 如果指定了算法库类型，进行过滤
        if algorithm_type:
            queryset = queryset.filter(type=algorithm_type)

        # 优先使用新的多类别筛选
        if category_ids:
            # 获取所有有效的类别ID，包括子类别
            valid_category_ids = []
            all_subcategory_ids = set()  # 用于存储所有子类别ID
            
            # 递归获取子类别的函数
            def get_all_child_categories(parent_id):
                children = AlgorithmCategory.objects.filter(parent_id=parent_id)
                child_ids = []
                for child in children:
                    child_ids.append(child.id)
                    # 递归获取子类别的子类别
                    child_ids.extend(get_all_child_categories(child.id))
                return child_ids
            
            # 处理每个传入的类别ID
            for cat_id in category_ids:
                try:
                    # 尝试转换为整数
                    cat_id_int = int(cat_id)
                    # 检查类别是否存在
                    category = AlgorithmCategory.objects.get(id=cat_id_int)
                    valid_category_ids.append(cat_id_int)
                    
                    # 获取该类别的所有子类别
                    subcategory_ids = get_all_child_categories(cat_id_int)
                    all_subcategory_ids.update(subcategory_ids)
                    
                except (ValueError, AlgorithmCategory.DoesNotExist):
                    # 如果类别ID不是整数或类别不存在，跳过
                    logger.warning(f"类别ID {cat_id} 无效或不存在")
                    continue
            
            # 将有效的类别ID和所有子类别ID合并
            all_category_ids = set(valid_category_ids) | all_subcategory_ids
            
            if all_category_ids:
                # 使用交集逻辑：算法库必须同时属于所有选中的类别
                # 为每个类别创建一个子查询
                for cat_id in valid_category_ids:
                    # 为每个类别创建一个子查询，包括其所有子类别
                    category_with_children = {cat_id} | {id for id in all_subcategory_ids 
                                                     if self._is_child_of(id, cat_id)}
                    # 使用交集逻辑：算法库必须属于该类别或其子类别
                    queryset = queryset.filter(categories__id__in=category_with_children)
                
                # 确保结果唯一
                queryset = queryset.distinct()
            else:
                # 如果所有类别ID都无效，返回空查询集
                queryset = queryset.none()
        # 兼容旧的单一类别筛选
        elif category_id:
            # 支持查询某个类别下的算法库，包括其子类别
            try:
                # 首先检查是否存在此类别
                category = AlgorithmCategory.objects.get(id=category_id)
                
                # 获取此类别及其所有子类别的ID列表
                category_ids = [int(category_id)]
                
                # 递归获取子类别
                def get_child_categories(parent_id):
                    children = AlgorithmCategory.objects.filter(parent_id=parent_id)
                    for child in children:
                        category_ids.append(child.id)
                        get_child_categories(child.id)
                
                get_child_categories(int(category_id))
                
                # 查询与这些类别关联的算法库
                queryset = queryset.filter(categories__id__in=category_ids).distinct()
            except AlgorithmCategory.DoesNotExist:
                # 如果类别不存在，返回空查询集
                queryset = queryset.none()
                
        if search:
            queryset = queryset.filter(name__icontains=search)
            
        return queryset
    
    def _is_child_of(self, child_id, parent_id):
        """判断一个类别是否是另一个类别的子类别"""
        try:
            child = AlgorithmCategory.objects.get(id=child_id)
            # 循环向上查找父级
            while child.parent:
                if child.parent.id == parent_id:
                    return True
                child = child.parent
            return False
        except AlgorithmCategory.DoesNotExist:
            return False

    def list(self, request, *args, **kwargs):
        """
        获取算法库列表
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return DetailResponse(data=serializer.data, msg="获取算法库列表成功")

    def retrieve(self, request, *args, **kwargs):
        """
        获取算法库详情
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return DetailResponse(data=serializer.data, msg="获取算法库详情成功")
    
    def create(self, request, *args, **kwargs):
        """
        创建算法库
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return DetailResponse(data=serializer.data, msg="创建算法库成功")
        
    @action(methods=["POST"], detail=True)
    def star(self, request, *args, **kwargs):
        """收藏算法库"""
        instance = self.get_object()
        instance.stars = F('stars') + 1
        instance.save()
        instance.refresh_from_db()
        return DetailResponse(data={"stars": instance.stars}, msg="收藏成功")
    
    @action(methods=["POST"], detail=True)
    def download(self, request, *args, **kwargs):
        """下载算法库，增加下载次数"""
        instance = self.get_object()
        instance.downloads = F('downloads') + 1
        instance.save()
        instance.refresh_from_db()
        return DetailResponse(data={"downloads": instance.downloads}, msg="下载次数更新成功")

    @action(detail=True, methods=['get'])
    def file_url(self, request, pk=None):
        """
        获取算法库文件的下载URL
        """
        model = self.get_object()
        file_path = request.query_params.get('file_path', '')
        if not file_path:
            return ErrorResponse(msg="文件路径不能为空")
        
        # 判断算法库类型
        if model.type == 'file':
            # 文件包类型，从MinIO获取下载链接
            if not model.minio_path:
                return ErrorResponse(msg="此算法库没有关联文件")
                
            full_path = f"{model.minio_path.rstrip('/')}/{file_path}"
            url = minio_client.get_object_url('models', full_path)
            if url:
                return DetailResponse(data={"url": url}, msg="获取下载链接成功")
            return ErrorResponse(msg="获取文件URL失败")
        elif model.type == 'docker':
            # Docker镜像类型，返回镜像信息
            if not model.docker_image:
                return ErrorResponse(msg="此算法库没有关联Docker镜像")
                
            return DetailResponse(data={
                "docker_image": model.docker_image,
                "docker_registry": model.docker_registry,
                "docker_tag": model.docker_tag or 'latest',
                "run_command": model.run_command
            }, msg="获取Docker镜像信息成功")
        else:
            return ErrorResponse(msg="不支持的算法库类型")

    @action(methods=["POST"], detail=False)
    def upload(self, request, *args, **kwargs):
        """
        创建算法库记录
        支持文件包和Docker镜像两种类型的算法库上传
        """
        import re
        import json
        
        # 获取请求中的基本参数
        name = request.data.get('name')
        group = request.data.get('group', 'default')
        description = request.data.get('description', '')
        algorithm_type = request.data.get('type', 'file')  # 默认为文件类型

        # 获取类别ID列表或单一类别ID
        category_ids = request.data.getlist('category_ids')
        if not category_ids:
            # 兼容旧API，尝试获取单一类别ID
            category_id = request.data.get('category_id')
            if category_id:
                category_ids = [category_id]

        # 参数验证
        if not name or not category_ids:
            return ErrorResponse(msg="算法库名称和分类不能为空")
        
        # 验证名称符合规则（3-63字符，只能包含小写字母、数字、点和连字符等）
        if not re.match(r'^[a-z0-9][a-z0-9.\-]{1,61}[a-z0-9]$', name):
            return ErrorResponse(msg="算法库名称必须是3-63个字符，只能包含小写字母、数字、点和连字符")
            
        # 创建算法库对象的基本信息
        algorithm_data = {
            'name': name,
            'group': group,
            'description': description,
            'type': algorithm_type,
            'category_ids': category_ids
        }
        
        # 根据算法库类型处理不同的参数
        if algorithm_type == 'file':
            # 文件包类型
            # 获取文件信息列表
            try:
                files_info = json.loads(request.data.get('files', '[]'))
            except json.JSONDecodeError:
                return ErrorResponse(msg="文件信息格式错误")
                
            if not files_info:
                return ErrorResponse(msg="文件信息不能为空")
                
            # 生成MinIO存储路径
            minio_path = f"algorithms/{group}/{name}/{datetime.now().strftime('%Y%m%d%H%M%S')}/"
            algorithm_data['minio_path'] = minio_path
            
        elif algorithm_type == 'docker':
            # Docker镜像类型
            docker_image = request.data.get('docker_image')
            docker_registry = request.data.get('docker_registry')
            docker_tag = request.data.get('docker_tag', 'latest')
            run_command = request.data.get('run_command', '')
            
            if not docker_image:
                return ErrorResponse(msg="Docker镜像地址不能为空")
                
            algorithm_data['docker_image'] = docker_image
            algorithm_data['docker_registry'] = docker_registry
            algorithm_data['docker_tag'] = docker_tag
            algorithm_data['run_command'] = run_command
        else:
            return ErrorResponse(msg="不支持的算法库类型")
            
        # 创建算法库记录
        serializer = self.get_serializer(data=algorithm_data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        
        return DetailResponse(data=serializer.data, msg=f"{'文件类型' if algorithm_type == 'file' else 'Docker镜像类型'}算法库创建成功")

    @action(detail=True, methods=['get'])
    def download_file(self, request, pk=None):
        """
        下载算法库文件
        """
        model = self.get_object()
        file_path = request.query_params.get('file_path', '')
        if not file_path:
            return ErrorResponse(msg="文件路径不能为空")
        
        # 只有文件类型的算法库支持直接下载
        if model.type != 'file' or not model.minio_path:
            return ErrorResponse(msg="只有文件类型的算法库支持直接下载")
        
        # 构建完整的文件路径
        full_path = f"{model.minio_path.rstrip('/')}/{file_path}"
        
        try:
            # 从MinIO获取文件
            response = minio_client.get_object('models', full_path)
            
            # 创建HTTP响应
            file_name = os.path.basename(file_path)
            http_response = HttpResponse(
                response.data,
                content_type='application/octet-stream'
            )
            http_response['Content-Disposition'] = f'attachment; filename="{file_name}"'
            http_response['Content-Length'] = response.headers.get('Content-Length')
            
            # 增加下载次数
            model.downloads = F('downloads') + 1
            model.save()
            
            return http_response
        except Exception as e:
            logger.error(f"下载文件失败: {str(e)}")
            return ErrorResponse(msg=f"下载文件失败: {str(e)}")

    @action(methods=["POST"], detail=False)
    def get_upload_url(self, request, *args, **kwargs):
        """
        获取预签名上传URL，支持文件类型算法库的上传
        """
        filename = request.data.get('filename')
        name = request.data.get('name')
        group = request.data.get('group', 'default')
        
        if not filename or not name:
            return ErrorResponse(msg="文件名和算法库名称不能为空")
        
        # 构建MinIO存储路径
        minio_path = f"algorithms/{group}/{name}/{datetime.now().strftime('%Y%m%d%H%M%S')}/{filename}"
        
        # 获取预签名上传URL
        try:
            url = minio_client.presigned_put_object(
                'models', 
                minio_path,
                expires=3600  # 链接有效期1小时
            )
            return DetailResponse(
                data={"upload_url": url, "minio_path": minio_path},
                msg="获取上传链接成功"
            )
        except Exception as e:
            logger.error(f"获取预签名URL失败: {str(e)}")
            return ErrorResponse(msg=f"获取上传链接失败: {str(e)}")
            
    @action(methods=["POST"], detail=False)
    def register_docker(self, request, *args, **kwargs):
        """
        注册Docker镜像类型的算法库
        """
        name = request.data.get('name')
        group = request.data.get('group', 'default')
        description = request.data.get('description', '')
        docker_image = request.data.get('docker_image')
        docker_registry = request.data.get('docker_registry')
        docker_tag = request.data.get('docker_tag', 'latest')
        run_command = request.data.get('run_command', '')
        
        # 获取类别ID列表
        category_ids = request.data.getlist('category_ids')
        if not category_ids:
            category_id = request.data.get('category_id')
            if category_id:
                category_ids = [category_id]
        
        # 参数验证
        if not name or not category_ids or not docker_image:
            return ErrorResponse(msg="算法库名称、分类和Docker镜像不能为空")
        
        # 创建Docker镜像类型的算法库记录
        algorithm_data = {
            'name': name,
            'group': group,
            'description': description,
            'type': 'docker',
            'docker_image': docker_image,
            'docker_registry': docker_registry,
            'docker_tag': docker_tag,
            'run_command': run_command,
            'category_ids': category_ids
        }
        
        serializer = self.get_serializer(data=algorithm_data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        
        return DetailResponse(data=serializer.data, msg="Docker镜像算法库注册成功")

    @action(detail=True, methods=['get'])
    def readme(self, request, pk=None):
        """
        获取算法库README内容
        """
        algorithm = self.get_object()

        # 只有文件类型的算法库支持README
        if algorithm.algorithm_type != 'file' or not algorithm.minio_path:
            return ErrorResponse(msg="只有文件类型的算法库支持README功能")

        # 构建README文件路径
        readme_path = f"{algorithm.minio_path.rstrip('/')}/README.md"

        try:
            # 从MinIO获取README文件内容
            response = minio_client.get_object('models', readme_path)
            content = response.data.decode('utf-8')

            return DetailResponse(data={
                "content": content,
                "path": readme_path
            }, msg="获取README内容成功")
        except Exception as e:
            # 如果README文件不存在，返回空内容
            logger.info(f"README文件不存在: {str(e)}")
            return DetailResponse(data={
                "content": "",
                "path": readme_path
            }, msg="README文件不存在，返回空内容")

    @action(detail=True, methods=['post'])
    def update_readme(self, request, pk=None):
        """
        更新算法库README内容
        """
        algorithm = self.get_object()
        content = request.data.get('content', '')

        # 只有文件类型的算法库支持README
        if algorithm.algorithm_type != 'file' or not algorithm.minio_path:
            return ErrorResponse(msg="只有文件类型的算法库支持README功能")

        # 构建README文件路径
        readme_path = f"{algorithm.minio_path.rstrip('/')}/README.md"

        try:
            # 将内容上传到MinIO
            from io import BytesIO
            content_bytes = content.encode('utf-8')
            minio_client.put_object(
                'models',
                readme_path,
                BytesIO(content_bytes),
                len(content_bytes),
                content_type='text/markdown'
            )

            return DetailResponse(data={
                "content": content,
                "path": readme_path
            }, msg="README内容更新成功")
        except Exception as e:
            logger.error(f"更新README失败: {str(e)}")
            return ErrorResponse(msg=f"更新README失败: {str(e)}")

    @action(detail=True, methods=['get'])
    def file_tree(self, request, pk=None):
        """
        获取算法库文件树结构
        """
        algorithm = self.get_object()

        # 只有文件类型的算法库支持文件树
        if algorithm.algorithm_type != 'file' or not algorithm.minio_path:
            return ErrorResponse(msg="只有文件类型的算法库支持文件树功能")

        try:
            # 从MinIO获取文件列表
            objects = minio_client.list_objects('models', prefix=algorithm.minio_path, recursive=True)

            # 构建文件树结构
            file_tree = self._build_file_tree(objects, algorithm.minio_path)

            return DetailResponse(data=file_tree, msg="获取文件树成功")
        except Exception as e:
            logger.error(f"获取文件树失败: {str(e)}")
            return ErrorResponse(msg=f"获取文件树失败: {str(e)}")

    def _build_file_tree(self, objects, base_path):
        """
        构建文件树结构
        """
        tree = []
        path_map = {}

        for obj in objects:
            # 移除基础路径前缀
            relative_path = obj.object_name[len(base_path):].lstrip('/')
            if not relative_path:
                continue

            path_parts = relative_path.split('/')
            current_level = tree
            current_path = ""

            for i, part in enumerate(path_parts):
                current_path = f"{current_path}/{part}" if current_path else part
                full_key = f"{base_path}{current_path}"

                # 查找当前层级是否已存在该节点
                existing_node = None
                for node in current_level:
                    if node['name'] == part:
                        existing_node = node
                        break

                if existing_node is None:
                    # 创建新节点
                    is_file = i == len(path_parts) - 1
                    node = {
                        'name': part,
                        'path': current_path,
                        'type': 'file' if is_file else 'directory',
                        'size': obj.size if is_file else 0,
                        'last_modified': obj.last_modified.isoformat() if is_file and obj.last_modified else None,
                        'children': [] if not is_file else None
                    }
                    current_level.append(node)
                    existing_node = node

                if not existing_node.get('children') is None:
                    current_level = existing_node['children']

        return tree

    @action(detail=True, methods=['get'])
    def main_doc(self, request, pk=None):
        """
        获取算法库主文档内容（优先README.md，其次description）
        """
        algorithm = self.get_object()

        # 首先尝试获取README.md内容
        if algorithm.algorithm_type == 'file' and algorithm.minio_path:
            readme_path = f"{algorithm.minio_path.rstrip('/')}/README.md"
            try:
                response = minio_client.get_object('models', readme_path)
                content = response.data.decode('utf-8')
                return DetailResponse(data={
                    "content": content,
                    "type": "markdown",
                    "source": "README.md"
                }, msg="获取主文档成功")
            except Exception:
                pass

        # 如果README.md不存在，返回description
        return DetailResponse(data={
            "content": algorithm.description or "",
            "type": "text",
            "source": "description"
        }, msg="获取主文档成功")

    @action(detail=True, methods=['post'])
    def upload_file(self, request, pk=None):
        """
        上传文件到算法库
        """
        algorithm = self.get_object()

        # 只有文件类型的算法库支持文件上传
        if algorithm.algorithm_type != 'file':
            return ErrorResponse(msg="只有文件类型的算法库支持文件上传")

        # 如果算法库还没有minio_path，创建一个
        if not algorithm.minio_path:
            algorithm.minio_path = f"algorithms/{algorithm.group}/{algorithm.name}/{datetime.now().strftime('%Y%m%d%H%M%S')}/"
            algorithm.save()

        uploaded_file = request.FILES.get('file')
        file_path = request.data.get('path', '')  # 相对路径

        if not uploaded_file:
            return ErrorResponse(msg="请选择要上传的文件")

        try:
            # 构建完整的MinIO路径
            if file_path:
                full_path = f"{algorithm.minio_path.rstrip('/')}/{file_path.lstrip('/')}"
            else:
                full_path = f"{algorithm.minio_path.rstrip('/')}/{uploaded_file.name}"

            # 上传文件到MinIO
            minio_client.put_object(
                'models',
                full_path,
                uploaded_file,
                uploaded_file.size,
                content_type=uploaded_file.content_type or 'application/octet-stream'
            )

            return DetailResponse(data={
                "filename": uploaded_file.name,
                "path": full_path,
                "size": uploaded_file.size
            }, msg="文件上传成功")
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            return ErrorResponse(msg=f"文件上传失败: {str(e)}")

    @action(detail=True, methods=['get'])
    def file_content(self, request, pk=None):
        """
        获取文件内容（用于代码预览）
        """
        algorithm = self.get_object()
        file_path = request.query_params.get('file_path', '')

        if not file_path:
            return ErrorResponse(msg="文件路径不能为空")

        # 只有文件类型的算法库支持文件内容获取
        if algorithm.algorithm_type != 'file' or not algorithm.minio_path:
            return ErrorResponse(msg="只有文件类型的算法库支持文件内容获取")

        # 构建完整的文件路径
        full_path = f"{algorithm.minio_path.rstrip('/')}/{file_path.lstrip('/')}"

        try:
            # 从MinIO获取文件内容
            response = minio_client.get_object('models', full_path)

            # 检查文件大小，避免加载过大的文件
            content_length = response.headers.get('Content-Length')
            if content_length and int(content_length) > 1024 * 1024:  # 1MB限制
                return ErrorResponse(msg="文件过大，无法预览")

            # 尝试解码文件内容
            try:
                content = response.data.decode('utf-8')
                encoding = 'utf-8'
            except UnicodeDecodeError:
                try:
                    content = response.data.decode('gbk')
                    encoding = 'gbk'
                except UnicodeDecodeError:
                    return ErrorResponse(msg="文件编码不支持，无法预览")

            # 根据文件扩展名判断文件类型
            file_ext = file_path.split('.')[-1].lower() if '.' in file_path else ''

            return DetailResponse(data={
                "content": content,
                "encoding": encoding,
                "file_type": file_ext,
                "size": len(response.data)
            }, msg="获取文件内容成功")
        except Exception as e:
            logger.error(f"获取文件内容失败: {str(e)}")
            return ErrorResponse(msg=f"获取文件内容失败: {str(e)}")

    @action(detail=True, methods=['delete'])
    def delete_file(self, request, pk=None):
        """
        删除算法库中的文件
        """
        algorithm = self.get_object()
        file_path = request.data.get('file_path', '')

        if not file_path:
            return ErrorResponse(msg="文件路径不能为空")

        # 只有文件类型的算法库支持文件删除
        if algorithm.algorithm_type != 'file' or not algorithm.minio_path:
            return ErrorResponse(msg="只有文件类型的算法库支持文件删除")

        # 构建完整的文件路径
        full_path = f"{algorithm.minio_path.rstrip('/')}/{file_path.lstrip('/')}"

        try:
            # 从MinIO删除文件
            minio_client.remove_object('models', full_path)

            return SuccessResponse(msg="文件删除成功")
        except Exception as e:
            logger.error(f"删除文件失败: {str(e)}")
            return ErrorResponse(msg=f"删除文件失败: {str(e)}")

class AlgorithmCommentViewSet(CustomModelViewSet):
    """
    算法库评论管理
    """
    queryset = AlgorithmComment.objects.all()
    serializer_class = AlgorithmCommentSerializer
    permission_classes = [IsAuthenticated]
    
    def perform_create(self, serializer):
        """
        创建评论时，自动关联当前用户
        """
        # 获取当前用户
        user = self.request.user
        
        # 将用户信息添加到序列化器数据中
        serializer.save(user=user)

    def get_queryset(self):
        """
        获取指定算法库下的一级评论（不包括回复）
        """
        queryset = super().get_queryset()
        
        # 获取URL参数
        algorithm_id = self.request.query_params.get('algorithm_id')
        
        if algorithm_id:
            # 只返回特定算法库的一级评论（parent为空）
            queryset = queryset.filter(model_id=algorithm_id, parent__isnull=True)
            
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return DetailResponse(data=serializer.data, msg="获取评论列表成功")
        
    def destroy(self, request, *args, **kwargs):
        """
        删除评论时，级联删除所有回复
        """
        instance = self.get_object()
        
        # 检查当前用户是否有权限删除此评论
        if instance.user.id != request.user.id and not request.user.is_superuser:
            return ErrorResponse(msg="您没有权限删除此评论")
        
        try:
            # 递归删除所有回复
            self.delete_replies(instance.id)
            
            # 删除当前评论
            instance.delete()
            
            return SuccessResponse(msg="评论删除成功")
        except Exception as e:
            logger.error(f"删除评论失败: {str(e)}")
            return ErrorResponse(msg=f"删除评论失败: {str(e)}")
    
    def delete_replies(self, comment_id):
        """
        递归删除评论的所有回复
        """
        # 获取所有回复
        replies = AlgorithmComment.objects.filter(parent_id=comment_id)
        
        # 递归删除每个回复的子回复
        for reply in replies:
            self.delete_replies(reply.id)
            
        # 删除所有回复
        replies.delete()
