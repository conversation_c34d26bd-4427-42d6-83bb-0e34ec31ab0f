from django.core.management.base import BaseCommand
from app_algorithm.models import AlgorithmCategory


class Command(BaseCommand):
    help = '初始化算法库分类数据'

    def handle(self, *args, **options):
        self.stdout.write('开始初始化算法库分类数据...')
        
        # 创建通用算法顶级分类
        general_categories = [
            {
                'name': '运筹优化',
                'code': 'operations_research',
                'order': 1,
                'children': [
                    {'name': '线性规划', 'code': 'linear_programming', 'order': 1},
                    {'name': '整数规划', 'code': 'integer_programming', 'order': 2},
                    {'name': '非线性规划', 'code': 'nonlinear_programming', 'order': 3},
                    {'name': '动态规划', 'code': 'dynamic_programming', 'order': 4},
                    {'name': '启发式算法', 'code': 'heuristic_algorithms', 'order': 5},
                    {'name': '遗传算法', 'code': 'genetic_algorithm', 'order': 6},
                    {'name': '模拟退火', 'code': 'simulated_annealing', 'order': 7},
                    {'name': '粒子群优化', 'code': 'particle_swarm_optimization', 'order': 8},
                ]
            },
            {
                'name': '深度学习',
                'code': 'deep_learning',
                'order': 2,
                'children': [
                    {'name': '卷积神经网络', 'code': 'cnn', 'order': 1},
                    {'name': '循环神经网络', 'code': 'rnn', 'order': 2},
                    {'name': '长短期记忆网络', 'code': 'lstm', 'order': 3},
                    {'name': '生成对抗网络', 'code': 'gan', 'order': 4},
                    {'name': '变分自编码器', 'code': 'vae', 'order': 5},
                    {'name': 'Transformer', 'code': 'transformer', 'order': 6},
                    {'name': '图神经网络', 'code': 'gnn', 'order': 7},
                    {'name': '自注意力机制', 'code': 'attention', 'order': 8},
                    {'name': '预训练模型', 'code': 'pretrained_models', 'order': 9},
                ]
            },
            {
                'name': '强化学习',
                'code': 'reinforcement_learning',
                'order': 3,
                'children': [
                    {'name': 'Q学习', 'code': 'q_learning', 'order': 1},
                    {'name': '深度Q网络', 'code': 'dqn', 'order': 2},
                    {'name': '策略梯度', 'code': 'policy_gradient', 'order': 3},
                    {'name': 'Actor-Critic', 'code': 'actor_critic', 'order': 4},
                    {'name': 'PPO算法', 'code': 'ppo', 'order': 5},
                    {'name': 'DDPG算法', 'code': 'ddpg', 'order': 6},
                    {'name': '多智能体强化学习', 'code': 'multi_agent_rl', 'order': 7},
                    {'name': '模仿学习', 'code': 'imitation_learning', 'order': 8},
                ]
            }
        ]
        
        # 创建专用算法顶级分类
        specialized_categories = [
            {
                'name': '计算机视觉',
                'code': 'computer_vision',
                'order': 4,
                'children': [
                    {'name': '目标检测', 'code': 'object_detection', 'order': 1},
                    {'name': '图像分类', 'code': 'image_classification', 'order': 2},
                    {'name': '语义分割', 'code': 'semantic_segmentation', 'order': 3},
                    {'name': '实例分割', 'code': 'instance_segmentation', 'order': 4},
                    {'name': '人脸识别', 'code': 'face_recognition', 'order': 5},
                    {'name': '光学字符识别', 'code': 'ocr', 'order': 6},
                    {'name': '图像生成', 'code': 'image_generation', 'order': 7},
                ]
            },
            {
                'name': '自然语言处理',
                'code': 'natural_language_processing',
                'order': 5,
                'children': [
                    {'name': '文本分类', 'code': 'text_classification', 'order': 1},
                    {'name': '命名实体识别', 'code': 'ner', 'order': 2},
                    {'name': '情感分析', 'code': 'sentiment_analysis', 'order': 3},
                    {'name': '机器翻译', 'code': 'machine_translation', 'order': 4},
                    {'name': '文本摘要', 'code': 'text_summarization', 'order': 5},
                    {'name': '问答系统', 'code': 'question_answering', 'order': 6},
                    {'name': '对话系统', 'code': 'dialogue_system', 'order': 7},
                ]
            },
            {
                'name': '推荐系统',
                'code': 'recommendation_system',
                'order': 6,
                'children': [
                    {'name': '协同过滤', 'code': 'collaborative_filtering', 'order': 1},
                    {'name': '内容推荐', 'code': 'content_based', 'order': 2},
                    {'name': '深度推荐', 'code': 'deep_recommendation', 'order': 3},
                    {'name': '序列推荐', 'code': 'sequential_recommendation', 'order': 4},
                    {'name': '多任务推荐', 'code': 'multi_task_recommendation', 'order': 5},
                ]
            },
            {
                'name': '时间序列分析',
                'code': 'time_series_analysis',
                'order': 7,
                'children': [
                    {'name': '时间序列预测', 'code': 'time_series_forecasting', 'order': 1},
                    {'name': '异常检测', 'code': 'anomaly_detection', 'order': 2},
                    {'name': '趋势分析', 'code': 'trend_analysis', 'order': 3},
                    {'name': '季节性分析', 'code': 'seasonal_analysis', 'order': 4},
                ]
            }
        ]
        
        # 合并所有分类
        all_categories = general_categories + specialized_categories
        
        # 创建分类
        created_count = 0
        for category_data in all_categories:
            # 创建或获取父级分类
            parent_category, created = AlgorithmCategory.objects.get_or_create(
                code=category_data['code'],
                defaults={
                    'name': category_data['name'],
                    'order': category_data['order'],
                    'is_active': True,
                    'parent': None
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'创建顶级分类: {parent_category.name}')
            
            # 创建子分类
            for child_data in category_data.get('children', []):
                child_category, created = AlgorithmCategory.objects.get_or_create(
                    code=child_data['code'],
                    defaults={
                        'name': child_data['name'],
                        'order': child_data['order'],
                        'is_active': True,
                        'parent': parent_category
                    }
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(f'  创建子分类: {child_category.name}')
        
        self.stdout.write(
            self.style.SUCCESS(f'算法库分类数据初始化完成！共创建 {created_count} 个分类')
        )
