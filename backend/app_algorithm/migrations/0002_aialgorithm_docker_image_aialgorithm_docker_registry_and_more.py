# Generated by Django 4.2.1 on 2025-07-11 23:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_algorithm', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='aialgorithm',
            name='docker_image',
            field=models.CharField(blank=True, help_text='Docker镜像地址，例如 registry.example.com/image:tag', max_length=255, null=True, verbose_name='Docker镜像'),
        ),
        migrations.AddField(
            model_name='aialgorithm',
            name='docker_registry',
            field=models.CharField(blank=True, help_text='Docker镜像仓库地址', max_length=255, null=True, verbose_name='Docker镜像仓库'),
        ),
        migrations.AddField(
            model_name='aialgorithm',
            name='docker_tag',
            field=models.CharField(blank=True, help_text='Docker镜像标签，如latest', max_length=100, null=True, verbose_name='Docker标签'),
        ),
        migrations.AddField(
            model_name='aialgorithm',
            name='run_command',
            field=models.TextField(blank=True, help_text='算法运行命令', null=True, verbose_name='运行命令'),
        ),
        migrations.AddField(
            model_name='aialgorithm',
            name='type',
            field=models.CharField(choices=[('docker', 'Docker镜像'), ('file', '文件包')], default='docker', help_text='算法库类型：Docker镜像或文件包', max_length=10, verbose_name='算法库类型'),
        ),
        migrations.AlterField(
            model_name='aialgorithm',
            name='minio_path',
            field=models.CharField(blank=True, help_text='MinIO 中的存储路径，例如 apple/DepthPro/', max_length=255, null=True),
        ),
    ]
