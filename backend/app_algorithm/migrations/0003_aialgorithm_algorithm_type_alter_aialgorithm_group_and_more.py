# Generated by Django 4.2.1 on 2025-08-05 22:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_algorithm', '0002_aialgorithm_docker_image_aialgorithm_docker_registry_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='aialgorithm',
            name='algorithm_type',
            field=models.CharField(choices=[('docker', 'Docker镜像'), ('file', '文件包')], default='file', help_text='算法库实现类型：Docker镜像或文件包', max_length=10, verbose_name='算法库实现类型'),
        ),
        migrations.AlterField(
            model_name='aialgorithm',
            name='group',
            field=models.CharField(db_index=True, default='qianlan', help_text='算法库所属项目组', max_length=100),
        ),
        migrations.AlterField(
            model_name='aialgorithm',
            name='name',
            field=models.Char<PERSON><PERSON>(db_index=True, max_length=200, verbose_name='算法库名称'),
        ),
        migrations.AlterField(
            model_name='aialgorithm',
            name='status',
            field=models.CharField(choices=[('online', '已上架'), ('offline', '已下架')], db_index=True, default='online', max_length=20, verbose_name='上架状态'),
        ),
        migrations.AlterField(
            model_name='aialgorithm',
            name='type',
            field=models.CharField(choices=[('general', '通用算法'), ('specialized', '专用算法')], db_index=True, default='general', help_text='算法库分类类型：通用算法或专用算法', max_length=20, verbose_name='算法库分类类型'),
        ),
        migrations.AddIndex(
            model_name='aialgorithm',
            index=models.Index(fields=['name', 'group'], name='app_algorit_name_54787d_idx'),
        ),
        migrations.AddIndex(
            model_name='aialgorithm',
            index=models.Index(fields=['type', 'status'], name='app_algorit_type_08029f_idx'),
        ),
        migrations.AddIndex(
            model_name='aialgorithm',
            index=models.Index(fields=['algorithm_type'], name='app_algorit_algorit_2d9fd4_idx'),
        ),
        migrations.AddIndex(
            model_name='aialgorithm',
            index=models.Index(fields=['create_datetime'], name='app_algorit_create__b9ea01_idx'),
        ),
    ]
