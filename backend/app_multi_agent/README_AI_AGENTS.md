# 多智能体任务处理系统

## 系统概述

该系统实现了基于阿里云通义千问API的多智能体任务处理框架，允许将复杂任务分解为子任务，并由专门的智能体执行这些子任务。每个智能体使用针对其专业领域优化的大模型或专门的处理方法，以提供更好的任务处理能力。

## 架构设计

系统由三个主要组件组成：

1. **任务规划器**（TaskPlannerView）：负责将用户输入的复杂任务分解为可执行的子任务序列，并为每个子任务分配合适的专家角色。
2. **专家智能体**（ExpertAgentView）：针对不同领域的专家角色，使用特定的大模型或处理方法处理分配给他们的子任务。
3. **任务执行器**（TaskExecutionView）：协调整个任务流程，按照依赖关系顺序调用专家智能体执行子任务。

## 专家角色和模型配置

系统支持多种专家角色，每个角色使用针对其专业领域优化的模型或处理方法：

| 专家角色 | 使用模型/方法 | 说明 |
|---------|---------|------|
| 任务规划器 | qwen-max | 最强模型，用于任务拆解和规划 |
| OCR专家 | qwen-vl-ocr-latest | 视觉OCR模型，专门用于图像文字识别 |
| 图像处理专家 | 基于Python的图像处理 | 使用OpenCV进行图像预处理，包括调整大小、增强对比度等 |
| 翻译专家 | qwen-mt-turbo | 专用翻译模型，支持多语言翻译 |
| 数据整合专家 | qwen-long | 长文本分析模型，用于整合和总结信息 |
| 需求分析师 | qwen-plus | 通用模型，用于分析任务需求 |
| 方案设计师 | qwen-plus | 通用模型，用于设计解决方案 |
| 执行专家 | qwen-plus | 通用模型，用于执行具体任务 |

## API调用方式

系统使用OpenAI格式的API调用方式，通过阿里云通义千问API的兼容模式进行调用：

```python
from openai import OpenAI

client = OpenAI(
    api_key=DASHSCOPE_API_KEY,
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

# 文本模型调用示例
completion = client.chat.completions.create(
    model="qwen-plus",
    messages=[
        {"role": "system", "content": "你是一个专业助手"},
        {"role": "user", "content": "请回答问题"}
    ]
)

# 视觉模型调用示例
completion = client.chat.completions.create(
    model="qwen-vl-ocr-latest",
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{encoded_string}",
                        "detail": "high"
                    }
                },
                {
                    "type": "text",
                    "text": "请识别图片中的文字"
                }
            ]
        }
    ]
)

# 翻译模型调用示例
completion = client.chat.completions.create(
    model="qwen-mt-turbo",
    messages=[
        {
            "role": "user",
            "content": "要翻译的文本"
        }
    ],
    extra_body={
        "translation_options": {
            "source_lang": "auto",
            "target_lang": "Chinese"
        }
    }
)

# 长文本模型调用示例
file_object = client.files.create(
    file=Path("文本文件.txt"),
    purpose="file-extract"
)
completion = client.chat.completions.create(
    model="qwen-long",
    messages=[
        {'role': 'system', 'content': f'fileid://{file_object.id}'},
        {'role': 'user', 'content': '请分析这篇文章'}
    ]
)
```

## API接口

系统提供以下API接口：

### 1. 任务规划
- **URL**: `/multi-agent/task-planner/`
- **方法**: POST
- **参数**: 
  - `task_description`: 任务描述
- **返回**: 分解后的子任务列表

### 2. 专家智能体
- **URL**: `/multi-agent/expert-agent/`
- **方法**: POST
- **参数**: 
  - `expert_role`: 专家角色
  - `task_description`: 任务描述
  - `task_input`: 任务输入
  - `image` (可选): 图片文件
- **返回**: 专家处理结果

### 3. 任务执行
- **URL**: `/multi-agent/task-execution/`
- **方法**: POST
- **参数**: 
  - `task_description`: 任务描述
  - `image` (可选): 图片文件
- **返回**: 完整的任务执行结果，包括所有子任务的结果

## 扩展新的专家角色

要添加新的专家角色，需要执行以下步骤：

1. 在 `EXPERT_MODELS` 字典中添加新角色及其使用的模型
2. 如果需要特殊处理逻辑，在 `ExpertAgentView` 中实现专门的处理函数
3. 如果不需要特殊处理逻辑，可以使用 `_generic_expert` 函数

## 使用示例

以OCR图像识别和翻译为例：

1. 用户上传一张包含英文文字的图片，并输入任务描述"对上传图片中的文字进行OCR识别并翻译成中文"
2. 系统将任务分解为四个子任务：图像预处理、OCR文字识别、文本翻译、结果整合
3. 系统按照依赖顺序执行子任务，每个子任务由对应的专家智能体处理
4. 最终返回完整的处理结果，包括原文和翻译

## 依赖

- Django REST Framework
- 阿里云通义千问API
- OpenAI Python客户端
- OpenCV (用于图像处理)
- NumPy
- Pillow

## 环境变量配置

系统使用以下环境变量配置：

```
# 阿里云通义千问API配置
DASHSCOPE_API_KEY=your_api_key
DEFAULT_MODEL=qwen-plus  # 默认使用的模型
```

## 示例用法

### OCR识别并翻译

1. 上传一张包含文字的图片
2. 提交任务描述："对图片中的文字进行OCR识别并翻译成中文"
3. 系统会自动：
   - 预处理图片
   - 识别图片中的文字
   - 翻译识别出的文字
   - 整合结果并返回

## 扩展开发

要添加新的专家智能体，请在 `ExpertAgentView` 类中添加新的处理方法，并在 `post` 方法中添加相应的条件分支。

## 系统架构

系统由以下几个主要组件组成：

1. **任务规划器**：负责分解任务为子任务，并指定专家角色和依赖关系
2. **专家智能体**：执行特定类型的子任务，如OCR识别、文本翻译等
3. **任务执行器**：协调整个任务流程，按照依赖关系调用专家智能体
4. **前端展示**：展示任务执行流程和结果

## 配置说明

### 阿里云通义千问API配置

1. 复制`env_example.txt`为`.env`文件
2. 在`.env`文件中填入您的阿里云通义千问API密钥：
   ```
   DASHSCOPE_API_KEY=your_dashscope_api_key
   ```

### 支持的专家智能体

系统当前支持以下专家智能体：

1. **OCR专家**：识别图像中的文字
2. **翻译专家**：将文本翻译为目标语言
3. **图像处理专家**：对图像进行预处理
4. **数据整合专家**：整合多个子任务的结果
5. **通用专家**：处理其他类型的任务

## API接口说明

### 1. 任务规划API

- **URL**: `/apis/ai-tasks/plan/`
- **方法**: POST
- **参数**:
  ```json
  {
    "task_description": "任务描述文本"
  }
  ```
- **返回**:
  ```json
  {
    "task_id": "任务ID",
    "task_description": "任务描述",
    "subtasks": [
      {
        "id": "task1",
        "name": "子任务名称",
        "description": "子任务描述",
        "expert": "专家角色",
        "required_tools": ["工具1", "工具2"],
        "depends_on": []
      },
      // 更多子任务...
    ]
  }
  ```

### 2. 专家智能体API

- **URL**: `/apis/ai-tasks/expert/`
- **方法**: POST
- **参数** (FormData):
  - `expert_role`: 专家角色名称
  - `task_description`: 任务描述
  - `task_input`: 任务输入数据
  - `image`: 图片文件（如果需要）
- **返回**: 根据专家角色不同，返回不同格式的结果

### 3. 任务执行API

- **URL**: `/apis/ai-tasks/execute/`
- **方法**: POST
- **参数** (FormData):
  - `task_description`: 任务描述
  - `image`: 图片文件（如果需要）
  - `model_id_X`: 模型ID（可多个）
- **返回**:
  ```json
  {
    "task_id": "任务ID",
    "task_description": "任务描述",
    "subtasks": [
      // 子任务列表
    ],
    "results": {
      "task1": { /* 子任务1结果 */ },
      "task2": { /* 子任务2结果 */ },
      // 更多子任务结果...
    }
  }
  ```

## 使用示例

### OCR识别和翻译示例

1. 上传一张包含文字的图片
2. 系统会自动分解任务为：图像预处理 -> OCR识别 -> 文本翻译 -> 结果整合
3. 依次执行各个子任务，并展示执行过程和结果

## 扩展指南

### 添加新的专家智能体

1. 在`backend/app_apis/views/ai_task_views.py`中的`ExpertAgentView`类中添加新的处理函数
2. 在`post`方法中添加对应的条件分支
3. 实现专家处理逻辑

### 自定义任务规划提示词

可以修改`TaskPlannerView`类中的`_generate_subtasks`方法，调整提示词以获得更好的任务分解结果。

## 注意事项

1. 确保已正确配置阿里云通义千问API密钥
2. 对于图像处理任务，确保已上传图片
3. 任务依赖关系应避免循环依赖 