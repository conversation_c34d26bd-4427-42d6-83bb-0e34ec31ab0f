from django.shortcuts import render
import json
import os
import requests
import uuid
import tempfile
import base64
from pathlib import Path
from django.http import JsonResponse
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.parsers import MultiPartParser, FormParser
from utils.json_response import DetailResponse, SuccessResponse, ErrorResponse
from openai import OpenAI
import cv2
import numpy as np
from io import BytesIO
import PIL.Image as Image

# Create your views here.

# 阿里云通义千问API配置
DASHSCOPE_API_KEY = os.environ.get('DASHSCOPE_API_KEY', 'sk-001b3a82e31a4bf39aedea1c3a285301')
QWEN_API_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"

# 专家模型配置
EXPERT_MODELS = {
    "任务规划器": "qwen-plus",  # 最强模型用于任务拆解
    "OCR专家": "qwen-vl-ocr-latest",  # 视觉OCR模型
    "OCR技术专家": "qwen-vl-ocr-latest",  # 视觉OCR模型
    "翻译专家": "qwen-mt-turbo",  # 翻译专用模型
    "语言翻译专家": "qwen-mt-turbo",  # 翻译专用模型
    "数据整合专家": "qwen-long",  # 长文本分析模型
    "需求分析师": "qwen-plus",  # 通用模型
    "方案设计师": "qwen-plus",  # 通用模型
    "执行专家": "qwen-plus",  # 通用模型
}

class TaskPlannerView(APIView):
    """
    任务规划器API，用于分解任务为子任务
    """
    def post(self, request):
        try:
            # 获取用户输入的任务描述
            task_description = request.data.get('task_description', '')
            if not task_description:
                return ErrorResponse(msg="任务描述不能为空", code=400, status=status.HTTP_400_BAD_REQUEST)
            
            # 调用大模型API，分解任务
            subtasks = self._generate_subtasks(task_description)
            
            return DetailResponse(data={
                "task_id": str(uuid.uuid4()),
                "task_description": task_description,
                "subtasks": subtasks
            })
        except Exception as e:
            return ErrorResponse(msg=str(e), code=500, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _generate_subtasks(self, task_description):
        """调用大模型API分解任务"""
        if not DASHSCOPE_API_KEY:
            # 如果没有配置API Key，返回模拟数据
            return self._mock_subtasks(task_description)
        
        try:
            client = OpenAI(
                api_key=DASHSCOPE_API_KEY,
                base_url=QWEN_API_BASE_URL,
            )
            
            # 构建提示词，要求大模型分解任务
            prompt = f"""
            我需要你帮我分解以下任务为可执行的子任务列表，并为每个子任务指定合适的专家角色。
            
            任务描述: {task_description}
            
            请以JSON格式返回子任务列表，格式如下:
            {{
                "subtasks": [
                    {{
                        "id": "task1",
                        "name": "子任务名称",
                        "description": "子任务详细描述",
                        "expert": "专家角色名称",
                        "required_tools": ["工具1", "工具2"],
                        "depends_on": [] // 依赖的其他子任务ID
                    }},
                    // 更多子任务...
                ]
            }}
            
            请确保JSON格式正确，并且子任务之间的依赖关系合理。
            """
            
            completion = client.chat.completions.create(
                model=EXPERT_MODELS["任务规划器"],
                messages=[
                    {"role": "system", "content": "你是一个专业的任务分解助手，擅长将复杂任务分解为可执行的子任务。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,  # 低温度以获得更确定性的输出
                max_tokens=2000
            )
            
            content = completion.choices[0].message.content
            
            # 从内容中提取JSON
            json_start = content.find('{')
            json_end = content.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                subtasks_data = json.loads(json_str)
                return subtasks_data.get('subtasks', [])
            
            return []
        except Exception as e:
            print(f"调用大模型API出错: {str(e)}")
            return self._mock_subtasks(task_description)
    
    def _mock_subtasks(self, task_description):
        """生成模拟的子任务数据"""
        if "ocr" in task_description.lower() and "翻译" in task_description:
            return [
                {
                    "id": "task1",
                    "name": "图像预处理",
                    "description": "对上传的图像进行预处理，包括调整大小、增强对比度等",
                    "expert": "图像处理专家",
                    "required_tools": ["图像处理库"],
                    "depends_on": []
                },
                {
                    "id": "task2",
                    "name": "OCR文字识别",
                    "description": "识别图像中的文字内容",
                    "expert": "OCR技术专家",
                    "required_tools": ["OCR引擎"],
                    "depends_on": ["task1"]
                },
                {
                    "id": "task3",
                    "name": "文本翻译",
                    "description": "将识别出的文字翻译成中文",
                    "expert": "语言翻译专家",
                    "required_tools": ["翻译API"],
                    "depends_on": ["task2"]
                },
                {
                    "id": "task4",
                    "name": "结果整合",
                    "description": "整合OCR识别和翻译结果，生成最终输出",
                    "expert": "数据整合专家",
                    "required_tools": ["数据处理工具"],
                    "depends_on": ["task3"]
                }
            ]
        else:
            # 默认的通用任务分解
            return [
                {
                    "id": "task1",
                    "name": "任务分析",
                    "description": f"分析'{task_description}'的需求和目标",
                    "expert": "需求分析师",
                    "required_tools": ["分析工具"],
                    "depends_on": []
                },
                {
                    "id": "task2",
                    "name": "方案设计",
                    "description": "设计解决方案和执行计划",
                    "expert": "方案设计师",
                    "required_tools": ["设计工具"],
                    "depends_on": ["task1"]
                },
                {
                    "id": "task3",
                    "name": "执行任务",
                    "description": "执行设计好的解决方案",
                    "expert": "执行专家",
                    "required_tools": ["执行工具"],
                    "depends_on": ["task2"]
                }
            ]


class ExpertAgentView(APIView):
    """
    专家智能体API，用于执行特定子任务
    """
    parser_classes = (MultiPartParser, FormParser)
    
    def post(self, request):
        try:
            expert_role = request.data.get('expert_role', '')
            task_description = request.data.get('task_description', '')
            task_input = request.data.get('task_input', {})
            
            if not expert_role or not task_description:
                return ErrorResponse(msg="专家角色和任务描述不能为空", code=400, status=status.HTTP_400_BAD_REQUEST)
            
            # 根据专家角色调用相应的处理函数
            if expert_role and ("ocr" in expert_role.lower() or "OCR" in expert_role):
                result = self._ocr_expert(task_description, request)
            elif expert_role and "翻译" in expert_role:
                result = self._translation_expert(task_description, request.data)
            elif expert_role == "图像处理专家":
                result = self._image_processing_expert(task_description, request)
            elif expert_role == "数据整合专家":
                result = self._data_integration_expert(task_description, request.data)
            else:
                print(f"未知专家角色: {expert_role}")
                # 对于未知的专家角色，使用通用大模型处理
                result = self._generic_expert(expert_role, task_description, task_input)
            
            return DetailResponse(data=result)
        except Exception as e:
            return ErrorResponse(msg=str(e), code=500, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _process_image(self, image_file):
        """使用Python进行图像处理"""
        try:
            # 读取图像
            image_data = image_file.read()
            # 重置文件指针，以便后续操作可以再次读取
            image_file.seek(0)
            
            nparr = np.frombuffer(image_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if img is None:
                return {
                    "status": "error",
                    "message": "未提供输入图像文件或数据。请提供需要处理的图像以进行OCR优化。"
                }
            
            # 图像处理步骤
            # 1. 调整大小（如果需要）
            max_size = 1200
            h, w = img.shape[:2]
            if max(h, w) > max_size:
                scale = max_size / max(h, w)
                img = cv2.resize(img, None, fx=scale, fy=scale)
            
            # 2. 转换为灰度图（对OCR有帮助）
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 3. 自适应直方图均衡化（增强对比度）
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            
            # 4. 噪声去除（可选）
            denoised = cv2.fastNlMeansDenoising(enhanced, None, 10, 7, 21)
            
            # 5. 锐化（可选）
            kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
            sharpened = cv2.filter2D(denoised, -1, kernel)
            
            # 保存处理后的图像
            processed_img_path = os.path.join(tempfile.gettempdir(), f"processed_{uuid.uuid4()}.jpg")
            cv2.imwrite(processed_img_path, sharpened)

            print(f"处理后的图像已保存到: {processed_img_path}")
            
            # 转换为base64以便在JSON中返回
            _, buffer = cv2.imencode('.jpg', sharpened)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            
            # 返回处理结果
            return {
                "status": "success",
                "message": "图像预处理完成",
                "processed_image_path": processed_img_path,
                "processed_image_base64": img_base64
            }
        except Exception as e:
            print(f"图像处理出错: {str(e)}")
            return {
                "status": "error",
                "message": f"图像处理出错: {str(e)}"
            }
    
    def _ocr_expert(self, task_description, request):
        """OCR专家处理函数"""
        try:
            print(f"OCR专家: 开始处理，任务描述: {task_description}")
            print(f"OCR专家: request.FILES内容: {list(request.FILES.keys()) if hasattr(request, 'FILES') else 'No FILES attribute'}")
            
            # 检查是否上传了图片
            if 'image' not in request.FILES:
                print("OCR专家: 未上传图片文件")
                return {"error": "未上传图片文件"}
            
            image_file = request.FILES['image']
            print(f"OCR专家: 接收到图像文件, 名称: {image_file.name}, 大小: {image_file.size} 字节")
            
            # 创建临时文件保存上传的图片
            with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                for chunk in image_file.chunks():
                    temp_file.write(chunk)
                temp_file_path = temp_file.name
                print(f"OCR专家: 图像已保存到临时文件: {temp_file_path}")
            
            # 确保图片文件存在且有效
            if not os.path.exists(temp_file_path) or os.path.getsize(temp_file_path) == 0:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                print("OCR专家: 图片文件无效或为空")
                return {"error": "图片文件无效或为空"}
            else:
                print(f"OCR专家: 临时文件有效，大小: {os.path.getsize(temp_file_path)} 字节")
            
            try:
                # 使用OpenAI客户端调用OCR模型
                client = OpenAI(
                    api_key=DASHSCOPE_API_KEY,
                    base_url=QWEN_API_BASE_URL,
                )
                
                # 将图片转换为base64编码
                with open(temp_file_path, "rb") as img_file:
                    encoded_string = base64.b64encode(img_file.read()).decode('utf-8')
                print(f"OCR专家: 图像已转换为base64编码，长度: {len(encoded_string)}")
                
                # 调用OCR模型
                print(f"OCR专家: 正在调用模型 {EXPERT_MODELS['OCR技术专家']}")
                completion = client.chat.completions.create(
                    model=EXPERT_MODELS["OCR技术专家"],
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{encoded_string}",
                                        "detail": "high"
                                    }
                                },
                                {
                                    "type": "text",
                                    "text": f"请识别图片中的所有文字，并按照原始布局返回。任务要求: {task_description}"
                                }
                            ]
                        }
                    ]
                )
                
                # 解析OCR结果
                ocr_result = completion.choices[0].message.content
                print(f"OCR专家: 获取到OCR结果，长度: {len(ocr_result)}")
                
                # 尝试解析JSON格式的结果
                try:
                    json_start = ocr_result.find('{')
                    json_end = ocr_result.rfind('}') + 1
                    if json_start >= 0 and json_end > json_start:
                        json_str = ocr_result[json_start:json_end]
                        parsed_result = json.loads(json_str)
                        if isinstance(parsed_result, dict):
                            # 清理临时文件
                            os.unlink(temp_file_path)
                            return {"text": ocr_result, "confidence": 0.95, "parsed_data": parsed_result}
                except:
                    pass
                
                # 清理临时文件
                os.unlink(temp_file_path)
                return {"text": ocr_result, "confidence": 0.95}
                
            except Exception as ocr_error:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                print(f"OCR API调用出错: {str(ocr_error)}")
                raise ocr_error
                
        except Exception as e:
            print(f"OCR处理出错: {str(e)}")
            # 如果API调用失败，返回模拟数据
            return {
                "text": "This is a sample OCR text recognition result. The quick brown fox jumps over the lazy dog.",
                "confidence": 0.95
            }
    
    def _translation_expert(self, task_description, data):
        """翻译专家处理函数"""
        try:
            text = data.get('text', '')
            target_language = data.get('target_language', 'zh')
            
            if not text:
                return {"error": "待翻译文本不能为空"}
            
            # 使用OpenAI客户端调用翻译模型
            client = OpenAI(
                api_key=DASHSCOPE_API_KEY,
                base_url=QWEN_API_BASE_URL,
            )
            
            # 设置翻译选项
            translation_options = {
                "source_lang": "auto",
                "target_lang": "Chinese" if target_language == "zh" else "English"
            }
            
            print(f"翻译目标语言: {translation_options['target_lang']}")
            
            # 调用翻译模型
            completion = client.chat.completions.create(
                model=EXPERT_MODELS["语言翻译专家"],
                messages=[
                    {
                        "role": "user",
                        "content": text
                    }
                ],
                extra_body={
                    "translation_options": translation_options
                }
            )
            
            # 获取翻译结果
            translated_text = completion.choices[0].message.content
            
            return {"translated_text": translated_text, "target_language": target_language}
        except Exception as e:
            print(f"翻译处理出错: {str(e)}")
            # 如果API调用失败，返回模拟数据
            if "quick brown fox" in text:
                return {"translated_text": "这是一个示例的OCR文本识别结果。敏捷的棕色狐狸跳过了懒惰的狗。", "target_language": target_language}
            return {"translated_text": f"[翻译结果] {text}", "target_language": target_language}
    
    def _image_processing_expert(self, task_description, request):
        """图像处理专家处理函数 - 使用Python代码处理图像"""
        try:
            # 检查是否上传了图片
            if 'image' not in request.FILES:
                return {"error": "未上传图片文件"}
            
            image_file = request.FILES['image']
            
            # 使用Python代码进行图像处理
            result = self._process_image(image_file)
            
            return result
        except Exception as e:
            print(f"图像处理出错: {str(e)}")
            # 如果处理失败，返回模拟数据
            return {
                "status": "success",
                "message": "图像预处理完成",
                "processed_image_url": "/temp_uploads/processed_image.jpg"
            }
    
    def _data_integration_expert(self, task_description, data):
        """数据整合专家处理函数"""
        try:
            original_text = data.get('original_text', '')
            translated_text = data.get('translated_text', '')
            
            # 创建临时文件保存文本内容
            with tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.txt', encoding='utf-8') as temp_file:
                temp_file.write(f"原文:\n{original_text}\n\n译文:\n{translated_text}")
                temp_file_path = temp_file.name
            
            # 使用OpenAI客户端调用长文本模型
            client = OpenAI(
                api_key=DASHSCOPE_API_KEY,
                base_url=QWEN_API_BASE_URL,
            )
            
            # 上传文件
            file_object = client.files.create(
                file=Path(temp_file_path),
                purpose="file-extract"
            )
            
            # 调用长文本模型
            completion = client.chat.completions.create(
                model=EXPERT_MODELS["数据整合专家"],
                messages=[
                    {'role': 'system', 'content': f'fileid://{file_object.id}'},
                    {'role': 'user', 'content': f'''
                    请分析上述原文和译文，并提供以下内容:
                    1. 原文的主要内容摘要
                    2. 翻译质量评估
                    3. 整体内容的简要总结
                    
                    请以JSON格式返回，格式如下:
                    {{
                        "original_text": "原文",
                        "translated_text": "译文",
                        "summary": "总结内容",
                        "translation_quality": "翻译质量评估"
                    }}
                    '''}
                ]
            )
            
            # 清理临时文件
            os.unlink(temp_file_path)
            
            # 解析结果
            content = completion.choices[0].message.content
            
            # 尝试提取JSON
            try:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = content[json_start:json_end]
                    result = json.loads(json_str)
                    return {"integrated_result": result}
            except:
                pass
            
            # 如果无法解析JSON，则尝试从文本中提取原文和译文
            lines = content.split('\n')
            result = {
                "original_text": original_text,
                "translated_text": translated_text,
                "summary": content,
                "timestamp": str(uuid.uuid4())
            }
            
            return {"integrated_result": result}
        except Exception as e:
            print(f"数据整合出错: {str(e)}")
            # 如果API调用失败，返回模拟数据
            return {
                "integrated_result": {
                    "original_text": original_text,
                    "translated_text": translated_text,
                    "summary": "成功完成OCR识别和翻译任务",
                    "timestamp": str(uuid.uuid4())
                }
            }
    
    def _generic_expert(self, expert_role, task_description, task_input):
        """通用专家处理函数，使用大模型处理未知专家角色的任务"""
        try:
            # 使用OpenAI客户端调用通用模型
            client = OpenAI(
                api_key=DASHSCOPE_API_KEY,
                base_url=QWEN_API_BASE_URL,
            )
            
            # 构建提示词
            prompt = f"""
            作为{expert_role}，请帮助解决以下任务:
            
            任务描述: {task_description}
            
            任务输入: {json.dumps(task_input, ensure_ascii=False)}
            
            请以JSON格式返回结果。
            """
            
            # 调用通用模型
            completion = client.chat.completions.create(
                model=EXPERT_MODELS.get(expert_role, "qwen-plus"),
                messages=[
                    {"role": "system", "content": f"你是一名专业的{expert_role}，专注于解决相关领域的问题。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1500
            )
            
            # 获取结果
            content = completion.choices[0].message.content
            
            # 尝试解析JSON
            try:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = content[json_start:json_end]
                    return json.loads(json_str)
            except:
                pass
            
            # 返回原始结果
            return {"result": content}
        except Exception as e:
            print(f"通用专家处理出错: {str(e)}")
            # 如果API调用失败，返回模拟数据
            return {
                "result": f"[{expert_role}] 已处理任务: {task_description}",
                "details": "这是一个模拟的专家处理结果"
            }


class TaskExecutionView(APIView):
    """
    任务执行API，用于协调和执行整个任务流程
    """
    parser_classes = (MultiPartParser, FormParser)
    
    def post(self, request):
        try:
            task_description = request.data.get('task_description', '')
            if not task_description:
                return ErrorResponse(msg="任务描述不能为空", code=400, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查是否上传了图片，如果有，保存一个副本
            image_temp_path = None
            if 'image' in request.FILES:
                image_file = request.FILES['image']
                # 创建临时文件保存上传的图片
                with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                    for chunk in image_file.chunks():
                        temp_file.write(chunk)
                    image_temp_path = temp_file.name
                    print(f"图像已保存到临时文件: {image_temp_path}")
                # 重置文件指针
                image_file.seek(0)
                print(f"图像文件指针已重置")
            else:
                print("请求中没有图像文件")
            
            # 步骤1: 调用任务规划器分解任务
            planner = TaskPlannerView()
            planner_response = planner.post(request).data
            
            if 'error' in planner_response:
                # 清理临时文件
                if image_temp_path and os.path.exists(image_temp_path):
                    os.unlink(image_temp_path)
                return ErrorResponse(msg=planner_response.get('error', '任务规划失败'), code=500, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            subtasks = planner_response.get('data', {}).get('subtasks', [])
            task_id = planner_response.get('data', {}).get('task_id')
            
            # 步骤2: 按照依赖关系执行子任务
            results = {}
            expert_agent = ExpertAgentView()
            
            # 创建依赖图
            dependency_graph = {task['id']: set(task['depends_on']) for task in subtasks}
            completed_tasks = set()
            task_data = {task['id']: task for task in subtasks}
            
            # 执行没有依赖的任务
            while len(completed_tasks) < len(subtasks):
                for task_id, dependencies in dependency_graph.items():
                    if task_id in completed_tasks:
                        continue
                    
                    if dependencies.issubset(completed_tasks):
                        # 所有依赖都已完成，可以执行此任务
                        task = task_data[task_id]
                        
                        # 准备任务输入
                        task_input = {
                            'expert_role': task['expert'],
                            'task_description': task['description'],
                            'task_input': {}
                        }
                        
                        # 添加依赖任务的结果作为输入
                        for dep_id in task['depends_on']:
                            if dep_id in results:
                                task_input['task_input'][dep_id] = results[dep_id]
                        
                        # 处理特殊情况
                        mock_files = {}
                        
                        # 如果需要图像，从临时文件创建新的文件对象
                        if (task['expert'] == 'OCR技术专家' or task['expert'] == 'OCR专家' or task['expert'] == '图像处理专家') and image_temp_path:
                            # 从临时文件创建新的InMemoryUploadedFile
                            with open(image_temp_path, 'rb') as f:
                                file_content = f.read()
                                print(f"为{task['expert']}读取临时文件内容，大小: {len(file_content)} 字节")
                                
                            # 创建类似于InMemoryUploadedFile的对象
                            from django.core.files.uploadedfile import InMemoryUploadedFile
                            import io
                            
                            # 获取原始文件名和内容类型
                            if 'image' in request.FILES:
                                original_name = request.FILES['image'].name
                                content_type = request.FILES['image'].content_type
                                print(f"使用原始文件名: {original_name}, 内容类型: {content_type}")
                            else:
                                original_name = 'image.jpg'
                                content_type = 'image/jpeg'
                                print(f"使用默认文件名和内容类型")
                            
                            # 创建文件对象
                            file_obj = InMemoryUploadedFile(
                                io.BytesIO(file_content),
                                'image',
                                original_name,
                                content_type,
                                len(file_content),
                                None
                            )
                            
                            mock_files = {'image': file_obj}
                            print(f"已为{task['expert']}创建图像文件对象，大小: {len(file_content)} 字节")
                            print(f"mock_files内容: {list(mock_files.keys())}")
                        
                        # 处理翻译任务
                        if (task['expert'] == '翻译专家' or task['expert'] == '语言翻译专家') and 'task2' in results:
                            # 如果有OCR结果，传递给翻译专家
                            task_input['text'] = results['task2'].get('text', '')
                            # 根据任务描述确定翻译目标语言
                            if '翻译成英文' in task_description or '翻译为英文' in task_description or 'translate to English' in task_description.lower():
                                task_input['target_language'] = 'en'
                            else:
                                task_input['target_language'] = 'zh'  # 默认翻译成中文
                        
                        # 处理数据整合任务
                        elif task['expert'] == '数据整合专家':
                            # 整合OCR和翻译结果
                            if 'task2' in results:
                                task_input['original_text'] = results['task2'].get('text', '')
                            if 'task3' in results:
                                task_input['translated_text'] = results['task3'].get('translated_text', '')
                        
                        # 调用专家代理执行任务
                        # 创建正确的MockRequest对象，确保FILES属性正确设置
                        class MockRequest:
                            def __init__(self, data, files):
                                self.data = data
                                self.FILES = files
                                # 确保data属性包含所有必要的字段
                                self.data.update({
                                    'expert_role': task['expert'],
                                    'task_description': task['description']
                                })
                        
                        mock_request = MockRequest(task_input, mock_files)
                        result = expert_agent.post(mock_request).data
                        
                        # 存储结果
                        results[task_id] = result.get('data', {})
                        completed_tasks.add(task_id)
                        break
                else:
                    # 如果没有任务可以执行，说明存在循环依赖
                    # 清理临时文件
                    if image_temp_path and os.path.exists(image_temp_path):
                        os.unlink(image_temp_path)
                    return ErrorResponse(msg="任务依赖关系存在循环，无法完成执行", code=400, status=status.HTTP_400_BAD_REQUEST)
            
            # 清理临时文件
            if image_temp_path and os.path.exists(image_temp_path):
                os.unlink(image_temp_path)
            
            # 步骤3: 返回最终结果
            return DetailResponse(data={
                "task_id": task_id,
                "task_description": task_description,
                "subtasks": subtasks,
                "results": results
            })
        except Exception as e:
            # 确保清理临时文件
            if 'image_temp_path' in locals() and image_temp_path and os.path.exists(image_temp_path):
                os.unlink(image_temp_path)
            return ErrorResponse(msg=str(e), code=500, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
