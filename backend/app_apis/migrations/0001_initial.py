# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.db import migrations, models
import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='APIS',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('path', models.CharField(help_text='路由地址', max_length=64, verbose_name='路由地址')),
                ('description', models.Char<PERSON>ield(blank=True, help_text='api中文描述', max_length=150, null=True, verbose_name='api中文描述')),
                ('api_group', models.CharField(help_text='api组', max_length=150, verbose_name='api组')),
                ('method', models.CharField(choices=[('POST', '创建'), ('GET', '查看'), ('PUT', '更新'), ('DELETE', '删除')], default='POST', help_text='创建POST(默认)|查看GET|更新PUT|删除DELETE', max_length=6, verbose_name='创建POST(默认)|查看GET|更新PUT|删除DELETE')),
                ('enable_datasource', models.CharField(choices=[('0', '需要'), ('1', '不需要')], db_index=True, default='0', help_text='激活数据权限', max_length=1, verbose_name='激活数据权限')),
            ],
            options={
                'verbose_name': '系统-API接口表',
                'verbose_name_plural': '系统-API接口表',
                'db_table': 'sys_apis',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
