import json
import os
import requests
import uuid
from django.http import JsonResponse
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.parsers import MultiPart<PERSON>ars<PERSON>, FormParser
from django.conf import settings

# 阿里云通义千问API配置
DASHSCOPE_API_KEY = os.environ.get('DASHSCOPE_API_KEY', '')
QWEN_API_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
QWEN_API_ENDPOINT = f"{QWEN_API_BASE_URL}/chat/completions"

class TaskPlannerView(APIView):
    """
    任务规划器API，用于分解任务为子任务
    """
    def post(self, request):
        try:
            # 获取用户输入的任务描述
            task_description = request.data.get('task_description', '')
            if not task_description:
                return Response({"error": "任务描述不能为空"}, status=status.HTTP_400_BAD_REQUEST)
            
            # 调用大模型API，分解任务
            subtasks = self._generate_subtasks(task_description)
            
            return Response({
                "task_id": str(uuid.uuid4()),
                "task_description": task_description,
                "subtasks": subtasks
            })
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _generate_subtasks(self, task_description):
        """调用大模型API分解任务"""
        if not DASHSCOPE_API_KEY:
            # 如果没有配置API Key，返回模拟数据
            return self._mock_subtasks(task_description)
        
        # 构建提示词，要求大模型分解任务
        prompt = f"""
        我需要你帮我分解以下任务为可执行的子任务列表，并为每个子任务指定合适的专家角色。
        
        任务描述: {task_description}
        
        请以JSON格式返回子任务列表，格式如下:
        {{
            "subtasks": [
                {{
                    "id": "task1",
                    "name": "子任务名称",
                    "description": "子任务详细描述",
                    "expert": "专家角色名称",
                    "required_tools": ["工具1", "工具2"],
                    "depends_on": [] // 依赖的其他子任务ID
                }},
                // 更多子任务...
            ]
        }}
        
        请确保JSON格式正确，并且子任务之间的依赖关系合理。
        """
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {DASHSCOPE_API_KEY}"
        }
        
        payload = {
            "model": "qwen-plus",
            "messages": [
                {"role": "system", "content": "你是一个专业的任务分解助手，擅长将复杂任务分解为可执行的子任务。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.2,  # 低温度以获得更确定性的输出
            "max_tokens": 2000
        }
        
        try:
            response = requests.post(QWEN_API_ENDPOINT, headers=headers, json=payload)
            response.raise_for_status()
            
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            # 从内容中提取JSON
            json_start = content.find('{')
            json_end = content.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                subtasks_data = json.loads(json_str)
                return subtasks_data.get('subtasks', [])
            
            return []
        except Exception as e:
            print(f"调用大模型API出错: {str(e)}")
            return self._mock_subtasks(task_description)
    
    def _mock_subtasks(self, task_description):
        """生成模拟的子任务数据"""
        if "ocr" in task_description.lower() and "翻译" in task_description:
            return [
                {
                    "id": "task1",
                    "name": "图像预处理",
                    "description": "对上传的图像进行预处理，包括调整大小、增强对比度等",
                    "expert": "图像处理专家",
                    "required_tools": ["图像处理库"],
                    "depends_on": []
                },
                {
                    "id": "task2",
                    "name": "OCR文字识别",
                    "description": "识别图像中的文字内容",
                    "expert": "OCR专家",
                    "required_tools": ["OCR引擎"],
                    "depends_on": ["task1"]
                },
                {
                    "id": "task3",
                    "name": "文本翻译",
                    "description": "将识别出的文字翻译成目标语言",
                    "expert": "翻译专家",
                    "required_tools": ["翻译API"],
                    "depends_on": ["task2"]
                },
                {
                    "id": "task4",
                    "name": "结果整合",
                    "description": "整合OCR识别和翻译结果，生成最终输出",
                    "expert": "数据整合专家",
                    "required_tools": ["数据处理工具"],
                    "depends_on": ["task3"]
                }
            ]
        else:
            # 默认的通用任务分解
            return [
                {
                    "id": "task1",
                    "name": "任务分析",
                    "description": f"分析'{task_description}'的需求和目标",
                    "expert": "需求分析师",
                    "required_tools": ["分析工具"],
                    "depends_on": []
                },
                {
                    "id": "task2",
                    "name": "方案设计",
                    "description": "设计解决方案和执行计划",
                    "expert": "方案设计师",
                    "required_tools": ["设计工具"],
                    "depends_on": ["task1"]
                },
                {
                    "id": "task3",
                    "name": "执行任务",
                    "description": "执行设计好的解决方案",
                    "expert": "执行专家",
                    "required_tools": ["执行工具"],
                    "depends_on": ["task2"]
                }
            ]


class ExpertAgentView(APIView):
    """
    专家智能体API，用于执行特定子任务
    """
    parser_classes = (MultiPartParser, FormParser)
    
    def post(self, request):
        try:
            expert_role = request.data.get('expert_role', '')
            task_description = request.data.get('task_description', '')
            task_input = request.data.get('task_input', {})
            
            if not expert_role or not task_description:
                return Response({"error": "专家角色和任务描述不能为空"}, status=status.HTTP_400_BAD_REQUEST)
            
            # 根据专家角色调用相应的处理函数
            if expert_role == "OCR专家":
                result = self._ocr_expert(task_description, request)
            elif expert_role == "翻译专家":
                result = self._translation_expert(task_description, request.data)
            elif expert_role == "图像处理专家":
                result = self._image_processing_expert(task_description, request)
            elif expert_role == "数据整合专家":
                result = self._data_integration_expert(task_description, request.data)
            else:
                # 对于未知的专家角色，使用通用大模型处理
                result = self._generic_expert(expert_role, task_description, task_input)
            
            return Response(result)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _ocr_expert(self, task_description, request):
        """OCR专家处理函数"""
        try:
            # 检查是否上传了图片
            if 'image' not in request.FILES:
                return {"error": "未上传图片文件"}
            
            image_file = request.FILES['image']
            
            if not DASHSCOPE_API_KEY:
                # 模拟OCR结果
                return {
                    "text": "This is a sample OCR text recognition result. The quick brown fox jumps over the lazy dog.",
                    "confidence": 0.95
                }
            
            # 这里可以调用实际的OCR API，例如阿里云的OCR服务
            # 为简化示例，这里使用通义千问的多模态能力来处理图像
            # 实际应用中应该使用专门的OCR服务
            
            # 模拟OCR处理
            return {
                "text": "This is a sample OCR text recognition result. The quick brown fox jumps over the lazy dog.",
                "confidence": 0.95
            }
        except Exception as e:
            return {"error": f"OCR处理出错: {str(e)}"}
    
    def _translation_expert(self, task_description, data):
        """翻译专家处理函数"""
        try:
            text = data.get('text', '')
            target_language = data.get('target_language', 'zh')
            
            if not text:
                return {"error": "待翻译文本不能为空"}
            
            if not DASHSCOPE_API_KEY:
                # 模拟翻译结果
                if "quick brown fox" in text:
                    return {"translated_text": "这是一个示例的OCR文本识别结果。敏捷的棕色狐狸跳过了懒惰的狗。"}
                return {"translated_text": f"[翻译结果] {text}"}
            
            # 调用通义千问API进行翻译
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {DASHSCOPE_API_KEY}"
            }
            
            prompt = f"""
            请将以下文本翻译成{target_language}语言:
            
            {text}
            
            只需返回翻译结果，不要添加任何解释或额外内容。
            """
            
            payload = {
                "model": "qwen-plus",
                "messages": [
                    {"role": "system", "content": "你是一个专业的翻译专家，能够准确翻译各种语言。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1
            }
            
            response = requests.post(QWEN_API_ENDPOINT, headers=headers, json=payload)
            response.raise_for_status()
            
            result = response.json()
            translated_text = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            return {"translated_text": translated_text}
        except Exception as e:
            return {"error": f"翻译处理出错: {str(e)}"}
    
    def _image_processing_expert(self, task_description, request):
        """图像处理专家处理函数"""
        try:
            # 检查是否上传了图片
            if 'image' not in request.FILES:
                return {"error": "未上传图片文件"}
            
            # 模拟图像处理结果
            return {
                "status": "success",
                "message": "图像预处理完成",
                "processed_image_url": "/temp_uploads/processed_image.jpg"
            }
        except Exception as e:
            return {"error": f"图像处理出错: {str(e)}"}
    
    def _data_integration_expert(self, task_description, data):
        """数据整合专家处理函数"""
        try:
            original_text = data.get('original_text', '')
            translated_text = data.get('translated_text', '')
            
            # 整合OCR和翻译结果
            return {
                "integrated_result": {
                    "original_text": original_text,
                    "translated_text": translated_text,
                    "summary": "成功完成OCR识别和翻译任务",
                    "timestamp": str(uuid.uuid4())
                }
            }
        except Exception as e:
            return {"error": f"数据整合出错: {str(e)}"}
    
    def _generic_expert(self, expert_role, task_description, task_input):
        """通用专家处理函数，使用大模型处理未知专家角色的任务"""
        try:
            if not DASHSCOPE_API_KEY:
                # 模拟通用专家结果
                return {
                    "result": f"[{expert_role}] 已处理任务: {task_description}",
                    "details": "这是一个模拟的专家处理结果"
                }
            
            # 构建提示词
            prompt = f"""
            你现在是一个专业的{expert_role}，需要处理以下任务:
            
            任务描述: {task_description}
            
            任务输入: {json.dumps(task_input, ensure_ascii=False)}
            
            请提供专业的解决方案和处理结果。
            """
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {DASHSCOPE_API_KEY}"
            }
            
            payload = {
                "model": "qwen-plus",
                "messages": [
                    {"role": "system", "content": f"你是一个专业的{expert_role}，擅长解决相关领域的问题。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3
            }
            
            response = requests.post(QWEN_API_ENDPOINT, headers=headers, json=payload)
            response.raise_for_status()
            
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            return {
                "result": content,
                "expert": expert_role
            }
        except Exception as e:
            return {"error": f"通用专家处理出错: {str(e)}"}


class TaskExecutionView(APIView):
    """
    任务执行API，用于协调和执行整个任务流程
    """
    parser_classes = (MultiPartParser, FormParser)
    
    def post(self, request):
        try:
            task_description = request.data.get('task_description', '')
            if not task_description:
                return Response({"error": "任务描述不能为空"}, status=status.HTTP_400_BAD_REQUEST)
            
            # 步骤1: 调用任务规划器分解任务
            planner = TaskPlannerView()
            planner_response = planner.post(request).data
            
            if 'error' in planner_response:
                return Response(planner_response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            subtasks = planner_response.get('subtasks', [])
            task_id = planner_response.get('task_id')
            
            # 步骤2: 按照依赖关系执行子任务
            results = {}
            expert_agent = ExpertAgentView()
            
            # 创建依赖图
            dependency_graph = {task['id']: set(task['depends_on']) for task in subtasks}
            completed_tasks = set()
            task_data = {task['id']: task for task in subtasks}
            
            # 执行没有依赖的任务
            while len(completed_tasks) < len(subtasks):
                for task_id, dependencies in dependency_graph.items():
                    if task_id in completed_tasks:
                        continue
                    
                    if dependencies.issubset(completed_tasks):
                        # 所有依赖都已完成，可以执行此任务
                        task = task_data[task_id]
                        
                        # 准备任务输入
                        task_input = {
                            'expert_role': task['expert'],
                            'task_description': task['description'],
                            'task_input': {}
                        }
                        
                        # 添加依赖任务的结果作为输入
                        for dep_id in task['depends_on']:
                            if dep_id in results:
                                task_input['task_input'][dep_id] = results[dep_id]
                        
                        # 如果是OCR任务且有图片，添加图片
                        if 'image' in request.FILES and task['expert'] == 'OCR专家':
                            task_input['image'] = request.FILES['image']
                        
                        # 调用专家代理执行任务
                        mock_request = type('MockRequest', (), {'data': task_input, 'FILES': request.FILES})
                        result = expert_agent.post(mock_request).data
                        
                        # 存储结果
                        results[task_id] = result
                        completed_tasks.add(task_id)
                        break
                else:
                    # 如果没有任务可以执行，说明存在循环依赖
                    return Response({"error": "任务依赖关系存在循环，无法完成执行"}, status=status.HTTP_400_BAD_REQUEST)
            
            # 步骤3: 返回最终结果
            return Response({
                "task_id": task_id,
                "task_description": task_description,
                "subtasks": subtasks,
                "results": results
            })
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 