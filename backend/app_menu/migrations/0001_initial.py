# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.db import migrations, models
import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Menu',
            fields=[
                ('id', utils.models.SnowflakeIDField(primary_key=True, serialize=False)),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('update_datetime', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('icon', models.CharField(blank=True, help_text='菜单图标', max_length=64, null=True, verbose_name='菜单图标')),
                ('menu_name', models.Char<PERSON>ield(help_text='菜单名称', max_length=64, verbose_name='菜单名称')),
                ('sort', models.IntegerField(default=1, help_text='显示排序', verbose_name='显示排序')),
                ('path', models.CharField(blank=True, help_text='路由地址', max_length=64, null=True, verbose_name='路由地址')),
                ('component', models.CharField(blank=True, help_text='组件地址', max_length=128, null=True, verbose_name='组件地址')),
                ('is_iframe', models.CharField(blank=True, choices=[('0', '是'), ('1', '否')], help_text='是否内嵌', max_length=1, null=True, verbose_name='是否内嵌')),
                ('is_link', models.CharField(blank=True, help_text='是否超级链接', max_length=225, null=True, verbose_name='是否超级链接')),
                ('menu_type', models.CharField(choices=[('M', '目录'), ('C', '菜单'), ('F', '按钮')], help_text='菜单类型（M目录 C菜单 F按钮）', max_length=1, verbose_name='菜单类型（M目录 C菜单 F按钮）')),
                ('is_hide', models.CharField(blank=True, choices=[('0', '显示'), ('1', '隐藏')], help_text='显示状态（0显示 1隐藏）', max_length=1, null=True, verbose_name='显示状态（0显示 1隐藏）')),
                ('is_keep_alive', models.CharField(blank=True, choices=[('0', '显示'), ('1', '隐藏')], help_text='是否缓存组件状态（0是 1否）', max_length=1, null=True, verbose_name='是否缓存组件状态（0是 1否）')),
                ('is_affix', models.CharField(blank=True, choices=[('0', '显示'), ('1', '隐藏')], help_text='是否固定在 tagsView 栏上（0是 1否）', max_length=1, null=True, verbose_name='是否固定在 tagsView 栏上（0是 1否）')),
                ('permission', models.CharField(blank=True, help_text='权限标识', max_length=32, null=True, verbose_name='权限标识')),
                ('status', models.CharField(choices=[('0', '正常'), ('1', '停用')], default='0', help_text='菜单状态（0正常 1停用）', max_length=1, verbose_name='菜单状态（0正常 1停用）')),
                ('remark', models.CharField(blank=True, help_text='备注', max_length=150, null=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '系统-菜单表',
                'verbose_name_plural': '系统-菜单表',
                'db_table': 'sys_menu',
                'ordering': ('sort',),
            },
        ),
    ]
