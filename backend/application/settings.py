"""
Django settings for application project.

Generated by 'django-admin startproject' using Django 4.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
import os
from pathlib import Path
import platform
import socket

from decouple import Config, RepositoryEnv

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
CASBIN_MODEL = os.path.join(BASE_DIR, "model.conf")

plat = platform.system().lower()
hostname = socket.gethostname()

if plat == 'windows':
    # Windows环境，加载开发环境配置文件
    config = Config(RepositoryEnv(os.path.join(BASE_DIR, ".env.development")))
    print("Windows环境，加载开发环境配置文件.env.development")
else:
    # Linux环境
    if 'qianlan' in hostname: # 虚拟机环境
        config = Config(RepositoryEnv(os.path.join(BASE_DIR, ".env.vmware.production")))
        print("Linux虚拟机环境，加载生产环境配置文件.env.vmware.production")
    else: # 小白楼环境
        config = Config(RepositoryEnv(os.path.join(BASE_DIR, ".env.production")))
        print("Linux小白楼环境，加载生产环境配置文件.env.production")

# redis
REDIS_URL = config("REDIS_URL")
# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-1k#8hh7*c0*hjxy_0#0j69rzdk%@5l1aqmio2+oq1vy57&)^-*'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config("DEBUG")

ALLOWED_HOSTS = ["*"]

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_comment_migrate',  # 注释
    'django_celery_results',
    'django_celery_beat',  # 计划任务
    'corsheaders',  # 跨域
    'rest_framework',  # drf
    'drf_yasg',  # 文档
    'rest_framework_simplejwt.token_blacklist',
    'import_export',  # 数据导出
    'django_filters',  # 过滤
    'captcha',  # 验证码
    'channels',  # django通过其实现websocket
    'casbin_adapter',  # api权限
    'app_post',  # 系统-岗位
    'app_dept',  # 系统-部门
    'app_menu',  # 系统-菜单
    'app_apis',  # 系统-API
    'app_role',  # 系统-角色
    'app_user',  # 系统-用户
    'app_login',  # 系统-登录
    'app_dict',  # 系统-字典
    'app_crontab',  # celery定时任务
    'app_monitor',  # 任务监控
    'app_operation_log',  # 操作日志
    'app_message',  # 信息中心
    'app_init',  # 数据初始化
    'app_dataset', # 数据集管理
    'app_model', # 模型管理
    'app_algorithm', # 算法库管理
    'app_model_deploy', # 模型部署
    # 'app_multi_agent', # 多智能体任务处理
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',  # 跨域中间件
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'utils.middleware.ApiLoggingMiddleware',  # 自定义日志中间件
]

ROOT_URLCONF = 'application.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'web')],  # 放置前端页面的地方
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'application.wsgi.application'

# ================================================= #
# ********************* channels配置 ******************* #
# ================================================= #
ASGI_APPLICATION = 'application.asgi.application'
CHANNEL_LAYERS = {
    'default': {
        # 1、使用内存作为通道（开发使用）
        # "BACKEND": "channels.layers.InMemoryChannelLayer",
        # 2、使用redis（上线使用）
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [(REDIS_URL)],  # 需修改
        },
    },
}

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': config("DATABASE_ENGINE"),
        'NAME': config("DATABASE_NAME"),
        'USER': config("DATABASE_USER"),
        'PASSWORD': config("DATABASE_PASSWORD"),
        'HOST': config("DATABASE_HOST"),
        'PORT': int(config("DATABASE_PORT")),
        'CONN_MAX_AGE': int(config("DATABASE_CONN_MAX_AGE")),
        'OPTIONS': {
            'connect_timeout': 30,  # 设置连接超时时间为30秒
            'charset': config("DATABASE_CHARSET"),
            'init_command': 'SET default_storage_engine=INNODB',  # innodb才支持事务
        }
    }
}
AUTH_USER_MODEL = "app_user.Users"

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'zh-Hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False  # 设置为中国时间

# 模型部署配置
MODEL_DEPLOY_CONFIG = {
    'SERVER_HOST': config('MODEL_DEPLOY_SERVER_HOST', default='127.0.0.1'),
    'SERVER_PORT': config('MODEL_DEPLOY_SERVER_PORT', default='8000'),
    'DEFAULT_SERVICE_PORT_RANGE': (8080, 9000),
}

# 获取完整的服务器地址
def get_server_url():
    host = MODEL_DEPLOY_CONFIG['SERVER_HOST']
    port = MODEL_DEPLOY_CONFIG['SERVER_PORT']
    return f"http://{host}:{port}"

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'

# 加入下面代码
# 这个是设置静态文件夹目录的路径
STATICFILES_DIRS = (
    os.path.join(BASE_DIR, 'static'),
)

# 配置媒体文件路径
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# MinIO 配置
MINIO_ENDPOINT = config("MINIO_ENDPOINT")
MINIO_ACCESS_KEY = config("MINIO_ROOT_USER")
MINIO_SECRET_KEY = config("MINIO_ROOT_PASSWORD")
MINIO_SECURE = False
MINIO_PUBLIC_URL = config("MINIO_PUBLIC_URL")
MINIO_DATASET_BUCKET = config("MINIO_DATASET_BUCKET")
MINIO_MODEL_BUCKET = config("MINIO_MODEL_BUCKET")

# 向量服务配置
VECTOR_SERVICE = {
    'ENABLE_SEMANTIC_SEARCH': False,  # 是否启用语义检索功能
    'REBUILD_INDEX_ON_STARTUP': False,  # 是否在启动时重建向量索引
    'AUTO_UPDATE_EMBEDDINGS': False,  # 是否在模型修改时自动更新向量嵌入
    'SIMILARITY_THRESHOLD': 0.4,  # 相似度阈值，0-1之间
    'TOP_K': 3,  # 返回的最大结果数量
    'MODEL_NAME': 'BAAI/bge-m3',  # 使用的模型名称
    'USE_FP16': True,  # 是否使用半精度
    'POOLING_METHOD': 'cls',  # 池化方法
    'MODEL_SOURCE': 'local',  # 模型来源: 'local', 'hub', 'auto'
    'LOCAL_MODEL_DIR': os.path.join(BASE_DIR, 'app_model', 'BGE_Weight', 'BAAI', 'bge-m3')  # 本地模型目录
}

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# ================================================= #
# *************** REST_FRAMEWORK配置 *************** #
# ================================================= #

REST_FRAMEWORK = {
    'DATETIME_FORMAT': "%Y-%m-%d %H:%M:%S",  # 日期时间格式配置
    'DATE_FORMAT': "%Y-%m-%d",
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ),
    'DEFAULT_PAGINATION_CLASS': 'utils.pagination.CustomPagination',  # 自定义分页
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        # 'rest_framework_simplejwt.authentication.JWTTokenUserAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    # "DEFAULT_PERMISSION_CLASSES": [
    #     "rest_framework.permissions.IsAuthenticated",  # 只有经过身份认证确定用户身份才能访问
    #     # 'rest_framework.permissions.IsAdminUser', # is_staff=True才能访问 —— 管理员(员工)权限
    #     # 'rest_framework.permissions.AllowAny', # 允许所有
    #     # 'rest_framework.permissions.IsAuthenticatedOrReadOnly', # 有身份 或者 只读访问(self.list,self.retrieve)
    # ],
    # 限速设置
    'DEFAULT_THROTTLE_CLASSES': (
        'rest_framework.throttling.AnonRateThrottle',  # 未登陆用户
        'rest_framework.throttling.UserRateThrottle'  # 登陆用户
    ),
    'DEFAULT_THROTTLE_RATES': {
        'anon': '30/minute',  # 未登录用户每分钟可以请求30次，还可以设置'100/day',天数
        'user': '60/minute'  # 已登录用户每分钟可以请求60次
    },
    'EXCEPTION_HANDLER': 'utils.exception.CustomExceptionHandler',  # 自定义的异常处理
    # 线上部署正式环境，关闭web接口测试页面
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
    ),
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
}

# # ================================================= #
# # ********************* 日志配置 ******************* #
# # ================================================= #
# # log 配置部分BEGIN #
SERVER_LOGS_FILE = os.path.join(BASE_DIR, "logs", "server.log")
ERROR_LOGS_FILE = os.path.join(BASE_DIR, "logs", "error.log")
LOGS_FILE = os.path.join(BASE_DIR, "logs")
if not os.path.exists(os.path.join(BASE_DIR, "logs")):
    os.makedirs(os.path.join(BASE_DIR, "logs"))

# 格式:[2020-04-22 23:33:01][micoservice.apps.ready():16] [INFO] 这是一条日志:
# 格式:[日期][模块.函数名称():行号] [级别] 信息
STANDARD_LOG_FORMAT = (
    "[%(asctime)s][%(name)s.%(funcName)s():%(lineno)d] [%(levelname)s] %(message)s"
)
CONSOLE_LOG_FORMAT = (
    "[%(asctime)s][%(name)s.%(funcName)s():%(lineno)d] [%(levelname)s] %(message)s"
)
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {"format": STANDARD_LOG_FORMAT},
        "console": {
            "format": CONSOLE_LOG_FORMAT,
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "file": {
            "format": CONSOLE_LOG_FORMAT,
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    },
    "handlers": {
        "file": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": SERVER_LOGS_FILE,
            "maxBytes": 1024 * 1024 * 100,  # 100 MB
            "backupCount": 5,  # 最多备份5个
            "formatter": "standard",
            "encoding": "utf-8",
        },
        "error": {
            "level": "ERROR",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": ERROR_LOGS_FILE,
            "maxBytes": 1024 * 1024 * 100,  # 100 MB
            "backupCount": 3,  # 最多备份3个
            "formatter": "standard",
            "encoding": "utf-8",
        },
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "console",
        },

    },
    "loggers": {
        "": {
            "handlers": ["console", "error", "file"],
            "level": "INFO",
        },
        "django": {
            "handlers": ["console", "error", "file"],
            "level": "INFO",
            "propagate": False,
        },
        'django.db.backends': {
            'handlers': ["console", "error", "file"],
            'propagate': False,
            'level': "INFO"
        },
        "uvicorn.error": {
            "level": "INFO",
            "handlers": ["console", "error", "file"],
        },
        "uvicorn.access": {
            "handlers": ["console", "error", "file"],
            "level": "INFO"
        },
        "casbin": {  # 添加 casbin 配置
            "handlers": ["console", "error", "file"],
            "level": "WARNING",
        },
    },
}

# ================================================= #
# ****************** simplejwt配置 ***************** #
# ================================================= #
from datetime import timedelta

SIMPLE_JWT = {
    # token有效时长
    "ACCESS_TOKEN_LIFETIME": timedelta(days=7),
    # token刷新后的有效时间
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),
    # 设置前缀
    "AUTH_HEADER_TYPES": ("JWT",),
    "ROTATE_REFRESH_TOKENS": True,
    'BLACKLIST_AFTER_ROTATION': True,
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
}

# ================================================= #
# ******************* 跨域的配置 ******************* #
# ================================================= #
# 如果为True，则将不使用白名单，并且将接受所有来源。默认为False
# 允许跨域
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_ALL_ORIGINS = True  # 新版 ACCESS_CONTROL_ALLOW_ORIGIN = '*' ,不能与CORS_ALLOW_CREDENTIALS一起使用
# 允许cookie
# CORS_ALLOW_CREDENTIALS = True  # 指明在跨域访问中，后端是否支持对cookie的操作
SECURE_CROSS_ORIGIN_OPENER_POLICY = 'None'

CORS_ALLOW_METHODS = (  # 允许的方法
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
    'VIEW',
)

CORS_ALLOW_HEADERS = (  # 允许的请求头
    'XMLHttpRequest',
    'X_FILENAME',
    'accept-encoding',
    'authorization',
    'Authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'Pragma',
)

# ================================================= #
# **************** 验证码配置  ******************* #
# ================================================= #
CAPTCHA_IMAGE_SIZE = (160, 60)  # 设置 captcha 图片大小
CAPTCHA_LENGTH = 4  # 字符个数
CAPTCHA_TIMEOUT = 1  # 超时(minutes)
CAPTCHA_OUTPUT_FORMAT = "%(image)s %(text_field)s %(hidden_field)s "
CAPTCHA_FONT_SIZE = 40  # 字体大小
CAPTCHA_FOREGROUND_COLOR = "#64DAAA"  # 前景色
CAPTCHA_BACKGROUND_COLOR = "#F5F7F4"  # 背景色
CAPTCHA_NOISE_FUNCTIONS = (
    "captcha.helpers.noise_arcs",  # 线
    # "captcha.helpers.noise_dots",  # 点
)
# CAPTCHA_CHALLENGE_FUNCT = 'captcha.helpers.random_char_challenge' #字母验证码
CAPTCHA_CHALLENGE_FUNCT = "captcha.helpers.math_challenge"  # 加减乘除验证码

# ================================================= #
# ******************** celery配置 ******************** #
# ================================================= #
CELERY_TIMEZONE = 'Asia/Shanghai'  # celery 时区问题
CELERY_BROKER_URL = f'{REDIS_URL}/11'  # Broker配置，使用Redis作为消息中间件
# CELERY_RESULT_BACKEND = 'redis://127.0.0.1:6379/11' # 把任务结果存在了Redis
CELERY_RESULT_BACKEND = 'django-db'  # celery结果存储到数据库中django-db
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'  # Backend数据库
CELERY_RESULT_PERSISTENT = True
CELERY_RESULT_EXTENDED = True
DJANGO_CELERY_BEAT_TZ_AWARE = False
CELERY_ENABLE_UTC = False
BROKER_TRANSPORT_OPTIONS = {'visibility_timeout': 3600}  # 连接超时
CELERY_TASK_SERIALIZER = 'json'  # 任务序列化和反序列化使json
CELERY_RESULT_SERIALIZER = 'json'
# CELERYD_CONCURRENCY = 2  #并发worker数量
CELERY_WORKER_CONCURRENCY = 2  # 并发数
CELERYD_FORCE_EXECV = True  # 防止死锁,应确保为True
CELERY_TASK_TIME_LIMIT = 60 * 30 * 5  # 限制celery任务执行时间，# 单个任务的运行时间限制，否则会被杀死
CELERYD_MAX_TASKS_PER_CHILD = 100  # worker执行100个任务自动销毁，防止内存泄露
CELERYD_TASK_SOFT_TIME_LIMIT = 6000  # 单个任务的运行时间不超过此值(秒)，否则会抛出(SoftTimeLimitExceeded)异常停止任务
CELERY_DISABLE_RATE_LIMITS = True  # 即使任务设置了明确的速率限制，也禁用所有速率限制。
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True

# ================================================= #
# ******************** redis缓存配置 ******************** #
# ================================================= #
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',  # 缓存后端 Redis
        # 连接Redis数据库(服务器地址)
        # 一主带多从(可以配置多个Redis，写走第一台，读走其他的机器)
        'LOCATION': [
            f'{REDIS_URL}/0',
        ],
        'KEY_PREFIX': 'pao',  # 项目名当做文件前缀
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',  # 连接选项(默认，不改)
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 512,  # 连接池的连接(最大连接)
            },
        }
    },
    'session': {  # 缓存session
        'BACKEND': 'django_redis.cache.RedisCache',  # 缓存后端 Redis
        # 连接Redis数据库(服务器地址)
        # 一主带多从(可以配置多个Redis，写走第一台，读走其他的机器)
        'LOCATION': [
            f'{REDIS_URL}/1',
        ],
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',  # 连接选项(默认，不改)
        }
    },
    "singletoken": {  # jwt单用户登录（确保一个账户只有一个地点登录，后一个会顶掉前一个）
        'BACKEND': 'django_redis.cache.RedisCache',  # 缓存后端 Redis
        # 连接Redis数据库(服务器地址)
        # 一主带多从(可以配置多个Redis，写走第一台，读走其他的机器)
        'LOCATION': [
            f'{REDIS_URL}/5',
        ],
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',  # 连接选项(默认，不改)
            'CONNECTION_POOL_KWARGS': {'decode_responses': True},  # 添加这一行,防止取出的值带有b'' bytes
        }
    },
}

EXEC_LOG_PATH = os.path.join(BASE_DIR, 'logs', 'paopao.log')
TEMP_EXEC_PATH = os.path.join(BASE_DIR, 'logs')

# ******************** 其他配置 ******************** #
# ================================================= #
API_LOG_ENABLE = True  # 全局控制日志记录
API_LOG_METHODS = ['POST', 'UPDATE', 'DELETE', 'PUT']  # ['POST', 'DELETE']
# 日志记录显示的请求模块中文名映射
API_MODEL_MAP = {
    "/login/": "登录模块",
    "/system/user/auth/": "获取用户个人权限",
}
IS_SINGLE_TOKEN = False
LOG_FORMAT = {
    "console": {
        "()": "utils.log_util.ColoredFormatter",
    },
}
