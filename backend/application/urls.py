"""
URL configuration for application project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

from app_login.views import LoginView
from rest_framework.documentation import include_docs_urls
from rest_framework.permissions import AllowAny

from drf_yasg.views import get_schema_view
from drf_yasg import openapi
schema_view = get_schema_view(
   openapi.Info(
      title="API Documentation",
      default_version='v1',
      description="API documentation for Django project",
   ),
   public=True,
   permission_classes=[AllowAny],
)

urlpatterns = [
    path('docs/', include_docs_urls(title='API文档', permission_classes=[AllowAny])),
    path('swagger<format>/', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    # path('getCaptcha/', CaptchaView.as_view()),
    path("login/", LoginView.as_view(), name="token_obtain_pair"),
    path('admin/', admin.site.urls),
    path('system/', include('app_post.urls')),
    path('system/', include('app_dept.urls')),
    path('system/', include('app_apis.urls')),
    path('system/', include('app_menu.urls')),
    path('system/', include('app_role.urls')),
    path('system/', include('app_dict.urls')),
    path('system/', include('app_user.urls')),
    path('system/', include('app_operation_log.urls')),
    path('system/', include('app_message.urls')),
    path('job/crontab/', include('app_crontab.urls')),
    path('tool/', include('app_monitor.urls')),
    path('dataset/', include('app_dataset.urls')),
    path('model/', include('app_model.urls')),
    path('algorithm/', include('app_algorithm.urls')),
    path('deploy/', include('app_model_deploy.urls')),
    # path('multi-agent/', include('app_multi_agent.urls')),
]

# 在开发环境中处理媒体文件
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
