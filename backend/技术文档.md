# PAO Django Admin 后端文档

## 1. 技术栈

### 1.1 后端技术栈
- **核心框架**：Django 4.2.1
- **权限认证**：Django REST framework + JWT + Casbin
- **数据库**：MySQL
- **缓存**：Redis
- **任务队列**：Celery
- **WebSocket**：Channels + Redis
- **跨域处理**：django-cors-headers
- **验证码**：django-simple-captcha
- **数据导出**：django-import-export

### 1.2 主要依赖包
- djangorestframework：REST API框架
- channels：WebSocket支持
- celery：异步任务队列
- django-celery-beat：定时任务
- django-redis：Redis缓存
- mysqlclient：MySQL数据库驱动
- casbin：权限管理

## 2. 系统架构

### 2.1 核心模块
1. **用户认证模块**（app_login）
   - 登录认证
   - 验证码生成

2. **用户管理模块**（app_user）
   - 用户信息管理
   - 用户状态控制
   - 多角色分配

3. **权限管理模块**
   - 角色管理（app_role）
   - 菜单管理（app_menu）
   - API权限（app_apis）
   - 数据权限控制

4. **组织架构模块**
   - 部门管理（app_dept）
   - 岗位管理（app_post）

5. **系统监控模块**
   - 操作日志（app_operation_log）
   - 任务监控（app_monitor）
   - 定时任务（app_crontab）

6. **系统管理模块**
   - 字典管理（app_dict）
   - 消息中心（app_message）

### 2.2 数据模型

#### 用户模型（Users）
- 继承自Django AbstractUser
- 支持用户状态控制
- 关联角色、部门、岗位
- MD5密码加密

#### 角色模型（Role）
- 角色名称、代码
- 数据权限范围控制
- 关联菜单权限
- 关联部门权限

#### 菜单模型（Menu）
- 支持多级菜单
- 菜单类型：目录、菜单、按钮
- 组件配置
- 权限标识

## 3. API接口设计

### 3.1 系统管理接口
- `/system/apis/`：API管理
- `/system/menu/`：菜单管理
- `/system/role/`：角色管理
- `/system/user/`：用户管理
- `/system/dept/`：部门管理
- `/system/post/`：岗位管理
- `/system/dict/`：字典管理

### 3.2 监控接口
- `/job/crontab/`：定时任务管理
- `/tool/`：系统监控工具

## 4. 特色功能

### 4.1 权限控制
- 基于Casbin的RBAC权限模型
- 支持多维度的数据权限控制
- 灵活的菜单权限配置

### 4.2 异步任务
- 基于Celery的任务队列
- 支持定时任务管理
- 任务执行监控

### 4.3 实时通信
- 基于Channels的WebSocket支持
- 消息实时推送

### 4.4 系统监控
- 操作日志记录
- 系统性能监控
- 任务执行状态监控

## 5. 部署说明

### 5.1 环境要求
- Python 3.9+
- MySQL 8.0+（Django 4.2要求）
- Redis

### 5.2 数据库配置
1. **MySQL配置要求**
   - 数据库版本：MySQL 8.0及以上
   - 字符集：utf8mb4
   - 排序规则：
     - MySQL 8.x：utf8mb4_0900_ai_ci
     - MySQL 5.7.x：utf8mb4_general_ci
   - 存储引擎：InnoDB（支持事务回滚）

2. **数据库隔离级别设置**
   - 临时设置（重启失效）：
     ```sql
     SET GLOBAL tx_isolation='READ-COMMITTED';
     ```
   - 永久设置：
     在my.cnf的[mysqld]部分添加：
     ```
     transaction-isolation=Read-Committed
     ```
   - 查看隔离级别：
     - 当前会话：`select @@tx_isolation;`
     - 全局设置：`select @@global.tx_isolation;`

### 5.3 部署步骤
1. **常规部署**
   - 安装依赖：
     ```bash
     pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
     ```
   - 执行数据库迁移：
     ```bash
     python manage.py makemigrations
     python manage.py migrate
     ```
   - 初始化数据：
     ```bash
     python manage.py init
     ```
   - 启动服务：
     ```bash
     python manage.py runserver 127.0.0.1:8000
     ```

2. **WebSocket服务部署**
   使用daphne支持WebSocket：
   ```bash
   daphne -b 0.0.0.0 -p 8000 --proxy-headers application.asgi:application
   ```

3. **Celery任务队列部署**
   - Linux/Mac环境：
     ```bash
     celery -A application worker -B -l info
     ```
   - Windows环境（需要安装eventlet）：
     ```bash
     pip install eventlet
     celery -A application worker -P eventlet -l info
     celery -A application beat -l info
     ```

4. **Docker部署**
   - 使用docker-compose一键部署
   - 提供独立的django-admin和django-web服务

### 5.4 初始账号信息
- 用户名：paopao
- 密码：123456

### 2.2 数据模型

#### 用户模型（Users）
- 继承自Django AbstractUser
- 主要字段：
  - username：用户名（唯一）
  - password：密码（MD5加密）
  - email：邮箱
  - is_active：状态（启用/禁用）
  - last_login：最后登录时间
  - roles：关联角色（多对多）
  - dept：所属部门（外键）
  - post：岗位（外键）

#### 角色模型（Role）
- 主要字段：
  - name：角色名称
  - code：角色代码（唯一）
  - status：状态（启用/禁用）
  - data_scope：数据权限范围
  - menus：关联菜单（多对多）
  - depts：关联部门（多对多）

#### 菜单模型（Menu）
- 主要字段：
  - name：菜单名称
  - parent：父级菜单（外键）
  - type：类型（目录/菜单/按钮）
  - path：路由地址
  - component：前端组件
  - permission：权限标识
  - icon：图标
  - sort：排序
  - status：状态

## 6. 系统安全设计

### 6.1 认证机制
- JWT Token认证
  - Token格式：Header.Payload.Signature
  - Token有效期：2小时
  - 支持Token自动续期
  - 退出登录自动失效

### 6.2 权限控制
- Casbin RBAC模型
  - 基于角色的访问控制
  - 支持资源级别的权限控制
  - 灵活的策略规则配置
  - 支持多维度数据权限

### 6.3 安全防护
- XSS防护：转义特殊字符
- CSRF防护：Token验证
- SQL注入防护：参数化查询
- 密码加密：MD5+Salt
- SECRET_KEY管理：
  ```python
  # 生成新的SECRET_KEY
  python manage.py shell
  from django.core.management import utils
  utils.get_random_secret_key()
  ```

## 7. 性能优化

### 7.1 缓存策略
- Redis缓存
  - 用户Token缓存
  - 菜单权限缓存
  - 验证码缓存
  - 字典数据缓存

### 7.2 异步任务
- Celery应用场景
  - 邮件发送
  - 报表导出
  - 数据同步
  - 定时任务

### 7.3 数据库优化
- 索引优化
- 查询优化
- 分页处理
- 慢查询日志

