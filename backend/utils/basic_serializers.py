from rest_framework import serializers
from utils.serializers import CustomModelSerializer
from app_model.models import AIModel
from app_dataset.models import AIDataset

class AIModelBasicSerializer(CustomModelSerializer):
    """
    AI模型基础序列化器
    """
    class Meta:
        model = AIModel
        fields = ['id', 'name', 'group', 'description']

class AIDatasetBasicSerializer(CustomModelSerializer):
    """
    AI数据集基础序列化器
    """
    class Meta:
        model = AIDataset
        fields = ['id', 'name', 'group', 'description'] 