import os
import io
import logging
import json
from minio import Minio
from minio.error import S3Error
from django.conf import settings
from datetime import timedelta
import urllib.parse
import uuid

logger = logging.getLogger(__name__)

class MinioStorage:
    """
    MinIO 存储工具类
    """
    def __init__(self):
        # MinIO 服务器地址、访问密钥和秘密密钥
        self.endpoint = getattr(settings, 'MINIO_ENDPOINT', 'localhost:9000')
        self.access_key = getattr(settings, 'MINIO_ACCESS_KEY', 'minioadmin')
        self.secret_key = getattr(settings, 'MINIO_SECRET_KEY', 'minioadmin')
        self.secure = getattr(settings, 'MINIO_SECURE', False)
        self.client = Minio(
            self.endpoint,
            access_key=self.access_key,
            secret_key=self.secret_key,
            secure=self.secure
        )
    
    def ensure_bucket_exists(self, bucket_name):
        """
        确保存储桶存在，如果不存在则创建
        """
        try:
            if not self.client.bucket_exists(bucket_name):
                logger.info(f"创建存储桶: {bucket_name}")
                self.client.make_bucket(bucket_name)
                # 设置公共读取权限
                policy = {
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Allow",
                            "Principal": {"AWS": "*"},
                            "Action": ["s3:GetObject"],
                            "Resource": [f"arn:aws:s3:::{bucket_name}/*"]
                        }
                    ]
                }
                self.client.set_bucket_policy(bucket_name, json.dumps(policy))
            return True
        except S3Error as e:
            logger.error(f"MinIO 错误: {str(e)}")
            return False
    
    def upload_file(self, bucket_name, object_name, file_path, content_type=None):
        """
        上传文件到 MinIO
        """
        try:
            self.ensure_bucket_exists(bucket_name)
            self.client.fput_object(
                bucket_name, object_name, file_path, content_type=content_type
            )
            return True
        except S3Error as e:
            logger.error(f"上传文件错误: {str(e)}")
            return False
    
    def upload_data(self, bucket_name, object_name, data, content_type=None):
        """
        上传数据到 MinIO
        """
        try:
            self.ensure_bucket_exists(bucket_name)
            file_data = io.BytesIO(data)
            file_size = len(data)
            self.client.put_object(
                bucket_name, object_name, file_data, file_size, content_type=content_type
            )
            return True
        except S3Error as e:
            logger.error(f"上传数据错误: {str(e)}")
            return False
    
    def download_file(self, bucket_name, object_name, file_path):
        """
        从 MinIO 下载文件
        """
        try:
            self.client.fget_object(bucket_name, object_name, file_path)
            return True
        except S3Error as e:
            logger.error(f"下载文件错误: {str(e)}")
            return False
    
    def get_object(self, bucket_name, object_name):
        """
        获取对象数据
        """
        try:
            response = self.client.get_object(bucket_name, object_name)
            data = response.read()
            return data
        except S3Error as e:
            logger.error(f"获取对象错误: {str(e)}")
            return None
        finally:
            if 'response' in locals():
                response.close()
                response.release_conn()
    
    def get_object_url(self, bucket_name, object_name, expires=7*24*60*60):
        """
        获取对象的临时 URL
        """
        try:
            expires_timedelta = timedelta(seconds=expires)
            url = self.client.presigned_get_object(
                bucket_name, object_name, expires=expires_timedelta
            )
            return url
        except S3Error as e:
            logger.error(f"获取URL错误: {str(e)}")
            return None
    
    def list_objects(self, bucket_name, prefix=None, recursive=True):
        """
        列出存储桶内的对象
        """
        try:
            objects = self.client.list_objects(
                bucket_name, prefix=prefix, recursive=recursive
            )
            return list(objects)
        except S3Error as e:
            logger.error(f"列出对象错误: {str(e)}")
            return []
    
    def remove_object(self, bucket_name, object_name):
        """
        删除对象
        """
        try:
            self.client.remove_object(bucket_name, object_name)
            return True
        except S3Error as e:
            logger.error(f"删除对象错误: {str(e)}")
            return False
    
    def remove_objects(self, bucket_name, object_names):
        """
        批量删除对象
        """
        try:
            errors = self.client.remove_objects(bucket_name, object_names)
            for error in errors:
                logger.error(f"删除对象错误: {error}")
            return True
        except S3Error as e:
            logger.error(f"批量删除对象错误: {str(e)}")
            return False
    
    def copy_object(self, source_bucket, source_object, target_bucket, target_object):
        """
        复制对象
        """
        try:
            self.ensure_bucket_exists(target_bucket)
            copy_source = {
                'Bucket': source_bucket,
                'Object': source_object
            }
            self.client.copy_object(target_bucket, target_object, copy_source)
            return True
        except S3Error as e:
            logger.error(f"复制对象错误: {str(e)}")
            return False
    
    def get_presigned_put_url(self, bucket_name, object_name, expires=3600):
        """
        获取预签名上传URL
        Args:
            bucket_name: 存储桶名称
            object_name: 对象名称
            expires: 过期时间（秒），默认1小时
        Returns:
            str: 预签名URL
        """
        try:
            # 确保存储桶存在
            self.ensure_bucket_exists(bucket_name)
            
            # 确保 expires 是 timedelta 对象
            if isinstance(expires, (int, float)):
                expires = timedelta(seconds=expires)
            
            # 获取预签名URL
            url = self.client.presigned_put_object(
                bucket_name, object_name, expires=expires
            )
            return url
        except S3Error as e:
            logger.error(f"获取预签名上传URL错误: {str(e)}")
            return None
            
    def initiate_multipart_upload(self, bucket_name, object_name):
        """
        初始化分片上传
        """
        try:
            # 确保存储桶存在
            self.ensure_bucket_exists(bucket_name)
            
            # 生成一个唯一的上传ID
            upload_id = str(uuid.uuid4())
            
            # 在MinIO中没有直接的initiate_multipart_upload方法
            # 我们使用预签名URL来模拟分片上传
            # 实际上，我们将为每个分片生成单独的预签名URL
            
            return upload_id
        except Exception as e:
            logger.error(f"初始化分片上传错误: {str(e)}")
            return None
            
    def get_presigned_upload_part_url(self, bucket_name, object_name, part_number, upload_id=None, expires=3600):
        """
        获取分片上传的预签名URL
        """
        try:
            # 确保存储桶存在
            self.ensure_bucket_exists(bucket_name)
            
            # 确保 expires 是 timedelta 对象
            if isinstance(expires, (int, float)):
                expires = timedelta(seconds=expires)
            
            # 为每个分片构建唯一的对象名
            part_object_name = f"{object_name}.part{part_number}"
            if upload_id:
                part_object_name = f"{part_object_name}.{upload_id}"
            
            # 获取预签名URL
            url = self.client.presigned_put_object(
                bucket_name, 
                part_object_name,
                expires=expires
            )
            
            return {
                "url": url,
                "part_object_name": part_object_name
            }
        except Exception as e:
            logger.error(f"获取分片上传URL错误: {str(e)}")
            return None
            
    def complete_multipart_upload(self, bucket_name, object_name, upload_id, parts):
        """
        完成分片上传
        parts: [{'part_number': number, 'part_object_name': string}, ...]
        """
        try:
            # 确保存储桶存在
            self.ensure_bucket_exists(bucket_name)
            
            # 按照分片编号排序
            sorted_parts = sorted(parts, key=lambda x: x['part_number'])
            
            # 创建一个临时的内存缓冲区来合并所有分片
            merged_data = io.BytesIO()
            
            # 读取并合并所有分片
            for part in sorted_parts:
                part_object_name = part['part_object_name']
                part_data = self.get_object(bucket_name, part_object_name)
                if part_data:
                    merged_data.write(part_data)
                else:
                    raise Exception(f"无法读取分片: {part_object_name}")
            
            # 将合并后的数据上传为最终对象
            merged_data.seek(0)
            self.client.put_object(
                bucket_name,
                object_name,
                merged_data,
                merged_data.getbuffer().nbytes
            )
            
            # 删除所有分片
            for part in parts:
                self.remove_object(bucket_name, part['part_object_name'])
            
            return True
        except Exception as e:
            logger.error(f"完成分片上传错误: {str(e)}")
            return False
            
    def abort_multipart_upload(self, bucket_name, object_name, upload_id, parts):
        """
        取消分片上传
        """
        try:
            # 删除所有已上传的分片
            for part in parts:
                part_object_name = part['part_object_name']
                self.remove_object(bucket_name, part_object_name)
            
            return True
        except Exception as e:
            logger.error(f"取消分片上传错误: {str(e)}")
            return False
            
    def get_multipart_upload_urls(self, bucket_name, object_name, total_parts, expires=3600):
        """
        获取所有分片的预签名URL
        """
        try:
            # 确保存储桶存在
            self.ensure_bucket_exists(bucket_name)
            
            # 初始化分片上传
            upload_id = self.initiate_multipart_upload(bucket_name, object_name)
            if not upload_id:
                return None
            
            # 获取每个分片的URL
            part_urls = {}
            part_object_names = {}
            for part_number in range(1, total_parts + 1):
                result = self.get_presigned_upload_part_url(
                    bucket_name, 
                    object_name, 
                    part_number, 
                    upload_id,
                    expires
                )
                if result:
                    part_urls[part_number] = result['url']
                    part_object_names[part_number] = result['part_object_name']
                else:
                    # 如果获取URL失败，取消上传
                    return None
                
            return {
                'upload_id': upload_id,
                'part_urls': part_urls,
                'part_object_names': part_object_names,
                'object_name': object_name
            }
        except Exception as e:
            logger.error(f"获取分片上传URLs错误: {str(e)}")
            return None

# 实例化并导出
minio_client = MinioStorage()


class ModelStorageManager:
    """
    模型存储管理器 - 专门用于模型部署的存储操作
    """

    def __init__(self):
        self.minio_client = minio_client
        self.model_bucket = getattr(settings, 'MINIO_MODEL_BUCKET', 'models')

    def download_model_weights(self, model_version, local_path):
        """
        下载模型权重文件到本地

        Args:
            model_version: 模型版本对象
            local_path: 本地存储路径

        Returns:
            bool: 是否成功下载
        """
        try:
            if not model_version.model_weights_path:
                logger.warning(f"模型版本 {model_version.id} 没有权重文件路径")
                return False

            # 确保本地目录存在
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # 拼接完整的MinIO路径：模型路径 + 权重文件路径
            model = model_version.model
            if hasattr(model, 'minio_path') and model.minio_path:
                full_path = f"{model.minio_path.rstrip('/')}/{model_version.model_weights_path}"
            else:
                # 如果模型没有minio_path，直接使用权重文件路径
                full_path = model_version.model_weights_path

            logger.info(f"下载权重文件: {full_path} -> {local_path}")

            # 从MinIO下载文件
            success = self.minio_client.download_file(
                bucket_name=self.model_bucket,
                object_name=full_path,
                file_path=local_path
            )

            if success:
                logger.info(f"权重文件下载成功: {local_path}")
                return True
            else:
                logger.error(f"权重文件下载失败: {full_path}")
                return False

        except Exception as e:
            logger.error(f"下载权重文件异常: {str(e)}")
            return False

    def download_model_files(self, model_version, local_dir):
        """
        下载模型相关的所有文件

        Args:
            model_version: 模型版本对象
            local_dir: 本地目录

        Returns:
            dict: 下载结果 {'success': bool, 'files': list, 'errors': list}
        """
        try:
            # 确保本地目录存在
            os.makedirs(local_dir, exist_ok=True)

            result = {
                'success': True,
                'files': [],
                'errors': []
            }

            # 下载权重文件
            if model_version.model_weights_path:
                # 保持原始文件名
                original_filename = os.path.basename(model_version.model_weights_path)
                if not original_filename:
                    original_filename = 'weights.pt'

                weights_local_path = os.path.join(local_dir, original_filename)
                if self.download_model_weights(model_version, weights_local_path):
                    result['files'].append({
                        'type': 'weights',
                        'remote_path': model_version.model_weights_path,
                        'local_path': weights_local_path
                    })
                else:
                    result['errors'].append(f"权重文件下载失败: {model_version.model_weights_path}")
                    result['success'] = False

            # 获取模型的minio_path
            model = model_version.model
            model_minio_path = model.minio_path if hasattr(model, 'minio_path') and model.minio_path else ""

            # 下载文档文件
            if model_version.model_docs_path:
                docs_local_path = os.path.join(local_dir, 'docs.md')
                docs_full_path = f"{model_minio_path.rstrip('/')}/{model_version.model_docs_path}" if model_minio_path else model_version.model_docs_path

                if self.minio_client.download_file(
                    bucket_name=self.model_bucket,
                    object_name=docs_full_path,
                    file_path=docs_local_path
                ):
                    result['files'].append({
                        'type': 'docs',
                        'remote_path': docs_full_path,
                        'local_path': docs_local_path
                    })
                else:
                    result['errors'].append(f"文档文件下载失败: {docs_full_path}")

            # 下载测试报告
            if model_version.test_report_path:
                report_local_path = os.path.join(local_dir, 'test_report.md')
                report_full_path = f"{model_minio_path.rstrip('/')}/{model_version.test_report_path}" if model_minio_path else model_version.test_report_path

                if self.minio_client.download_file(
                    bucket_name=self.model_bucket,
                    object_name=report_full_path,
                    file_path=report_local_path
                ):
                    result['files'].append({
                        'type': 'test_report',
                        'remote_path': report_full_path,
                        'local_path': report_local_path
                    })
                else:
                    result['errors'].append(f"测试报告下载失败: {report_full_path}")

            return result

        except Exception as e:
            logger.error(f"下载模型文件异常: {str(e)}")
            return {
                'success': False,
                'files': [],
                'errors': [str(e)]
            }

    def cleanup_local_files(self, local_dir):
        """
        清理本地文件

        Args:
            local_dir: 本地目录
        """
        try:
            if os.path.exists(local_dir):
                import shutil
                shutil.rmtree(local_dir)
                logger.info(f"本地文件清理成功: {local_dir}")
        except Exception as e:
            logger.error(f"清理本地文件失败: {str(e)}")


# 全局模型存储管理器实例
model_storage_manager = ModelStorageManager()