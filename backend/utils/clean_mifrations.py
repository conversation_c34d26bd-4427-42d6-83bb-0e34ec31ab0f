import os
import shutil


def clean_migrations():
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))  # 当前文件所在目录utils
    base_dir = os.path.dirname(current_dir)  # 上一层目录（父目录）

    # 遍历所有应用目录
    for app in os.listdir(base_dir):
        migrations_dir = os.path.join(base_dir, app, 'migrations')
        if os.path.exists(migrations_dir):
            # 删除所有迁移文件，但保留 __init__.py
            for file in os.listdir(migrations_dir):
                if file.startswith('0') and file.endswith('.py'):
                    os.remove(os.path.join(migrations_dir, file))
            print(f'Cleaned migrations in {app}')


if __name__ == '__main__':
    clean_migrations()