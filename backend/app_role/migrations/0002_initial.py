# Generated by Django 4.2.1 on 2025-07-02 18:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('app_role', '0001_initial'),
        ('app_menu', '0002_initial'),
        ('app_dept', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='role',
            name='creator',
            field=models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='role',
            name='dept',
            field=models.ManyToManyField(db_constraint=False, help_text='数据权限-关联部门', to='app_dept.dept', verbose_name='数据权限-关联部门'),
        ),
        migrations.AddField(
            model_name='role',
            name='menu',
            field=models.ManyToManyField(db_constraint=False, help_text='数据权限-关联菜单', to='app_menu.menu', verbose_name='数据权限-关联菜单'),
        ),
    ]
