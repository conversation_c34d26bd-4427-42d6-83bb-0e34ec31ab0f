# 模型部署系统 API 文档

## 基础信息

- **Base URL**: `http://localhost:8000/api/deploy/`
- **认证方式**: JWT Token
- **数据格式**: JSON (除文件上传接口)
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {},
  "total": 0,
  "page": 1,
  "pageSize": 10
}
```

### 错误响应
```json
{
  "code": 400,
  "msg": "错误信息",
  "data": null
}
```

## 1. 部署管理接口

### 1.1 获取部署列表

**接口地址**: `GET /deployments/`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| search | string | 否 | 搜索关键词 |
| status | string | 否 | 状态筛选：pending/deploying/running/stopped/failed/error |
| model_version | int | 否 | 模型版本ID |

**响应示例**:
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": [
    {
      "id": 1,
      "deployment_name": "yolo_v1_deploy",
      "status": "running",
      "model_name": "YOLO11n",
      "version_number": "v1.0",
      "docker_image": "yolo-inference:latest",
      "service_port": 8080,
      "service_url": "http://localhost:8080",
      "container_id": "abc123def456",
      "deployed_at": "2024-01-15T10:30:00Z",
      "service": {
        "id": 1,
        "is_healthy": true,
        "total_requests": 150,
        "successful_requests": 145,
        "failed_requests": 5
      }
    }
  ],
  "total": 1
}
```

### 1.2 部署模型

**接口地址**: `POST /deployments/deploy/`

**请求参数**:
```json
{
  "model_version_id": 1,
  "deployment_name": "yolo_v1_deploy",
  "service_port": 8080,
  "deploy_config": {
    "memory_limit": "2g",
    "cpu_limit": 1.0,
    "environment": {
      "MODEL_NAME": "yolo11n",
      "DEBUG": "false"
    },
    "auto_restart": true
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "模型部署成功",
  "data": {
    "id": 1,
    "deployment_name": "yolo_v1_deploy",
    "status": "running",
    "container_id": "abc123def456",
    "service_url": "http://localhost:8080"
  }
}
```

### 1.3 停止部署

**接口地址**: `POST /deployments/{id}/stop/`

**响应示例**:
```json
{
  "code": 200,
  "msg": "部署停止成功"
}
```

### 1.4 重启部署

**接口地址**: `POST /deployments/{id}/restart/`

**响应示例**:
```json
{
  "code": 200,
  "msg": "部署重启成功"
}
```

### 1.5 删除部署

**接口地址**: `DELETE /deployments/{id}/remove/`

**响应示例**:
```json
{
  "code": 200,
  "msg": "部署删除成功"
}
```

### 1.6 获取部署日志

**接口地址**: `GET /deployments/{id}/logs/`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tail | int | 否 | 获取最后几行，默认100 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "获取日志成功",
  "data": {
    "logs": "2024-01-15 10:30:00 - INFO - 模型加载成功\n2024-01-15 10:30:01 - INFO - 服务启动完成"
  }
}
```

### 1.7 获取部署统计

**接口地址**: `GET /deployments/stats/`

**响应示例**:
```json
{
  "code": 200,
  "msg": "获取统计信息成功",
  "data": {
    "total_deployments": 10,
    "running_deployments": 7,
    "stopped_deployments": 2,
    "failed_deployments": 1,
    "total_services": 7,
    "healthy_services": 6
  }
}
```

## 2. 服务管理接口

### 2.1 获取服务列表

**接口地址**: `GET /services/`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| is_healthy | bool | 否 | 健康状态筛选 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": [
    {
      "id": 1,
      "service_name": "model_1_yolo_v1_deploy",
      "api_endpoint": "http://localhost:8080/predict",
      "health_check_url": "http://localhost:8080/health",
      "is_healthy": true,
      "total_requests": 150,
      "successful_requests": 145,
      "failed_requests": 5,
      "success_rate": 96.67,
      "average_response_time": 0.245,
      "model_name": "YOLO11n",
      "version_number": "v1.0"
    }
  ]
}
```

### 2.2 服务健康检查

**接口地址**: `POST /services/{id}/health_check/`

**响应示例**:
```json
{
  "code": 200,
  "msg": "服务健康"
}
```

### 2.3 获取服务统计

**接口地址**: `GET /services/{id}/stats/`

**响应示例**:
```json
{
  "code": 200,
  "msg": "获取统计信息成功",
  "data": {
    "total_requests": 150,
    "successful_requests": 145,
    "failed_requests": 5,
    "success_rate": 96.67,
    "average_response_time": 0.245,
    "requests_per_hour": 25
  }
}
```

## 3. 推理接口

### 3.1 单张图片推理

**接口地址**: `POST /inference/predict/{service_id}/`

**请求格式**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image | file | 是 | 图片文件 |
| conf | float | 否 | 置信度阈值，默认0.25 |
| iou | float | 否 | IoU阈值，默认0.45 |
| max_det | int | 否 | 最大检测数，默认1000 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "推理成功",
  "data": {
    "predictions": {
      "detections": [
        {
          "bbox": {
            "x1": 100.5,
            "y1": 150.2,
            "x2": 300.8,
            "y2": 400.6,
            "width": 200.3,
            "height": 250.4
          },
          "confidence": 0.8567,
          "class_id": 0,
          "class_name": "person"
        }
      ],
      "summary": {
        "total_detections": 1,
        "classes_detected": ["person"],
        "avg_confidence": 0.8567
      }
    },
    "model_info": {
      "name": "yolo11n",
      "version": "v1.0"
    },
    "inference_time": 0.245,
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "request_id": "req_123456789",
  "response_time": 0.245
}
```

### 3.2 批量图片推理

**接口地址**: `POST /inference/batch_predict/{service_id}/`

**请求格式**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| images | file[] | 是 | 图片文件列表（最多10张） |
| conf | float | 否 | 置信度阈值，默认0.25 |
| iou | float | 否 | IoU阈值，默认0.45 |
| max_det | int | 否 | 最大检测数，默认1000 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "批量推理成功",
  "data": {
    "results": [
      {
        "filename": "image1.jpg",
        "predictions": {
          "detections": [...],
          "summary": {...}
        }
      },
      {
        "filename": "image2.jpg",
        "error": "图像格式不支持"
      }
    ],
    "total_images": 2,
    "successful_predictions": 1,
    "total_time": 0.456
  },
  "request_id": "batch_123456789",
  "response_time": 0.456
}
```

### 3.3 服务健康检查

**接口地址**: `GET /inference/health/{service_id}/`

**响应示例**:
```json
{
  "code": 200,
  "msg": "服务健康",
  "data": {
    "status": "healthy",
    "model_info": {
      "name": "yolo11n",
      "version": "v1.0",
      "loaded_at": "2024-01-15T10:00:00Z"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "response_time": 0.012
}
```

## 4. 监控接口

### 4.1 获取性能图表数据

**接口地址**: `GET /metrics/chart_data/`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| service_id | int | 是 | 服务ID |
| hours | int | 否 | 时间范围（小时），默认24 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "获取图表数据成功",
  "data": {
    "timestamps": [
      "2024-01-15T10:00:00Z",
      "2024-01-15T10:05:00Z"
    ],
    "cpu_usage": [25.5, 30.2],
    "memory_usage": [45.8, 48.1],
    "gpu_usage": [null, null],
    "requests_per_minute": [5, 8],
    "average_response_time": [0.245, 0.267],
    "error_rate": [0.0, 2.5]
  }
}
```

### 4.2 获取推理日志

**接口地址**: `GET /logs/`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认20 |
| service | int | 否 | 服务ID筛选 |
| status | string | 否 | 状态筛选：success/failed/timeout |

**响应示例**:
```json
{
  "code": 200,
  "msg": "获取推理日志成功",
  "data": [
    {
      "id": 1,
      "request_id": "req_123456789",
      "service_name": "model_1_yolo_v1_deploy",
      "model_name": "YOLO11n",
      "status": "success",
      "response_time": 0.245,
      "client_ip": "*************",
      "create_datetime": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 1
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## 使用示例

### Python 示例

```python
import requests

# 部署模型
deploy_data = {
    "model_version_id": 1,
    "deployment_name": "yolo_test",
    "service_port": 8080,
    "deploy_config": {
        "memory_limit": "2g",
        "cpu_limit": 1.0,
        "auto_restart": True
    }
}

response = requests.post(
    "http://localhost:8000/api/deploy/deployments/deploy/",
    json=deploy_data,
    headers={"Authorization": "Bearer your_token"}
)

# 推理请求
with open("test_image.jpg", "rb") as f:
    files = {"image": f}
    data = {"conf": 0.25, "iou": 0.45}
    
    response = requests.post(
        "http://localhost:8000/api/deploy/inference/predict/1/",
        files=files,
        data=data,
        headers={"Authorization": "Bearer your_token"}
    )
```

### JavaScript 示例

```javascript
// 部署模型
const deployModel = async () => {
  const response = await fetch('/api/deploy/deployments/deploy/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your_token'
    },
    body: JSON.stringify({
      model_version_id: 1,
      deployment_name: 'yolo_test',
      service_port: 8080,
      deploy_config: {
        memory_limit: '2g',
        cpu_limit: 1.0,
        auto_restart: true
      }
    })
  });
  
  return await response.json();
};

// 推理请求
const predictImage = async (serviceId, imageFile) => {
  const formData = new FormData();
  formData.append('image', imageFile);
  formData.append('conf', '0.25');
  formData.append('iou', '0.45');
  
  const response = await fetch(`/api/deploy/inference/predict/${serviceId}/`, {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer your_token'
    },
    body: formData
  });
  
  return await response.json();
};
```
