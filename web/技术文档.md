# PaoDjangoAdmin 前端技术文档

## 技术栈

本项目采用现代化的前端技术栈进行开发：

- **Vue 3.0**：采用 Composition API，提供更好的代码组织和复用能力
- **TypeScript**：为 JavaScript 添加类型支持，提高代码可维护性
- **Vite 3**：下一代前端构建工具，提供极速的开发体验
- **Element Plus**：基于 Vue 3 的组件库，提供丰富的 UI 组件
- **Pinia**：Vue 3 的官方状态管理方案，替代 Vuex
- **Vue Router**：Vue.js 官方路由管理器

## 项目结构

```
├── src/
│   ├── api/                # API 接口封装
│   ├── assets/             # 静态资源文件
│   ├── components/         # 公共组件
│   ├── i18n/              # 国际化配置
│   ├── layout/            # 布局组件
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   ├── theme/             # 主题相关
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── public/               # 静态公共资源
├── types/                # TypeScript 类型定义
└── vite.config.ts       # Vite 配置文件
```

## 核心功能实现

### 1. 状态管理

项目使用 Pinia 进行状态管理，主要包含以下几个 Store：

- **userStore**：用户信息、权限等
- **appStore**：应用配置、主题等
- **tagsViewStore**：标签页管理

### 2. 路由权限

采用基于角色的访问控制（RBAC），主要通过以下方式实现：

- 路由守卫进行权限校验
- 动态路由生成
- 按钮级权限控制

### 3. 组件使用

#### 3.1 布局组件

- `Layout`：主布局组件，包含顶部导航、侧边栏等
- `TagsView`：多页签导航组件
- `Sidebar`：侧边栏导航组件

#### 3.2 通用组件

- `SvgIcon`：SVG 图标组件
- `ProTable`：高级表格组件
- `IconSelect`：图标选择器

### 4. 主题定制

项目支持主题定制，主要通过以下方式实现：

- CSS 变量定义主题色值
- 动态切换主题
- 深色模式支持

### 5. 国际化

使用 vue-i18n 实现国际化，支持：

- 中英文切换
- 组件内文案国际化
- 动态加载语言包

## 开发指南

### 1. 环境准备

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

### 2. 开发规范

- 组件命名采用 PascalCase
- TypeScript 类型定义放在 types 目录
- API 接口按模块组织在 api 目录
- 使用 ESLint + Prettier 进行代码规范

### 3. 新增页面流程

1. 在 views 目录下创建页面组件
2. 在 router 中添加路由配置
3. 在 api 目录添加接口定义
4. 配置页面权限

### 4. 构建部署

#### 开发环境

- 启用 HMR（模块热替换）
- 本地 API 代理配置
- Source Map 支持

#### 生产环境

- 代码压缩混淆
- 静态资源优化
- 按需加载

## 常见问题

1. 权限配置问题
   - 检查角色权限是否正确配置
   - 确认路由 meta 中的权限设置

2. 组件复用问题
   - 使用组合式 API 抽取公共逻辑
   - 合理使用 Props 和 Emit

3. 性能优化建议
   - 合理使用异步组件
   - 避免不必要的组件重渲染
   - 使用 keep-alive 缓存组件