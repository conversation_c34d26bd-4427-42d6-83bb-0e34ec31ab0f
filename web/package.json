{"name": "PaoDjangoUI", "version": "1.0.0", "scripts": {"dev": "vite --force", "build": "vite build", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vue-office/docx": "^1.6.3", "@vuemap/vue-amap": "^2.0.22", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.4.0", "codemirror": "^5.65.1", "codemirror-editor-vue3": "^2.1.7", "countup.js": "^2.6.0", "cropperjs": "^1.5.13", "echarts": "^5.4.2", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.0.0", "element-plus": "^2.3.10", "file-saver": "^2.0.5", "js-cookie": "^3.0.1", "jsplumb": "^2.15.6", "marked": "^15.0.11", "md-editor-v3": "^5.5.1", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.35", "print-js": "^1.6.0", "qrcodejs2-fixes": "^0.0.2", "resize-detector": "^0.3.0", "screenfull": "^6.0.0", "sortablejs": "^1.15.0", "spark-md5": "^3.0.2", "vue": "^3.3.0", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.10", "vue-i18n": "^9.2.2", "vue-json-pretty": "^2.5.0", "vue-router": "^4.2.2", "websocket": "^1.0.34"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/clipboard": "^2.0.1", "@types/js-cookie": "^3.0.2", "@types/node": "^17.0.24", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.10.7", "@types/spark-md5": "^3.0.5", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@vitejs/plugin-vue": "^4.2.1", "@vue/compiler-sfc": "^3.2.47", "dotenv": "^10.0.0", "eslint": "^8.13.0", "eslint-plugin-vue": "^8.6.0", "prettier": "^2.6.2", "sass": "^1.62.1", "sass-loader": "^12.4.0", "script-loader": "^0.7.2", "typescript": "^5.0.4", "vite": "^4.3.5", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^8.3.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": ""}, "engines": {"node": ">=12.0.0", "npm": ">= 6.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "repository": {"type": "git", "url": ""}}