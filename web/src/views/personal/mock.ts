/**
 * 消息通知
 * @returns 返回模拟数据
 */
export const newsInfoList: Array<object> = [
	{
		title: '[发布] 2025年03月01日 爬虫管理平台正式上线',
		date: '12/15',
		link: 'https://github.com/taskPyroer/taskpyro',
	},
	{
		title: '[更新] 2025年03月02日 Django Admin后台管理系统重大更新',
		date: '11/30',
		link: 'https://github.com/your-repo/django-admin',
	},
	{
		title: '[文章] Python爬虫技术实践与优化',
		date: '11/15',
		link: 'https://docs.taskpyro.cn/',
	}
];

/**
 * 营销推荐
 * @returns 返回模拟数据
 */
export const recommendList: Array<object> = [
	{
		title: 'Python',
		msg: '核心开发语言',
		icon: 'elementStar',
		bg: '#3776AB',
		iconColor: '#4B8BBE',
	},
	{
		title: 'Vue',
		msg: '前端开发框架',
		icon: 'elementMonitor',
		bg: '#42B883',
		iconColor: '#35495E',
	},
	{
		title: 'Django',
		msg: '后端开发框架',
		icon: 'elementConnection',
		bg: '#092E20',
		iconColor: '#44B78B',
	},
	{
		title: '爬虫',
		msg: '数据采集技术',
		icon: 'elementDataLine',
		bg: '#FF6B6B',
		iconColor: '#FF8787',
	},
];
