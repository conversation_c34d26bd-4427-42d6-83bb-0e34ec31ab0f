<template>
  <div class="app-container">
    <!-- 顶部算法库类型切换 -->
    <div class="algorithm-type-header">
      <el-card shadow="always" class="type-switch-card">
        <div class="type-switch-container">
          <div class="type-switch-title">
            <h2>算法库管理</h2>
            <p>探索和管理各类算法库资源</p>
          </div>
          <div class="type-switch-tabs">
            <el-radio-group v-model="state.currentAlgorithmType" @change="handleAlgorithmTypeChange" size="large">
              <el-radio-button label="general">
                <el-icon><Collection /></el-icon>
                通用算法
              </el-radio-button>
              <el-radio-button label="specialized">
                <el-icon><Tools /></el-icon>
                专用算法
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </el-card>
    </div>

    <el-row :gutter="20">
      <!-- 左侧分类树 -->
      <el-col :span="6" class="left-card">
        <el-card shadow="always">
          <template #header>
            <div class="card-header">
              <span class="card-header-text">
                {{ state.currentAlgorithmType === 'general' ? '通用算法分类' : '专用算法分类' }}
              </span>
            </div>
          </template>

          <!-- 已选择的类别 -->
          <div class="selected-categories" v-if="state.selectedCategories.length > 0">
            <div class="selected-title">
              <span>已选类别：</span>
              <el-button
                type="primary"
                link
                size="small"
                @click="clearSelectedCategories"
              >
                <SvgIcon name="elementDelete" />
                清空
              </el-button>
            </div>
            <div class="selected-tags">
              <el-tag
                v-for="category in state.selectedCategories"
                :key="category.id"
                closable
                size="small"
                @close="removeSelectedCategory(category)"
              >
                {{ category.name }}
              </el-tag>
            </div>
          </div>

          <!-- 算法库类型说明 -->
          <div class="algorithm-type-description">
            <el-alert
              :title="state.currentAlgorithmType === 'general' ? '通用算法库' : '专用算法库'"
              :description="state.currentAlgorithmType === 'general' ?
                '包含运筹优化、深度学习、强化学习等基础算法库' :
                '包含计算机视觉、自然语言处理、推荐系统等专用算法库'"
              type="info"
              :closable="false"
              show-icon
            />
          </div>

          <!-- 分类树 -->
          <div class="category-tree-container">
            <!-- 通用算法分类 -->
            <div v-if="state.currentAlgorithmType === 'general'" class="general-categories">
              <div
                v-for="category in state.generalCategories"
                :key="category.id"
                class="category-group"
              >
                <div class="category-group-header" @click="toggleCategoryGroup(category.id)">
                  <el-icon class="category-icon">
                    <component :is="getCategoryIcon(category.code)" />
                  </el-icon>
                  <span class="category-name">{{ category.name }}</span>
                  <el-icon class="expand-icon" :class="{ 'expanded': state.expandedGroups.includes(category.id) }">
                    <ArrowRight />
                  </el-icon>
                </div>
                <div v-show="state.expandedGroups.includes(category.id)" class="category-children">
                  <div
                    v-for="child in category.children"
                    :key="child.id"
                    class="category-child"
                    :class="{ 'selected': isSelectedCategory(child.id) }"
                    @click="handleCategorySelect(child)"
                  >
                    <span class="child-name">{{ child.name }}</span>
                    <span class="child-count" v-if="child.algorithm_count">{{ child.algorithm_count }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 专用算法分类 -->
            <div v-else class="specialized-categories">
              <div
                v-for="category in state.specializedCategories"
                :key="category.id"
                class="category-group"
              >
                <div class="category-group-header" @click="toggleCategoryGroup(category.id)">
                  <el-icon class="category-icon">
                    <component :is="getCategoryIcon(category.code)" />
                  </el-icon>
                  <span class="category-name">{{ category.name }}</span>
                  <el-icon class="expand-icon" :class="{ 'expanded': state.expandedGroups.includes(category.id) }">
                    <ArrowRight />
                  </el-icon>
                </div>
                <div v-show="state.expandedGroups.includes(category.id)" class="category-children">
                  <div
                    v-for="child in category.children"
                    :key="child.id"
                    class="category-child"
                    :class="{ 'selected': isSelectedCategory(child.id) }"
                    @click="handleCategorySelect(child)"
                  >
                    <span class="child-name">{{ child.name }}</span>
                    <span class="child-count" v-if="child.algorithm_count">{{ child.algorithm_count }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="!state.generalCategories.length && !state.specializedCategories.length" class="empty-tree">
              <el-empty description="暂无分类数据" :image-size="60" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧内容区 -->
      <el-col :span="18">
        <!-- 搜索栏 -->
        <el-card shadow="always" class="search-card">
          <div class="search-header">
            <el-form :model="state.queryParams" ref="queryForm" :inline="true">
              <el-form-item label="算法库名称" prop="search">
                <el-input
                  v-model="state.queryParams.search"
                  placeholder="请输入算法库名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" plain @click="handleQuery">
                  <SvgIcon name="elementSearch"/>
                  搜索功能
                </el-button>
                <el-button @click="resetQuery">
                  <SvgIcon name="elementRefresh"/>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>

        <!-- 算法库列表 -->
        <el-row :gutter="16" class="model-list">
          <el-col :xs="24" :sm="12" :md="8" :lg="8" v-for="algorithm in state.algorithmList" :key="algorithm.id">
            <div class="model-card-wrapper">
              <el-card class="model-card" shadow="hover" @click="handleViewDetail(algorithm)">
                <!-- 算法库卡片顶部：标题和标签 -->
                <div class="model-card-header">
                  <div class="model-title-wrapper">
                    <h3 class="model-title">{{ algorithm.name }}</h3>
                    <span class="new-badge" v-if="algorithm.isNew">NEW</span>
                  </div>
                  <div class="model-categories">
                    <el-tag 
                      v-for="category in algorithm.leaf_categories.slice(0, 2)" 
                      :key="category.id" 
                      size="small"
                      class="category-tag">
                      {{ category.name }}
                    </el-tag>
                    <span class="more-categories" v-if="algorithm.leaf_categories.length > 2">+{{ algorithm.leaf_categories.length - 2 }}</span>
                  </div>
                </div>
                
                <!-- 算法库卡片中部：描述 -->
                <div class="model-description">
                  <p class="description-text">{{ algorithm.description || '暂无描述' }}</p>
                </div>
                
                <!-- 算法库卡片底部：元数据和统计信息 -->
                <div class="model-card-footer">
                  <div class="model-meta">
                    <div class="meta-item creator">
                      <SvgIcon name="elementUser" class="meta-icon"/> 
                      <span class="meta-text">{{ algorithm.creator_name }}</span>
                    </div>
                    <div class="meta-item date">
                      <SvgIcon name="elementCalendar" class="meta-icon"/> 
                      <span class="meta-text">{{ formatDate(algorithm.create_datetime) }}</span>
                    </div>
                  </div>
                  
                  <div class="model-stats">
                    <div class="stats-item stars">
                      <SvgIcon name="elementStar" class="stats-icon"/>
                      <span class="stats-count">{{ formatCount(algorithm.stars) }}</span>
                    </div>
                    <div class="stats-item downloads">
                      <SvgIcon name="elementDownload" class="stats-icon"/>
                      <span class="stats-count">{{ formatCount(algorithm.downloads) }}</span>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>

        <!-- 无数据时的空状态 -->
        <div class="empty-state" v-if="state.algorithmList.length === 0 && !state.loading">
          <el-empty 
            description="暂无符合条件的算法库" 
            :image-size="200">
          </el-empty>
        </div>

        <!-- 加载状态 -->
        <div class="loading-state" v-if="state.loading">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>

        <!-- 分页 -->
        <div v-show="state.total > 0">
          <el-divider></el-divider>
          <el-pagination
              background
              :total="state.total"
              :current-page="state.queryParams.page"
              :page-size="state.queryParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { getAlgorithmList, deleteAlgorithm } from '@/api/algorithm/algorithms'
import { getAlgorithmCategoryTree } from '@/api/algorithm/categories'
import 'md-editor-v3/lib/style.css';
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Collection,
  Tools,
  ArrowRight,
  Calculator,
  View,
  ChatDotRound,
  Star,
  Cpu,
  Folder
} from '@element-plus/icons-vue'

// 路由实例
const router = useRouter()

// 状态管理
const state = reactive({
  loading: false,
  algorithmList: [] as any[],
  categoryTree: [] as any[],
  total: 0,
  // 当前算法库类型：general(通用) 或 specialized(专用)
  currentAlgorithmType: 'general',
  queryParams: {
    page: 1,
    pageSize: 10,
    category_ids: [] as number[],
    search: undefined,
    type: 'general' // 添加类型过滤
  },
  // 通用算法分类（运筹优化、深度学习、强化学习）
  generalCategories: [] as any[],
  // 专用算法分类（计算机视觉、自然语言处理等）
  specializedCategories: [] as any[],
  // 展开的分类组
  expandedGroups: [] as number[],
  // 选中的分类
  selectedCategories: [] as any[]
})

/** 查询算法库列表 */
const handleQuery = async () => {
  state.loading = true
  try {
    // 构建查询参数
    const params: any = { ...state.queryParams }

    // 添加算法库类型过滤
    params.type = state.currentAlgorithmType

    // 将选中的类别ID转换为请求参数
    if (state.selectedCategories.length > 0) {
      params.category_ids = state.selectedCategories.map((c: any) => c.id)
    }

    console.log('查询参数:', params)
    const res = await getAlgorithmList(params)
    state.algorithmList = res.data.data
    state.total = res.data.total
  } catch (error) {
    console.error('获取算法库列表失败:', error)
  } finally {
    state.loading = false
  }
}

/** 重置查询参数 */
const resetQuery = () => {
  state.queryParams.search = undefined
  state.selectedCategories = []
  state.queryParams.page = 1
  handleQuery()
}

/** 算法库类型切换 */
const handleAlgorithmTypeChange = (type: string) => {
  state.currentAlgorithmType = type
  state.queryParams.type = type
  state.selectedCategories = []
  state.queryParams.page = 1
  handleQuery()
}

/** 获取分类树数据 */
const loadCategoryTree = async () => {
  try {
    const res = await getAlgorithmCategoryTree()
    state.categoryTree = res.data

    // 分离通用算法和专用算法分类
    const generalCategoryCodes = ['operations_research', 'deep_learning', 'reinforcement_learning']
    const specializedCategoryCodes = ['computer_vision', 'natural_language_processing', 'recommendation_system', 'time_series_analysis']

    state.generalCategories = res.data.filter((category: any) =>
      category.parent === null && generalCategoryCodes.includes(category.code)
    )

    state.specializedCategories = res.data.filter((category: any) =>
      category.parent === null && specializedCategoryCodes.includes(category.code)
    )

    // 默认展开所有分类组
    state.expandedGroups = [
      ...state.generalCategories.map((c: any) => c.id),
      ...state.specializedCategories.map((c: any) => c.id)
    ]

    console.log('通用算法分类:', state.generalCategories)
    console.log('专用算法分类:', state.specializedCategories)
  } catch (error) {
    console.error('获取分类树失败:', error)
  }
}

/** 切换分类组展开状态 */
const toggleCategoryGroup = (categoryId: number) => {
  const index = state.expandedGroups.indexOf(categoryId)
  if (index > -1) {
    state.expandedGroups.splice(index, 1)
  } else {
    state.expandedGroups.push(categoryId)
  }
}

/** 处理分类选择 */
const handleCategorySelect = (category: any) => {
  // 检查是否已经选择了这个类别
  const index = state.selectedCategories.findIndex((c: any) => c.id === category.id)

  if (index === -1) {
    // 添加到已选列表
    state.selectedCategories.push({
      id: category.id,
      name: category.name
    })

    // 重置到第一页并重新查询
    state.queryParams.page = 1
    handleQuery()
  }
}

/** 判断分类是否已选中 */
const isSelectedCategory = (categoryId: number) => {
  return state.selectedCategories.some((c: any) => c.id === categoryId)
}

/** 获取分类图标 */
const getCategoryIcon = (categoryCode: string) => {
  const iconMap: Record<string, string> = {
    'operations_research': 'Calculator',
    'deep_learning': 'Cpu',
    'reinforcement_learning': 'Cpu',
    'computer_vision': 'View',
    'natural_language_processing': 'ChatDotRound',
    'recommendation_system': 'Star',
    'time_series_analysis': 'Star'
  }
  return iconMap[categoryCode] || 'Folder'
}

/** 查看算法库详情 */
const handleViewDetail = (algorithm: any) => {
  router.push(`/algorithm/algorithms/${algorithm.id}`)
}

/** 分页大小改变 */
const handleSizeChange = (val: number) => {
  state.queryParams.pageSize = val
  handleQuery()
}

/** 页码改变 */
const handleCurrentChange = (val: number) => {
  state.queryParams.page = val
  handleQuery()
}

// 页面加载时
onMounted(() => {
  loadCategoryTree()
  handleQuery()
})

/** 清空已选分类 */
const clearSelectedCategories = () => {
  state.selectedCategories = []
  state.queryParams.page = 1
  handleQuery()
}

// 移除已选类别
const removeSelectedCategory = (category: any) => {
  // 从已选列表中移除
  state.selectedCategories = state.selectedCategories.filter((c: any) => c.id !== category.id)
  
  // 记录日志
  console.log('移除类别后的已选类别:', state.selectedCategories)
  
  // 重置到第一页并重新查询
  state.queryParams.page = 1
  handleQuery()
}



// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
}

// 格式化数字（如1000->1k）
const formatCount = (count: number) => {
  if (!count && count !== 0) return '0';
  if (count < 1000) return count.toString();
  return (count / 1000).toFixed(1) + 'k';
}

// 处理模型点击
const handleModelClick = (model: any) => {
  router.push({
    path: `/model/maintenance`,
    query: { 
      id: model.id,
      mode: 'view'
    }
  })
}

// 查看详情
const handleView = (algorithm: any) => {
  router.push({
    path: `/algorithm/detail/${algorithm.id}`
  })
}

// 编辑算法库
const handleEdit = (algorithm: any) => {
  router.push({
    path: `/algorithm/maintenance`,
    query: { 
      id: algorithm.id,
      mode: 'edit'
    }
  })
}

// 删除算法库
const handleDelete = (algorithm: any) => {
  ElMessageBox.confirm(
    '确定要删除该算法库吗？此操作不可恢复',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteAlgorithm(algorithm.id)
      ElMessage.success('删除成功')
      handleQuery()
    } catch (error) {
      console.error('删除算法库失败:', error)
    }
  })
}

// 新增算法库
const handleAdd = () => {
  router.push({
    path: '/algorithm/maintenance',
    query: { mode: 'create' }
  })
}
</script>

<style lang="scss" scoped>
.app-container {
  .left-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    position: sticky;
    top: 24px;
    min-height: 600px;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px;
    }

    .card-header {
      border-bottom: 1px solid #EBEEF5;
      margin-bottom: 16px;
      .card-header-text {
        font-size: 16px;
        font-weight: 600;
        color: #1F2A44;
        display: flex;
        align-items: center;
        
        &::before {
          content: '';
          width: 4px;
          height: 16px;
          background-color: #6B48FF;
          margin-right: 8px;
          border-radius: 2px;
        }
      }
    }
  }

  .selected-categories {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #e0e0e0;

    .selected-title {
      font-weight: 500;
      margin-bottom: 8px;
      color: #1F2A44;
      display: flex;
      justify-content: space-between;
      align-items: center;

      :deep(.el-button) {
        padding: 0;
        height: auto;
        font-size: 13px;
        
        .svg-icon {
          margin-right: 4px;
          font-size: 14px;
        }
        
        &:hover {
          opacity: 0.8;
        }
      }
    }

    .selected-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      :deep(.el-tag) {
        background-color: #f0f7ff;
        color: #1F2A44;
        font-size: 12px;
        border: 1px solid #d9ecff;
        border-radius: 4px;
        padding: 3px 8px;
        margin-bottom: 4px;
        
        .el-tag__close {
          color: #409EFF;
          
          &:hover {
            background-color: #409EFF;
            color: #fff;
          }
        }
      }
    }
  }

  .root-tabs {
    width: 100%;
    margin-bottom: 16px;
    
    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
    
    :deep(.el-tabs__nav) {
      width: 100%;
    }
    
    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      font-size: 14px;
      padding: 0 10px;
    }
    
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
    }
    
    :deep(.el-tabs__active-bar) {
      background-color: #6B48FF;
    }
  }

  .category-tree-container {
    flex: 1;
    min-height: 350px;
    overflow-y: auto;
    padding: 12px 6px;
    border-radius: 8px;
    background-color: #fff;
    border: 1px solid #f0f0f0;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
      
      &:hover {
        background-color: #c0c4cc;
      }
    }
    
    .category-tree {
      flex: 1;
      min-height: 300px;
      background-color: transparent;
      
      :deep(.el-tree-node) {
        margin: 4px 0;
      }
      
      :deep(.el-tree-node__content) {
        height: 36px;
        border-radius: 6px;
        transition: all 0.2s ease;
        padding: 0 12px;
        
        &:hover {
          background-color: #f5f1ff;
        }
        
        &.is-current {
          background-color: #efe7ff;
          color: #6B48FF;
          font-weight: 500;
        }
      }
      
      :deep(.el-tree-node__label) {
        font-size: 14px;
      }
      
      :deep(.el-tree-node__expand-icon) {
        color: #6B48FF;
        
        &.is-leaf {
          color: transparent;
        }
      }
    }
    
    .custom-tree-node {
      display: flex;
      align-items: center;
      width: 100%;
      font-size: 14px;
      
      .folder-icon {
        margin-right: 8px;
        width: 16px;
        height: 16px;
        color: #6B48FF;
      }
      
      .node-label {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .node-count {
        margin-left: 8px;
        background-color: #f0f7ff;
        color: #409EFF;
        padding: 0 6px;
        border-radius: 10px;
        font-size: 12px;
      }
    }
  }

  .empty-category-tip {
    font-size: 14px;
    color: #909399;
    margin: 10px 0;
    text-align: center;
    
    a {
      color: #6B48FF;
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .empty-tree {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 300px;
  }

  .search-card {
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .search-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    :deep(.el-form-item) {
      margin-bottom: 0;

      .el-form-item__label {
        color: #1F2A44;
        font-size: 14px;
        font-weight: 500;
      }

      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 0 0 1px #E4E7ED;
        
        &:hover {
          box-shadow: 0 0 0 1px #6B48FF;
        }
        
        &.is-focus {
          box-shadow: 0 0 0 1px #6B48FF;
        }
      }
    }

    :deep(.el-button) {
      border-radius: 8px;
      font-size: 14px;
      padding: 8px 20px;
      height: 40px;
    }
  }

  .form-tip {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
  }

  .model-list {
    margin-top: 16px;
  }

  .model-card-wrapper {
    margin-bottom: 24px;
    height: 100%;
    perspective: 1000px;
  }

  .model-card {
    height: 240px;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    border: 1px solid #EAECF0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(107, 72, 255, 0.15);
      border-color: #6B48FF;
    }
    
    .model-card-header {
      padding: 16px 16px 12px;
      border-bottom: 1px solid #F2F4F7;
      
      .model-title-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .model-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #101828;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 85%;
        }
        
        .new-badge {
          margin-left: 8px;
          background-color: #ECFDF3;
          color: #027A48;
          padding: 2px 8px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 500;
        }
      }
      
      .model-categories {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 6px;
        
        .category-tag {
          background-color: #F9F5FF;
          border-color: #F9F5FF;
          color: #6941C6;
          font-weight: normal;
          border-radius: 16px;
        }
        
        .more-categories {
          font-size: 12px;
          color: #6941C6;
          background-color: #F9F5FF;
          padding: 2px 8px;
          border-radius: 16px;
        }
      }
    }
    
    .model-description {
      flex: 1;
      padding: 12px 16px;
      overflow: hidden;
      
      .description-text {
        color: #475467;
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
    
    .model-card-footer {
      padding: 12px 16px;
      border-top: 1px solid #F2F4F7;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .model-meta {
        display: flex;
        gap: 16px;
        
        .meta-item {
          display: flex;
          align-items: center;
          gap: 6px;
          
          .meta-icon {
            color: #667085;
            font-size: 14px;
          }
          
          .meta-text {
            color: #667085;
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100px;
          }
          
          &.creator .meta-text {
            font-weight: 500;
            color: #6941C6;
          }
        }
      }
      
      .model-stats {
        display: flex;
        gap: 16px;
        
        .stats-item {
          display: flex;
          align-items: center;
          gap: 6px;
          
          .stats-icon {
            color: #667085;
            font-size: 14px;
          }
          
          .stats-count {
            color: #667085;
            font-size: 12px;
            font-weight: 500;
          }
          
          &.stars {
            .stats-icon {
              color: #F79009;
            }
            .stats-count {
              color: #F79009;
            }
          }
        }
      }
    }
  }
  
  .empty-state {
    margin: 60px 0;
    text-align: center;
    
    :deep(.el-empty__description) {
      margin-top: 16px;
      font-size: 16px;
      color: #667085;
    }
    
    .el-button {
      margin-top: 24px;
      padding: 10px 24px;
      font-weight: 600;
      border-radius: 8px;
    }
  }
  
  .loading-state {
    margin: 24px 0;
    
    :deep(.el-skeleton) {
      margin-bottom: 16px;
    }
  }

  .category-group {
    margin-bottom: 10px;

    .root-category-label {
      font-weight: 500;
      margin-bottom: 5px;
    }

    .category-cascader {
      width: 100%;
    }
  }

  // 算法库类型切换头部样式
  .algorithm-type-header {
    margin-bottom: 20px;

    .type-switch-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1);

      :deep(.el-card__body) {
        padding: 24px;
      }
    }

    .type-switch-container {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .type-switch-title {
        h2 {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #101828;
        }

        p {
          margin: 0;
          font-size: 16px;
          color: #667085;
        }
      }

      .type-switch-tabs {
        :deep(.el-radio-group) {
          .el-radio-button {
            margin-right: 8px;

            .el-radio-button__inner {
              padding: 12px 20px;
              font-size: 16px;
              font-weight: 500;
              border-radius: 8px;
              border: 1px solid #D0D5DD;
              background: #FFFFFF;
              color: #344054;
              display: flex;
              align-items: center;
              gap: 8px;

              .el-icon {
                font-size: 18px;
              }

              &:hover {
                border-color: #7C3AED;
                color: #7C3AED;
              }
            }

            &.is-active .el-radio-button__inner {
              background: #7C3AED;
              border-color: #7C3AED;
              color: #FFFFFF;
            }
          }
        }
      }
    }
  }

  // 算法库类型说明样式
  .algorithm-type-description {
    margin-bottom: 16px;

    :deep(.el-alert) {
      border-radius: 8px;
      border: none;
      background: #F0F9FF;

      .el-alert__title {
        font-weight: 600;
        color: #0369A1;
      }

      .el-alert__description {
        color: #0284C7;
      }

      .el-alert__icon {
        color: #0EA5E9;
      }
    }
  }

  // 分类组样式
  .general-categories,
  .specialized-categories {
    .category-group {
      margin-bottom: 12px;
      border-radius: 8px;
      border: 1px solid #E4E7EC;
      overflow: hidden;

      .category-group-header {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background: #F9FAFB;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: #F3F4F6;
        }

        .category-icon {
          font-size: 18px;
          color: #7C3AED;
          margin-right: 12px;
        }

        .category-name {
          flex: 1;
          font-size: 14px;
          font-weight: 600;
          color: #374151;
        }

        .expand-icon {
          font-size: 16px;
          color: #9CA3AF;
          transition: transform 0.2s;

          &.expanded {
            transform: rotate(90deg);
          }
        }
      }

      .category-children {
        background: #FFFFFF;

        .category-child {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 10px 16px 10px 46px;
          cursor: pointer;
          transition: all 0.2s;
          border-top: 1px solid #F3F4F6;

          &:hover {
            background: #F9FAFB;
          }

          &.selected {
            background: #EDE9FE;
            border-left: 3px solid #7C3AED;

            .child-name {
              color: #7C3AED;
              font-weight: 500;
            }
          }

          .child-name {
            font-size: 13px;
            color: #6B7280;
          }

          .child-count {
            font-size: 12px;
            color: #9CA3AF;
            background: #F3F4F6;
            padding: 2px 6px;
            border-radius: 10px;
          }
        }
      }
    }
  }

  .related-models {
    margin-top: 16px;
    
    .section-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
    }
    
    .model-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .model-tag {
        cursor: pointer;
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>