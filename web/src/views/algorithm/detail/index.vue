<template>
  <div class="app-container">
    <!-- 加载状态 -->
    <div v-if="state.loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <template v-else>

      <!-- 算法库基本信息卡片 -->
      <el-card class="model-info-card">
        <div class="model-header">
          <div class="model-title">
            <h1>{{ state.algorithmDetail.name }}</h1>
            <el-tag size="small" type="success">{{ state.algorithmDetail.category?.name }}</el-tag>
            <el-tag size="small" type="info" class="ml-2">{{ state.algorithmDetail.group }}</el-tag>
            <el-tag size="small" :type="state.algorithmDetail.status === 'online' ? 'primary' : 'danger'" class="ml-2">
              {{ state.algorithmDetail.status === 'online' ? '已上架' : '已下架' }}
            </el-tag>
          </div>
          <div class="model-actions">
            <el-button type="primary" @click="handleStar">
              <el-icon><Star /></el-icon>
              收藏 ({{ state.algorithmDetail.stars }})
            </el-button>
            <el-button type="success" @click="handleDownload">
              <el-icon><Download /></el-icon>
              下载 ({{ state.algorithmDetail.downloads }})
            </el-button>
          </div>
        </div>

        <div class="model-meta">
          <div class="meta-item">
            <el-icon><User /></el-icon>
            <span>创建者: {{ state.algorithmDetail.creator?.username || '未知' }}</span>
          </div>
          <div class="meta-item">
            <el-icon><Calendar /></el-icon>
            <span>更新时间: {{ formatDate(state.algorithmDetail.update_datetime) }}</span>
          </div>
          <div class="meta-item">
            <el-icon><Box /></el-icon>
            <span>MinIO路径: {{ state.algorithmDetail.minio_path }}</span>
          </div>
          
          <!-- 添加关联模型显示 -->
          <div class="meta-item models-container" v-if="state.algorithmDetail.models && state.algorithmDetail.models.length > 0">
            <el-icon><Connection /></el-icon>
            <span>训练模型: </span>
            <div class="model-tags">
              <el-tag
                v-for="model in state.algorithmDetail.models"
                :key="model.id"
                type="success"
                effect="plain"
                class="model-tag"
                @click="navigateToModel(model.id)"
              >
                {{ model.name }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 算法库参数和指标 -->
        <div class="model-metrics">
          <div class="metrics-section">
            <h3>算法库参数</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item v-for="(value, key) in state.algorithmDetail.parameters" :key="`param-${key}`" :label="key">
                {{ value }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="metrics-section">
            <h3>测试指标</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item v-for="(value, key) in state.algorithmDetail.metrics" :key="`metric-${key}`" :label="key">
                {{ value }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>

      <!-- 主要内容区域 -->
      <el-row :gutter="20" class="main-content">
        <!-- 左侧文件树 -->
        <el-col :span="6" v-if="state.algorithmDetail.algorithm_type === 'file'">
          <el-card class="file-tree-card" shadow="always">
            <template #header>
              <div class="card-header">
                <el-icon><FolderOpened /></el-icon>
                <span>文件结构</span>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="refreshFileTree"
                  :loading="state.fileTreeLoading"
                >
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>

            <div class="file-tree-container">
              <el-tree
                ref="fileTreeRef"
                :data="state.fileTree"
                :props="fileTreeProps"
                @node-click="handleFileClick"
                :expand-on-click-node="false"
                default-expand-all
                highlight-current
                node-key="path"
                class="file-tree"
              >
                <template #default="{ node, data }">
                  <div class="file-tree-node">
                    <el-icon class="file-icon">
                      <component :is="getFileIcon(data)" />
                    </el-icon>
                    <span class="file-name">{{ data.name }}</span>
                    <span v-if="data.type === 'file'" class="file-size">
                      {{ formatSize(data.size) }}
                    </span>
                  </div>
                </template>
              </el-tree>

              <div v-if="!state.fileTree.length && !state.fileTreeLoading" class="empty-tree">
                <el-empty description="暂无文件" :image-size="60" />
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧内容区域 -->
        <el-col :span="state.algorithmDetail.algorithm_type === 'file' ? 18 : 24">
          <el-card class="content-card" shadow="always">
            <template #header>
              <div class="content-header">
                <el-tabs v-model="state.activeTab" @tab-change="handleTabChange">
                  <el-tab-pane label="README" name="readme">
                    <template #label>
                      <el-icon><Document /></el-icon>
                      README
                    </template>
                  </el-tab-pane>
                  <el-tab-pane label="代码预览" name="code" v-if="state.selectedFile && isCodeFile(state.selectedFile)">
                    <template #label>
                      <el-icon><View /></el-icon>
                      {{ state.selectedFile.name }}
                    </template>
                  </el-tab-pane>
                  <el-tab-pane label="算法库信息" name="info">
                    <template #label>
                      <el-icon><InfoFilled /></el-icon>
                      详细信息
                    </template>
                  </el-tab-pane>
                  <el-tab-pane label="评论" name="comments">
                    <template #label>
                      <el-icon><ChatDotRound /></el-icon>
                      评论 ({{ state.comments.length }})
                    </template>
                  </el-tab-pane>
                </el-tabs>

                <!-- 工具栏 -->
                <div class="content-toolbar" v-if="state.activeTab === 'readme' || state.activeTab === 'code'">
                  <el-button-group>
                    <el-button
                      size="small"
                      :type="state.previewMode === 'edit' ? 'primary' : ''"
                      @click="state.previewMode = 'edit'"
                      v-if="state.activeTab === 'readme'"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button
                      size="small"
                      :type="state.previewMode === 'preview' ? 'primary' : ''"
                      @click="state.previewMode = 'preview'"
                    >
                      <el-icon><View /></el-icon>
                      预览
                    </el-button>
                  </el-button-group>
                </div>
              </div>
            </template>

            <!-- README标签页 -->
            <div v-if="state.activeTab === 'readme'" class="readme-content">
              <!-- 编辑模式 -->
              <div v-if="state.previewMode === 'edit'" class="readme-editor">
                <md-editor
                  v-model="state.readmeContent"
                  :toolbars="['bold', 'underline', 'italic', 'strikeThrough', 'title', 'sub', 'sup', 'quote', 'unorderedList', 'orderedList', 'task', 'codeRow', 'code', 'link', 'image', 'table', 'revoke', 'next', 'save']"
                  theme="light"
                  language="zh-CN"
                  preview-theme="github"
                  height="600px"
                  :preview="false"
                  :code-theme="codeTheme"
                  @save="saveReadme"
                  placeholder="请输入README内容（支持Markdown格式）"
                />
                <div class="editor-actions">
                  <el-button type="primary" @click="saveReadme" :loading="state.saving">
                    <el-icon><Check /></el-icon>
                    保存
                  </el-button>
                  <el-button @click="cancelEdit">
                    <el-icon><Close /></el-icon>
                    取消
                  </el-button>
                </div>
              </div>

              <!-- 预览模式 -->
              <div v-else class="readme-preview">
                <md-preview
                  :modelValue="state.readmeContent || '暂无README内容'"
                  class="md-preview"
                  preview-theme="github"
                  :code-theme="codeTheme"
                />
              </div>
            </div>

            <!-- 代码预览标签页 -->
            <div v-else-if="state.activeTab === 'code'" class="code-content">
              <div v-if="state.selectedFile" class="code-viewer">
                <div class="code-header">
                  <div class="file-info">
                    <el-icon><Document /></el-icon>
                    <span class="file-path">{{ state.selectedFile.path }}</span>
                    <el-tag size="small" type="info">{{ getFileLanguage(state.selectedFile.name) }}</el-tag>
                  </div>
                  <div class="code-actions">
                    <el-button size="small" @click="downloadFile(state.selectedFile.path)">
                      <el-icon><Download /></el-icon>
                      下载
                    </el-button>
                  </div>
                </div>

                <div class="code-container" v-loading="state.codeLoading">
                  <pre v-if="state.fileContent" class="code-block"><code :class="`language-${getFileLanguage(state.selectedFile.name)}`">{{ state.fileContent }}</code></pre>
                  <el-empty v-else description="无法加载文件内容" />
                </div>
              </div>
            </div>

            <!-- 算法库信息标签页 -->
            <div v-else-if="state.activeTab === 'info'" class="info-content">
              <div class="info-section">
                <h3>基本信息</h3>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="算法库名称">{{ state.algorithmDetail.name }}</el-descriptions-item>
                  <el-descriptions-item label="项目组">{{ state.algorithmDetail.group }}</el-descriptions-item>
                  <el-descriptions-item label="算法库类型">{{ state.algorithmDetail.type_display }}</el-descriptions-item>
                  <el-descriptions-item label="实现类型">{{ state.algorithmDetail.algorithm_type_display }}</el-descriptions-item>
                  <el-descriptions-item label="状态">
                    <el-tag :type="state.algorithmDetail.status === 'online' ? 'success' : 'danger'">
                      {{ state.algorithmDetail.status === 'online' ? '已上架' : '已下架' }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="创建时间">{{ formatDate(state.algorithmDetail.create_datetime) }}</el-descriptions-item>
                  <el-descriptions-item label="更新时间">{{ formatDate(state.algorithmDetail.update_datetime) }}</el-descriptions-item>
                  <el-descriptions-item label="收藏数">{{ state.algorithmDetail.stars }}</el-descriptions-item>
                  <el-descriptions-item label="下载数">{{ state.algorithmDetail.downloads }}</el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 算法库参数 -->
              <div class="info-section" v-if="Object.keys(state.algorithmDetail.parameters || {}).length > 0">
                <h3>算法库参数</h3>
                <el-descriptions :column="2" border>
                  <el-descriptions-item v-for="(value, key) in state.algorithmDetail.parameters" :key="`param-${key}`" :label="key">
                    {{ value }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 测试指标 -->
              <div class="info-section" v-if="Object.keys(state.algorithmDetail.metrics || {}).length > 0">
                <h3>测试指标</h3>
                <el-descriptions :column="2" border>
                  <el-descriptions-item v-for="(value, key) in state.algorithmDetail.metrics" :key="`metric-${key}`" :label="key">
                    {{ value }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 分类信息 -->
              <div class="info-section" v-if="state.algorithmDetail.categories && state.algorithmDetail.categories.length > 0">
                <h3>分类信息</h3>
                <div class="category-tags">
                  <el-tag
                    v-for="category in state.algorithmDetail.categories"
                    :key="category.id"
                    type="primary"
                    class="category-tag"
                  >
                    {{ category.name }}
                  </el-tag>
                </div>
              </div>
            </div>

        <!-- 算法库评论 -->
        <div v-else-if="state.activeTab === 'comments'" class="model-comments">
          <!-- 评论列表 -->
          <div class="comments-list">
            <template v-if="state.comments.length > 0">
              <div class="comment-tree">
                <comment-item
                  v-for="comment in state.comments"
                  :key="comment.id"
                  :comment="comment"
                  @reply="handleReply"
                  @delete="handleDeleteComment"
                />
              </div>
            </template>
            <el-empty v-else description="暂无评论"></el-empty>
          </div>

          <!-- 评论输入框 -->
          <div class="comment-form">
            <h3>{{ state.replyTo ? `回复: ${state.replyTo.user.username}` : '发表评论' }}</h3>
            <div v-if="state.replyTo" class="reply-info">
              <span>{{ state.replyTo.content }}</span>
              <el-button type="text" @click="cancelReply">取消回复</el-button>
            </div>
            <el-input
              v-model="state.commentContent"
              type="textarea"
              :rows="4"
              placeholder="请输入评论内容..."
            />
            <div class="form-actions">
              <el-button type="primary" @click="submitComment" :loading="state.submittingComment">
                发表评论
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 下载进度对话框 -->
      <el-dialog
        v-model="downloadState.visible"
        title="文件下载"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        width="400px"
      >
        <div class="download-progress">
          <div class="file-info">
            <el-icon><Document /></el-icon>
            <span>{{ downloadState.fileName }}</span>
          </div>
          <el-progress 
            :percentage="downloadState.progress"
            :format="(p: number) => `${p}%`"
            :stroke-width="20"
            status="success"
          />
          <div class="download-speed">
            <span>下载速度: {{ downloadState.speed }}</span>
          </div>
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Star, Download, User, Calendar, Box, Document, Connection,
  FolderOpened, Refresh, View, Edit, Check, Close, InfoFilled, ChatDotRound, Picture
} from '@element-plus/icons-vue'
import { MdPreview, MdEditor } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import CommentItem from './CommentItem.vue'
import {
  getAlgorithm,
  starAlgorithm,
  downloadAlgorithm,
  downloadAlgorithmFile,
  getAlgorithmFileTree,
  getAlgorithmReadme,
  updateAlgorithmReadme,
  getAlgorithmFileContent
} from '@/api/algorithm/algorithms'
import { getCommentList as getComments, createComment } from '@/api/algorithm/comments'

const route = useRoute()
const router = useRouter()

// 状态管理
const state = reactive({
  loading: true,
  algorithmDetail: {} as any,
  algorithmId: 0,
  activeTab: 'readme',
  // 文件树相关
  fileTree: [] as any[],
  fileTreeLoading: false,
  selectedFile: null as any,
  // README相关
  readmeContent: '',
  previewMode: 'preview', // 'edit' | 'preview'
  saving: false,
  // 代码预览相关
  fileContent: '',
  codeLoading: false,
  // 评论相关
  comments: [] as any[],
  commentContent: '',
  replyTo: null as any,
  submittingComment: false
})

// 下载状态
const downloadState = reactive({
  visible: false,
  fileName: '',
  progress: 0,
  speed: '0 KB/s',
  startTime: 0,
  downloadedBytes: 0
})

// Markdown 主题
const previewTheme = 'github'
const codeTheme = 'github'

// 文件树属性
const fileTreeProps = {
  children: 'children',
  label: 'name',
  isLeaf: (data: any) => data.type === 'file'
}

// 初始化
onMounted(async () => {
  // 获取路由参数中的算法库ID
  const id = route.params.id
  console.log(id)
  if (id) {
    state.algorithmId = Number(id)
    await fetchAlgorithmDetail()
  } else {
    ElMessage.error('算法库ID不存在')
    router.push('/algorithm')
  }
})

// 获取算法库详情
const fetchAlgorithmDetail = async () => {
  try {
    state.loading = true
    const res = await getAlgorithm(state.algorithmId)
    state.algorithmDetail = res.data

    // 如果是文件类型的算法库，加载文件树和README
    if (state.algorithmDetail.algorithm_type === 'file') {
      await Promise.all([
        loadFileTree(),
        loadReadmeContent()
      ])
    }

    // 获取评论列表
    await fetchComments()
  } catch (error) {
    console.error('获取算法库详情失败:', error)
    ElMessage.error('获取算法库详情失败')
  } finally {
    state.loading = false
  }
}

// 加载文件树
const loadFileTree = async () => {
  try {
    state.fileTreeLoading = true
    const res = await getAlgorithmFileTree(state.algorithmId)
    state.fileTree = res.data
  } catch (error) {
    console.error('加载文件树失败:', error)
  } finally {
    state.fileTreeLoading = false
  }
}

// 刷新文件树
const refreshFileTree = () => {
  loadFileTree()
}

// 加载README内容
const loadReadmeContent = async () => {
  try {
    const res = await getAlgorithmReadme(state.algorithmId)
    state.readmeContent = res.data.content || ''
  } catch (error) {
    console.error('加载README失败:', error)
    state.readmeContent = ''
  }
}

// 保存README
const saveReadme = async () => {
  try {
    state.saving = true
    await updateAlgorithmReadme(state.algorithmId, { content: state.readmeContent })
    ElMessage.success('README保存成功')
    state.previewMode = 'preview'
  } catch (error) {
    console.error('保存README失败:', error)
    ElMessage.error('保存README失败')
  } finally {
    state.saving = false
  }
}

// 取消编辑
const cancelEdit = () => {
  state.previewMode = 'preview'
  loadReadmeContent() // 重新加载原内容
}

// 处理文件点击
const handleFileClick = async (data: any) => {
  if (data.type === 'file') {
    state.selectedFile = data
    state.activeTab = 'code'
    await loadFileContent(data.path)
  }
}

// 加载文件内容
const loadFileContent = async (filePath: string) => {
  try {
    state.codeLoading = true
    const res = await getAlgorithmFileContent(state.algorithmId, { file_path: filePath })
    state.fileContent = res.data.content
  } catch (error) {
    console.error('加载文件内容失败:', error)
    ElMessage.error('加载文件内容失败')
    state.fileContent = ''
  } finally {
    state.codeLoading = false
  }
}

// 处理标签页切换
const handleTabChange = (tab: string) => {
  state.activeTab = tab
}

// 收藏算法库
const handleStar = async () => {
  try {
    await starAlgorithm(state.algorithmId)
    state.algorithmDetail.stars++
    ElMessage.success('收藏成功')
  } catch (error) {
    console.error('收藏失败:', error)
    ElMessage.error('收藏失败')
  }
}

// 下载算法库
const handleDownload = async () => {
  try {
    await downloadAlgorithm(state.algorithmId)
    state.algorithmDetail.downloads++
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 获取文件图标
const getFileIcon = (data: any) => {
  if (data.type === 'directory') {
    return 'FolderOpened'
  }

  const ext = data.name.split('.').pop()?.toLowerCase()
  const iconMap: Record<string, string> = {
    'py': 'Document',
    'js': 'Document',
    'ts': 'Document',
    'vue': 'Document',
    'html': 'Document',
    'css': 'Document',
    'scss': 'Document',
    'json': 'Document',
    'md': 'Document',
    'txt': 'Document',
    'yml': 'Document',
    'yaml': 'Document',
    'xml': 'Document',
    'jpg': 'Picture',
    'jpeg': 'Picture',
    'png': 'Picture',
    'gif': 'Picture',
    'svg': 'Picture'
  }

  return iconMap[ext || ''] || 'Document'
}

// 判断是否为代码文件
const isCodeFile = (file: any) => {
  if (!file || file.type !== 'file') return false

  const ext = file.name.split('.').pop()?.toLowerCase()
  const codeExts = ['py', 'js', 'ts', 'vue', 'html', 'css', 'scss', 'json', 'md', 'txt', 'yml', 'yaml', 'xml', 'java', 'cpp', 'c', 'h', 'go', 'rs', 'php', 'rb', 'sh']

  return codeExts.includes(ext || '')
}

// 获取文件语言类型
const getFileLanguage = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  const langMap: Record<string, string> = {
    'py': 'python',
    'js': 'javascript',
    'ts': 'typescript',
    'vue': 'vue',
    'html': 'html',
    'css': 'css',
    'scss': 'scss',
    'json': 'json',
    'md': 'markdown',
    'txt': 'text',
    'yml': 'yaml',
    'yaml': 'yaml',
    'xml': 'xml',
    'java': 'java',
    'cpp': 'cpp',
    'c': 'c',
    'h': 'c',
    'go': 'go',
    'rs': 'rust',
    'php': 'php',
    'rb': 'ruby',
    'sh': 'bash'
  }

  return langMap[ext || ''] || 'text'
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString()
}

// 格式化文件大小
const formatSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 下载文件
const downloadFile = async (fileName: string) => {
  try {
    // 初始化下载状态
    downloadState.visible = true
    downloadState.fileName = fileName
    downloadState.progress = 0
    downloadState.speed = '0 KB/s'
    downloadState.startTime = Date.now()
    downloadState.downloadedBytes = 0
    
    // 下载文件
    const res = await downloadAlgorithmFile(state.algorithmId, fileName, (progress) => {
      // 更新下载进度
      downloadState.progress = progress
      
      // 计算下载速度
      const now = Date.now()
      const timeElapsed = (now - downloadState.startTime) / 1000 // 秒
      if (timeElapsed > 0) {
        const file = state.fileList.find(f => f.name === fileName)
        if (file) {
          const totalBytes = file.size
          const downloadedBytes = totalBytes * (progress / 100)
          const bytesPerSecond = downloadedBytes / timeElapsed
          downloadState.speed = formatSize(bytesPerSecond) + '/s'
        }
      }
    })
    
    // 创建下载链接
    const blob = new Blob([res as unknown as BlobPart])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // 下载完成
    downloadState.progress = 100
    setTimeout(() => {
      downloadState.visible = false
    }, 1000)
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败')
    downloadState.visible = false
  }
}

// 获取评论列表
const fetchComments = async () => {
  try {
    const res = await getComments({
      model: state.algorithmId,
      parent__isnull: true
    })
    state.comments = res.data || []
  } catch (error) {
    console.error('获取评论列表失败:', error)
  }
}

// 提交评论
const submitComment = async () => {
  if (!state.commentContent.trim()) {
    ElMessage.warning('评论内容不能为空')
    return
  }
  
  try {
    state.submittingComment = true
    const commentData = {
      model: state.algorithmId,
      content: state.commentContent,
      parent: state.replyTo ? state.replyTo.id : null
    }
    
    await createComment(commentData)
    ElMessage.success('评论发表成功')
    
    // 清空评论内容
    state.commentContent = ''
    
    // 清除回复状态
    if (state.replyTo) {
      state.replyTo = null
    }
    
    // 重新获取评论列表
    await fetchComments()
  } catch (error) {
    console.error('评论发表失败:', error)
    ElMessage.error('评论发表失败')
  } finally {
    state.submittingComment = false
  }
}

// 处理回复评论
const handleReply = (comment: any) => {
  state.replyTo = comment
  // 滚动到评论框
  setTimeout(() => {
    const commentForm = document.querySelector('.comment-form')
    if (commentForm) {
      commentForm.scrollIntoView({ behavior: 'smooth' })
    }
  }, 100)
}

// 取消回复
const cancelReply = () => {
  state.replyTo = null
}

// 处理删除评论
const handleDeleteComment = async (commentId: number) => {
  try {
    ElMessageBox.confirm(
      '确定要删除此评论吗？',
      '删除评论',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      // 在此处调用删除评论的API
      // await deleteComment(commentId)
      ElMessage.success('评论已删除')
      // 重新获取评论列表
      await fetchComments()
    })
  } catch (error) {
    console.error('删除评论失败:', error)
    ElMessage.error('删除评论失败')
  }
}

// 导航到模型详情
const navigateToModel = (modelId: number) => {
  router.push(`/model/models/${modelId}`);
}
</script>

<style lang="scss" scoped>
.app-container {
  .loading-container {
    padding: 20px;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  .ml-2 {
    margin-left: 8px;
  }

  .model-info-card {
    margin-bottom: 16px;
  }

  .tab-card {
    margin-bottom: 10px;
  }

  .content-card {
    min-height: 400px;
  }

  .model-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .model-title {
      h1 {
        margin: 0 0 10px 0;
      }
    }
  }

  .model-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #606266;
    }
  }

  .model-metrics {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .metrics-section {
      flex: 1;
      min-width: 300px;

      h3 {
        margin-top: 0;
        margin-bottom: 16px;
      }
    }
  }

  .model-detail-content {
    padding: 10px 0;
    
    .description-content {
      h3 {
        margin-bottom: 16px;
        font-size: 18px;
        color: #1F2A44;
      }
      
      :deep(.md-preview) {
        border: none;
        background-color: transparent;
        
        .md-editor-preview-wrapper {
          padding: 0;
          
          code {
            background-color: #f8f8f8;
            padding: 2px 4px;
            border-radius: 4px;
            color: #d56161;
          }
          
          pre {
            background-color: #f8f8f8;
            border-radius: 4px;
            padding: 12px;
          }
          
          img {
            max-width: 100%;
            border-radius: 4px;
          }
          
          h1, h2, h3 {
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 8px;
            margin-top: 24px;
          }
          
          table {
            border-collapse: collapse;
            margin: 16px 0;
            
            th, td {
              border: 1px solid #ebeef5;
              padding: 8px 16px;
            }
            
            th {
              background-color: #f8f8f8;
            }
          }
          
          blockquote {
            border-left: 4px solid #6B48FF;
            padding-left: 16px;
            color: #606266;
            background-color: #f9f9fa;
            padding: 8px 16px;
            margin: 16px 0;
            border-radius: 4px;
          }
        }
      }
    }
  }

  .model-files {
    .el-table {
      margin-bottom: 20px;
    }
  }

  .model-comments {
    .comments-list {
      margin-bottom: 30px;
    }

    .comment-form {
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 4px;

      h3 {
        margin-top: 0;
        margin-bottom: 16px;
      }

      .reply-info {
        background-color: #ecf5ff;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .form-actions {
        margin-top: 16px;
        text-align: right;
      }
    }
  }

  .download-progress {
    padding: 20px;

    .file-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      color: #606266;
    }

    .download-speed {
      margin-top: 12px;
      text-align: right;
      color: #909399;
      font-size: 14px;
    }
  }
}

.models-container {
  .model-tags {
    display: inline-flex;
    flex-wrap: wrap;
    margin-left: 4px;
    gap: 8px;
    
    .model-tag {
      cursor: pointer;
      
      &:hover {
        opacity: 0.8;
        color: var(--el-color-primary);
      }
    }
  }
}

// 新增样式
.main-content {
  margin-top: 20px;
}

.file-tree-card {
  height: calc(100vh - 200px);

  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;

    .el-button {
      margin-left: auto;
    }
  }

  .file-tree-container {
    height: calc(100% - 60px);
    overflow-y: auto;

    .file-tree {
      .file-tree-node {
        display: flex;
        align-items: center;
        gap: 6px;
        flex: 1;

        .file-icon {
          font-size: 16px;
          color: #409EFF;
        }

        .file-name {
          flex: 1;
          font-size: 13px;
        }

        .file-size {
          font-size: 11px;
          color: #909399;
        }
      }
    }

    .empty-tree {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }
}

.content-card {
  height: calc(100vh - 200px);

  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    :deep(.el-tabs) {
      flex: 1;

      .el-tabs__header {
        margin-bottom: 0;
      }

      .el-tabs__item {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }

    .content-toolbar {
      margin-left: 20px;
    }
  }

  .readme-content {
    height: calc(100% - 100px);

    .readme-editor {
      height: 100%;

      .editor-actions {
        margin-top: 16px;
        text-align: right;
      }
    }

    .readme-preview {
      height: 100%;
      overflow-y: auto;
      padding: 20px;
      background: #fafafa;
      border-radius: 8px;
    }
  }

  .code-content {
    height: calc(100% - 100px);

    .code-viewer {
      height: 100%;
      display: flex;
      flex-direction: column;

      .code-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f5f5f5;
        border-radius: 8px 8px 0 0;
        border-bottom: 1px solid #e4e7ed;

        .file-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .file-path {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            color: #606266;
          }
        }
      }

      .code-container {
        flex: 1;
        overflow: auto;
        background: #fafafa;

        .code-block {
          margin: 0;
          padding: 20px;
          background: #fafafa;
          border-radius: 0 0 8px 8px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 13px;
          line-height: 1.5;
          white-space: pre-wrap;
          word-wrap: break-word;
        }
      }
    }
  }

  .info-content {
    height: calc(100% - 100px);
    overflow-y: auto;
    padding: 20px;

    .info-section {
      margin-bottom: 32px;

      h3 {
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }

      .category-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .category-tag {
          margin: 0;
        }
      }
    }
  }
}
</style>