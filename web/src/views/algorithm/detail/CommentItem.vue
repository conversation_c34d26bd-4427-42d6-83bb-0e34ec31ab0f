<template>
  <div class="comment-item">
    <!-- 评论内容 -->
    <div class="comment-content">
      <div class="comment-avatar">
        <el-avatar :size="40" :src="comment.user.avatar">
          {{ comment.user.username ? comment.user.username.substring(0, 1).toUpperCase() : '?' }}
        </el-avatar>
      </div>
      <div class="comment-body">
        <div class="comment-header">
          <span class="username">{{ comment.user.username }}</span>
          <span class="time">{{ formatDate(comment.create_datetime) }}</span>
        </div>
        <div class="comment-text">{{ comment.content }}</div>
        <div class="comment-actions">
          <el-button type="text" @click="handleReply">回复</el-button>
          <el-button type="text" @click="handleDelete">删除</el-button>
        </div>
      </div>
    </div>

    <!-- 子评论 - 递归渲染 -->
    <div v-if="comment.replies && comment.replies.length > 0" class="replies">
      <comment-item
        v-for="reply in comment.replies"
        :key="reply.id"
        :comment="reply"
        @reply="onReplyClicked"
        @delete="onDeleteClicked"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// 定义评论类型
interface User {
  id: number;
  username: string;
  avatar?: string;
}

interface Reply {
  id: number;
  content: string;
  create_datetime: string;
  user: User;
  parent?: number;
  replies?: Reply[];
}

const props = defineProps<{
  comment: Reply
}>();

const emit = defineEmits<{
  (e: 'reply', comment: Reply): void;
  (e: 'delete', commentId: number): void;
}>();

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 回复评论
const handleReply = () => {
  emit('reply', props.comment);
};

// 删除评论
const handleDelete = () => {
  emit('delete', props.comment.id);
};

// 子评论的回复和删除
const onReplyClicked = (comment: Reply) => {
  emit('reply', comment);
};

const onDeleteClicked = (commentId: number) => {
  emit('delete', commentId);
};
</script>

<style lang="scss" scoped>
.comment-item {
  margin-bottom: 20px;

  .comment-content {
    display: flex;
    gap: 16px;
  }

  .comment-body {
    flex: 1;
  }

  .comment-header {
    margin-bottom: 8px;

    .username {
      font-weight: bold;
      margin-right: 10px;
    }

    .time {
      color: #909399;
      font-size: 12px;
    }
  }

  .comment-text {
    line-height: 1.6;
    margin-bottom: 8px;
  }

  .comment-actions {
    display: flex;
    gap: 16px;
  }

  .replies {
    margin-top: 16px;
    margin-left: 56px;
    border-left: 2px solid #ebeef5;
    padding-left: 16px;
  }
}
</style> 