<template>
  <div class="system-model-container">
    <el-dialog v-model="state.isShowDialog" width="70%" center class="model-upload-dialog">
      <template #header>
        <div style="font-size: large" v-drag="['.system-model-container .el-dialog', '.system-model-container .el-dialog__header']">{{title}}</div>
      </template>
      
      <!-- 步骤条 -->
      <el-steps 
        :active="state.activeStep" 
        finish-status="success" 
        process-status="process" 
        class="upload-steps"
        align-center
      >
        <el-step title="基础信息" :icon="stepOneIcon" />
        <el-step title="算法库描述" :icon="stepTwoIcon" />
        <el-step title="算法库文件" :icon="stepThreeIcon" />
      </el-steps>
      
      <el-form
        :model="state.ruleForm"
        :rules="state.ruleRules"
        ref="ruleFormRef"
        label-width="120px"
      >
        <!-- 步骤一：基础信息 -->
        <div v-if="state.activeStep === 0">
          <div class="step-description">
            <div class="step-title">第一步：填写算法库基本信息</div>
            <div class="step-info">请提供算法库的名称、所属项目组以及分类信息</div>
          </div>
          <el-form-item label="算法库名称" prop="name">
            <el-input 
              v-model="state.ruleForm.name" 
              placeholder="请输入算法库名称（3-63字符，只能包含小写字母、数字、点和连字符）"
            />
            <div class="form-tip">名称将用于MinIO存储路径，只能包含小写字母、数字、点和连字符</div>
          </el-form-item>
          <el-form-item label="项目组" prop="group">
            <el-input v-model="state.ruleForm.group" placeholder="请输入项目组名称" />
          </el-form-item>
          <el-form-item label="算法库类型" prop="type">
            <el-radio-group v-model="state.ruleForm.type" @change="handleAlgorithmTypeChange">
              <el-radio label="general">
                <el-icon><Collection /></el-icon>
                通用算法
              </el-radio>
              <el-radio label="specialized">
                <el-icon><Tools /></el-icon>
                专用算法
              </el-radio>
            </el-radio-group>
            <div class="form-tip">通用算法包括运筹优化、深度学习、强化学习；专用算法包括计算机视觉、自然语言处理等</div>
          </el-form-item>

          <el-form-item label="算法库分类" prop="category_ids">
            <div class="category-select-container">
              <!-- 通用算法分类 -->
              <div v-if="state.ruleForm.type === 'general'" class="category-groups">
                <div class="category-group-title">通用算法分类</div>
                <div class="category-grid">
                  <div
                    v-for="category in state.generalCategories"
                    :key="category.id"
                    class="category-card"
                    :class="{ 'selected': isRootCategorySelected(category.id) }"
                    @click="toggleRootCategory(category.id)"
                  >
                    <div class="category-header">
                      <el-icon class="category-icon">
                        <component :is="getCategoryIcon(category.code)" />
                      </el-icon>
                      <span class="category-name">{{ category.name }}</span>
                      <el-icon class="check-icon" v-if="isRootCategorySelected(category.id)">
                        <Check />
                      </el-icon>
                    </div>
                    <div class="category-children" v-if="isRootCategorySelected(category.id)">
                      <el-checkbox-group v-model="state.selectedSubCategories[category.id]">
                        <el-checkbox
                          v-for="child in category.children"
                          :key="child.id"
                          :label="child.id"
                          class="sub-category-item"
                        >
                          {{ child.name }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 专用算法分类 -->
              <div v-else class="category-groups">
                <div class="category-group-title">专用算法分类</div>
                <div class="category-grid">
                  <div
                    v-for="category in state.specializedCategories"
                    :key="category.id"
                    class="category-card"
                    :class="{ 'selected': isRootCategorySelected(category.id) }"
                    @click="toggleRootCategory(category.id)"
                  >
                    <div class="category-header">
                      <el-icon class="category-icon">
                        <component :is="getCategoryIcon(category.code)" />
                      </el-icon>
                      <span class="category-name">{{ category.name }}</span>
                      <el-icon class="check-icon" v-if="isRootCategorySelected(category.id)">
                        <Check />
                      </el-icon>
                    </div>
                    <div class="category-children" v-if="isRootCategorySelected(category.id)">
                      <el-checkbox-group v-model="state.selectedSubCategories[category.id]">
                        <el-checkbox
                          v-for="child in category.children"
                          :key="child.id"
                          :label="child.id"
                          class="sub-category-item"
                        >
                          {{ child.name }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-tip">请选择至少一个分类，可以选择多个子分类</div>
          </el-form-item>
          <el-form-item label="算法库状态" prop="status">
            <el-radio-group v-model="state.ruleForm.status">
              <el-radio label="online">上架</el-radio>
              <el-radio label="offline">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        
        <!-- 步骤二：算法库描述 -->
        <div v-if="state.activeStep === 1">
          <div class="step-description">
            <div class="step-title">第二步：编写算法库描述</div>
            <div class="step-info">请使用Markdown格式详细描述算法库的功能、使用场景、技术参数等信息，左侧编辑右侧预览</div>
          </div>
          <el-form-item label="算法库描述" prop="description">
            <md-editor
              v-model="state.ruleForm.description"
              :toolbars="['bold', 'underline', 'italic', 'strikeThrough', 'title', 'sub', 'sup', 'quote', 'unorderedList', 'orderedList', 'task', 'codeRow', 'code', 'link', 'image', 'table', 'revoke', 'next', 'save']"
              theme="light" 
              language="zh-CN"
              preview-theme="github"
              height="300px"
              model-value-type="string"
              :preview="true"
              :code-theme="codeTheme"
              class="markdown-editor"
              :show-code-row-number="true"
              placeholder="请输入算法库描述（支持Markdown格式）"
            />
          </el-form-item>
        </div>
        
        <!-- 步骤三：算法库文件 -->
        <div v-if="state.activeStep === 2">
          <div class="step-description">
            <div class="step-title">第三步：上传算法库文件</div>
            <div class="step-info">请选择需要上传的算法库文件或文件夹，支持批量上传</div>
          </div>
          
          <!-- 编辑模式下显示已有文件列表 -->
          <div v-if="state.isEdit && state.ruleForm.file_list && state.ruleForm.file_list.length > 0">
            <el-form-item label="已有文件">
              <el-table class="file-list-table" :data="state.ruleForm.file_list">
                <el-table-column label="文件名" prop="name" />
                <el-table-column label="大小" width="120">
                  <template #default="scope">
                    {{ formatFileSize(scope.row.size) }}
                  </template>
                </el-table-column>
                <el-table-column label="最后修改时间" width="180" prop="last_modified" />
              </el-table>
            </el-form-item>
          </div>
          
          <!-- 仅在新增模式下显示文件上传区域 -->
          <el-form-item label="算法库文件" v-if="!state.isEdit" required>
            <div class="upload-buttons">
              <!-- 文件上传 -->
              <el-upload
                ref="uploadRef"
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                :file-list="fileListRef"
                multiple
                :http-request="() => {}"
                class="model-upload"
              >
                <el-button type="primary">
                  <SvgIcon name="elementUpload"/>
                  选择文件
                </el-button>
              </el-upload>
              
              <!-- 文件夹上传 -->
              <input
                ref="uploadFolderRef"
                type="file"
                style="display: none"
                @change="handleFolderChange"
                webkitdirectory
                multiple
              />
              <el-button type="primary" @click="triggerFolderUpload">
                <SvgIcon name="elementFolder"/>
                选择文件夹
              </el-button>
            </div>
            <div class="form-tip">支持单独选择文件或整个文件夹上传</div>
            
            <!-- 已选文件列表 -->
            <el-table v-if="uploadStore.files.length > 0" class="file-list-table" :data="uploadStore.files">
              <el-table-column label="文件名" prop="name" />
              <el-table-column label="路径" prop="relativePath" width="200" />
              <el-table-column label="大小" width="120">
                <template #default="scope">
                  {{ formatFileSize(scope.row.size) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button type="danger" link @click="handleFileRemove(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </div>
      </el-form>
      
      <!-- 步骤导航按钮 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel">取消</el-button>
          <el-button v-if="state.activeStep > 0" @click="prevStep">上一步</el-button>
          <el-button 
            v-if="state.activeStep < 2" 
            type="primary" 
            @click="nextStep"
          >下一步</el-button>
          <el-button 
            v-if="state.activeStep === 2" 
            type="primary" 
            :loading="state.loading" 
            @click="onSubmit"
          >保存</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 上传进度组件 -->
    <UploadProgress
      v-if="state.uploadProgressVisible"
      v-model:visible="state.uploadProgressVisible"
      :files="state.uploadFiles"
      @close="handleUploadComplete"
    />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, unref, getCurrentInstance, computed, watch } from "vue";
import { createAlgorithm, updateAlgorithm, getUploadUrl, uploadAlgorithm } from "@/api/algorithm/algorithms";
import { getAlgorithmCategoryTree } from "@/api/algorithm/categories";
import { ElMessage } from "element-plus";
import { useUploadStore } from '@/stores/uploadStore';
import UploadProgress from '@/components/UploadProgress.vue';
// @ts-ignore
import { MdEditor } from 'md-editor-v3';
import 'md-editor-v3/lib/style.css';
import {
  Collection,
  Tools,
  Check,
  Calculator,
  Cpu,
  View,
  ChatDotRound,
  Star
} from '@element-plus/icons-vue';

const props = defineProps({
  title: {
    type: String,
    default: () => "",
  },
})

const { proxy } = getCurrentInstance() as any;
const ruleFormRef = ref<HTMLElement | null>(null);
// 上传相关
const fileListRef = ref<any[]>([]);
const uploadRef = ref();
const uploadFolderRef = ref();
const uploadStore = useUploadStore();

// Markdown编辑器配置
const codeTheme = 'atom';

const state = reactive({
  // 是否显示弹出层
  isShowDialog: false,
  loading: false,
  isEdit: false, // 是否为编辑模式
  activeStep: 0, // 当前步骤
  // 算法库对象
  ruleForm: {
    id: 0, // 算法库ID
    name: "", // 算法库名称
    description: "", // 算法库描述
    type: "general", // 算法库类型：general(通用) 或 specialized(专用)
    group: "", // 项目组
    status: "online", // 算法库状态
    file_list: [] as any[], // 已有文件列表
    files: [] as any[] // 新上传文件
  },
  // 分类相关
  generalCategories: [] as any[], // 通用算法分类
  specializedCategories: [] as any[], // 专用算法分类
  selectedRootCategories: [] as number[], // 选中的根分类ID
  selectedSubCategories: {} as Record<number, number[]>, // 选中的子分类ID映射
  // 上传进度相关
  uploadProgressVisible: false,
  uploadFiles: [] as any[],
  // 表单校验
  ruleRules: {
    name: [
      { required: true, message: "算法库名称不能为空", trigger: "blur" }
    ],
    description: [
      { required: true, message: "算法库描述不能为空", trigger: "blur" }
    ],
    group: [
      { required: true, message: "项目组不能为空", trigger: "blur" }
    ]
  },
});

// 步骤图标
const stepOneIcon = computed(() => {
  return state.activeStep > 0 ? 'Check' : 'Edit';
});

const stepTwoIcon = computed(() => {
  if (state.activeStep > 1) return 'Check';
  if (state.activeStep === 1) return 'EditPen';
  return 'Edit';
});

const stepThreeIcon = computed(() => {
  if (state.activeStep === 2) return 'Upload';
  return 'Edit';
});

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 加载分类树
const loadCategoryTree = async () => {
  try {
    const res = await getAlgorithmCategoryTree();

    // 分离通用算法和专用算法分类
    const generalCategoryCodes = ['operations_research', 'deep_learning', 'reinforcement_learning']
    const specializedCategoryCodes = ['computer_vision', 'natural_language_processing', 'recommendation_system', 'time_series_analysis']

    state.generalCategories = res.data.filter((category: any) =>
      category.parent === null && generalCategoryCodes.includes(category.code)
    )

    state.specializedCategories = res.data.filter((category: any) =>
      category.parent === null && specializedCategoryCodes.includes(category.code)
    )
    
    // 初始化分类映射
    if (state.rootCategories && state.rootCategories.length > 0) {
      state.rootCategories.forEach((root: any) => {
        // 每个顶级分类初始化为null
        if (!state.ruleForm.categoryMap[root.id]) {
          state.ruleForm.categoryMap[root.id] = null;
        }
      });
    }
  } catch (error) {
    console.error('获取分类树失败:', error);
  }
};

// 算法库类型变更处理
const handleAlgorithmTypeChange = (type: string) => {
  // 清空已选分类
  state.selectedRootCategories = []
  state.selectedSubCategories = {}
};

// 获取分类图标
const getCategoryIcon = (categoryCode: string) => {
  const iconMap: Record<string, string> = {
    'operations_research': 'Calculator',
    'deep_learning': 'Cpu',
    'reinforcement_learning': 'Cpu',
    'computer_vision': 'View',
    'natural_language_processing': 'ChatDotRound',
    'recommendation_system': 'Star',
    'time_series_analysis': 'Star'
  }
  return iconMap[categoryCode] || 'Star'
};

// 判断根分类是否被选中
const isRootCategorySelected = (categoryId: number) => {
  return state.selectedRootCategories.includes(categoryId)
};

// 切换根分类选择状态
const toggleRootCategory = (categoryId: number) => {
  const index = state.selectedRootCategories.indexOf(categoryId)
  if (index > -1) {
    // 取消选择
    state.selectedRootCategories.splice(index, 1)
    delete state.selectedSubCategories[categoryId]
  } else {
    // 选择
    state.selectedRootCategories.push(categoryId)
    state.selectedSubCategories[categoryId] = []
  }
};

// 下一步
const nextStep = () => {
  // 表单验证
  if (state.activeStep === 0) {
    // 验证基础信息
    if (!state.ruleForm.name) {
      ElMessage.warning('请填写算法库名称');
      return;
    }
    if (!state.ruleForm.group) {
      ElMessage.warning('请填写项目组');
      return;
    }

    // 验证分类选择
    if (!state.isEdit) { // 仅在新增模式下验证分类选择
      const categoryIds: number[] = [];
      for (const rootId of state.selectedRootCategories) {
        const subCategories = state.selectedSubCategories[rootId] || [];
        categoryIds.push(...subCategories);
      }

      // if (categoryIds.length === 0) {
      //   ElMessage.warning('请至少选择一个分类');
      //   return;
      // }
    }
  }

  state.activeStep++;
};

// 上一步
const prevStep = () => {
  if (state.activeStep > 0) {
    state.activeStep--;
  }
};

// 触发文件夹上传
const triggerFolderUpload = () => {
  if (uploadFolderRef.value) {
    uploadFolderRef.value.click();
  }
};

// 处理文件夹变更
const handleFolderChange = (event: any) => {
  if (!state.ruleForm.name) {
    ElMessage.warning('请先填写算法库名称');
    return;
  }
  
  const files = event.target.files;
  if (files && files.length > 0) {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      // 添加到上传状态管理
      uploadStore.addFile(file);
    }
  }
  // 重置input以允许再次选择相同文件夹
  if (uploadFolderRef.value) {
    uploadFolderRef.value.value = '';
  }
};

// 处理文件变更
const handleFileChange = (file: any) => {
  if (!state.ruleForm.name) {
    ElMessage.warning('请先填写算法库名称');
    return false;
  }
  
  // 将文件添加到上传状态管理
  uploadStore.addFile(file);
  return true;
};

// 处理文件删除
const handleFileRemove = (file: any) => {
  const index = uploadStore.files.findIndex(f => f.name === file.name && f.relativePath === file.relativePath);
  if (index > -1) {
    uploadStore.files.splice(index, 1);
  }
};

// 上传单个文件
const uploadSingleFile = async (file: any) => {
  try {
    const fileName = file.relativePath;
    
    // 获取预签名上传URL
    const { data } = await getUploadUrl({
      filename: fileName,
      name: state.ruleForm.name,
      group: state.ruleForm.group
    });
    
    if (data.url) {
      // 上传文件
      await uploadFile(file.raw, data.url, (progress: number) => {
        uploadStore.updateFileProgress(file.name, progress);
      });
      
      // 将文件信息添加到表单
      state.ruleForm.files.push({
        name: fileName,
        size: file.raw.size,
        type: file.raw.type,
        object_name: data.object_name,
        relative_path: file.relativePath
      });

      uploadStore.updateFileStatus(file.name, 'success');
      return true;
    }
    return false;
  } catch (error) {
    console.error('文件上传失败:', error);
    uploadStore.updateFileStatus(file.name, 'error');
    return false;
  }
};

// 上传文件
const uploadFile = async (file: File, uploadUrl: string, onProgress: (progress: number) => void) => {
  try {
    const xhr = new XMLHttpRequest();
    
    await new Promise((resolve, reject) => {
      xhr.upload.onprogress = (e) => {
        if (e.lengthComputable) {
          const percentComplete = Math.round((e.loaded / e.total) * 100);
          onProgress(percentComplete);
        }
      };

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve(xhr.response);
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };

      xhr.onerror = () => reject(new Error('Upload failed'));
      xhr.onabort = () => reject(new Error('Upload aborted'));

      xhr.open('PUT', uploadUrl, true);
      xhr.setRequestHeader('Content-Type', file.type || 'application/octet-stream');
      xhr.send(file);
    });

    onProgress(100);
  } catch (error) {
    console.error('Upload error:', error);
    throw error;
  }
};

// 处理上传完成
const handleUploadComplete = () => {
  state.uploadFiles = [];
  closeDialog(state.ruleForm);
};

// 处理分类数据，从categories设置选中的分类
const setupCategoryMap = (row: any) => {
  // 重置分类选择
  state.selectedRootCategories = [];
  state.selectedSubCategories = {};

  if (row.categories && row.categories.length > 0) {
    // 遍历算法库的分类
    row.categories.forEach((category: any) => {
      // 找到此分类所属的顶级分类
      const rootCategory = findRootCategory(category.id);
      if (rootCategory) {
        // 添加根分类（如果还没有）
        if (!state.selectedRootCategories.includes(rootCategory.id)) {
          state.selectedRootCategories.push(rootCategory.id);
          state.selectedSubCategories[rootCategory.id] = [];
        }
        // 添加子分类
        if (!state.selectedSubCategories[rootCategory.id].includes(category.id)) {
          state.selectedSubCategories[rootCategory.id].push(category.id);
        }
      }
    });
  }
};

// 查找分类所属的顶级分类
const findRootCategory = (categoryId: number): any | null => {
  // 合并所有分类进行查找
  const allCategories = [...state.generalCategories, ...state.specializedCategories];

  // 先检查是否本身就是顶级分类
  const directRoot = allCategories.find((cat: any) => cat.id === categoryId);
  if (directRoot) {
    return directRoot;
  }

  // 在子分类中查找
  for (const rootCategory of allCategories) {
    const children = rootCategory.children || [];
    const found = children.find((c: any) => c.id === categoryId);
    if (found) {
      return rootCategory;
    }
  }

  return null;
};

// 打开弹窗
const openDialog = async (row: any) => {
  // 清空状态
  uploadStore.$reset();
  state.activeStep = 0;
  state.isEdit = !!row.id;
  
  // 如果有ID，表示是编辑模式
  if (row.id) {
    state.ruleForm = JSON.parse(JSON.stringify(row));
    // 确保算法库类型字段存在，如果没有则默认为通用算法
    if (!state.ruleForm.type) {
      state.ruleForm.type = "general";
    }
  } else {
    // 新增模式
    state.ruleForm = {
      id: 0,
      name: "",
      description: "",
      type: "general",
      group: "",
      status: "online",
      file_list: [],
      files: []
    };
    // 重置分类选择
    state.selectedRootCategories = [];
    state.selectedSubCategories = {};
  }
  
  state.isShowDialog = true;
  state.loading = false;
  
  // 加载分类树
  await loadCategoryTree();
  
  // 如果是编辑模式，设置分类选择
  if (state.isEdit && row.categories) {
    setupCategoryMap(row);
  }
};

// 关闭弹窗
const closeDialog = (row?: object) => {
  proxy.mittBus.emit("onEditAlgorithmModule", row);
  state.isShowDialog = false;
};

// 取消
const onCancel = () => {
  closeDialog();
};

// 保存
const onSubmit = async () => {
  // 仅在新增模式下验证
  if (!state.isEdit && uploadStore.files.length === 0) {
    ElMessage.warning('请选择至少一个算法库文件');
    return;
  }
  
  state.loading = true;
  
  try {
    // 处理分类ID - 收集所有选中的子分类
    const categoryIds: number[] = [];
    for (const rootId of state.selectedRootCategories) {
      const subCategories = state.selectedSubCategories[rootId] || [];
      categoryIds.push(...subCategories);
    }

    // if (categoryIds.length === 0) {
    //   ElMessage.warning('请至少选择一个分类');
    //   state.loading = false;
    //   return;
    // }
    
    if (state.isEdit) {
      // 修改已有算法库
      await updateAlgorithm(state.ruleForm.id, {
        ...state.ruleForm,
        category_ids: categoryIds
      });
      ElMessage.success("修改成功");
      state.loading = false;
      closeDialog(state.ruleForm);
    } else {
      // 新增算法库，先上传文件
      if (uploadStore.files.length > 0) {
        for (const file of uploadStore.files) {
          if (file.status === 'pending') {
            await uploadSingleFile(file);
          }
        }
        
        // 提交表单
        const formData = new FormData();
        formData.append('name', state.ruleForm.name);
        formData.append('group', state.ruleForm.group || 'default');
        formData.append('description', state.ruleForm.description);
        formData.append('type', state.ruleForm.type);
        formData.append('status', state.ruleForm.status);
        
        // 添加类别ID
        categoryIds.forEach(id => {
          formData.append('category_ids', id.toString());
        });
        
        formData.append('files', JSON.stringify(state.ruleForm.files));
        
        await uploadAlgorithm(formData);
        ElMessage.success("算法库上传成功");
      }
      
      state.loading = false;
      closeDialog(state.ruleForm);
    }
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('操作失败，请重试');
    state.loading = false;
  }
};

defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

.upload-buttons {
  display: flex;
  gap: 10px;
}

.file-list-table {
  margin-top: 15px;
}

.step-description {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #6B48FF;
  
  .step-title {
    font-size: 16px;
    font-weight: 600;
    color: #1F2A44;
    margin-bottom: 5px;
  }
  
  .step-info {
    font-size: 14px;
    color: #64748B;
    line-height: 1.5;
  }
}

.model-upload-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 25px;
  }

  .upload-steps {
    margin-bottom: 30px;
    
    :deep(.el-step__title) {
      font-size: 16px;
      font-weight: 500;
      
      &.is-process {
        color: #6B48FF;
        font-weight: 600;
      }
      
      &.is-success {
        color: #67C23A;
      }
    }
    
    :deep(.el-step__head.is-process) {
      color: #6B48FF;
      border-color: #6B48FF;
    }
    
    :deep(.el-step__head.is-success) {
      color: #67C23A;
      border-color: #67C23A;
    }
  }
}

.category-select-container {
  padding: 16px;
  border-radius: 8px;
  background-color: #f9f9fa;
  border: 1px solid #e4e7ed;

  .category-groups {
    .category-group-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        width: 4px;
        height: 16px;
        background: #409EFF;
        border-radius: 2px;
      }
    }

    .category-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;

      .category-card {
        border: 2px solid #e4e7ed;
        border-radius: 8px;
        padding: 16px;
        background: #ffffff;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409EFF;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        &.selected {
          border-color: #409EFF;
          background: #f0f9ff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        .category-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;

          .category-icon {
            font-size: 20px;
            color: #409EFF;
          }

          .category-name {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          .check-icon {
            font-size: 18px;
            color: #67C23A;
          }
        }

        .category-children {
          border-top: 1px solid #e4e7ed;
          padding-top: 12px;

          :deep(.el-checkbox-group) {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .el-checkbox {
              margin-right: 0;

              .el-checkbox__label {
                font-size: 14px;
                color: #606266;
              }

              &.is-checked .el-checkbox__label {
                color: #409EFF;
              }
            }
          }
        }
      }
    }
  }
}

:deep(.markdown-editor) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #E4E7ED;
  
  .cm-editor {
    font-size: 14px;
    font-family: 'Consolas', 'Monaco', monospace;
  }
  
  .md-editor-preview-wrapper {
    padding: 16px;
    font-size: 14px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  width: 100%;
}
</style> 