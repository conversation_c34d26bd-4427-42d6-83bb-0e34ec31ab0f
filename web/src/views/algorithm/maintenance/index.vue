<template>
  <div class="app-container">
    <el-card shadow="always">
      <!-- 查询 -->
      <el-form
          :model="state.queryParams"
          ref="queryForm"
          :inline="true"
          label-width="68px"
      >
        <el-form-item label="关键词" prop="search">
          <el-input
              placeholder="请输入算法库名称/描述模糊查询"
              clearable
              @keyup.enter="handleQuery"
              style="width: 240px"
              v-model="state.queryParams.search"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
              v-model="state.queryParams.status"
              placeholder="算法库状态"
              clearable
              style="width: 240px"
          >
            <el-option
                v-for="dict in state.statusOptions"
                :key="dict.dict_value"
                :label="dict.dict_label"
                :value="dict.dict_value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="handleQuery">
            <SvgIcon name="elementSearch"/>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <SvgIcon name="elementRefresh"/>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="card-header-text">算法库列表</span>
          <div>
            <el-button
                type="primary"
                plain
                @click="onOpenAddModule"
            >
              <SvgIcon name="elementPlus"/>
              新增
            </el-button>
            <el-button
                type="danger"
                plain
                :disabled="state.multiple"
                @click="onTabelRowDel"
            >
              <SvgIcon name="elementDelete"/>
              删除
            </el-button>
            <el-button
                type="warning"
                plain
                @click="handleExport"
            >
              <SvgIcon name="elementDownload"/>
              导出
            </el-button>
          </div>
        </div>
      </template>
      <!--数据表格-->
      <el-table
          v-loading="state.loading"
          :data="state.tableData"
          @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="算法库ID" align="center" prop="id" width="100"/>
        <el-table-column label="算法库名称" align="center" prop="name" width="120"/>
        <el-table-column label="项目组" align="center" prop="group" width="100"/>
        <el-table-column label="算法库分类" align="center" width="180">
          <template #default="scope">
            <el-tag 
              v-for="category in scope.row.leaf_categories" 
              :key="category.id" 
              size="small"
              style="margin-right: 5px; margin-bottom: 5px">
              {{ category.name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建者" align="center" prop="creator_name" width="100"/>
        <el-table-column label="创建时间" align="center" prop="create_datetime" width="160"/>
        <el-table-column label="下载次数" align="center" prop="downloads" width="90"/>
        <el-table-column label="收藏数" align="center" prop="stars" width="90"/>
        <el-table-column label="存储路径" align="center" prop="minio_path" width="160"/>
        <el-table-column
            label="状态"
            align="center"
            prop="status"
            width="90"
        >
          <template #default="scope">
            <el-tag
                :type="scope.row.status === 'online' ? 'success' : 'danger'"
                disable-transitions
            >{{ scope.row.status === 'online' ? '上架' : '下架' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="120"
        >
          <template #default="scope">
            <el-popover placement="left">
              <template #reference>
                <el-button type="primary" circle>
                  <SvgIcon name="elementStar"/>
                </el-button>
              </template>
              <div>
                <el-button text type="primary" @click="onOpenEditModule(scope.row)">
                  <SvgIcon name="elementEdit"/>
                  修改
                </el-button>
              </div>
              <div>
                <el-button text type="primary" @click="onTabelRowDel(scope.row)">
                  <SvgIcon name="elementDelete"/>
                  删除
                </el-button>
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页设置-->
      <div v-show="state.total > 0">
        <el-divider></el-divider>
        <el-pagination
            background
            :total="state.total"
            :current-page="state.queryParams.page"
            :page-size="state.queryParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <!-- 添加或修改算法库对话框 -->
    <EditModule ref="editModuleRef" :title="state.title"/>
  </div>
</template>

<script lang="ts" setup>
import {
  ref,
  reactive,
  onMounted,
  getCurrentInstance,
  onUnmounted,
} from "vue";
import {ElMessageBox, ElMessage} from "element-plus";
import {getAlgorithmList, deleteAlgorithm} from "@/api/algorithm/algorithms";
import EditModule from "./component/editModule.vue";
import {handleFileError} from "@/utils/export";

const {proxy} = getCurrentInstance() as any;
const editModuleRef = ref();
const state = reactive({
  // 遮罩层
  loading: true,
  // 选中数组
  ids: [] as number[],
  // 非单个禁用
  single: true,
  // 非多个禁用
  multiple: true,
  // 弹出层标题
  title: "",
  // 算法库表格数据
  tableData: [],
  // 总条数
  total: 0,
  // 状态数据字典
  statusOptions: [] as any[],
  // 查询参数
  queryParams: {
    // 页码
    page: 1,
    // 每页大小
    pageSize: 10,
    search: '',
    status: undefined,
  },
});

/** 查询算法库列表 */
const handleQuery = () => {
  state.loading = true;
  getAlgorithmList(state.queryParams).then((response) => {
    state.tableData = response.data.data;
    state.total = response.data.total;
    state.loading = false;
  });
};

/** 重置按钮操作 */
const resetQuery = () => {
  state.queryParams = {
    page: 1,
    pageSize: 10,
    search: '',
    status: undefined,
  };
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: any[]) => {
  state.ids = selection.map((item: any) => item.id);
  state.single = selection.length !== 1;
  state.multiple = !selection.length;
};

/** 新增按钮操作 */
const onOpenAddModule = () => {
  state.title = "添加算法库";
  editModuleRef.value.openDialog({});
};

/** 修改按钮操作 */
const onOpenEditModule = (row: any) => {
  state.title = "修改算法库";
  editModuleRef.value.openDialog(row);
};

/** 删除按钮操作 */
const onTabelRowDel = (row?: any) => {
  const algorithmIds = row?.id || state.ids;
  if (!algorithmIds) return;
  
  ElMessageBox.confirm(`是否确认删除算法库编号为"${algorithmIds}"的数据?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(function () {
    return deleteAlgorithm(algorithmIds);
  }).then(() => {
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

/** 导出按钮操作 */
const handleExport = () => {
  // 导出逻辑
};

/** 每页显示条数改变 */
const handleSizeChange = (val: number) => {
  state.queryParams.pageSize = val;
  handleQuery();
};

/** 页码改变 */
const handleCurrentChange = (val: number) => {
  state.queryParams.page = val;
  handleQuery();
};

/** 查询状态字典 */
const getStatusDict = () => {
  state.statusOptions = [
    { dict_label: '已上架', dict_value: 'online' },
    { dict_label: '已下架', dict_value: 'offline' }
  ];
};

/** 初始化 */
onMounted(() => {
  getStatusDict();
  handleQuery();
});
</script>

<style lang="scss" scoped>
.app-container {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .card-header-text {
    font-size: 18px;
    font-weight: bold;
  }
}
</style>
