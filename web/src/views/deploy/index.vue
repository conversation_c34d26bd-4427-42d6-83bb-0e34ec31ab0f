<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon running">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.running_deployments }}</div>
              <div class="stats-label">运行中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon stopped">
              <el-icon><VideoPause /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.stopped_deployments }}</div>
              <div class="stats-label">已停止</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon failed">
              <el-icon><Close /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.failed_deployments }}</div>
              <div class="stats-label">部署失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon healthy">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.healthy_services }}</div>
              <div class="stats-label">健康服务</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作栏 -->
    <el-card class="operation-card">
      <div class="operation-header">
        <div class="operation-left">
          <el-button type="primary" @click="handleDeploy" :icon="Plus">
            部署模型
          </el-button>
          <el-button @click="handleRefresh" :icon="Refresh">
            刷新
          </el-button>
        </div>
        <div class="operation-right">
          <el-input
            v-model="queryParams.search"
            placeholder="搜索部署名称或模型名称"
            style="width: 300px"
            clearable
            @keyup.enter="handleQuery"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select
            v-model="queryParams.status"
            placeholder="状态筛选"
            style="width: 120px; margin-left: 10px"
            clearable
            @change="handleQuery"
          >
            <el-option label="运行中" value="running" />
            <el-option label="已停止" value="stopped" />
            <el-option label="部署中" value="deploying" />
            <el-option label="失败" value="failed" />
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 部署列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="deploymentList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="deployment_name" label="部署名称" min-width="150">
          <template #default="{ row }">
            <div class="deployment-name">
              <span>{{ row.deployment_name }}</span>
              <el-tag v-if="row.status === 'running'" type="success" size="small">运行中</el-tag>
              <el-tag v-else-if="row.status === 'stopped'" type="info" size="small">已停止</el-tag>
              <el-tag v-else-if="row.status === 'deploying'" type="warning" size="small">部署中</el-tag>
              <el-tag v-else-if="row.status === 'failed'" type="danger" size="small">失败</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="model_name" label="模型名称" min-width="120" />
        <el-table-column prop="version_number" label="版本" width="100" />
        <el-table-column prop="service_port" label="端口" width="80" />
        <el-table-column label="服务状态" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.service_info && row.service_info.is_healthy" type="success" size="small">
              <el-icon><CircleCheck /></el-icon> 健康
            </el-tag>
            <el-tag v-else-if="row.service_info && !row.service_info.is_healthy" type="danger" size="small">
              <el-icon><CircleClose /></el-icon> 异常
            </el-tag>
            <el-tag v-else type="info" size="small">
              <el-icon><Warning /></el-icon> 未知
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="deployed_at" label="部署时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.deployed_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                v-if="row.status === 'running'"
                type="warning"
                size="small"
                @click="handleStop(row)"
              >
                停止
              </el-button>
              <el-button
                v-if="row.status === 'stopped' || row.status === 'failed'"
                type="success"
                size="small"
                @click="handleRestart(row)"
              >
                重启
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="handleViewDetail(row)"
              >
                详情
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleRemove(row)"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-show="total > 0"
          :current-page="queryParams.page"
          :page-size="queryParams.pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 部署对话框 -->
    <DeployDialog
      v-model:visible="deployDialog.visible"
      :preselected-model-version="deployDialog.preselectedModelVersion"
      @success="handleDeploySuccess"
    />

    <!-- 详情对话框 -->
    <DetailDialog
      v-model:visible="detailDialog.visible"
      :deployment-id="detailDialog.deploymentId"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, rowContextKey } from 'element-plus'
import {
  Plus, Refresh, Search, VideoPlay, VideoPause, Close, CircleCheck, CircleClose, Warning
} from '@element-plus/icons-vue'
import { getDeploymentList, getDeploymentStats, stopDeployment, restartDeployment, removeDeployment } from '@/api/deploy/deployments'
import { formatTime } from '@/utils/formatTime'
import DeployDialog from './components/DeployDialog.vue'
import DetailDialog from './components/DetailDialog.vue'

// 响应式数据
const loading = ref(false)
const deploymentList = ref([])
const total = ref(0)
const selectedRows = ref([])
const route = useRoute()

// 统计数据
const stats = reactive({
  total_deployments: 0,
  running_deployments: 0,
  stopped_deployments: 0,
  failed_deployments: 0,
  total_services: 0,
  healthy_services: 0
})

// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  search: '',
  status: ''
})

// 对话框状态
const deployDialog = reactive({
  visible: false,
  preselectedModelVersion: null as any
})

const detailDialog = reactive({
  visible: false,
  deploymentId: null
})

// 获取部署列表
const getList = async () => {
  loading.value = true
  try {
    const res = await getDeploymentList(queryParams)
    deploymentList.value = res.data.data
    total.value = res.data.total
  } catch (error) {
    console.error('获取部署列表失败:', error)
    ElMessage.error('获取部署列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const getStats = async () => {
  try {
    const res = await getDeploymentStats()
    Object.assign(stats, res.data)
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  getList()
}

// 刷新
const handleRefresh = () => {
  getList()
  getStats()
}

// 部署模型
const handleDeploy = () => {
  deployDialog.visible = true

  // 如果从模型管理页面跳转过来，传递参数给部署对话框
  if (route.query.model_version_id) {
    deployDialog.preselectedModelVersion = {
      id: Number(route.query.model_version_id),
      model_name: route.query.model_name as string,
      version_number: route.query.version_number as string
    }
  }
}

// 停止部署
const handleStop = async (row: any) => {
  try {
    await ElMessageBox.confirm('确认停止此部署吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await stopDeployment(row.id)
    ElMessage.success('停止成功')
    getList()
    getStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止部署失败:', error)
      ElMessage.error('停止失败')
    }
  }
}

// 重启部署
const handleRestart = async (row: any) => {
  try {
    await ElMessageBox.confirm('确认重启此部署吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await restartDeployment(row.id)
    ElMessage.success('重启成功')
    getList()
    getStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重启部署失败:', error)
      ElMessage.error('重启失败')
    }
  }
}

// 删除部署
const handleRemove = async (row: any) => {
  try {
    await ElMessageBox.confirm('确认删除此部署吗？此操作不可恢复！', '危险操作', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error'
    })

    await removeDeployment(row.id)
    ElMessage.success('删除成功')
    getList()
    getStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除部署失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 查看详情
const handleViewDetail = (row: any) => {
  detailDialog.deploymentId = row.id
  detailDialog.visible = true
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  getList()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  getList()
}

// 部署成功回调
const handleDeploySuccess = () => {
  getList()
  getStats()
}

// 初始化
onMounted(() => {
  getList()
  getStats()
})
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  .stats-content {
    display: flex;
    align-items: center;
    
    .stats-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      font-size: 24px;
      color: white;
      
      &.running {
        background: linear-gradient(135deg, #67c23a, #85ce61);
      }
      
      &.stopped {
        background: linear-gradient(135deg, #909399, #b1b3b8);
      }
      
      &.failed {
        background: linear-gradient(135deg, #f56c6c, #f78989);
      }
      
      &.healthy {
        background: linear-gradient(135deg, #409eff, #66b1ff);
      }
    }
    
    .stats-info {
      .stats-number {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        line-height: 1;
      }
      
      .stats-label {
        font-size: 14px;
        color: #909399;
        margin-top: 5px;
      }
    }
  }
}

.operation-card {
  margin-bottom: 20px;
  
  .operation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.table-card {
  .deployment-name {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
