<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <el-card class="header-card">
      <div class="header-content">
        <div class="header-left">
          <h2>在线推理演示</h2>
          <p>选择已部署的模型服务进行在线推理测试</p>
        </div>
        <div class="header-right">
          <el-button @click="handleRefresh" :icon="Refresh">
            刷新服务列表
          </el-button>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20">
      <!-- 左侧：服务选择和参数配置 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>配置参数</span>
            </div>
          </template>

          <!-- 服务选择 -->
          <el-form :model="form" label-width="100px">
            <el-form-item label="选择服务">
              <el-select
                v-model="form.serviceId"
                placeholder="请选择推理服务"
                style="width: 100%"
                @change="handleServiceChange"
              >
                <el-option
                  v-for="service in serviceList"
                  :key="service.id"
                  :label="service.service_name"
                  :value="service.id"
                >
                  <div class="service-option">
                    <div class="service-info">
                      <span class="service-name">{{ service.service_name }}</span>
                      <span class="model-name">{{ service.model_name }}</span>
                    </div>
                    <el-tag
                      :type="service.is_healthy ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ service.is_healthy ? '健康' : '异常' }}
                    </el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <!-- 推理参数 -->
            <div v-if="form.serviceId" class="inference-params">
              <h4>推理参数</h4>
              
              <el-form-item label="置信度阈值">
                <el-slider
                  v-model="form.params.conf"
                  :min="0.1"
                  :max="1.0"
                  :step="0.05"
                  show-input
                  :show-input-controls="false"
                />
              </el-form-item>

              <el-form-item label="IoU阈值">
                <el-slider
                  v-model="form.params.iou"
                  :min="0.1"
                  :max="1.0"
                  :step="0.05"
                  show-input
                  :show-input-controls="false"
                />
              </el-form-item>

              <el-form-item label="最大检测数">
                <el-input-number
                  v-model="form.params.max_det"
                  :min="1"
                  :max="1000"
                  style="width: 100%"
                />
              </el-form-item>
            </div>

            <!-- 批量推理选项 -->
            <el-form-item label="批量推理">
              <el-switch
                v-model="form.batchMode"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 推理历史 -->
        <el-card class="history-card">
          <template #header>
            <div class="card-header">
              <span>推理历史</span>
              <el-button size="small" @click="clearHistory">清空</el-button>
            </div>
          </template>

          <div class="history-list">
            <div
              v-for="(item, index) in inferenceHistory"
              :key="index"
              class="history-item"
              @click="handleHistoryClick(item)"
            >
              <div class="history-info">
                <div class="history-time">{{ formatTime(item.timestamp) }}</div>
                <div class="history-result">
                  <el-tag
                    :type="item.success ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ item.success ? '成功' : '失败' }}
                  </el-tag>
                  <span class="response-time">{{ item.response_time }}s</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：图像上传和结果展示 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16">
        <el-card class="inference-card">
          <template #header>
            <div class="card-header">
              <span>{{ form.batchMode ? '批量推理' : '单张推理' }}</span>
              <div class="header-actions">
                <el-button
                  type="primary"
                  :disabled="!canInference"
                  :loading="inferencing"
                  @click="handleInference"
                >
                  {{ form.batchMode ? '批量推理' : '开始推理' }}
                </el-button>
              </div>
            </div>
          </template>

          <!-- 图像上传区域 -->
          <div class="upload-section">
            <el-upload
              ref="uploadRef"
              :multiple="form.batchMode"
              :limit="form.batchMode ? 10 : 1"
              :auto-upload="false"
              :show-file-list="true"
              :accept="'image/*'"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              drag
              class="upload-dragger"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将图片拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持 jpg/png/jpeg 格式，{{ form.batchMode ? '最多10张' : '单张' }}图片
                </div>
              </template>
            </el-upload>
          </div>

          <!-- 推理结果展示 -->
          <div v-if="inferenceResult" class="result-section">
            <el-divider>推理结果</el-divider>
            
            <!-- 单张推理结果 -->
            <div v-if="!form.batchMode && inferenceResult.predictions" class="single-result">
              <div class="result-summary">
                <el-descriptions :column="3" border size="small">
                  <el-descriptions-item label="检测数量">
                    {{ inferenceResult.predictions.summary.total_detections }}
                  </el-descriptions-item>
                  <el-descriptions-item label="检测类别">
                    {{ inferenceResult.predictions.summary.classes_detected.join(', ') || '无' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="推理时间">
                    {{ inferenceResult.inference_time }}s
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 可视化和详情标签页 -->
              <div class="result-tabs">
                <el-tabs v-model="activeTab" type="border-card">
                  <!-- 可视化标签页 -->
                  <el-tab-pane label="检测可视化" name="visualization">
                    <div class="visualization-container">
                      <canvas
                        ref="visualizationCanvas"
                        class="visualization-canvas"
                        @click="handleCanvasClick"
                      ></canvas>
                      <div class="visualization-controls">
                        <el-button-group size="small">
                          <el-button
                            @click="toggleBoundingBoxes"
                            :type="showBoundingBoxes ? 'primary' : ''"
                          >
                            {{ showBoundingBoxes ? '隐藏' : '显示' }}边界框
                          </el-button>
                          <el-button
                            @click="toggleLabels"
                            :type="showLabels ? 'primary' : ''"
                          >
                            {{ showLabels ? '隐藏' : '显示' }}标签
                          </el-button>
                          <el-button
                            @click="toggleConfidence"
                            :type="showConfidence ? 'primary' : ''"
                          >
                            {{ showConfidence ? '隐藏' : '显示' }}置信度
                          </el-button>
                          <el-button @click="downloadVisualization">
                            下载图片
                          </el-button>
                        </el-button-group>
                      </div>
                    </div>
                  </el-tab-pane>

                  <!-- 详细数据标签页 -->
                  <el-tab-pane label="检测详情" name="details">
                    <div v-if="inferenceResult.predictions.detections.length > 0" class="detections-list">
                      <el-table
                        :data="inferenceResult.predictions.detections"
                        size="small"
                        border
                        max-height="400"
                        @row-click="highlightDetection"
                      >
                        <el-table-column type="index" label="#" width="50" />
                        <el-table-column prop="class_name" label="类别" width="100" />
                        <el-table-column prop="confidence" label="置信度" width="120">
                          <template #default="{ row }">
                            <el-progress
                              :percentage="Math.round(row.confidence * 100)"
                              :stroke-width="6"
                              :show-text="true"
                              :format="() => row.confidence.toFixed(3)"
                            />
                          </template>
                        </el-table-column>
                        <el-table-column label="边界框" width="200">
                          <template #default="{ row }">
                            <span class="bbox-info">
                              ({{ Math.round(row.bbox.x1) }}, {{ Math.round(row.bbox.y1) }}) -
                              ({{ Math.round(row.bbox.x2) }}, {{ Math.round(row.bbox.y2) }})
                            </span>
                          </template>
                        </el-table-column>
                        <el-table-column label="尺寸" width="120">
                          <template #default="{ row }">
                            <span class="size-info">
                              {{ Math.round(row.bbox.x2 - row.bbox.x1) }} ×
                              {{ Math.round(row.bbox.y2 - row.bbox.y1) }}
                            </span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                    <div v-else class="no-detections">
                      <el-empty description="未检测到任何目标" />
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>

            <!-- 批量推理结果 -->
            <div v-else-if="form.batchMode && inferenceResult.results" class="batch-result">
              <div class="batch-summary">
                <el-descriptions :column="4" border size="small">
                  <el-descriptions-item label="总图片数">
                    {{ inferenceResult.total_images }}
                  </el-descriptions-item>
                  <el-descriptions-item label="成功数量">
                    {{ inferenceResult.successful_predictions }}
                  </el-descriptions-item>
                  <el-descriptions-item label="失败数量">
                    {{ inferenceResult.total_images - inferenceResult.successful_predictions }}
                  </el-descriptions-item>
                  <el-descriptions-item label="总耗时">
                    {{ inferenceResult.total_time }}s
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 批量结果列表 -->
              <div class="batch-results-list">
                <h4>批量结果</h4>
                <el-table
                  :data="inferenceResult.results"
                  size="small"
                  border
                  max-height="400"
                >
                  <el-table-column prop="filename" label="文件名" width="200" />
                  <el-table-column label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag
                        :type="row.predictions ? 'success' : 'danger'"
                        size="small"
                      >
                        {{ row.predictions ? '成功' : '失败' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="检测数量" width="100">
                    <template #default="{ row }">
                      {{ row.predictions ? row.predictions.summary.total_detections : '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column label="检测类别">
                    <template #default="{ row }">
                      {{ row.predictions ? row.predictions.summary.classes_detected.join(', ') : row.error }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <!-- 错误信息 -->
            <div v-if="inferenceResult.error" class="error-result">
              <el-alert
                :title="inferenceResult.error"
                type="error"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, UploadFilled } from '@element-plus/icons-vue'
import { getServiceList } from '@/api/deploy/services'
import { inferencePredict, inferenceBatchPredict } from '@/api/deploy/services'
import { formatTime } from '@/utils/formatTime'

// 响应式数据
const serviceList = ref([])
const inferencing = ref(false)
const inferenceResult = ref(null)
const inferenceHistory = ref([])
const uploadRef = ref()

// 可视化相关
const visualizationCanvas = ref()
const activeTab = ref('visualization')
const showBoundingBoxes = ref(true)
const showLabels = ref(true)
const showConfidence = ref(true)
const highlightedDetection = ref(-1)
const originalImage = ref(null)
const canvasScale = ref(1)

// 表单数据
const form = reactive({
  serviceId: null as number | null,
  batchMode: false,
  params: {
    conf: 0.25,
    iou: 0.45,
    max_det: 1000
  },
  files: [] as any[]
})

// 计算属性
const canInference = computed(() => {
  return form.serviceId && form.files.length > 0
})

// 获取服务列表
const getServices = async () => {
  try {
    const res = await getServiceList({
      page_size: 100
    })
    serviceList.value = res.data.data
  } catch (error) {
    console.error('获取服务列表失败:', error)
    ElMessage.error('获取服务列表失败')
  }
}

// 刷新服务列表
const handleRefresh = () => {
  getServices()
}

// 服务变化处理
const handleServiceChange = () => {
  inferenceResult.value = null
}

// 文件变化处理
const handleFileChange = (file: any, fileList: any[]) => {
  form.files = fileList
}

// 文件移除处理
const handleFileRemove = (file: any, fileList: any[]) => {
  form.files = fileList
}

// 执行推理
const handleInference = async () => {
  if (!form.serviceId || form.files.length === 0) {
    ElMessage.warning('请选择服务和上传图片')
    return
  }

  inferencing.value = true
  inferenceResult.value = null

  try {
    // 准备FormData
    const formData = new FormData()
    
    if (form.batchMode) {
      // 批量推理
      form.files.forEach(file => {
        formData.append('images', file.raw)
      })
    } else {
      // 单张推理
      formData.append('image', form.files[0].raw)
    }

    // 添加参数
    Object.keys(form.params).forEach(key => {
      formData.append(key, form.params[key])
    })

    // 发送推理请求
    const startTime = Date.now()
    let res
    
    if (form.batchMode) {
      res = await inferenceBatchPredict(form.serviceId, formData)
    } else {
      res = await inferencePredict(form.serviceId, formData)
    }

    const endTime = Date.now()
    const responseTime = ((endTime - startTime) / 1000).toFixed(3)

    inferenceResult.value = res.data

    // 如果是单张推理且有检测结果，加载图片并绘制可视化
    if (!form.batchMode && res.data.predictions && form.files.length > 0) {
      setTimeout(() => {
        loadImageAndVisualize(form.files[0])
      }, 100)
    }

    // 添加到历史记录
    inferenceHistory.value.unshift({
      timestamp: new Date(),
      success: true,
      response_time: responseTime,
      result: res.data
    })

    // 限制历史记录数量
    if (inferenceHistory.value.length > 10) {
      inferenceHistory.value = inferenceHistory.value.slice(0, 10)
    }

    ElMessage.success('推理完成')

  } catch (error) {
    console.error('推理失败:', error)
    
    // 添加失败记录到历史
    inferenceHistory.value.unshift({
      timestamp: new Date(),
      success: false,
      response_time: '0',
      error: error.response?.data?.msg || '推理失败'
    })

    ElMessage.error('推理失败')
  } finally {
    inferencing.value = false
  }
}

// 点击历史记录
const handleHistoryClick = (item: any) => {
  if (item.success && item.result) {
    inferenceResult.value = item.result
  }
}

// 清空历史记录
const clearHistory = () => {
  inferenceHistory.value = []
  ElMessage.success('历史记录已清空')
}

// 可视化相关方法
const drawVisualization = () => {
  if (!visualizationCanvas.value || !inferenceResult.value || !originalImage.value) {
    return
  }

  const canvas = visualizationCanvas.value
  const ctx = canvas.getContext('2d')
  const img = originalImage.value
  const detections = inferenceResult.value.predictions?.detections || []

  // 设置画布尺寸
  const maxWidth = 800
  const maxHeight = 600
  let { width, height } = img

  if (width > maxWidth || height > maxHeight) {
    const ratio = Math.min(maxWidth / width, maxHeight / height)
    width *= ratio
    height *= ratio
  }

  canvas.width = width
  canvas.height = height
  canvasScale.value = width / img.width

  // 清空画布并绘制原图
  ctx.clearRect(0, 0, width, height)
  ctx.drawImage(img, 0, 0, width, height)

  // 绘制检测框
  if (showBoundingBoxes.value && detections.length > 0) {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ]

    detections.forEach((detection: any, index: number) => {
      const { bbox, class_name, confidence } = detection
      const isHighlighted = highlightedDetection.value === index

      // 缩放坐标
      const x1 = bbox.x1 * canvasScale.value
      const y1 = bbox.y1 * canvasScale.value
      const x2 = bbox.x2 * canvasScale.value
      const y2 = bbox.y2 * canvasScale.value
      const boxWidth = x2 - x1
      const boxHeight = y2 - y1

      // 设置颜色
      const color = colors[index % colors.length]

      // 绘制边界框
      ctx.strokeStyle = isHighlighted ? '#FF0000' : color
      ctx.lineWidth = isHighlighted ? 3 : 2
      ctx.strokeRect(x1, y1, boxWidth, boxHeight)

      // 绘制高亮背景
      if (isHighlighted) {
        ctx.fillStyle = 'rgba(255, 0, 0, 0.1)'
        ctx.fillRect(x1, y1, boxWidth, boxHeight)
      }

      // 绘制标签
      if (showLabels.value || showConfidence.value) {
        let label = ''
        if (showLabels.value) label += class_name
        if (showConfidence.value) {
          if (label) label += ' '
          label += `${(confidence * 100).toFixed(1)}%`
        }

        if (label) {
          ctx.font = '14px Arial'
          const textMetrics = ctx.measureText(label)
          const textWidth = textMetrics.width + 8
          const textHeight = 20

          // 绘制标签背景
          ctx.fillStyle = isHighlighted ? '#FF0000' : color
          ctx.fillRect(x1, y1 - textHeight, textWidth, textHeight)

          // 绘制文本
          ctx.fillStyle = '#FFFFFF'
          ctx.fillText(label, x1 + 4, y1 - 6)
        }
      }
    })
  }
}

// 加载图片并绘制可视化
const loadImageAndVisualize = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const img = new Image()
    img.onload = () => {
      originalImage.value = img
      drawVisualization()
    }
    img.src = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)
}

// 切换显示选项
const toggleBoundingBoxes = () => {
  showBoundingBoxes.value = !showBoundingBoxes.value
  drawVisualization()
}

const toggleLabels = () => {
  showLabels.value = !showLabels.value
  drawVisualization()
}

const toggleConfidence = () => {
  showConfidence.value = !showConfidence.value
  drawVisualization()
}

// 高亮检测结果
const highlightDetection = (row: any) => {
  const index = inferenceResult.value?.predictions?.detections?.indexOf(row) || -1
  highlightedDetection.value = highlightedDetection.value === index ? -1 : index
  drawVisualization()
}

// 画布点击事件
const handleCanvasClick = (event: MouseEvent) => {
  if (!inferenceResult.value?.predictions?.detections) return

  const canvas = visualizationCanvas.value
  const rect = canvas.getBoundingClientRect()
  const x = (event.clientX - rect.left) / canvasScale.value
  const y = (event.clientY - rect.top) / canvasScale.value

  // 查找点击的检测框
  const detections = inferenceResult.value.predictions.detections
  for (let i = detections.length - 1; i >= 0; i--) {
    const { bbox } = detections[i]
    if (x >= bbox.x1 && x <= bbox.x2 && y >= bbox.y1 && y <= bbox.y2) {
      highlightedDetection.value = highlightedDetection.value === i ? -1 : i
      drawVisualization()
      break
    }
  }
}

// 下载可视化图片
const downloadVisualization = () => {
  if (!visualizationCanvas.value) return

  const canvas = visualizationCanvas.value
  const link = document.createElement('a')
  link.download = `detection_result_${Date.now()}.png`
  link.href = canvas.toDataURL()
  link.click()
  ElMessage.success('图片已下载')
}

// 初始化
onMounted(() => {
  getServices()
})
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-left {
      h2 {
        margin: 0 0 8px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #909399;
      }
    }
  }
}

.config-card,
.history-card,
.inference-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .service-info {
    .service-name {
      font-weight: bold;
      margin-right: 8px;
    }
    
    .model-name {
      color: #909399;
      font-size: 12px;
    }
  }
}

.inference-params {
  h4 {
    margin: 20px 0 15px 0;
    color: #303133;
    font-size: 14px;
  }
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
  
  .history-item {
    padding: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409eff;
      background-color: #f0f9ff;
    }
    
    .history-info {
      .history-time {
        font-size: 12px;
        color: #909399;
        margin-bottom: 5px;
      }
      
      .history-result {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .response-time {
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }
}

.upload-section {
  margin-bottom: 20px;
  
  .upload-dragger {
    width: 100%;
  }
}

.result-section {
  .result-summary,
  .batch-summary {
    margin-bottom: 20px;
  }
  
  .detections-list,
  .batch-results-list {
    h4 {
      margin: 15px 0 10px 0;
      color: #303133;
    }
  }
  
  .bbox-info {
    font-family: 'Courier New', monospace;
    font-size: 12px;
  }
  
  .error-result {
    margin-top: 20px;
  }
}
</style>
