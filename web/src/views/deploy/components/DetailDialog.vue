<template>
  <el-dialog
    v-model="dialogVisible"
    title="部署详情"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="detail-content">
      <el-tabs v-model="activeTab" type="border-card" @tab-change="handleTabChange">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="部署名称">
              {{ deploymentDetail.deployment_name }}
            </el-descriptions-item>
            <el-descriptions-item label="部署状态">
              <el-tag
                :type="getStatusType(deploymentDetail.status)"
                size="small"
              >
                {{ getStatusText(deploymentDetail.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="模型名称">
              {{ deploymentDetail.model_name }}
            </el-descriptions-item>
            <el-descriptions-item label="模型版本">
              {{ deploymentDetail.version_number }}
            </el-descriptions-item>
            <el-descriptions-item label="Docker镜像">
              {{ deploymentDetail.docker_image }}
            </el-descriptions-item>
            <el-descriptions-item label="服务端口">
              {{ deploymentDetail.service_port }}
            </el-descriptions-item>
            <el-descriptions-item label="服务URL">
              <el-link
                v-if="deploymentDetail.service_url"
                :href="deploymentDetail.service_url"
                target="_blank"
                type="primary"
              >
                {{ deploymentDetail.service_url }}
              </el-link>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="容器ID">
              <el-text v-if="deploymentDetail.container_id" class="container-id">
                {{ deploymentDetail.container_id.substring(0, 12) }}
              </el-text>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="部署人">
              {{ deploymentDetail.deployed_by_info?.username || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="部署时间">
              {{ formatTime(deploymentDetail.deployed_at) }}
            </el-descriptions-item>
          </el-descriptions>

          <!-- 错误信息 -->
          <div v-if="deploymentDetail.error_message" class="error-section">
            <h4>错误信息</h4>
            <el-alert
              :title="deploymentDetail.error_message"
              type="error"
              :closable="false"
              show-icon
            />
          </div>

          <!-- 部署配置 -->
          <div v-if="deploymentDetail.deploy_config" class="config-section">
            <h4>部署配置</h4>
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="内存限制">
                {{ deploymentDetail.deploy_config.memory_limit || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="CPU限制">
                {{ deploymentDetail.deploy_config.cpu_limit || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="自动重启">
                <el-tag :type="deploymentDetail.deploy_config.auto_restart ? 'success' : 'info'" size="small">
                  {{ deploymentDetail.deploy_config.auto_restart ? '启用' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <!-- 环境变量 -->
            <div v-if="deploymentDetail.deploy_config.environment" class="env-vars">
              <h5>环境变量</h5>
              <el-table
                :data="Object.entries(deploymentDetail.deploy_config.environment)"
                size="small"
                border
              >
                <el-table-column prop="0" label="变量名" width="200" />
                <el-table-column prop="1" label="变量值" />
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 服务状态 -->
        <el-tab-pane label="服务状态" name="service">
          <div v-if="serviceInfo" class="service-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="服务名称">
                {{ serviceInfo.service_name }}
              </el-descriptions-item>
              <el-descriptions-item label="健康状态">
                <el-tag :type="serviceInfo.is_healthy ? 'success' : 'danger'" size="small">
                  <el-icon>
                    <CircleCheck v-if="serviceInfo.is_healthy" />
                    <CircleClose v-else />
                  </el-icon>
                  {{ serviceInfo.is_healthy ? '健康' : '异常' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="API端点">
                <el-link :href="serviceInfo.api_endpoint" target="_blank" type="primary">
                  {{ serviceInfo.api_endpoint }}
                </el-link>
              </el-descriptions-item>
              <el-descriptions-item label="最后检查时间">
                {{ formatTime(serviceInfo.last_health_check) }}
              </el-descriptions-item>
              <el-descriptions-item label="总请求数">
                {{ serviceInfo.total_requests }}
              </el-descriptions-item>
              <el-descriptions-item label="成功请求数">
                {{ serviceInfo.successful_requests }}
              </el-descriptions-item>
              <el-descriptions-item label="失败请求数">
                {{ serviceInfo.failed_requests }}
              </el-descriptions-item>
              <el-descriptions-item label="成功率">
                {{ serviceInfo.success_rate }}%
              </el-descriptions-item>
              <el-descriptions-item label="平均响应时间">
                {{ serviceInfo.average_response_time }}s
              </el-descriptions-item>
            </el-descriptions>

            <div class="service-actions">
              <el-button
                type="primary"
                @click="handleHealthCheck"
                :loading="healthChecking"
              >
                健康检查
              </el-button>
              <el-button
                type="success"
                @click="handleTestInference"
              >
                测试推理
              </el-button>
            </div>
          </div>
          <el-empty v-else description="暂无服务信息" />
        </el-tab-pane>

        <!-- 容器日志 -->
        <el-tab-pane label="容器日志" name="logs">
          <div class="logs-section">
            <div class="logs-header">
              <el-button
                type="primary"
                size="small"
                @click="refreshLogs"
                :loading="logsLoading"
              >
                刷新日志
              </el-button>
              <el-select
                v-model="logTail"
                size="small"
                style="width: 120px; margin-left: 10px"
                @change="refreshLogs"
              >
                <el-option label="最后50行" :value="50" />
                <el-option label="最后100行" :value="100" />
                <el-option label="最后200行" :value="200" />
                <el-option label="最后500行" :value="500" />
              </el-select>
            </div>
            <div class="logs-content">
              <pre v-if="logs" class="logs-text">{{ logs }}</pre>
              <el-empty v-else description="暂无日志" />
            </div>
          </div>
        </el-tab-pane>

        <!-- 性能监控 -->
        <el-tab-pane label="性能监控" name="metrics">
          <div class="metrics-section">
            <MetricsChart
              v-if="serviceInfo"
              ref="metricsChartRef"
              :service-id="serviceInfo.id"
            />
            <el-empty v-else description="暂无监控数据" />
          </div>
        </el-tab-pane>

        <!-- API调用指南 -->
        <el-tab-pane label="API调用指南" name="api-guide">
          <ApiGuidePanel :service-info="serviceInfo" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { getDeployment, getDeploymentLogs } from '@/api/deploy/deployments'
import { healthCheckService } from '@/api/deploy/services'
import { formatTime } from '@/utils/formatTime'
import MetricsChart from './MetricsChart.vue'
import ApiGuidePanel from './ApiGuidePanel.vue'

const route = useRoute()
const router = useRouter()

// Props
interface Props {
  visible: boolean
  deploymentId: number | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const activeTab = ref('basic')
const deploymentDetail = ref<any>({})
const serviceInfo = ref<any>(null)
const logs = ref('')
const logsLoading = ref(false)
const logTail = ref(100)
const healthChecking = ref(false)
const metricsChartRef = ref()

// 监听visible变化
watch(() => props.visible, (val: boolean) => {
  dialogVisible.value = val
  if (val && props.deploymentId) {
    getDetail()
  }
})

watch(dialogVisible, (val: boolean) => {
  emit('update:visible', val)
})

// 获取部署详情
const getDetail = async () => {
  if (!props.deploymentId) return

  loading.value = true
  try {
    // 获取部署详情
    const deploymentRes = await getDeployment(props.deploymentId)
    deploymentDetail.value = deploymentRes.data.data

    // 从部署详情中获取服务信息
    if (deploymentDetail.value.service_info) {
      serviceInfo.value = deploymentDetail.value.service_info
    } else if (deploymentDetail.value.status === 'running') {
      // 如果没有service_info但状态是运行中，尝试构造基本服务信息
      serviceInfo.value = {
        id: null,
        service_name: deploymentDetail.value.container_name || 'Unknown Service',
        api_endpoint: deploymentDetail.value.service_url ? `${deploymentDetail.value.service_url}/predict` : null,
        health_check_url: deploymentDetail.value.service_url ? `${deploymentDetail.value.service_url}/health` : null,
        is_healthy: false,
        total_requests: 0,
        successful_requests: 0,
        failed_requests: 0,
        success_rate: 0,
        average_response_time: 0
      }
    } else {
      serviceInfo.value = null
    }
  } catch (error) {
    console.error('获取部署详情失败:', error)
    ElMessage.error('获取部署详情失败')
  } finally {
    loading.value = false
  }
}

// 刷新日志
const refreshLogs = async () => {
  if (!props.deploymentId) return

  logsLoading.value = true
  try {
    const res = await getDeploymentLogs(props.deploymentId, logTail.value)
    logs.value = res.data.logs
  } catch (error) {
    console.error('获取日志失败:', error)
    ElMessage.error('获取日志失败')
  } finally {
    logsLoading.value = false
  }
}

// 健康检查
const handleHealthCheck = async () => {
  if (!serviceInfo.value) return

  healthChecking.value = true
  try {
    await healthCheckService(serviceInfo.value.id)
    ElMessage.success('健康检查完成')
    // 重新获取详情
    getDetail()
  } catch (error) {
    console.error('健康检查失败:', error)
    ElMessage.error('健康检查失败')
  } finally {
    healthChecking.value = false
  }
}

// 测试推理
const handleTestInference = () => {
  // 这里可以打开推理测试对话框
  router.push(`/deploy/deploy/inference`);
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    running: 'success',
    stopped: 'info',
    deploying: 'warning',
    failed: 'danger',
    error: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    running: '运行中',
    stopped: '已停止',
    deploying: '部署中',
    failed: '部署失败',
    error: '运行异常'
  }
  return statusMap[status] || status
}



// 标签页切换处理
const handleTabChange = async (tabName: string) => {
  if (tabName === 'metrics' && metricsChartRef.value) {
    // 切换到性能监控标签页时，等待一小段时间后重新初始化图表
    await nextTick()
    setTimeout(() => {
      metricsChartRef.value?.reinitCharts()
    }, 200)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  activeTab.value = 'basic'
  deploymentDetail.value = {}
  serviceInfo.value = null
  logs.value = ''
}
</script>

<style scoped lang="scss">
.detail-content {
  min-height: 400px;
}

.error-section,
.config-section {
  margin-top: 20px;
  
  h4 {
    margin-bottom: 10px;
    color: #303133;
  }
}

.env-vars {
  margin-top: 15px;
  
  h5 {
    margin-bottom: 10px;
    color: #606266;
  }
}

.service-info {
  .service-actions {
    margin-top: 20px;
    text-align: center;
  }
}

.logs-section {
  .logs-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .logs-content {
    .logs-text {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      max-height: 400px;
      overflow-y: auto;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.container-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.dialog-footer {
  text-align: right;
}
</style>
