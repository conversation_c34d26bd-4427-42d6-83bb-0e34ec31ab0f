<template>
  <el-dialog
    v-model="dialogVisible"
    title="部署模型"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="选择模型版本" prop="model_version_id">
        <el-select
          v-model="form.model_version_id"
          placeholder="请选择要部署的模型版本"
          style="width: 100%"
          filterable
          @change="handleModelVersionChange"
        >
          <el-option
            v-for="version in modelVersions"
            :key="version.id"
            :label="`${version.model_name} - ${version.version_number}`"
            :value="version.id"
          >
            <div class="version-option">
              <div class="version-info">
                <span class="model-name">{{ version.model_name }}</span>
                <span class="version-number">{{ version.version_number }}</span>
              </div>
              <el-tag v-if="version.status === 'online'" type="success" size="small">已上架</el-tag>
              <el-tag v-else-if="version.status === 'test_pass'" type="primary" size="small">测试通过</el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="部署名称" prop="deployment_name">
        <el-input
          v-model="form.deployment_name"
          placeholder="请输入部署名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="服务端口" prop="service_port">
        <el-input-number
          v-model="form.service_port"
          :min="8000"
          :max="9999"
          placeholder="留空自动分配"
          style="width: 100%"
        />
        <div class="form-tip">留空将自动分配可用端口</div>
      </el-form-item>

      <el-form-item label="资源配置">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="内存限制" label-width="80px">
              <el-select v-model="form.deploy_config.memory_limit" style="width: 100%">
                <el-option label="1GB" value="1g" />
                <el-option label="2GB" value="2g" />
                <el-option label="4GB" value="4g" />
                <el-option label="8GB" value="8g" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="CPU限制" label-width="80px">
              <el-select v-model="form.deploy_config.cpu_limit" style="width: 100%">
                <el-option label="0.5核" :value="0.5" />
                <el-option label="1核" :value="1.0" />
                <el-option label="2核" :value="2.0" />
                <el-option label="4核" :value="4.0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="环境变量">
        <div class="env-vars">
          <div
            v-for="(env, index) in form.deploy_config.environment"
            :key="index"
            class="env-var-item"
          >
            <el-input
              v-model="env.key"
              placeholder="变量名"
              style="width: 40%"
            />
            <span class="env-separator">=</span>
            <el-input
              v-model="env.value"
              placeholder="变量值"
              style="width: 40%"
            />
            <el-button
              type="danger"
              size="small"
              @click="removeEnvVar(index)"
              :icon="Delete"
            />
          </div>
          <el-button
            type="primary"
            size="small"
            @click="addEnvVar"
            :icon="Plus"
          >
            添加环境变量
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="自动重启">
        <el-switch
          v-model="form.deploy_config.auto_restart"
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-tip">启用后，服务异常时将自动尝试重启</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          开始部署
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { deployModel } from '@/api/deploy/deployments'
import { getVersionList } from '@/api/model/versions'

// Props
interface Props {
  visible: boolean
  preselectedModelVersion?: {
    id: number
    model_name: string
    version_number: string
  } | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const submitting = ref(false)
const modelVersions = ref([])

// 表单数据
const form = reactive({
  model_version_id: null,
  deployment_name: '',
  service_port: null,
  deploy_config: {
    memory_limit: '2g',
    cpu_limit: 1.0,
    environment: [] as Array<{ key: string; value: string }>,
    auto_restart: false
  }
})

// 表单验证规则
const rules = {
  model_version_id: [
    { required: true, message: '请选择模型版本', trigger: 'change' }
  ],
  deployment_name: [
    { required: true, message: '请输入部署名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  service_port: [
    { type: 'number', min: 8000, max: 9999, message: '端口范围 8000-9999', trigger: 'blur' }
  ]
}

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val

  // 如果有预选的模型版本，自动选中
  if (val && props.preselectedModelVersion) {
    form.model_version_id = props.preselectedModelVersion.id
    handleModelVersionChange(props.preselectedModelVersion.id)
  }
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 获取模型版本列表
const getModelVersions = async () => {
  try {
    const res = await getVersionList({
      status: 'online',
      page_size: 100
    })
    modelVersions.value = res.data.filter((version: any) => version.docker_image)
  } catch (error) {
    console.error('获取模型版本失败:', error)
    ElMessage.error('获取模型版本失败')
  }
}

// 模型版本变化处理
const handleModelVersionChange = (versionId: number) => {
  const version = modelVersions.value.find((v: any) => v.id === versionId)
  if (version) {
    // 自动生成部署名称
    form.deployment_name = `${version.model_name.toLowerCase()}_${version.version_number}_${Date.now()}`
  }
}

// 添加环境变量
const addEnvVar = () => {
  form.deploy_config.environment.push({ key: '', value: '' })
}

// 删除环境变量
const removeEnvVar = (index: number) => {
  form.deploy_config.environment.splice(index, 1)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitting.value = true

    // 处理环境变量
    const envVars: Record<string, string> = {}
    form.deploy_config.environment.forEach(env => {
      if (env.key && env.value) {
        envVars[env.key] = env.value
      }
    })

    const deployData = {
      model_version_id: form.model_version_id,
      deployment_name: form.deployment_name,
      service_port: form.service_port,
      deploy_config: {
        ...form.deploy_config,
        environment: envVars
      }
    }

    await deployModel(deployData)
    
    ElMessage.success('部署任务已提交，请稍候查看部署状态')
    emit('success')
    handleClose()
    
  } catch (error) {
    console.error('部署失败:', error)
    ElMessage.error('部署失败，请检查配置')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  Object.assign(form, {
    model_version_id: null,
    deployment_name: '',
    service_port: null,
    deploy_config: {
      memory_limit: '2g',
      cpu_limit: 1.0,
      environment: [],
      auto_restart: false
    }
  })
}

// 初始化
onMounted(() => {
  getModelVersions()
})
</script>

<style scoped lang="scss">
.version-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .version-info {
    .model-name {
      font-weight: bold;
      margin-right: 8px;
    }
    
    .version-number {
      color: #909399;
    }
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.env-vars {
  .env-var-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    .env-separator {
      margin: 0 10px;
      color: #909399;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
