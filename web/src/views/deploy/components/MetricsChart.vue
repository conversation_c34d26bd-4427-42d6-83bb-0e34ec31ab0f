<template>
  <div class="metrics-chart">
    <div class="chart-header">
      <h4>性能监控</h4>
      <el-select
        v-model="timeRange"
        size="small"
        style="width: 120px"
        @change="refreshData"
      >
        <el-option label="最近1小时" :value="1" />
        <el-option label="最近6小时" :value="6" />
        <el-option label="最近24小时" :value="24" />
        <el-option label="最近7天" :value="168" />
      </el-select>
    </div>

    <div v-loading="loading" class="charts-container">
      <el-row :gutter="20">
        <!-- CPU使用率 -->
        <el-col :span="12">
          <div class="chart-item">
            <h5>CPU使用率 (%)</h5>
            <div ref="cpuChartRef" class="chart"></div>
          </div>
        </el-col>

        <!-- 内存使用率 -->
        <el-col :span="12">
          <div class="chart-item">
            <h5>内存使用率 (%)</h5>
            <div ref="memoryChartRef" class="chart"></div>
          </div>
        </el-col>

        <!-- 请求量 -->
        <el-col :span="12">
          <div class="chart-item">
            <h5>每分钟请求数</h5>
            <div ref="requestsChartRef" class="chart"></div>
          </div>
        </el-col>

        <!-- 响应时间 -->
        <el-col :span="12">
          <div class="chart-item">
            <h5>平均响应时间 (秒)</h5>
            <div ref="responseTimeChartRef" class="chart"></div>
          </div>
        </el-col>

        <!-- 错误率 -->
        <el-col :span="24">
          <div class="chart-item">
            <h5>错误率 (%)</h5>
            <div ref="errorRateChartRef" class="chart"></div>
          </div>
        </el-col>
      </el-row>
    </div>

    <el-empty v-if="!loading && !hasData" description="暂无监控数据" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { getChartData } from '@/api/deploy/metrics'

// Props
interface Props {
  serviceId: number
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const timeRange = ref(24)
const hasData = ref(false)

// 图表引用
const cpuChartRef = ref<HTMLElement>()
const memoryChartRef = ref<HTMLElement>()
const requestsChartRef = ref<HTMLElement>()
const responseTimeChartRef = ref<HTMLElement>()
const errorRateChartRef = ref<HTMLElement>()

// 图表实例
let cpuChart: echarts.ECharts | null = null
let memoryChart: echarts.ECharts | null = null
let requestsChart: echarts.ECharts | null = null
let responseTimeChart: echarts.ECharts | null = null
let errorRateChart: echarts.ECharts | null = null

// 获取图表数据
const getMetricsData = async () => {
  loading.value = true
  try {
    const res = await getChartData({
      service_id: props.serviceId,
      hours: timeRange.value
    })
    
    const data = res.data
    hasData.value = data.timestamps && data.timestamps.length > 0
    console.log(hasData.value)
    
    if (hasData.value) {
      updateCharts(data)
    }
  } catch (error) {
    console.error('获取监控数据失败:', error)
    ElMessage.error('获取监控数据失败')
  } finally {
    loading.value = false
  }
}

// 更新图表
const updateCharts = (data: any) => {
  const timestamps = data.timestamps.map((time: string) => 
    new Date(time).toLocaleTimeString()
  )

  // 更新CPU图表
  if (cpuChart) {
    cpuChart.setOption({
      xAxis: { data: timestamps },
      series: [{
        data: data.cpu_usage,
        type: 'line',
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#409EFF' }
      }]
    })
  }

  // 更新内存图表
  if (memoryChart) {
    memoryChart.setOption({
      xAxis: { data: timestamps },
      series: [{
        data: data.memory_usage,
        type: 'line',
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#67C23A' }
      }]
    })
  }

  // 更新请求量图表
  if (requestsChart) {
    requestsChart.setOption({
      xAxis: { data: timestamps },
      series: [{
        data: data.requests_per_minute,
        type: 'bar',
        itemStyle: { color: '#E6A23C' }
      }]
    })
  }

  // 更新响应时间图表
  if (responseTimeChart) {
    responseTimeChart.setOption({
      xAxis: { data: timestamps },
      series: [{
        data: data.average_response_time,
        type: 'line',
        smooth: true,
        itemStyle: { color: '#909399' }
      }]
    })
  }

  // 更新错误率图表
  if (errorRateChart) {
    errorRateChart.setOption({
      xAxis: { data: timestamps },
      series: [{
        data: data.error_rate,
        type: 'line',
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#F56C6C' }
      }]
    })
  }
}

// 检查DOM元素是否可见且有尺寸
const checkElementSize = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect()
  return rect.width > 0 && rect.height > 0
}

// 等待元素可见
const waitForElementVisible = (element: HTMLElement, maxWait = 3000): Promise<boolean> => {
  return new Promise((resolve) => {
    const startTime = Date.now()

    const check = () => {
      if (checkElementSize(element)) {
        resolve(true)
        return
      }

      if (Date.now() - startTime > maxWait) {
        resolve(false)
        return
      }

      requestAnimationFrame(check)
    }

    check()
  })
}

// 初始化图表
const initCharts = async () => {
  await nextTick()

  // 等待一小段时间确保DOM完全渲染
  await new Promise(resolve => setTimeout(resolve, 100))

  // 通用图表配置
  const commonOption = {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: { fontSize: 10 }
    },
    yAxis: {
      type: 'value',
      axisLabel: { fontSize: 10 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    }
  }

  // 初始化CPU图表
  if (cpuChartRef.value) {
    const isVisible = await waitForElementVisible(cpuChartRef.value)
    if (isVisible) {
      cpuChart = echarts.init(cpuChartRef.value)
      cpuChart.setOption({
        ...commonOption,
        yAxis: { ...commonOption.yAxis, max: 100 }
      })
    }
  }

  // 初始化内存图表
  if (memoryChartRef.value) {
    const isVisible = await waitForElementVisible(memoryChartRef.value)
    if (isVisible) {
      memoryChart = echarts.init(memoryChartRef.value)
      memoryChart.setOption({
        ...commonOption,
        yAxis: { ...commonOption.yAxis, max: 100 }
      })
    }
  }

  // 初始化请求量图表
  if (requestsChartRef.value) {
    const isVisible = await waitForElementVisible(requestsChartRef.value)
    if (isVisible) {
      requestsChart = echarts.init(requestsChartRef.value)
      requestsChart.setOption({
        ...commonOption,
        xAxis: { ...commonOption.xAxis, boundaryGap: true }
      })
    }
  }

  // 初始化响应时间图表
  if (responseTimeChartRef.value) {
    const isVisible = await waitForElementVisible(responseTimeChartRef.value)
    if (isVisible) {
      responseTimeChart = echarts.init(responseTimeChartRef.value)
      responseTimeChart.setOption(commonOption)
    }
  }

  // 初始化错误率图表
  if (errorRateChartRef.value) {
    const isVisible = await waitForElementVisible(errorRateChartRef.value)
    if (isVisible) {
      errorRateChart = echarts.init(errorRateChartRef.value)
      errorRateChart.setOption({
        ...commonOption,
        yAxis: { ...commonOption.yAxis, max: 100 }
      })
    }
  }
}

// 刷新数据
const refreshData = () => {
  getMetricsData()
}

// 重新初始化图表（用于对话框打开后）
const reinitCharts = async () => {
  // 销毁现有图表
  cpuChart?.dispose()
  memoryChart?.dispose()
  requestsChart?.dispose()
  responseTimeChart?.dispose()
  errorRateChart?.dispose()

  // 重新初始化
  await initCharts()

  // 如果有数据，重新获取
  if (props.serviceId) {
    getMetricsData()
  }
}

// 暴露方法给父组件
defineExpose({
  refreshData,
  reinitCharts
})

// 窗口大小变化处理
const handleResize = () => {
  cpuChart?.resize()
  memoryChart?.resize()
  requestsChart?.resize()
  responseTimeChart?.resize()
  errorRateChart?.resize()
}

// 监听serviceId变化
watch(() => props.serviceId, async (newServiceId) => {
  if (newServiceId) {
    // 如果图表还没初始化，先初始化
    if (!cpuChart) {
      await initCharts()
    }
    getMetricsData()
  }
}, { immediate: false })

// 生命周期
onMounted(async () => {
  await initCharts()
  if (props.serviceId) {
    getMetricsData()
  }
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  cpuChart?.dispose()
  memoryChart?.dispose()
  requestsChart?.dispose()
  responseTimeChart?.dispose()
  errorRateChart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.metrics-chart {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h4 {
      margin: 0;
      color: #303133;
    }
  }
  
  .charts-container {
    min-height: 600px;
  }
  
  .chart-item {
    margin-bottom: 20px;
    
    h5 {
      margin: 0 0 10px 0;
      color: #606266;
      font-size: 14px;
    }
    
    .chart {
      height: 200px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
    }
  }
}
</style>
