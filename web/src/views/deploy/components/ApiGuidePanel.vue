<template>
  <div class="api-guide-panel">
    <div v-if="serviceInfo" class="api-content">
      <!-- 服务信息概览 -->
      <div class="service-overview">
        <h3>服务信息</h3>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="服务ID">
            {{ serviceInfo.id || 'N/A' }}
          </el-descriptions-item>
          <el-descriptions-item label="服务名称">
            {{ serviceInfo.service_name }}
          </el-descriptions-item>
          <el-descriptions-item label="直接访问URL">
            <el-link 
              v-if="serviceInfo.api_endpoint" 
              :href="serviceInfo.api_endpoint" 
              target="_blank" 
              type="primary"
            >
              {{ serviceInfo.api_endpoint }}
            </el-link>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="健康检查URL">
            <el-link 
              v-if="serviceInfo.health_check_url" 
              :href="serviceInfo.health_check_url" 
              target="_blank" 
              type="primary"
            >
              {{ serviceInfo.health_check_url }}
            </el-link>
            <span v-else>-</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- API调用方法 -->
      <el-tabs v-model="activeTab" type="border-card" class="api-tabs">
        <!-- 单张图像推理 -->
        <el-tab-pane label="单张图像推理" name="single">
          <div class="api-method">
            <h4>1. 通过系统接口调用（推荐）</h4>
            <div class="method-section">
              <p><strong>接口地址：</strong></p>
              <div class="code-block">
                <code>POST {{ getSystemSinglePredictUrl() }}</code>
                <el-button size="small" @click="copyToClipboard(getSystemSinglePredictUrl())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
              
              <p><strong>cURL示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getSystemSingleCurlExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getSystemSingleCurlExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>

              <p><strong>Python示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getSystemSinglePythonExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getSystemSinglePythonExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
            </div>

            <h4>2. 直接调用服务接口</h4>
            <div class="method-section">
              <p><strong>接口地址：</strong></p>
              <div class="code-block">
                <code>POST {{ getDirectSinglePredictUrl() }}</code>
                <el-button size="small" @click="copyToClipboard(getDirectSinglePredictUrl())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
              
              <p><strong>cURL示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getDirectSingleCurlExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getDirectSingleCurlExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>

              <p><strong>Python示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getDirectSinglePythonExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getDirectSinglePythonExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 批量图像推理 -->
        <el-tab-pane label="批量图像推理" name="batch">
          <div class="api-method">
            <h4>1. 通过系统接口调用（推荐）</h4>
            <div class="method-section">
              <p><strong>接口地址：</strong></p>
              <div class="code-block">
                <code>POST {{ getSystemBatchPredictUrl() }}</code>
                <el-button size="small" @click="copyToClipboard(getSystemBatchPredictUrl())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
              
              <p><strong>cURL示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getSystemBatchCurlExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getSystemBatchCurlExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>

              <p><strong>Python示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getSystemBatchPythonExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getSystemBatchPythonExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
            </div>

            <h4>2. 直接调用服务接口</h4>
            <div class="method-section">
              <p><strong>接口地址：</strong></p>
              <div class="code-block">
                <code>POST {{ getDirectBatchPredictUrl() }}</code>
                <el-button size="small" @click="copyToClipboard(getDirectBatchPredictUrl())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
              
              <p><strong>cURL示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getDirectBatchCurlExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getDirectBatchCurlExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>

              <p><strong>Python示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getDirectBatchPythonExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getDirectBatchPythonExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 健康检查 -->
        <el-tab-pane label="健康检查" name="health">
          <div class="api-method">
            <h4>1. 通过系统接口调用（推荐）</h4>
            <div class="method-section">
              <p><strong>接口地址：</strong></p>
              <div class="code-block">
                <code>GET {{ getSystemHealthCheckUrl() }}</code>
                <el-button size="small" @click="copyToClipboard(getSystemHealthCheckUrl())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
              
              <p><strong>cURL示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getSystemHealthCurlExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getSystemHealthCurlExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>

              <p><strong>Python示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getSystemHealthPythonExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getSystemHealthPythonExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
            </div>

            <h4>2. 直接调用服务接口</h4>
            <div class="method-section">
              <p><strong>接口地址：</strong></p>
              <div class="code-block">
                <code>GET {{ getDirectHealthCheckUrl() }}</code>
                <el-button size="small" @click="copyToClipboard(getDirectHealthCheckUrl())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
              
              <p><strong>cURL示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getDirectHealthCurlExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getDirectHealthCurlExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>

              <p><strong>Python示例：</strong></p>
              <div class="code-block">
                <pre><code>{{ getDirectHealthPythonExample() }}</code></pre>
                <el-button size="small" @click="copyToClipboard(getDirectHealthPythonExample())">
                  <el-icon><CopyDocument /></el-icon>复制
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 响应格式 -->
        <el-tab-pane label="响应格式" name="response">
          <div class="api-method">
            <h4>成功响应格式</h4>
            <div class="code-block">
              <pre><code>{{ getSuccessResponseExample() }}</code></pre>
            </div>

            <h4>错误响应格式</h4>
            <div class="code-block">
              <pre><code>{{ getErrorResponseExample() }}</code></pre>
            </div>

            <h4>响应字段说明</h4>
            <el-table :data="responseFields" style="width: 100%" size="small">
              <el-table-column prop="field" label="字段名" width="150" />
              <el-table-column prop="type" label="类型" width="100" />
              <el-table-column prop="description" label="说明" />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-empty v-else description="暂无服务信息" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { CopyDocument } from '@element-plus/icons-vue'
import { DEPLOY_CONFIG, generateApiExamples } from '@/config/deploy'

// Props
interface Props {
  serviceInfo: any
}

const props = defineProps<Props>()

// 响应式数据
const activeTab = ref('single')

// 响应字段说明
const responseFields = [
  { field: 'code', type: 'int', description: '响应状态码，200表示成功' },
  { field: 'msg', type: 'string', description: '响应消息' },
  { field: 'data', type: 'object', description: '推理结果数据' },
  { field: 'data.detections', type: 'array', description: '检测结果列表' },
  { field: 'data.image_info', type: 'object', description: '图像信息' },
  { field: 'data.inference_time', type: 'float', description: '推理耗时(秒)' },
  { field: 'request_id', type: 'string', description: '请求唯一标识' },
  { field: 'response_time', type: 'float', description: '总响应时间(秒)' }
]

// 生成API示例URLs（响应式）
const apiUrls = computed(() => generateApiExamples(props.serviceInfo))

// API URL生成方法
const getSystemSinglePredictUrl = () => {
  return apiUrls.value.system.predict
}

const getSystemBatchPredictUrl = () => {
  return apiUrls.value.system.batchPredict
}

const getSystemHealthCheckUrl = () => {
  return apiUrls.value.system.health
}

const getDirectSinglePredictUrl = () => {
  return apiUrls.value.direct.predict
}

const getDirectBatchPredictUrl = () => {
  return apiUrls.value.direct.batchPredict
}

const getDirectHealthCheckUrl = () => {
  return apiUrls.value.direct.health
}

// 代码示例生成方法
const getSystemSingleCurlExample = () => {
  return `curl -X POST "${getSystemSinglePredictUrl()}" \\
  -H "Content-Type: multipart/form-data" \\
  -F "image=@/path/to/your/image.jpg" \\
  -F "confidence=0.5" \\
  -F "iou_threshold=0.45"`
}

const getSystemSinglePythonExample = () => {
  return `import requests

url = "${getSystemSinglePredictUrl()}"
files = {'image': open('/path/to/your/image.jpg', 'rb')}
data = {
    'confidence': 0.5,
    'iou_threshold': 0.45
}

response = requests.post(url, files=files, data=data)
result = response.json()
print(result)`
}

const getDirectSingleCurlExample = () => {
  return `curl -X POST "${getDirectSinglePredictUrl()}" \\
  -H "Content-Type: multipart/form-data" \\
  -F "image=@/path/to/your/image.jpg" \\
  -F "confidence=0.5" \\
  -F "iou_threshold=0.45"`
}

const getDirectSinglePythonExample = () => {
  return `import requests

url = "${getDirectSinglePredictUrl()}"
files = {'image': open('/path/to/your/image.jpg', 'rb')}
data = {
    'confidence': 0.5,
    'iou_threshold': 0.45
}

response = requests.post(url, files=files, data=data)
result = response.json()
print(result)`
}

const getSystemBatchCurlExample = () => {
  return `curl -X POST "${getSystemBatchPredictUrl()}" \\
  -H "Content-Type: multipart/form-data" \\
  -F "images=@/path/to/image1.jpg" \\
  -F "images=@/path/to/image2.jpg" \\
  -F "confidence=0.5"`
}

const getSystemBatchPythonExample = () => {
  return `import requests

url = "${getSystemBatchPredictUrl()}"
files = [
    ('images', open('/path/to/image1.jpg', 'rb')),
    ('images', open('/path/to/image2.jpg', 'rb'))
]
data = {'confidence': 0.5}

response = requests.post(url, files=files, data=data)
result = response.json()
print(result)`
}

const getDirectBatchCurlExample = () => {
  return `curl -X POST "${getDirectBatchPredictUrl()}" \\
  -H "Content-Type: multipart/form-data" \\
  -F "images=@/path/to/image1.jpg" \\
  -F "images=@/path/to/image2.jpg" \\
  -F "confidence=0.5"`
}

const getDirectBatchPythonExample = () => {
  return `import requests

url = "${getDirectBatchPredictUrl()}"
files = [
    ('images', open('/path/to/image1.jpg', 'rb')),
    ('images', open('/path/to/image2.jpg', 'rb'))
]
data = {'confidence': 0.5}

response = requests.post(url, files=files, data=data)
result = response.json()
print(result)`
}

const getSystemHealthCurlExample = () => {
  return `curl -X GET "${getSystemHealthCheckUrl()}"`
}

const getSystemHealthPythonExample = () => {
  return `import requests

url = "${getSystemHealthCheckUrl()}"
response = requests.get(url)
result = response.json()
print(result)`
}

const getDirectHealthCurlExample = () => {
  return `curl -X GET "${getDirectHealthCheckUrl()}"`
}

const getDirectHealthPythonExample = () => {
  return `import requests

url = "${getDirectHealthCheckUrl()}"
response = requests.get(url)
result = response.json()
print(result)`
}

const getSuccessResponseExample = () => {
  return `{
  "code": 200,
  "msg": "推理成功",
  "data": {
    "detections": [
      {
        "class_id": 0,
        "class_name": "person",
        "confidence": 0.85,
        "bbox": [100, 50, 200, 300]
      }
    ],
    "image_info": {
      "width": 640,
      "height": 480,
      "channels": 3
    },
    "inference_time": 0.123
  },
  "request_id": "uuid-string",
  "response_time": 0.156
}`
}

const getErrorResponseExample = () => {
  return `{
  "code": 400,
  "msg": "缺少图像文件",
  "data": null,
  "request_id": "uuid-string",
  "response_time": 0.001
}`
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败')
  }
}
</script>

<style scoped>
.api-guide-panel {
  .service-overview {
    margin-bottom: 20px;

    h3 {
      margin-bottom: 15px;
      color: #303133;
      font-size: 16px;
      border-left: 3px solid #409eff;
      padding-left: 12px;
    }
  }

  .api-tabs {
    margin-top: 20px;
  }

  .api-method {
    padding: 20px;

    h4 {
      margin: 20px 0 15px 0;
      color: #303133;
      font-size: 14px;
      border-left: 3px solid #67c23a;
      padding-left: 12px;
    }

    h4:first-child {
      margin-top: 0;
    }

    .method-section {
      margin-bottom: 30px;

      p {
        margin: 10px 0 8px 0;
        color: #606266;
        font-weight: 500;
      }
    }

    .code-block {
      position: relative;
      margin: 8px 0 16px 0;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      overflow: hidden;

      code {
        display: block;
        padding: 12px 16px;
        background-color: #f8f9fa;
        color: #495057;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
        overflow-x: auto;
      }

      pre {
        margin: 0;
        padding: 16px;
        background-color: #f8f9fa;
        color: #495057;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
        overflow-x: auto;
        white-space: pre-wrap;
        word-break: break-all;
      }

      .el-button {
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 1;
      }
    }

    .el-table {
      margin-top: 16px;
    }
  }
}

@media (max-width: 768px) {
  .api-guide-panel {
    .api-method {
      padding: 12px;

      .code-block {
        pre, code {
          padding: 12px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
