<template>
  <div class="llm-chat-container">
    <el-card class="chat-window" shadow="hover">
      <template #header>
        <div class="chat-header-content">
          <div class="chat-header-top-row">
            <span class="chat-title">大语言模型对话</span>
            <div class="action-buttons">
              <el-tooltip content="选择语言模型" placement="top">
                <el-button :icon="MagicStick" @click="showModelSelectionDialog = true">选择模型</el-button>
              </el-tooltip>
              <el-tooltip content="选择知识库数据集" placement="top">
                <el-button :icon="Files" @click="showDatasetSelectionDialog = true">选择数据集</el-button>
              </el-tooltip>
              <el-tooltip :content="isSemanticSearchEnabled ? '关闭模型语义检索' : '开启模型语义检索'" placement="top">
                <el-button 
                  :type="isSemanticSearchEnabled ? 'primary' : ''"
                  :icon="Search" 
                  circle 
                  @click="toggleSemanticSearch" 
                />
              </el-tooltip>
            </div>
          </div>
          <div class="selected-items-display" v-if="currentModels.length > 0 || currentDatasets.length > 0">
            <div v-if="currentModels.length > 0" class="selected-item-group">
              <span class="selected-item-label">当前模型:</span>
              <el-tag
                v-for="model in currentModels"
                :key="model.id"
                closable
                @close="removeSelectedModel(model.id)"
                type="success"
                class="model-tag"
              >
                {{ model.group }} / {{ model.name }}
              </el-tag>
            </div>
            <div v-if="currentDatasets.length > 0" class="selected-item-group">
              <span class="selected-item-label">当前数据集:</span>
              <el-tag
                v-for="dataset in currentDatasets"
                :key="dataset.id"
                closable
                @close="removeSelectedDataset(dataset.id)"
                type="info"
                class="dataset-tag"
              >
                {{ dataset.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </template>

      <div class="chat-messages" ref="messageContainerRef">
        <div v-for="(message, index) in messages" :key="index" class="message-item" :class="{'user-message': message.isUser, 'ai-message': !message.isUser}">
          <el-avatar :size="30" :icon="message.isUser ? 'UserFilled' : 'ChatDotRound'" class="message-avatar"></el-avatar>
          <div class="message-content">
            <div v-if="message.isSemanticResult && message.semanticItems && message.semanticItems.length > 0" class="semantic-results-section">
              <p>以下是为您检索到的相关模型，请选择要加入对话的模型：</p>
              <div class="semantic-items-container">
                <el-card
                  v-for="(item, itemIndex) in message.semanticItems"
                  :key="itemIndex"
                  class="semantic-item-card"
                  shadow="hover"
                >
                  <template #header>
                    <div class="semantic-item-card-header">
                      <strong class="semantic-item-title">{{ item.title }}</strong>
                      <el-checkbox
                        :model-value="message.selectedSemanticItems && message.selectedSemanticItems.includes(item.id)"
                        @change="() => toggleSemanticItemSelection(message, item)" 
                        size="large"
                      >
                         选择
                      </el-checkbox>
                    </div>
                  </template>
                  <div class="semantic-item-card-content">
                    <el-rate v-if="item.score" :model-value="item.score" disabled size="small" style="margin-bottom: 8px;" />
                    <p class="semantic-item-snippet">{{ item.snippet }}</p>
                  </div>
                  <div class="semantic-item-card-footer" v-if="item.modelData && item.modelData.id">
                    <el-button type="primary" link size="small" @click="goToModelDetails(item.modelData.id)">
                      查看详情
                    </el-button>
                  </div>
                </el-card>
              </div>
            </div>
            <div v-else>
              <p>{{ message.text }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="chat-input-area">
        <el-input
          v-model="userInput"
          type="textarea"
          :rows="3"
          :placeholder="isSemanticSearchEnabled ? '输入模型需求进行语义检索...' : '请输入您的消息...'"
          @keyup.enter.prevent="handleSendMessage"
        />
        <el-button type="primary" @click="handleSendMessage" :disabled="!userInput.trim() || isSendingMessage" class="send-button">
          {{ isSendingMessage ? '发送中...' : '发送' }}
        </el-button>
      </div>
    </el-card>

    <!-- 模型选择弹窗 -->
    <el-dialog v-model="showModelSelectionDialog" title="选择模型" width="500px">
      <!-- 模型列表和搜索 -->
      <el-input v-model="modelSearchKeyword" placeholder="搜索模型..." class="mb15"></el-input>
      <el-checkbox-group v-model="selectedModelIds" class="mb15 full-width">
        <el-checkbox v-for="model in filteredModels" :key="model.id" :label="model.id" border class="block-checkbox">
          {{ model.group }} / {{ model.name }}
        </el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <el-button @click="showModelSelectionDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmModelSelection">确定</el-button>
      </template>
    </el-dialog>

    <!-- 数据集选择弹窗 -->
    <el-dialog v-model="showDatasetSelectionDialog" title="选择数据集" width="500px">
      <!-- 数据集列表和搜索 -->
      <el-input v-model="datasetSearchKeyword" placeholder="搜索数据集..." class="mb15"></el-input>
      <el-checkbox-group v-model="selectedDatasetIds" class="mb15 full-width">
        <el-checkbox v-for="dataset in filteredDatasets" :key="dataset.id" :label="dataset.id" border class="block-checkbox">
           {{ dataset.group }} / {{ dataset.name }}
        </el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <el-button @click="showDatasetSelectionDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmDatasetSelection">确定</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script lang="ts" setup name="llmChat">
import { ref, reactive, onMounted, nextTick, computed, watch } from 'vue';
import { ElMessage, ElScrollbar, ElRate } from 'element-plus'; // ElScrollbar might not be needed if chat-messages handles overflow
import { UserFilled, ChatDotRound, MagicStick, Files, Search } from '@element-plus/icons-vue'; // Import icons
import { getModelList, semanticSearchModels } from '@/api/model/models'; // Added semanticSearchModels
import { getDatasetList } from '@/api/dataset/datasets';
import { useRouter } from 'vue-router'; // Import useRouter

const router = useRouter(); // Initialize router

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isSemanticResult?: boolean;
  semanticItems?: SemanticItem[];
  selectedSemanticItems?: string[]; // Store IDs of selected semantic items
}

interface SemanticItem {
  id: string; // Unique ID for the semantic item
  title: string;
  snippet: string;
  fullText?: string; // Full text to be used when appended to history
  modelData?: Model; // Store the actual model object if item is a model
  score?: number; // For semantic search result score (0-5)
}

interface Model {
  id: string;
  name: string;
  description: string;
  details: any; // Placeholder for full model details
  group?: string; // Changed from creatorName to group
  score?: number; // For semantic search result score
  // Ensure your Model interface matches the actual API response structure
}

interface Dataset {
  id: string;
  name: string;
  description: string;
  details: any; // Placeholder for full dataset details
  group?: string; // Changed from creatorName to group
  // Ensure your Dataset interface matches the actual API response structure
}

const userInput = ref('');
const messages = ref<Message[]>([]);
const messageContainerRef = ref<HTMLElement | null>(null);

const showModelSelectionDialog = ref(false);
const modelSearchKeyword = ref('');
const availableModels = ref<Model[]>([]); // Initialized as empty, will be loaded from API
const selectedModelIds = ref<string[]>([]); 
const currentModels = ref<Model[]>([]);

const showDatasetSelectionDialog = ref(false);
const datasetSearchKeyword = ref('');
const availableDatasets = ref<Dataset[]>([]); // Initialized as empty, will be loaded from API
const selectedDatasetIds = ref<string[]>([]);
const currentDatasets = ref<Dataset[]>([]);

const isSemanticSearchEnabled = ref(false);
const isSendingMessage = ref(false); // For loading state of send button

const toggleSemanticItemSelection = (message: Message, item: SemanticItem) => {
  if (!message.selectedSemanticItems) {
    message.selectedSemanticItems = [];
  }
  if (!item || !item.modelData || !item.modelData.id) return; // Ensure item and modelData are valid

  const modelToToggle = item.modelData;
  const itemId = item.id;

  const indexInMessageSelection = message.selectedSemanticItems.indexOf(itemId);

  if (indexInMessageSelection > -1) {
    // Item was selected, now unselecting from this message
    message.selectedSemanticItems.splice(indexInMessageSelection, 1);
    
    // Also remove from global currentModels and selectedModelIds
    const globalModelIdIndex = selectedModelIds.value.indexOf(modelToToggle.id);
    if (globalModelIdIndex > -1) {
      selectedModelIds.value.splice(globalModelIdIndex, 1);
    }
    currentModels.value = currentModels.value.filter(m => m.id !== modelToToggle.id);
    ElMessage.info(`已从对话上下文中移除模型: ${modelToToggle.name}`);

  } else {
    // Item was not selected, now selecting in this message
    message.selectedSemanticItems.push(itemId);

    // Add to global currentModels and selectedModelIds if not already there
    if (!selectedModelIds.value.includes(modelToToggle.id)) {
      selectedModelIds.value.push(modelToToggle.id);
      // Ensure we add the full model object if it's not already in currentModels
      // modelToToggle should be the full model object from semantic search result
      if (!currentModels.value.some(m => m.id === modelToToggle.id)) {
         currentModels.value.push(modelToToggle); 
      }
      ElMessage.success(`已添加模型到对话上下文: ${modelToToggle.name}`);
    } else {
      // If already in selectedModelIds, ensure it's in currentModels (consistency check)
      if (!currentModels.value.some(m => m.id === modelToToggle.id)) {
        const existingModelData = availableModels.value.find(m => m.id === modelToToggle.id) || modelToToggle;
        currentModels.value.push(existingModelData);
      }
       ElMessage.info(`模型 ${modelToToggle.name} 已在当前上下文中。`);
    }
  }
};

// Method to load models from API
const loadAvailableModels = async () => {
  try {
    // Assuming getModelList can be called without params to get all, or adjust as needed
    const res = await getModelList({ page: 1, pageSize: 1000 }); // Fetch a large number, or implement pagination if needed
    availableModels.value = res.data.data.map((model: any) => ({
      id: model.id.toString(), // Ensure ID is a string if your interface expects it
      name: model.name,
      description: model.description,
      group: model.group || 'N/A', // Changed from creator_name to group
      details: model, // Or map specific details: { type: model.type, version: model.version }
      score: model.similarity_score ? parseFloat((model.similarity_score * 5).toFixed(1)) : undefined
    }));
  } catch (error) {
    ElMessage.error('加载模型列表失败');
    console.error('Error loading models:', error);
  }
};

// Method to load datasets from API
const loadAvailableDatasets = async () => {
  try {
    // Assuming getDatasetList can be called without params to get all, or adjust as needed
    const res = await getDatasetList({ page: 1, pageSize: 1000 }); // Fetch a large number, or implement pagination if needed
    availableDatasets.value = res.data.data.map((dataset: any) => ({
      id: dataset.id.toString(), // Ensure ID is a string
      name: dataset.name,
      description: dataset.description,
      group: dataset.group || 'N/A', // Changed from creator_name to group
      details: dataset // Or map specific details: { domain: dataset.domain, size: dataset.size }
    }));
  } catch (error) {
    ElMessage.error('加载数据集列表失败');
    console.error('Error loading datasets:', error);
  }
};

const filteredModels = computed(() => {
  if (!modelSearchKeyword.value) {
    return availableModels.value;
  }
  const keyword = modelSearchKeyword.value.toLowerCase();
  return availableModels.value.filter(model => 
    model.name.toLowerCase().includes(keyword) ||
    (model.description && model.description.toLowerCase().includes(keyword))
  );
});

const filteredDatasets = computed(() => {
  if (!datasetSearchKeyword.value) {
    return availableDatasets.value;
  }
  const keyword = datasetSearchKeyword.value.toLowerCase();
  return availableDatasets.value.filter(dataset => 
    dataset.name.toLowerCase().includes(keyword) ||
    (dataset.description && dataset.description.toLowerCase().includes(keyword))
  );
});

const scrollToBottom = () => {
  nextTick(() => {
    if (messageContainerRef.value) {
      messageContainerRef.value.scrollTop = messageContainerRef.value.scrollHeight;
    }
  });
};

const addMessage = (text: string, isUser: boolean, isSemanticResult: boolean = false, semanticItems: SemanticItem[] = []) => {
  const newMessage: Message = {
    id: Date.now().toString(),
    text,
    isUser,
    timestamp: new Date(),
    isSemanticResult,
    semanticItems: isSemanticResult ? semanticItems : undefined,
    selectedSemanticItems: isSemanticResult ? [] : undefined,
  };
  messages.value.push(newMessage);
  scrollToBottom();
};

const handleSendMessage = async () => {
  const text = userInput.value.trim();
  if (!text) return;

  isSendingMessage.value = true;

  if (isSemanticSearchEnabled.value) {
    addMessage(text, true); // Add user's query
    userInput.value = '';
    try {
      const res = await semanticSearchModels({
        description: text,
      });
      if (res.data && Array.isArray(res.data) && res.data.length > 0) {
        const semanticModelItems: SemanticItem[] = res.data.map((item: any) => ({
          id: item.id.toString(),
          title: `${item.group || 'N/A'} / ${item.name}`,
          snippet: item.description || '暂无描述',
          modelData: item, // Store the full model object
          score: item.similarity_score ? parseFloat((item.similarity_score * 5).toFixed(1)) : undefined
        }));
        addMessage('以下是为您检索到的相关模型：', false, true, semanticModelItems);
      } else {
        addMessage('未检索到符合条件的模型，请尝试更换关键词。 ', false);
      }
    } catch (error) {
      console.error('模型语义检索失败:', error);
      addMessage('模型语义检索失败，请稍后再试。', false);
      ElMessage.error('模型语义检索失败');
    } finally {
      isSendingMessage.value = false;
    }
    return; // End processing for semantic search here
  }

  // Standard message sending logic
  addMessage(text, true);
  userInput.value = '';

  // Prepare data for backend
  let prompt = text;
  // Note: The logic for appending selectedSemanticDetails (text snippets) to prompt might need re-evaluation
  // if isSemanticResult is now solely for models. For now, it will pick up any selected text.
  const selectedSemanticDetails: string[] = [];
  messages.value.forEach(msg => {
    if (!msg.isUser && msg.isSemanticResult && msg.selectedSemanticItems && msg.selectedSemanticItems.length > 0) {
      msg.semanticItems?.forEach(item => {
        // This part is for text snippets, ensure it doesn't conflict if modelData is the primary use now
        if (!item.modelData && msg.selectedSemanticItems?.includes(item.id)) { 
          selectedSemanticDetails.push(`[参考资料: ${item.title}]\n${item.fullText || item.snippet}\n[/参考资料]`);
        }
      });
    }
  });

  if (selectedSemanticDetails.length > 0) {
    prompt = `${selectedSemanticDetails.join('\n')}\n用户问题：${text}`;
  }
  
  const requestPayload: any = {
    prompt: prompt, 
    original_text: text, 
  };

  if (currentModels.value.length > 0) {
    requestPayload.model_details = currentModels.value.map(m => m.details);
  }
  if (currentDatasets.value.length > 0) {
    requestPayload.dataset_details = currentDatasets.value.map(d => d.details);
  }

  // Simulate LLM call
  console.log('Frontend: Sending to LLM (simulated with updated context):', requestPayload);
  setTimeout(() => {
    addMessage(`后端大模型回复 (模拟 for "${text}" with ${currentModels.value.length} models, ${currentDatasets.value.length} datasets)`, false);
    ElMessage.success('消息已发送至大模型 (模拟)');
    isSendingMessage.value = false;
  }, 1000);
};

const toggleSemanticSearch = () => {
  isSemanticSearchEnabled.value = !isSemanticSearchEnabled.value;
  ElMessage.info(isSemanticSearchEnabled.value ? '模型语义检索已开启，请输入您的模型需求。' : '模型语义检索已关闭，将进行常规对话。');
};

const confirmModelSelection = () => {
  currentModels.value = availableModels.value.filter(m => selectedModelIds.value.includes(m.id));
  if (currentModels.value.length > 0) {
    ElMessage.success(`已选择模型: ${currentModels.value.map(m => m.name).join(', ')}`);
  } else {
    // This case might be hit if the dialog is closed with no selection after having selections before.
    // If selectedModelIds is empty, clearSelectedModel might be more appropriate or no message needed if already cleared.
  }
  showModelSelectionDialog.value = false;
};

const clearSelectedModel = () => {
  selectedModelIds.value = [];
  currentModels.value = [];
  ElMessage.info('已清除所有选定模型');
};

const removeSelectedModel = (modelIdToRemove: string) => {
  selectedModelIds.value = selectedModelIds.value.filter(id => id !== modelIdToRemove);
  const removedModel = currentModels.value.find(m => m.id === modelIdToRemove);
  currentModels.value = currentModels.value.filter(m => m.id !== modelIdToRemove);
  
  if (removedModel) {
    ElMessage.info(`已移除模型: ${removedModel.name}`);
  }
  if (currentModels.value.length === 0 && selectedModelIds.value.length === 0) {
     ElMessage.info('所有模型已移除'); // Or a more specific message if needed
  }
};

const confirmDatasetSelection = () => {
  currentDatasets.value = availableDatasets.value.filter(d => selectedDatasetIds.value.includes(d.id));
  if (currentDatasets.value.length > 0) {
    ElMessage.success(`已选择数据集: ${currentDatasets.value.map(d => d.name).join(', ')}`);
  } else {
    // ElMessage.info('已取消选择数据集'); // Message handled by removeSelectedDataset or if nothing was selected
  }
  showDatasetSelectionDialog.value = false;
};

const removeSelectedDataset = (datasetIdToRemove: string) => {
  selectedDatasetIds.value = selectedDatasetIds.value.filter(id => id !== datasetIdToRemove);
  const previouslySelectedDatasetsCount = currentDatasets.value.length;
  currentDatasets.value = availableDatasets.value.filter(d => selectedDatasetIds.value.includes(d.id));
  
  const removedDataset = availableDatasets.value.find(d => d.id === datasetIdToRemove);
  if (removedDataset) {
    ElMessage.info(`已移除数据集: ${removedDataset.name}`);
  } else {
    ElMessage.info('已移除数据集');
  }
  // If all datasets are removed, provide a general message
  if (previouslySelectedDatasetsCount > 0 && currentDatasets.value.length === 0) {
    ElMessage.info('所有数据集已移除');
  }
};

const goToModelDetails = (modelId: string) => {
  if (!modelId) return;
  router.push(`/model/models/${modelId}`);
};

onMounted(() => {
  // Initial greeting or instructions
  addMessage('您好！请问有什么可以帮助您的吗？您可以选择模型、数据集，或开启语义搜索。', false);
  // Load available models and datasets from API
  loadAvailableModels();
  loadAvailableDatasets();
  // For now, using mock data
  if (availableModels.value.length > 0 && !selectedModelIds.value.length) {
    // selectedModelIds.value = availableModels.value.map(m => m.id); // Default to all models
    // confirmModelSelection(); // auto-confirm
  }
});

// Watch for changes in messages to auto-scroll
watch(messages, () => {
  scrollToBottom();
}, { deep: true });

</script>

<style lang="scss" scoped>
.llm-chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px); // Adjust based on your layout's header/footer
  padding: 15px;
  box-sizing: border-box;
}

.chat-window {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Needed for child flex to work correctly */
  
  :deep(.el-card__header) {
    padding: 10px 15px;
    background-color: #f5f7fa;
  }
  :deep(.el-card__body) {
    padding: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Important for scrolling */
  }
}

.chat-header-content {
  display: flex;
  flex-direction: column;
}

.chat-header-top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.chat-title {
  font-size: 16px;
  font-weight: bold;
}

.action-buttons > .el-button {
  margin-left: 10px;
}

.selected-items-display {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* For spacing between model group and dataset group */
}

.selected-item-group {
  display: flex;
  align-items: center;
  gap: 5px; /* For spacing between label and tag(s) */
  flex-wrap: wrap; /* Allow tags to wrap within a group if many */
}

.selected-item-label {
  font-size: 0.9em;
  color: #606266;
}

.dataset-tag {
  margin-right: 5px;
  margin-bottom: 5px; /* If tags wrap */
}

.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #fff;

  .message-item {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;

    .message-avatar {
      margin-right: 10px;
      flex-shrink: 0; // Prevent avatar from shrinking
    }

    .message-content {
      padding: 10px 15px;
      border-radius: 8px;
      max-width: calc(100% - 100px); // Adjust based on avatar and spacing
      word-wrap: break-word;
      font-size: 14px;
    }

    &.user-message {
      flex-direction: row-reverse;
      .message-avatar {
        margin-left: 10px;
        margin-right: 0;
      }
      .message-content {
        background-color: #d9ecff; // Element Plus primary light
        color: #333;
         margin-left: auto; /* Aligns to the right */
      }
    }

    &.ai-message {
      .message-content {
        background-color: #f0f2f5; // A light grey
        color: #333;
        margin-right: auto; /* Aligns to the left */
      }
    }
  }
}

.semantic-results-section {
  margin-top: 10px;
  > p { // The introductory paragraph, direct child
    margin-bottom: 15px;
    font-size: 1em;
    color: #303133;
  }
}

.semantic-items-container {
  display: flex;
  flex-direction: column;
  gap: 15px; // Space between cards
}

.semantic-item-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fff;
  display: flex; // Added for footer alignment
  flex-direction: column; // Added for footer alignment

  :deep(.el-card__header) {
    padding: 10px 15px;
    background-color: #f9fafc; // Lighter header background
    border-bottom: 1px solid #ebeef5;
  }

  .semantic-item-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .semantic-item-title {
      font-size: 1.05em;
      font-weight: 600; // Bolder title
      color: #303133;
      margin-right: 10px; // Space between title and checkbox
    }
    .el-checkbox {
      // Customizations for the checkbox if needed
      .el-checkbox__label {
        font-size: 0.95em;
      }
    }
  }

  :deep(.el-card__body) {
    padding: 15px;
    flex-grow: 1; // Allow body to grow and push footer down
  }

  .semantic-item-card-content {
    .semantic-item-snippet {
      font-size: 0.9em;
      color: #606266;
      line-height: 1.6;
      // Removed -webkit-line-clamp and -webkit-box-orient for scrolling
      // overflow: hidden; 
      // text-overflow: ellipsis;
      max-height: 70px; // Approx 3-4 lines, adjust as needed
      overflow-y: auto; // Enable scrolling
      margin: 0;
      padding-right: 5px; // Add some padding if scrollbar appears
    }
  }
  .semantic-item-card-footer {
    padding: 10px 15px;
    border-top: 1px solid #f5f7fa;
    text-align: right;
    margin-top: auto; // Push footer to bottom
  }
}

.chat-input-area {
  padding: 15px;
  border-top: 1px solid #e4e7ed;
  background-color: #fff;
  display: flex;
  align-items: flex-end; // Align items to bottom for textarea grow

  .el-textarea {
    margin-right: 10px;
    flex-grow: 1;
  }
  .send-button {
    flex-shrink: 0; // Prevent button from shrinking
  }
}

.mb15 {
  margin-bottom: 15px;
}

.full-width {
  width: 100%;
}

.block-radio, .block-checkbox {
  display: block;
  width: calc(100% - 24px); // Adjust for padding/border
  margin-bottom: 10px !important; // Ensure spacing between items
  margin-right: 0 !important; // Override Element Plus default margin
  padding: 10px;
  :deep(.el-radio__label), :deep(.el-checkbox__label) {
    white-space: normal; // Allow text to wrap
    line-height: 1.4;
  }
}

// If you have specific icons like 'iconfont icon-liaotian', ensure they are defined in your project's icon font CSS.
// For Element Plus icons, they should work out of the box if Element Plus is configured correctly.
// Example for a custom icon (if you were using a font icon class)
// .icon-liaotian::before { content: "\eXXX"; /* replace eXXX with actual icon code */ }
</style> 