<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>多智能体任务处理系统</span>
        </div>
      </template>
      
      <!-- 任务输入区域 -->
      <el-form ref="formRef" :model="taskForm" label-width="120px">
        <el-form-item label="任务描述" prop="taskDescription">
          <el-input
            v-model="taskForm.taskDescription"
            type="textarea"
            :rows="4"
            placeholder="请输入您要执行的任务描述，例如：对上传图片中的文字进行OCR识别并翻译成中文"
          />
        </el-form-item>
        
        <el-form-item label="上传图片" prop="image">
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
            accept="image/*"
          >
            <template #trigger>
              <el-button type="primary">选择图片</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                仅支持 jpg/png 文件，且不超过 5MB
                <div v-if="taskForm.imageFile" class="selected-file">
                  已选择文件: {{ taskForm.imageFile.name }}
                </div>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handlePlanTask" :loading="loading.plan">
            规划任务
          </el-button>
          <el-button type="success" @click="handleExecuteTask" :loading="loading.execute">
            执行任务
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 系统简介 -->
      <div v-if="!taskPlan.subtasks || taskPlan.subtasks.length === 0" class="system-intro">
        <el-alert
          title="多智能体任务处理系统"
          type="info"
          description="本系统使用阿里云通义千问系列大模型实现多智能体协作，每个专家智能体使用针对其专业领域优化的模型。"
          :closable="false"
          show-icon
        />
        <el-divider content-position="left">专家智能体模型配置</el-divider>
        <el-table :data="expertModels" style="width: 100%">
          <el-table-column prop="expert" label="专家角色" width="180" />
          <el-table-column prop="model" label="使用模型" width="180" />
          <el-table-column prop="description" label="说明" />
        </el-table>
      </div>
      
      <!-- 任务规划结果展示 -->
      <div v-if="taskPlan.subtasks && taskPlan.subtasks.length > 0" class="task-plan-result">
        <h3>任务规划结果</h3>
        <el-timeline>
          <el-timeline-item
            v-for="(task, index) in taskPlan.subtasks"
            :key="index"
            :type="getTimelineItemType(index)"
            :color="getTimelineItemColor(index)"
            :hollow="index >= currentTaskIndex"
            :timestamp="task.name"
          >
            <el-card class="task-card">
              <template #header>
                <div class="task-header">
                  <div>
                    <span>{{ task.expert }}</span>
                    <el-tag size="small" type="info" effect="plain" class="model-tag">
                      {{ getExpertModel(task.expert) }}
                    </el-tag>
                  </div>
                  <el-tag size="small" effect="dark" :type="getTaskStatusType(index)">
                    {{ getTaskStatusText(index) }}
                  </el-tag>
                </div>
              </template>
              <div class="task-content">
                <p>{{ task.description }}</p>
                <div v-if="taskResults[task.id]" class="task-result">
                  <el-divider content-position="left">执行结果</el-divider>
                  <div v-if="task.expert === 'OCR专家' && taskResults[task.id].text">
                    <p><strong>识别文本：</strong></p>
                    <el-input
                      type="textarea"
                      :rows="3"
                      v-model="taskResults[task.id].text"
                      readonly
                    />
                    <p><strong>置信度：</strong> {{ taskResults[task.id].confidence }}</p>
                  </div>
                  <div v-else-if="task.expert === '翻译专家' && taskResults[task.id].translated_text">
                    <p><strong>翻译结果：</strong></p>
                    <el-input
                      type="textarea"
                      :rows="3"
                      v-model="taskResults[task.id].translated_text"
                      readonly
                    />
                  </div>
                  <div v-else-if="task.expert === '图像处理专家' && taskResults[task.id].status">
                    <p><strong>处理状态：</strong> {{ taskResults[task.id].status }}</p>
                    <p><strong>消息：</strong> {{ taskResults[task.id].message }}</p>
                    <p v-if="taskResults[task.id].details"><strong>详细分析：</strong> {{ taskResults[task.id].details }}</p>
                  </div>
                  <div v-else-if="task.expert === '数据整合专家' && taskResults[task.id].integrated_result">
                    <p><strong>整合结果：</strong></p>
                    <el-descriptions :column="1" border>
                      <el-descriptions-item label="原文">
                        {{ taskResults[task.id].integrated_result.original_text }}
                      </el-descriptions-item>
                      <el-descriptions-item label="译文">
                        {{ taskResults[task.id].integrated_result.translated_text }}
                      </el-descriptions-item>
                      <el-descriptions-item label="摘要">
                        {{ taskResults[task.id].integrated_result.summary }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>
                  <div v-else>
                    <div class="json-viewer-header">
                      <el-tag size="small" effect="dark" type="info">JSON 数据</el-tag>
                      <div class="json-viewer-actions">
                        <el-tooltip content="点击括号可折叠/展开内容" placement="top">
                          <el-icon class="action-icon"><InfoFilled /></el-icon>
                        </el-tooltip>
                        <el-tooltip content="复制JSON数据" placement="top">
                          <el-icon class="action-icon" @click="copyJsonData(taskResults[task.id])"><CopyDocument /></el-icon>
                        </el-tooltip>
                      </div>
                    </div>
                    <vue-json-pretty
                      :data="taskResults[task.id]"
                      :deep="2"
                      :show-length="true"
                      :show-double-quotes="true"
                      :show-line="true"
                      :collapsed-on-click-brackets="true"
                      :path-selectable="false"
                      class="json-viewer"
                      :highlightMouseoverNode="true"
                      :selectableType="'multiple'"
                    />
                  </div>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
      
      <!-- 最终结果展示 -->
      <div v-if="finalResult" class="final-result">
        <h3>任务执行完成</h3>
        <el-alert
          title="任务已成功执行完毕"
          type="success"
          :closable="false"
          show-icon
        />
        <el-divider content-position="left">最终结果</el-divider>
        <el-card shadow="hover">
          <div v-if="finalResult.results && finalResult.results.task4">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="原文">
                {{ finalResult.results.task2?.text || '无识别结果' }}
              </el-descriptions-item>
              <el-descriptions-item label="译文">
                {{ finalResult.results.task3?.translated_text || '无翻译结果' }}
              </el-descriptions-item>
              <el-descriptions-item label="摘要">
                {{ finalResult.results.task4?.integrated_result?.summary || '无摘要' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div v-else>
            <div class="json-viewer-header">
              <el-tag size="small" effect="dark" type="info">JSON 数据</el-tag>
              <div class="json-viewer-actions">
                <el-tooltip content="点击括号可折叠/展开内容" placement="top">
                  <el-icon class="action-icon"><InfoFilled /></el-icon>
                </el-tooltip>
                <el-tooltip content="复制JSON数据" placement="top">
                  <el-icon class="action-icon" @click="copyJsonData(finalResult)"><CopyDocument /></el-icon>
                </el-tooltip>
              </div>
            </div>
            <vue-json-pretty
              :data="finalResult"
              :deep="2"
              :show-length="true"
              :show-double-quotes="true"
              :show-line="true"
              :collapsed-on-click-brackets="true"
              :path-selectable="false"
              class="json-viewer"
              :highlightMouseoverNode="true"
              :selectableType="'multiple'"
            />
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { planTask, executeExpertTask, executeTask } from '@/api/aiTask/tasks';
import VueJsonPretty from 'vue-json-pretty';
import 'vue-json-pretty/lib/styles.css';
import { InfoFilled, CopyDocument } from '@element-plus/icons-vue';

// 表单数据
const taskForm = reactive({
  taskDescription: '对上传图片中的文字进行OCR识别并翻译成中文',
  imageFile: null as File | null,
});

// 专家模型配置
const expertModels = [
  { expert: 'OCR专家', model: 'qwen-vl-ocr-latest', description: '视觉OCR模型，专门用于图像文字识别' },
  { expert: '图像处理专家', model: '基于Python的图像处理', description: '使用OpenCV进行图像预处理，包括调整大小、增强对比度等' },
  { expert: '翻译专家', model: 'qwen-mt-turbo', description: '专用翻译模型，支持多语言翻译' },
  { expert: '数据整合专家', model: 'qwen-long', description: '长文本分析模型，用于整合和总结信息' },
  { expert: '需求分析师', model: 'qwen-plus', description: '通用模型，用于分析任务需求' },
  { expert: '方案设计师', model: 'qwen-plus', description: '通用模型，用于设计解决方案' },
  { expert: '执行专家', model: 'qwen-plus', description: '通用模型，用于执行具体任务' },
  { expert: '任务规划器', model: 'qwen-plus', description: '最强模型，用于任务拆解和规划' },
];

// 获取专家对应的模型
const getExpertModel = (expertRole: string) => {
  const expert = expertModels.find(item => item.expert === expertRole);
  return expert ? expert.model : 'qwen-plus';
};

// 加载状态
const loading = reactive({
  plan: false,
  execute: false,
});

// 任务规划结果
const taskPlan = ref({
  task_id: '',
  task_description: '',
  subtasks: [] as any[],
});

// 当前执行到的任务索引
const currentTaskIndex = ref(0);

// 任务执行结果
const taskResults = ref({} as Record<string, any>);

// 最终结果
const finalResult = ref(null as any);

// 处理文件变更
const handleFileChange = (file: any) => {
  if (file && file.raw) {
    taskForm.imageFile = file.raw;
    ElMessage.success(`成功选择文件: ${file.name}`);
  } else {
    taskForm.imageFile = null;
  }
};

// 规划任务
const handlePlanTask = async () => {
  if (!taskForm.taskDescription) {
    ElMessage.warning('请输入任务描述');
    return;
  }
  
  try {
    loading.plan = true;
    const response = await planTask({ task_description: taskForm.taskDescription });
    taskPlan.value = response.data;
    currentTaskIndex.value = 0;
    taskResults.value = {};
    finalResult.value = null;
    ElMessage.success('任务规划完成');
  } catch (error: any) {
    ElMessage.error(error.message || '任务规划失败');
  } finally {
    loading.plan = false;
  }
};

// 执行任务
const handleExecuteTask = async () => {
  if (!taskForm.taskDescription) {
    ElMessage.warning('请输入任务描述');
    return;
  }
  
  if (!taskForm.imageFile) {
    ElMessage.warning('请上传图片文件');
    return;
  }
  
  try {
    loading.execute = true;
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('task_description', taskForm.taskDescription);
    
    // 如果有图片，添加图片
    if (taskForm.imageFile) {
      formData.append('image', taskForm.imageFile);
    }
    
    // 调用执行任务API
    const response = await executeTask(formData);
    finalResult.value = response.data;
    
    // 更新任务结果
    if (response.data.results) {
      taskResults.value = response.data.results;
      currentTaskIndex.value = taskPlan.value.subtasks.length;
    }
    
    ElMessage.success('任务执行完成');
  } catch (error: any) {
    ElMessage.error(error.message || '任务执行失败');
  } finally {
    loading.execute = false;
  }
};

// 重置表单
const resetForm = () => {
  taskForm.taskDescription = '';
  taskForm.imageFile = null;
  taskPlan.value = { task_id: '', task_description: '', subtasks: [] };
  currentTaskIndex.value = 0;
  taskResults.value = {};
  finalResult.value = null;
};

// 获取时间线项目类型
const getTimelineItemType = (index: number) => {
  if (index < currentTaskIndex.value) return 'success';
  if (index === currentTaskIndex.value) return 'primary';
  return 'info';
};

// 获取时间线项目颜色
const getTimelineItemColor = (index: number) => {
  if (index < currentTaskIndex.value) return '#67C23A';
  if (index === currentTaskIndex.value) return '#409EFF';
  return '#909399';
};

// 获取任务状态类型
const getTaskStatusType = (index: number) => {
  if (index < currentTaskIndex.value) return 'success';
  if (index === currentTaskIndex.value) return 'primary';
  return 'info';
};

// 获取任务状态文本
const getTaskStatusText = (index: number) => {
  if (index < currentTaskIndex.value) return '已完成';
  if (index === currentTaskIndex.value) return '进行中';
  return '等待中';
};

// 复制JSON数据
const copyJsonData = (data: any): void => {
  const jsonString = JSON.stringify(data, null, 2);
  navigator.clipboard.writeText(jsonString)
    .then(() => {
      ElMessage.success('JSON数据已复制到剪贴板');
    })
    .catch(() => {
      // 回退到旧方法
      const input = document.createElement('textarea');
      input.value = jsonString;
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      ElMessage.success('JSON数据已复制到剪贴板');
    });
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.system-intro {
  margin: 20px 0;
}

.task-plan-result {
  margin-top: 30px;
}

.task-card {
  margin-bottom: 10px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-tag {
  margin-left: 8px;
}

.task-content {
  font-size: 14px;
}

.task-result {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.final-result {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.selected-file {
  margin-top: 8px;
  padding: 6px 10px;
  background-color: #ecf5ff;
  color: #409EFF;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.selected-file::before {
  content: "✓";
  margin-right: 5px;
  font-weight: bold;
}

/* JSON 树形结构样式 */
:deep(.vjs-tree) {
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  line-height: 1.5;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
  max-height: 400px;
  overflow: auto;
}

:deep(.vjs-tree .vjs-tree__content) {
  padding-left: 18px !important;
}

:deep(.vjs-tree .vjs-tree__brackets) {
  cursor: pointer;
}

:deep(.vjs-tree .vjs-value__string) {
  color: #c41a16;
}

:deep(.vjs-tree .vjs-value__number) {
  color: #1a1aa6;
}

:deep(.vjs-tree .vjs-value__boolean) {
  color: #0b7500;
}

:deep(.vjs-tree .vjs-value__null) {
  color: #808080;
}

.json-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  background-color: #f0f2f5;
  padding: 8px 12px;
  border-radius: 4px 4px 0 0;
  border-bottom: 1px solid #e6e6e6;
}

.json-viewer {
  border: 1px solid #e6e6e6;
  border-radius: 0 0 4px 4px;
  margin-bottom: 16px;
}

.json-viewer-actions {
  display: flex;
  align-items: center;
}

.action-icon {
  cursor: pointer;
  margin-left: 8px;
}

/* 增加JSON查看器的响应式高度 */
@media (max-width: 768px) {
  :deep(.vjs-tree) {
    max-height: 300px;
  }
}

@media (min-width: 769px) {
  :deep(.vjs-tree) {
    max-height: 500px;
  }
}
</style>