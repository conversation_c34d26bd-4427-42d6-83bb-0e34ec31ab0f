<template>
    <vue-office-docx
        :src="docx"
        style="height: 100vh;"
        @rendered="rendered"
    />
</template>

<script>
//引入VueOfficeDocx组件
import VueOfficeDocx from '@vue-office/docx/lib/v3/vue-office-docx.mjs'
//引入相关样式
import '@vue-office/docx/lib/v3/index.css'

export default {
    components:{
        VueOfficeDocx
    },
    data(){
        return {
            docx: 'http://**************:9000/models/kmg/yolo11/v1.0/yolo11n.pt?X-Amz-Algorithm=AWS4-HMAC-SHA256&amp;X-Amz-Credential=minioadmin%2F20250724%2Fus-east-1%2Fs3%2Faws4_request&amp;X-Amz-Date=20250724T105448Z&amp;X-Amz-Expires=604800&amp;X-Amz-SignedHeaders=host&amp;X-Amz-Signature=9d5599c5cb4fea197c77c5b279c1359cea050c6599a353fbb174f07bcb20d73e' //设置文档网络地址
        }
    },
    methods:{
        rendered(){
            console.log("渲染完成")
        }
    }
}
</script>