<template>
  <div class="app-container">
    <!-- 加载状态 -->
    <div v-if="state.loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <template v-else>
      <!-- 模型基本信息卡片 -->
      <el-card class="model-info-card">
        <div class="model-header">
          <div class="model-title">
            <h1>{{ state.modelDetail.name }}</h1>
            <el-tag 
              v-for="category in state.modelDetail.categories" 
              :key="category.id" 
              size="small"
              type="success"
              class="ml-2">
              {{ category.name }}
            </el-tag>
            <el-tag size="small" type="info" class="ml-2">{{ state.modelDetail.group }}</el-tag>
          </div>
        </div>

        <div class="model-meta">
          <div class="meta-item">
            <el-icon><User /></el-icon>
            <span>创建者: {{ state.modelDetail.creator_name || '未知' }}</span>
          </div>
          <div class="meta-item">
            <el-icon><Calendar /></el-icon>
            <span>更新时间: {{ formatDate(state.modelDetail.update_datetime) }}</span>
          </div>
          <div class="meta-item">
            <el-icon><Box /></el-icon>
            <span>MinIO路径: {{ state.modelDetail.minio_path }}</span>
          </div>
          
          <!-- 添加关联数据集显示 -->
          <div class="meta-item datasets-container" v-if="state.modelDetail.datasets && state.modelDetail.datasets.length > 0">
            <el-icon><DataLine /></el-icon>
            <span>训练数据集: </span>
            <div class="dataset-tags">
              <el-tag
                v-for="dataset in state.modelDetail.datasets"
                :key="dataset.id"
                type="info"
                effect="plain"
                class="dataset-tag"
                @click="navigateToDataset(dataset.id)"
              >
                {{ dataset.name }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 模型参数和指标 -->
        <div class="model-metrics">
          <div class="metrics-section">
            <h3>模型参数</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item v-for="(value, key) in state.modelDetail.parameters" :key="`param-${key}`" :label="key">
                {{ value }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="metrics-section">
            <h3>测试指标</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item v-for="(value, key) in state.modelDetail.metrics" :key="`metric-${key}`" :label="key">
                {{ value }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>

      <!-- 面包屑导航 -->
      <el-card class="tab-card">
        <el-tabs v-model="state.activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="模型详情" name="detail"></el-tab-pane>
          <el-tab-pane label="版本管理与文件" name="versions"></el-tab-pane>
          <el-tab-pane label="交流反馈" name="comments"></el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 内容区域 -->
      <el-card class="content-card">
        <!-- 模型详情 -->
        <div v-if="state.activeTab === 'detail'" class="model-detail-content">
          <div v-if="state.modelDetail.description" class="description-content">
            <h3>模型描述</h3>
            <md-preview
              :modelValue="state.modelDetail.description"
              class="md-preview"
              :preview-theme="previewTheme"
              :code-theme="codeTheme"
            />
          </div>
          <el-empty v-else description="暂无详细描述"></el-empty>
        </div>

        <!-- 版本管理 -->
        <div v-else-if="state.activeTab === 'versions'" class="model-versions">
          <el-tabs v-model="state.versionTab" class="version-tabs">
            <el-tab-pane label="版本列表" name="list">
              <el-table v-if="state.versions.length > 0" :data="state.versions" @row-click="selectVersion">
                <el-table-column label="版本号" min-width="50">
                  <template #default="scope">
                    {{ scope.row.version_number }}
                  </template>
                </el-table-column>
                <el-table-column label="版本描述" min-width="150">
                  <template #default="scope">
                    {{ scope.row.description || '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="状态" min-width="100">
                  <template #default="scope">
                    <el-tag :type="getStatusTagType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="Docker镜像" min-width="150">
                  <template #default="scope">
                    <div v-if="scope.row.docker_image" class="resource-item">
                      <el-icon><Picture /></el-icon>
                      <el-tooltip :content="scope.row.docker_image" placement="top">
                        <span class="truncate-text">{{ scope.row.docker_image }}</span>
                      </el-tooltip>
                      <el-button v-if="scope.row.docker_image" type="primary" link size="small" @click.stop="copyToClipboard(scope.row.docker_image)">
                        <el-icon><CopyDocument /></el-icon>
                      </el-button>
                    </div>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="文件数" min-width="80">
                  <template #default="scope">
                    {{ scope.row.files ? scope.row.files.length : 0 }}
                  </template>
                </el-table-column>
                <el-table-column label="创建者" min-width="100">
                  <template #default="scope">
                    {{ scope.row.creator_name || '未知' }}
                  </template>
                </el-table-column>
                <el-table-column label="创建时间" min-width="180" prop="create_datetime" />
                <el-table-column label="操作" width="320">
                  <template #default="scope">
                    <el-button size="small" type="primary" @click.stop="viewVersionFiles(scope.row)">
                      <el-icon><Document /></el-icon>查看文件
                    </el-button>
                    <el-button size="small" type="success" @click.stop="downloadAllFiles(scope.row)" :loading="scope.row.downloading">
                      <el-icon><Download /></el-icon>打包下载
                    </el-button>
                    <el-button size="small" type="info" @click.stop="showDeploymentGuide(scope.row)">
                      <el-icon><Guide /></el-icon>部署手册
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-empty v-else description="暂无版本"></el-empty>
            </el-tab-pane>
            
            <el-tab-pane label="文件浏览" name="files">
              <div v-if="state.currentVersion" class="version-info">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="版本号">
                    {{ state.currentVersion.version_number }}
                  </el-descriptions-item>
                  <el-descriptions-item label="创建者">
                    {{ state.currentVersion.creator_name || '未知' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="状态">
                    <el-tag :type="getStatusTagType(state.currentVersion.status)">
                      {{ getStatusText(state.currentVersion.status) }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="创建时间">
                    {{ state.currentVersion.create_datetime }}
                  </el-descriptions-item>
                </el-descriptions>
                
                <!-- 版本关键资源 -->
                <div class="version-resources">
                  <h3>版本关键资源</h3>
                  <el-card shadow="never" class="resource-card">
                    <div class="resource-list">
                      <!-- Docker 镜像 -->
                      <div class="resource-item" v-if="state.currentVersion.docker_image">
                        <div class="resource-icon">
                          <el-icon><Picture /></el-icon>
                        </div>
                        <div class="resource-content">
                          <div class="resource-title">Docker镜像</div>
                          <div class="resource-value">
                            <el-tooltip :content="state.currentVersion.docker_image" placement="top">
                              <span class="truncate-text">{{ state.currentVersion.docker_image }}</span>
                            </el-tooltip>
                            <el-button type="primary" link size="small" @click="copyToClipboard(state.currentVersion.docker_image)">
                              <el-icon><CopyDocument /></el-icon>复制
                            </el-button>
                          </div>
                          <div class="resource-command">
                            <div class="command-title">拉取命令:</div>
                            <div class="command-content">
                              <code>docker pull {{ state.currentVersion.docker_image }}</code>
                              <el-button type="primary" link size="small" @click="copyToClipboard(`docker pull ${state.currentVersion.docker_image}`)">
                                <el-icon><CopyDocument /></el-icon>
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 模型权重 -->
                      <div class="resource-item" v-if="state.currentVersion.model_weights_path">
                        <div class="resource-icon">
                          <el-icon><Files /></el-icon>
                        </div>
                        <div class="resource-content">
                          <div class="resource-title">模型权重文件</div>
                          <div class="resource-value">
                            <el-tooltip :content="state.currentVersion.model_weights_path" placement="top">
                              <span class="truncate-text">{{ getFileNameFromPath(state.currentVersion.model_weights_path) }}</span>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>

                      <!-- 测试报告 -->
                      <div class="resource-item" v-if="state.currentVersion.test_report_path">
                        <div class="resource-icon">
                          <el-icon><DataAnalysis /></el-icon>
                        </div>
                        <div class="resource-content">
                          <div class="resource-title">测试报告</div>
                          <div class="resource-value">
                            <el-tooltip :content="state.currentVersion.test_report_path" placement="top">
                              <span class="truncate-text">{{ getFileNameFromPath(state.currentVersion.test_report_path) }}</span>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>

                      <!-- 文档 -->
                      <div class="resource-item" v-if="state.currentVersion.model_docs_path">
                        <div class="resource-icon">
                          <el-icon><Reading /></el-icon>
                        </div>
                        <div class="resource-content">
                          <div class="resource-title">模型文档</div>
                          <div class="resource-value">
                            <el-tooltip :content="state.currentVersion.model_docs_path" placement="top">
                              <span class="truncate-text">{{ getFileNameFromPath(state.currentVersion.model_docs_path) }}</span>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>

                      <!-- 如果没有任何资源 -->
                      <el-empty v-if="!state.currentVersion.docker_image && !state.currentVersion.model_weights_path && !state.currentVersion.test_report_path && !state.currentVersion.model_docs_path" description="暂无关键资源"></el-empty>
                    </div>
                  </el-card>
                </div>
              
                <!-- 文件浏览器 -->
                <div class="file-browser">
                  <h3>文件浏览器</h3>
                  <!-- 面包屑导航 -->
                  <div class="file-browser-header-row">
                    <div class="file-browser-breadcrumb">
                      <el-breadcrumb separator="/">
                        <el-breadcrumb-item @click="goToRootDirectory">
                          <el-icon><FolderOpened /></el-icon>
                          根目录
                        </el-breadcrumb-item>
                        <el-breadcrumb-item 
                          v-for="item in breadcrumbs" 
                          :key="item.path" 
                          @click="state.currentPath = item.path"
                        >
                          {{ item.name }}
                        </el-breadcrumb-item>
                      </el-breadcrumb>
                    </div>
                  </div>
                  
                  <!-- 文件列表 -->
                  <div class="file-browser-list">
                    <!-- 文件表头 -->
                    <div class="file-browser-header file-browser-row">
                      <div class="file-item-icon-name">名称</div>
                      <div class="file-browser-item-size">大小</div>
                      <div class="file-browser-item-time">更新时间</div>
                      <div class="file-browser-item-action">操作</div>
                    </div>

                    <!-- 返回上一级 -->
                    <div v-if="state.currentPath" class="file-browser-item file-browser-row" @click="goToParentDirectory">
                      <div class="file-item-icon-name">
                        <div class="file-browser-item-icon">
                          <el-icon><Back /></el-icon>
                        </div>
                        <div class="file-browser-item-name">..</div>
                      </div>
                      <div class="file-browser-item-size">-</div>
                      <div class="file-browser-item-time">-</div>
                      <div class="file-browser-item-action"></div>
                    </div>
                    
                    <!-- 目录和文件 -->
                    <template v-for="(item, index) in getCurrentDirectoryContents" :key="item.name">
                      <!-- 目录 -->
                      <div 
                        v-if="item.type === 'directory'" 
                        class="file-browser-item file-browser-row"
                        @click="enterDirectory(item)"
                      >
                        <div class="file-item-icon-name">
                          <div class="file-browser-item-icon">
                            <el-icon><Folder /></el-icon>
                          </div>
                          <div class="file-browser-item-name">{{ item.name }}</div>
                        </div>
                        <div class="file-browser-item-size">-</div>
                        <div class="file-browser-item-time">-</div>
                        <div class="file-browser-item-action"></div>
                      </div>
                      
                      <!-- 文件 -->
                      <div v-else class="file-browser-item file-browser-row">
                        <div class="file-item-icon-name">
                          <div class="file-browser-item-icon">
                            <el-icon><Document /></el-icon>
                          </div>
                          <div class="file-browser-item-name">{{ item.name }}</div>
                        </div>
                        <div class="file-browser-item-size">
                          {{ formatSize(item.file.file_size) }}
                        </div>
                        <div class="file-browser-item-time">
                          {{ formatDate(item.file.last_modified) }}
                        </div>
                        <div class="file-browser-item-action">
                          <el-tooltip content="下载文件" placement="top">
                            <el-button 
                              size="small" 
                              type="primary" 
                              circle 
                              @click="downloadVersionFile(item.file)"
                            >
                              <el-icon><Download /></el-icon>
                            </el-button>
                          </el-tooltip>
                        </div>
                      </div>
                    </template>
                    
                    <!-- 空目录提示 -->
                    <div v-if="getCurrentDirectoryContents.length === 0" class="file-browser-empty">
                      <el-empty description="当前目录为空" />
                    </div>
                  </div>
                </div>
              </div>
              <el-empty v-else description="请从版本列表中选择一个版本"></el-empty>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 模型评论 -->
        <div v-else-if="state.activeTab === 'comments'" class="model-comments">
          <!-- 评论列表 -->
          <div class="comments-list">
            <template v-if="state.comments.length > 0">
              <div class="comment-tree">
                <comment-item
                  v-for="comment in state.comments"
                  :key="comment.id"
                  :comment="comment"
                  @reply="handleReply"
                  @delete="handleDeleteComment"
                />
              </div>
            </template>
            <el-empty v-else description="暂无评论"></el-empty>
          </div>

          <!-- 评论输入框 -->
          <div class="comment-form">
            <h3>{{ state.replyTo ? `回复: ${state.replyTo.user.username}` : '发表评论' }}</h3>
            <div v-if="state.replyTo" class="reply-info">
              <span>{{ state.replyTo.content }}</span>
              <el-button type="text" @click="cancelReply">取消回复</el-button>
            </div>
            <el-input
              v-model="state.commentContent"
              type="textarea"
              :rows="4"
              placeholder="请输入评论内容..."
            />
            <div class="form-actions">
              <el-button type="primary" @click="submitComment" :loading="state.submittingComment">
                发表评论
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 下载进度对话框 -->
      <el-dialog
        v-model="downloadState.visible"
        title="文件下载"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        width="400px"
      >
        <div class="download-progress">
          <div class="file-info">
            <el-icon><Document /></el-icon>
            <span>{{ downloadState.fileName }}</span>
          </div>
          <el-progress 
            :percentage="downloadState.progress"
            :format="(p: number) => `${p}%`"
            :stroke-width="20"
            status="success"
          />
          <div class="download-speed">
            <span>下载速度: {{ downloadState.speed }}</span>
          </div>
        </div>
      </el-dialog>

      <!-- 部署指南对话框 -->
      <el-dialog
        v-model="deploymentGuide.visible"
        :title="`${deploymentGuide.version.version_number || ''} 部署指南`"
        width="60%"
        class="deployment-guide-dialog"
      >
        <div class="deployment-guide-content">
          <el-tabs v-model="deploymentGuide.activeTab">
            <el-tab-pane label="Docker部署" name="docker">
              <div class="guide-section">
                <h3>Docker镜像部署</h3>
                <div v-if="deploymentGuide.version.docker_image" class="guide-step">
                  <div class="step-title">1. 拉取Docker镜像</div>
                  <div class="step-code">
                    <el-input readonly :value="`docker pull ${deploymentGuide.version.docker_image}`">
                      <template #append>
                        <el-button @click="copyToClipboard(`docker pull ${deploymentGuide.version.docker_image}`)">
                          <el-icon><CopyDocument /></el-icon>复制
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                </div>
                <div class="guide-step">
                  <div class="step-title">2. 运行Docker容器</div>
                  <div class="step-code">
                    <el-input readonly :value="`docker run -d --name ${state.modelDetail.name}-${deploymentGuide.version.version_number} -p 8000:8000 ${deploymentGuide.version.docker_image || '[镜像名称]'}`">
                      <template #append>
                        <el-button @click="copyToClipboard(`docker run -d --name ${state.modelDetail.name}-${deploymentGuide.version.version_number} -p 8000:8000 ${deploymentGuide.version.docker_image || '[镜像名称]'}`)">
                          <el-icon><CopyDocument /></el-icon>复制
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                  <div class="step-note">
                    注意：根据实际需要调整端口映射和环境变量
                  </div>
                </div>
                <div class="guide-step">
                  <div class="step-title">3. 查看容器状态</div>
                  <div class="step-code">
                    <el-input readonly value="docker ps">
                      <template #append>
                        <el-button @click="copyToClipboard('docker ps')">
                          <el-icon><CopyDocument /></el-icon>复制
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                </div>
                <div class="guide-step">
                  <div class="step-title">4. 查看容器日志</div>
                  <div class="step-code">
                    <el-input readonly :value="`docker logs ${state.modelDetail.name}-${deploymentGuide.version.version_number}`">
                      <template #append>
                        <el-button @click="copyToClipboard(`docker logs ${state.modelDetail.name}-${deploymentGuide.version.version_number}`)">
                          <el-icon><CopyDocument /></el-icon>复制
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="模型权重使用" name="weights">
              <div class="guide-section">
                <h3>模型权重文件使用指南</h3>
                <div v-if="deploymentGuide.version.model_weights_path" class="guide-step">
                  <div class="step-title">1. 下载模型权重文件</div>
                  <div class="step-action">
                    <el-button type="primary" @click="downloadSpecificFile(deploymentGuide.version.model_weights_path)">
                      <el-icon><Download /></el-icon>下载权重文件
                    </el-button>
                  </div>
                </div>
                <div class="guide-step">
                  <div class="step-title">2. 加载模型权重示例代码</div>
                  <div class="step-code">
                    <el-input
                      type="textarea"
                      rows="6"
                      readonly
                      :value="getWeightLoadingCode()"
                    >
                      <template #append>
                        <el-button @click="copyToClipboard(getWeightLoadingCode())">
                          <el-icon><CopyDocument /></el-icon>复制
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                </div>
                <div class="guide-step">
                  <div class="step-title">3. 模型推理示例代码</div>
                  <div class="step-code">
                    <el-input
                      type="textarea"
                      rows="6"
                      readonly
                      :value="getInferenceCode()"
                    >
                      <template #append>
                        <el-button @click="copyToClipboard(getInferenceCode())">
                          <el-icon><CopyDocument /></el-icon>复制
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="文档资料" name="docs">
              <div class="guide-section">
                <h3>相关文档资料</h3>
                <div v-if="deploymentGuide.version.model_docs_path" class="guide-step">
                  <div class="step-title">模型使用文档</div>
                  <div class="step-action">
                    <el-button type="primary" @click="downloadSpecificFile(deploymentGuide.version.model_docs_path)">
                      <el-icon><Document /></el-icon>查看模型文档
                    </el-button>
                  </div>
                </div>
                <div v-if="deploymentGuide.version.test_report_path" class="guide-step">
                  <div class="step-title">模型测试报告</div>
                  <div class="step-action">
                    <el-button type="primary" @click="downloadSpecificFile(deploymentGuide.version.test_report_path)">
                      <el-icon><DataAnalysis /></el-icon>查看测试报告
                    </el-button>
                  </div>
                </div>
                <el-empty v-if="!deploymentGuide.version.model_docs_path && !deploymentGuide.version.test_report_path" description="暂无相关文档"></el-empty>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ArrowLeft, Star, Download, User, Calendar, Box, Document,
  FolderOpened, Folder, Back, Picture, CopyDocument, Files, DataAnalysis, Reading, Guide, DataLine
} from '@element-plus/icons-vue'
import { getModel, starModel, downloadModel, downloadModelFile } from '@/api/model/models'
import { getCommentList, createComment, deleteComment } from '@/api/model/comments'
import { getVersionList, getVersion, downloadVersionAllFiles } from '@/api/model/versions'
import CommentItem from './CommentItem.vue'
// @ts-ignore
import { MdPreview } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'

const route = useRoute()
const router = useRouter()

const state = reactive({
  loading: true,
  modelDetail: null as any,
  activeTab: 'detail',
  versionTab: 'list',
  fileList: [] as any[],
  comments: [] as any[],
  commentContent: '',
  replyTo: null as any,
  submittingComment: false,
  versions: [] as any[],
  currentVersion: null as any,
  currentPath: '', // 当前目录路径
  fileTree: {} as any // 文件目录树
})

// 下载状态
const downloadState = reactive({
  visible: false,
  progress: 0,
  fileName: '',
  speed: '0 KB/s',
  startTime: 0,
  loaded: 0
})

// 部署指南状态
const deploymentGuide = reactive({
  visible: false,
  version: {} as any,
  activeTab: 'docker'
})

// Markdown编辑器配置
const previewTheme = 'github'
const codeTheme = 'atom'

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化文件大小
const formatSize = (bytes: number) => {
  if (!bytes) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let i = 0
  while (bytes >= 1024 && i < units.length - 1) {
    bytes /= 1024
    i++
  }
  return `${bytes.toFixed(2)} ${units[i]}`
}

// 格式化下载速度
const formatSpeed = (loaded: number, elapsed: number) => {
  if (elapsed === 0) return '0 KB/s'
  const bytesPerSecond = loaded / elapsed
  if (bytesPerSecond < 1024) return `${bytesPerSecond.toFixed(2)} B/s`
  if (bytesPerSecond < 1024 * 1024) return `${(bytesPerSecond / 1024).toFixed(2)} KB/s`
  return `${(bytesPerSecond / (1024 * 1024)).toFixed(2)} MB/s`
}

// 获取状态对应的标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    'dev_done': 'info',
    'train_done': 'warning',
    'test_pass': 'success',
    'test_fail': 'danger',
    'online': 'primary',
    'offline': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态对应的文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'dev_done': '开发完毕待训练',
    'train_done': '训练完毕待测试',
    'test_pass': '测试通过',
    'test_fail': '测试未通过',
    'online': '已上架',
    'offline': '已下架'
  }
  return statusMap[status] || status
}

// 从路径中获取文件名
const getFileNameFromPath = (path: string) => {
  if (!path) return ''
  return path.split('/').pop() || path
}

// 复制内容到剪贴板
const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('复制成功')
  }).catch(err => {
    console.error('复制失败:', err)
    ElMessage.error('复制失败')
  })
}

// 下载特定文件
const downloadSpecificFile = async (filePath: string) => {
  if (!filePath) {
    ElMessage.error('文件路径为空')
    return
  }
  
  try {
    // 获取文件名
    const fileName = getFileNameFromPath(filePath)
    const versionFile = deploymentGuide.version.files.find((f: any) => {
    // 处理路径可能不完全匹配的情况
    return f.file_path.endsWith(fileName);
  });
    
    // 创建一个a标签，模拟点击下载
    const link = document.createElement('a')
    
    // 这里假设后端提供了一个通用的文件下载接口
    // 实际情况可能需要根据具体的API调整
    const downloadUrl = versionFile.download_url
    
    link.href = downloadUrl
    link.download = fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('下载已开始')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

// 显示部署指南
const showDeploymentGuide = (version: any) => {
  deploymentGuide.version = version
  deploymentGuide.visible = true
  deploymentGuide.activeTab = 'docker'
}

// 获取模型权重加载示例代码
const getWeightLoadingCode = () => {
  const modelName = state.modelDetail?.name || 'model'
  return `import torch
from transformers import AutoModel, AutoTokenizer

# 加载模型权重
model_path = "${getFileNameFromPath(deploymentGuide.version.model_weights_path || 'model_weights.bin')}"
model = AutoModel.from_pretrained(model_path)
tokenizer = AutoTokenizer.from_pretrained(model_path)

# 模型移至GPU（如果可用）
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = model.to(device)`
}

// 获取模型推理示例代码
const getInferenceCode = () => {
  const modelName = state.modelDetail?.name || 'model'
  return `# 准备输入数据
text = "这是一个示例输入文本"
inputs = tokenizer(text, return_tensors="pt").to(device)

# 执行推理
with torch.no_grad():
    outputs = model(**inputs)

# 处理输出结果
embeddings = outputs.last_hidden_state
# 根据具体模型类型和任务进行后续处理
print(f"输出形状: {embeddings.shape}")
print(f"输出示例: {embeddings[0][0][:5]}")`
}

// 获取模型详情
const getModelDetail = async () => {
  const id = route.params.id as string
  if (!id) {
    ElMessage.error('模型ID不能为空')
    return
  }

  state.loading = true
  try {
    const res = await getModel(parseInt(id))
    state.modelDetail = res.data
    // 加载文件列表
    state.fileList = state.modelDetail.file_list || []
    // 加载评论
    await loadComments()
    // 加载版本列表
    await loadVersions()
  } catch (error) {
    console.error('获取模型详情失败:', error)
    ElMessage.error('获取模型详情失败')
  } finally {
    state.loading = false
  }
}

// 加载评论
const loadComments = async () => {
  try {
    const res = await getCommentList({ model: state.modelDetail.id, only_top: true })
    state.comments = res.data || []
  } catch (error) {
    console.error('获取评论失败:', error)
    ElMessage.error('获取评论失败')
  }
}

// 加载版本列表
const loadVersions = async () => {
  if (!state.modelDetail?.id) return;
  
  try {
    const response = await getVersionList({ model_id: state.modelDetail.id });
    state.versions = response.data || [];
  } catch (error) {
    console.error('获取版本列表失败:', error);
    ElMessage.error('获取版本列表失败');
  }
};

// 定义文件项和目录项的类型
interface FileItem {
  type: 'file';
  name: string;
  path: string;
  file: any;
}

interface DirectoryItem {
  type: 'directory';
  name: string;
  path: string;
  children: Record<string, FileItem | DirectoryItem>;
}

type FileTreeItem = FileItem | DirectoryItem;

// 处理文件树结构
const buildFileTree = (files: any[]): DirectoryItem => {
  const tree: DirectoryItem = {
    type: 'directory',
    name: 'root',
    path: '',
    children: {}
  };

  // 遍历所有文件
  files.forEach(file => {
    // 获取文件路径（从完整路径中提取相对路径）
    const pathParts = file.file_path.split('/');
    // 找到版本号所在的索引
    const modelNameIndex = pathParts.findIndex((part: string) => part === state.modelDetail.name);
    const versionIndex = modelNameIndex + 1;
    
    // 获取相对路径部分（不包括组名/模型名/版本号）
    const validPathParts = pathParts.slice(versionIndex + 1);
    
    // 如果没有有效路径部分，直接放在根目录下
    if (validPathParts.length === 0) {
      tree.children[file.filename] = {
        type: 'file',
        name: file.filename,
        path: '',
        file: file
      };
      return;
    }

    // 构建文件树
    let currentNode: DirectoryItem = tree;
    const dirPath = validPathParts.slice(0, -1); // 目录部分
    const fileName = validPathParts[validPathParts.length - 1]; // 文件名
    
    // 创建目录结构
    let currentPath = '';
    for (const dirName of dirPath) {
      currentPath = currentPath ? `${currentPath}/${dirName}` : dirName;
      
      if (!currentNode.children[dirName]) {
        currentNode.children[dirName] = {
          type: 'directory',
          name: dirName,
          path: currentPath,
          children: {}
        } as DirectoryItem;
      }
      currentNode = currentNode.children[dirName] as DirectoryItem;
    }
    
    // 添加文件
    currentNode.children[fileName] = {
      type: 'file',
      name: fileName,
      path: currentPath ? `${currentPath}/${fileName}` : fileName,
      file: file
    } as FileItem;
  });

  return tree;
};

// 获取当前目录内容
const getCurrentDirectoryContents = computed(() => {
  if (!state.fileTree || !state.fileTree.children) {
    return [] as FileTreeItem[];
  }
  
  // 按照路径导航到当前目录
  let currentDir: DirectoryItem = state.fileTree as DirectoryItem;
  if (state.currentPath) {
    const pathParts = state.currentPath.split('/');
    for (const part of pathParts) {
      if (currentDir.children && currentDir.children[part]) {
        const childItem = currentDir.children[part];
        if (childItem.type === 'directory') {
          currentDir = childItem;
        } else {
          return [] as FileTreeItem[]; // 如果路径指向文件而非目录，返回空数组
        }
      } else {
        return [] as FileTreeItem[]; // 路径无效
      }
    }
  }
  
  // 将对象转换为数组并排序
  return Object.values(currentDir.children).sort((a: FileTreeItem, b: FileTreeItem) => {
    // 目录优先
    if (a.type !== b.type) {
      return a.type === 'directory' ? -1 : 1;
    }
    // 按名称排序
    return a.name.localeCompare(b.name);
  });
});

// 进入目录
const enterDirectory = (directory: DirectoryItem) => {
  state.currentPath = directory.path;
};

// 返回上级目录
const goToParentDirectory = () => {
  const pathParts = state.currentPath.split('/');
  pathParts.pop();
  state.currentPath = pathParts.join('/');
};

// 导航到根目录
const goToRootDirectory = () => {
  state.currentPath = '';
};

// 获取面包屑导航
const breadcrumbs = computed(() => {
  if (!state.currentPath) {
    return [];
  }
  
  const parts = state.currentPath.split('/');
  return parts.map((part: string, index: number) => {
    return {
      name: part,
      path: parts.slice(0, index + 1).join('/')
    };
  });
});

// 选择版本
const selectVersion = (version: any) => {
  state.currentVersion = version;
};

// 查看版本文件
const viewVersionFiles = async (version: any) => {
  state.currentVersion = version;
  state.versionTab = 'files';
  
  try {
    // 获取版本完整信息
    const response = await getVersion(version.id);
    state.currentVersion = response.data;
    
    // 重置当前路径
    state.currentPath = '';
    
    // 构建文件树
    if (state.currentVersion.files && state.currentVersion.files.length > 0) {
      state.fileTree = buildFileTree(state.currentVersion.files);
    } else {
      state.fileTree = {
        type: 'directory',
        name: 'root',
        path: '',
        children: {}
      } as DirectoryItem;
    }
  } catch (error) {
    console.error('获取版本详情失败:', error);
    ElMessage.error('获取版本详情失败');
  }
};

// 下载版本文件
const downloadVersionFile = (file: any) => {
  if (!file.download_url) {
    ElMessage.error('无法获取下载链接');
    return;
  }
  
  // 创建一个a标签，模拟点击下载
  const link = document.createElement('a');
  link.href = file.download_url;
  link.download = file.filename || 'download';
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 处理标签页切换
const handleTabChange = (tab: string) => {
  state.activeTab = tab
  
  if (tab === 'versions') {
    // 加载版本列表
    loadVersions()
  }
}

// 返回列表页
const goBack = () => {
  router.back()
}

// 收藏模型
const handleStar = async () => {
  try {
    const res = await starModel(state.modelDetail.id)
    ElMessage.success('收藏成功')
    state.modelDetail.stars = res.data.stars
  } catch (error) {
    console.error('收藏失败:', error)
    ElMessage.error('收藏失败')
  }
}

// 下载模型
const handleDownload = async () => {
  try {
    const res = await downloadModel(state.modelDetail.id)
    ElMessage.success('下载成功')
    state.modelDetail.downloads = res.data.downloads
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 下载单个文件
const downloadFile = async (fileName: string) => {
  try {
    // 重置下载状态
    downloadState.visible = true
    downloadState.progress = 0
    downloadState.fileName = fileName
    downloadState.speed = '0 KB/s'
    downloadState.startTime = Date.now()
    downloadState.loaded = 0

    const response = await downloadModelFile(state.modelDetail.id, fileName, (progress) => {
      downloadState.progress = progress
      const elapsed = (Date.now() - downloadState.startTime) / 1000
      downloadState.speed = formatSpeed(downloadState.loaded, elapsed)
    })

    // 创建下载链接
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    // 更新下载次数
    await downloadModel(state.modelDetail.id)
    state.modelDetail.downloads += 1

    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  } finally {
    downloadState.visible = false
  }
}

// 处理回复评论
const handleReply = (comment: any) => {
  state.replyTo = comment
  // 滚动到评论框
  setTimeout(() => {
    const commentForm = document.querySelector('.comment-form')
    if (commentForm) {
      commentForm.scrollIntoView({ behavior: 'smooth' })
    }
  }, 100)
}

// 取消回复
const cancelReply = () => {
  state.replyTo = null
}

// 提交评论
const submitComment = async () => {
  if (!state.commentContent.trim()) {
    ElMessage.warning('评论内容不能为空')
    return
  }

  state.submittingComment = true
  try {
    const data: any = {
      model: state.modelDetail.id,
      content: state.commentContent
    }

    // 如果是回复，添加父评论ID
    if (state.replyTo) {
      data.parent_id = state.replyTo.id
    }

    await createComment(data)
    ElMessage.success('评论发表成功')
    
    // 重置表单
    state.commentContent = ''
    state.replyTo = null
    
    // 重新加载评论
    await loadComments()
  } catch (error) {
    console.error('评论发表失败:', error)
    ElMessage.error('评论发表失败')
  } finally {
    state.submittingComment = false
  }
}

// 删除评论
const handleDeleteComment = async (commentId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteComment(commentId)
    ElMessage.success('评论删除成功')
    
    // 重新加载评论
    await loadComments()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除评论失败:', error)
      ElMessage.error('删除评论失败')
    }
  }
}

// 下载版本所有文件
const downloadAllFiles = async (version: any) => {
  // 添加下载状态
  version.downloading = true;
  
  try {
    const response = await downloadVersionAllFiles(version.id);
    
    if (response.data.url) {
      // 直接获取到下载链接，开始下载
      startDownload(response.data.url, response.data.filename || `${state.modelDetail.name}_${version.version_number}.zip`);
      ElMessage.success('文件下载已开始');
    } else {
      ElMessage.error('获取下载链接失败');
    }
  } catch (error) {
    console.error('打包下载失败:', error);
    ElMessage.error('打包下载失败');
  } finally {
    version.downloading = false;
  }
};

// 开始下载文件
const startDownload = (url: string, filename: string) => {
  // 创建一个a标签，模拟点击下载
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 跳转到数据集详情页
const navigateToDataset = (datasetId: number) => {
  router.push(`/dataset/datasets/${datasetId}`);
}

onMounted(() => {
  getModelDetail()
})
</script>

<style lang="scss" scoped>
.app-container {
  .loading-container {
    padding: 20px;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  .ml-2 {
    margin-left: 8px;
  }

  .model-info-card {
    margin-bottom: 16px;
  }

  .tab-card {
    margin-bottom: 10px;
  }

  .content-card {
    min-height: 400px;
  }

  .model-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .model-title {
      h1 {
        margin: 0 0 10px 0;
      }
    }
  }

  .model-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #606266;
    }
  }

  .model-metrics {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .metrics-section {
      flex: 1;
      min-width: 300px;

      h3 {
        margin-top: 0;
        margin-bottom: 16px;
      }
    }
  }

  .model-detail-content {
    padding: 10px 0;
    
    .description-content {
      h3 {
        margin-bottom: 16px;
        font-size: 18px;
        color: #1F2A44;
      }
      
      :deep(.md-preview) {
        border: none;
        background-color: transparent;
        
        .md-editor-preview-wrapper {
          padding: 0;
          
          code {
            background-color: #f8f8f8;
            padding: 2px 4px;
            border-radius: 4px;
            color: #d56161;
          }
          
          pre {
            background-color: #f8f8f8;
            border-radius: 4px;
            padding: 12px;
          }
          
          img {
            max-width: 100%;
            border-radius: 4px;
          }
          
          h1, h2, h3 {
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 8px;
            margin-top: 24px;
          }
          
          table {
            border-collapse: collapse;
            margin: 16px 0;
            
            th, td {
              border: 1px solid #ebeef5;
              padding: 8px 16px;
            }
            
            th {
              background-color: #f8f8f8;
            }
          }
          
          blockquote {
            border-left: 4px solid #6B48FF;
            padding-left: 16px;
            color: #606266;
            background-color: #f9f9fa;
            padding: 8px 16px;
            margin: 16px 0;
            border-radius: 4px;
          }
        }
      }
    }
  }

  .model-files {
    .el-table {
      margin-bottom: 20px;
    }
  }

  .model-comments {
    .comments-list {
      margin-bottom: 30px;
    }

    .comment-form {
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 4px;

      h3 {
        margin-top: 0;
        margin-bottom: 16px;
      }

      .reply-info {
        background-color: #ecf5ff;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .form-actions {
        margin-top: 16px;
        text-align: right;
      }
    }
  }

  .download-progress {
    padding: 20px;

    .file-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      color: #606266;
    }

    .download-speed {
      margin-top: 12px;
      text-align: right;
      color: #909399;
      font-size: 14px;
    }
  }

  .model-versions {
    .version-tabs {
      :deep(.el-tabs__header) {
        margin-bottom: 15px;
      }
      
      :deep(.el-tabs__content) {
        padding: 5px;
      }
    }
    
    .version-info {
      margin-bottom: 20px;
    }
    
    // 文件浏览器样式
    .file-browser {
      margin-top: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
      width: 100%;
      min-height: 300px;

      h3 {
        margin: 15px 15px 10px;
        font-size: 16px;
        color: #303133;
      }
    }

    .file-browser-list {
      padding: 10px;
      max-height: 500px;
      overflow-y: auto;
      min-height: 200px;
      width: 100%;
    }

    .file-browser-row {
      width: 100%;
      height: 45px;
      padding: 8px 15px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: background-color 0.2s;
      margin-bottom: 5px;
      border-bottom: 1px solid #f0f0f0;
      justify-content: space-between;
      
      &:hover {
        background-color: #f5f7fa;
      }
    }

    .file-item-icon-name {
      display: flex;
      align-items: center;
      min-width: 0;
      flex: 3;
    }

    .file-browser-item-icon {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      color: #606266;
    }

    .file-browser-item-name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
    }

    .file-browser-item-size {
      width: 100px;
      text-align: right;
      color: #909399;
      font-size: 13px;
      margin-right: 10px;
    }

    .file-browser-item-time {
      width: 180px;
      text-align: right;
      color: #909399;
      font-size: 13px;
      margin-right: 10px;
    }

    .file-browser-item-action {
      width: 100px;
      display: flex;
      justify-content: flex-end;
      gap: 5px;
    }

    .file-browser-empty {
      width: 100%;
      padding: 50px 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .file-browser-header-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      background-color: #f9f9fa;
      border-bottom: 1px solid #e0e0e0;
      width: 100%;
    }

    .file-browser-breadcrumb {
      flex: 1;
      
      :deep(.el-breadcrumb__item) {
        cursor: pointer;
      }
    }

    .file-browser-header {
      font-weight: 600;
      color: #303133;
      border-bottom: 2px solid #e0e0e0;
      height: 40px;
      background-color: #f9f9fa;
      cursor: default;
    }

    // 版本关键资源样式
    .version-resources {
      margin-bottom: 20px;

      h3 {
        margin: 15px 0 10px;
        font-size: 16px;
        color: #303133;
      }

      .resource-card {
        border-radius: 8px;
        
        :deep(.el-card__body) {
          padding: 15px;
        }
      }

      .resource-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
      }

      .resource-item {
        display: flex;
        align-items: flex-start;
        padding: 12px;
        border-radius: 6px;
        background-color: #f9fafc;
        border: 1px solid #ebeef5;
        
        .resource-icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #ecf5ff;
          border-radius: 8px;
          margin-right: 12px;
          color: #409eff;
          font-size: 20px;
        }
        
        .resource-content {
          flex: 1;
          min-width: 0;
        }
        
        .resource-title {
          font-weight: 600;
          color: #303133;
          margin-bottom: 8px;
        }
        
        .resource-value {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 8px;
          
          .truncate-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 300px;
            color: #606266;
          }
        }
        
        .resource-command {
          background-color: #f0f2f5;
          border-radius: 4px;
          padding: 8px 12px;
          
          .command-title {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .command-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            
            code {
              font-family: monospace;
              color: #606266;
              font-size: 13px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  // 表格中的资源项
  .resource-item {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .truncate-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px;
    }
  }

  .datasets-container {
    .dataset-tags {
      display: inline-flex;
      flex-wrap: wrap;
      margin-left: 4px;
      gap: 8px;
      
      .dataset-tag {
        cursor: pointer;
        
        &:hover {
          opacity: 0.8;
          color: var(--el-color-primary);
        }
      }
    }
  }
}

// 部署指南对话框样式
.deployment-guide-dialog {
  :deep(.el-dialog__header) {
    padding: 15px 20px;
    margin-right: 0;
    border-bottom: 1px solid #ebeef5;
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  .deployment-guide-content {
    .guide-section {
      margin-bottom: 20px;
      
      h3 {
        margin-top: 0;
        margin-bottom: 16px;
        font-size: 16px;
        color: #303133;
      }
      
      .guide-step {
        margin-bottom: 20px;
        
        .step-title {
          font-weight: 600;
          margin-bottom: 10px;
          color: #303133;
        }
        
        .step-code {
          margin-bottom: 10px;
          
          :deep(.el-input__wrapper) {
            padding-right: 0;
          }
          
          :deep(.el-input__inner) {
            font-family: monospace;
            color: #606266;
          }
          
          :deep(.el-input-group__append) {
            padding: 0;
            
            .el-button {
              border: none;
              height: 100%;
              margin: 0;
            }
          }
          
          :deep(.el-textarea__inner) {
            font-family: monospace;
            color: #606266;
            padding: 10px;
            line-height: 1.5;
          }
        }
        
        .step-note {
          color: #909399;
          font-size: 13px;
          margin-top: 5px;
          padding-left: 4px;
        }
        
        .step-action {
          margin-top: 10px;
        }
      }
    }
  }
}
</style> 