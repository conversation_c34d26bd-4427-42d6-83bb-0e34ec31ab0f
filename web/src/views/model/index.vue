<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧分类树 -->
      <el-col :span="6" class="left-card">
        <el-card shadow="always">
          <template #header>
            <div class="card-header">
              <span class="card-header-text">模型分类</span>
            </div>
          </template>
          
          <!-- 已选择的类别 -->
          <div class="selected-categories" v-if="state.selectedCategories.length > 0">
            <div class="selected-title">
              <span>已选类别：</span>
              <el-button 
                type="primary" 
                link 
                size="small" 
                @click="clearSelectedCategories"
              >
                <SvgIcon name="elementDelete" />
                清空
              </el-button>
            </div>
            <div class="selected-tags">
              <el-tag
                v-for="category in state.selectedCategories"
                :key="category.id"
                closable
                size="small"
                @close="removeSelectedCategory(category)"
              >
                {{ category.name }}
              </el-tag>
            </div>
          </div>
          
          <!-- 顶级分类导航 -->
          <div class="top-category-nav">
            <el-tabs v-model="state.activeRootCategory" class="root-tabs">
              <el-tab-pane 
                v-for="category in state.rootCategories" 
                :key="category.id"
                :label="category.name"
                :name="category.id.toString()"
              ></el-tab-pane>
              <!-- 当没有顶级分类时显示提示 -->
              <div v-if="!state.rootCategories || state.rootCategories.length === 0" class="empty-category-tip">
                暂无模型分类，请先<router-link to="/model/category">创建分类</router-link>
              </div>
            </el-tabs>
          </div>
          
          <!-- 分类树 -->
          <div class="category-tree-container">
            <el-tree
              ref="treeRef"
              :data="state.currentCategoryTree"
              :props="state.defaultProps"
              @node-click="handleNodeClick"
              default-expand-all
              highlight-current
              v-if="state.currentCategoryTree.length > 0"
              node-key="id"
              class="category-tree"
            >
              <!-- 自定义节点渲染 -->
              <template #default="{ node, data }">
                <div class="custom-tree-node">
                  <SvgIcon 
                    v-if="node.isLeaf" 
                    name="elementFolderOpened" 
                    class="folder-icon"
                  />
                  <SvgIcon 
                    v-else 
                    name="elementFolder" 
                    class="folder-icon"
                  />
                  <span class="node-label">{{ node.label }}</span>
                  <span class="node-count" v-if="data.count">{{ data.count }}</span>
                </div>
              </template>
            </el-tree>
            <div v-else class="empty-tree">
              <el-empty description="暂无子分类" :image-size="60" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧内容区 -->
      <el-col :span="18">
        <!-- 搜索栏 -->
        <el-card shadow="always" class="search-card">
          <div class="search-header">
            <el-form :model="state.queryParams" ref="queryForm" :inline="true">
              <el-form-item label="模型名称" prop="search">
                <el-input
                  v-model="state.queryParams.search"
                  placeholder="请输入模型名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" plain @click="handleQuery">
                  <SvgIcon name="elementSearch"/>
                  搜索
                </el-button>
                <el-button @click="resetQuery">
                  <SvgIcon name="elementRefresh"/>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
            <!-- 添加任务语义检索按钮 -->
            <el-button type="success" @click="openSemanticSearchDialog">
              <SvgIcon name="elementConnection"/>
              任务语义检索
            </el-button>
          </div>
        </el-card>

        <!-- 任务语义检索弹窗 -->
        <el-dialog
          v-model="semanticSearchVisible"
          title="任务语义检索"
          width="550px"
          destroy-on-close
        >
          <el-form :model="semanticSearchForm" label-position="top">
            <el-form-item label="任务需求描述" required>
              <el-input
                v-model="semanticSearchForm.description"
                type="textarea"
                :rows="4"
                placeholder="请输入具体的任务需求描述..."
              />
            </el-form-item>
            <el-form-item label="输入">
              <el-input
                v-model="semanticSearchForm.input"
                type="textarea"
                :rows="3"
                placeholder="请描述任务的输入内容..."
              />
            </el-form-item>
            <el-form-item label="输出">
              <el-input
                v-model="semanticSearchForm.output"
                type="textarea"
                :rows="3"
                placeholder="请描述期望的输出内容..."
              />
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="semanticSearchVisible = false">取消</el-button>
              <el-button type="primary" @click="submitSemanticSearch" :loading="semanticSearchLoading">
                提交检索
              </el-button>
            </span>
          </template>
        </el-dialog>

        <!-- 语义检索结果弹窗 -->
        <el-dialog
          v-model="searchResultVisible"
          title="检索结果"
          width="800px"
          destroy-on-close
        >
          <div v-if="semanticSearchLoading" class="search-loading">
            <el-skeleton :rows="6" animated />
          </div>
          <div v-else-if="semanticSearchResults.length === 0" class="empty-result">
            <el-empty description="未找到匹配的模型" />
          </div>
          <div v-else class="search-results">
            <el-card v-for="(result, index) in semanticSearchResults" :key="index" class="result-card">
              <div class="result-header">
                <h3 class="result-title">{{ result.name }}</h3>
                <div class="result-score">
                  <span class="score-label">相关度:</span>
                  <el-rate
                    v-model="result.score"
                    disabled
                    text-color="#ff9900"
                    score-template="{value}"
                  />
                </div>
              </div>
              <div class="result-content">
                <p>{{ result.description }}</p>
              </div>
              <div class="result-footer">
                <el-button type="primary" size="small" @click="handleViewDetail(result)">
                  查看详情
                </el-button>
              </div>
            </el-card>
          </div>
        </el-dialog>

        <!-- 模型列表 -->
        <el-row :gutter="12" class="model-list">
          <el-col :xs="24" :sm="12" :md="8" :lg="8" v-for="model in state.modelList" :key="model.id">
            <div class="model-card-wrapper">
              <el-card class="model-card" shadow="hover" @click="handleViewDetail(model)">
                <!-- 模型卡片顶部：标题和标签 -->
                <div class="model-card-header">
                  <div class="model-title-wrapper">
                    <h3 class="model-title" :title="model.name">{{ model.name }}</h3>
                    <span class="new-badge" v-if="model.isNew">NEW</span>
                  </div>
                  <div class="model-categories">
                    <el-tag 
                      v-for="category in model.categories.slice(0, 3)" 
                      :key="category.id" 
                      size="small"
                      class="category-tag">
                      {{ category.name }}
                    </el-tag>
                    <span class="more-categories" v-if="model.categories.length > 3">+{{ model.categories.length - 3 }}</span>
                  </div>
                </div>
                
                <!-- 模型卡片中部：描述 -->
                <div class="model-description">
                  <p class="description-text" :title="model.description">{{ model.description || '暂无描述' }}</p>
                </div>
                
                <!-- 模型卡片底部：元数据和统计信息 -->
                <div class="model-card-footer">
                  <div class="model-meta">
                    <div class="meta-item creator" :title="model.creator_name">
                      <SvgIcon name="elementUser" class="meta-icon"/> 
                      <span class="meta-text">{{ model.creator_name }}</span>
                    </div>
                    <div class="meta-item date" :title="formatDateFull(model.create_datetime)">
                      <SvgIcon name="elementCalendar" class="meta-icon"/> 
                      <span class="meta-text">{{ formatDate(model.create_datetime) }}</span>
                    </div>
                  </div>
                  
                  <div class="model-stats">
                    <div class="stats-item stars">
                      <SvgIcon name="elementStar" class="stats-icon"/>
                      <span class="stats-count">{{ formatCount(model.stars) }}</span>
                    </div>
                    <div class="stats-item downloads">
                      <SvgIcon name="elementDownload" class="stats-icon"/>
                      <span class="stats-count">{{ formatCount(model.downloads) }}</span>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>

        <!-- 无数据时的空状态 -->
        <div class="empty-state" v-if="state.modelList.length === 0 && !state.loading">
          <el-empty 
            description="暂无符合条件的模型" 
            :image-size="200">
          </el-empty>
        </div>

        <!-- 加载状态 -->
        <div class="loading-state" v-if="state.loading">
          <el-row :gutter="12">
            <el-col :xs="24" :sm="12" :md="8" :lg="8" v-for="i in 6" :key="i">
              <el-skeleton animated>
                <template #template>
                  <div class="skeleton-card">
                    <div class="skeleton-header">
                      <el-skeleton-item variant="text" style="width: 70%; height: 20px" />
                      <el-skeleton-item variant="text" style="width: 20%; height: 16px" />
                    </div>
                    <div class="skeleton-categories">
                      <el-skeleton-item variant="text" style="width: 30%; height: 16px" />
                      <el-skeleton-item variant="text" style="width: 30%; height: 16px" />
                    </div>
                    <div class="skeleton-content">
                      <el-skeleton-item variant="text" style="width: 90%; height: 16px" />
                      <el-skeleton-item variant="text" style="width: 80%; height: 16px" />
                      <el-skeleton-item variant="text" style="width: 60%; height: 16px" />
                    </div>
                    <div class="skeleton-footer">
                      <el-skeleton-item variant="text" style="width: 40%; height: 16px" />
                      <el-skeleton-item variant="text" style="width: 30%; height: 16px" />
                    </div>
                  </div>
                </template>
              </el-skeleton>
            </el-col>
          </el-row>
        </div>

        <!-- 分页 -->
        <div v-show="state.total > 0">
          <el-divider></el-divider>
          <el-pagination
              background
              :total="state.total"
              :current-page="state.queryParams.page"
              :page-size="state.queryParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { getModelList, semanticSearchModels } from '@/api/model/models'
import { getModelCategoryTree } from '@/api/model/categories'
import 'md-editor-v3/lib/style.css';
import { ElMessage } from 'element-plus'

// 路由实例
const router = useRouter()

// 语义检索相关的响应式变量
const semanticSearchVisible = ref(false)
const searchResultVisible = ref(false)
const semanticSearchLoading = ref(false)
const semanticSearchResults = ref<{id: number; name: string; description: string; score: number}[]>([])
const semanticSearchForm = reactive({
  description: '',
  input: '',
  output: ''
})

// 状态管理
const state = reactive({
  loading: false,
  modelList: [] as any[],
  categoryTree: [] as any[],
  total: 0,
  queryParams: {
    page: 1,
    pageSize: 10,
    category_ids: [] as number[],
    search: undefined,
    current_status: 'online'
  },
  defaultProps: {
    children: 'children',
    label: 'name'
  },
  uploadForm: {
    name: '',
    group: '',
    categoryMap: {} as Record<string, number | null>,
    description: '',
    parameters: {} as Record<string, any>,
    metrics: {} as Record<string, any>,
    files: [] as any[],
  },
  rootCategories: [] as any[],
  activeRootCategory: '',
  currentCategoryTree: [] as any[],
  selectedCategories: [] as any[]
})

/** 查询模型列表 */
const handleQuery = async () => {
  state.loading = true
  try {
    // 构建查询参数
    const params = {
      ...state.queryParams,
      category_ids: state.queryParams.category_ids,
      current_status: state.queryParams.current_status
    }
    
    const res = await getModelList(params)
    state.modelList = res.data.data
    state.total = res.data.total
  } catch (error) {
    console.error('获取模型列表失败:', error)
  } finally {
    state.loading = false
  }
}

/** 重置查询参数 */
const resetQuery = () => {
  state.queryParams.search = undefined
  state.queryParams.category_ids = []
  state.selectedCategories = []
  state.queryParams.page = 1
  handleQuery()
}

/** 获取分类树数据 */
const loadCategoryTree = async () => {
  try {
    const res = await getModelCategoryTree()
    state.categoryTree = res.data
    // 过滤出顶级分类（parent === null的分类）
    state.rootCategories = res.data.filter((category: any) => category.parent === null)
    
    // 确保有顶级分类后再设置activeRootCategory
    if (state.rootCategories && state.rootCategories.length > 0) {
      state.activeRootCategory = state.rootCategories[0].id.toString()
      // 获取当前选中顶级分类的子分类
      updateCurrentCategoryTree()
    } else {
      // 没有顶级分类，清空当前树
      state.activeRootCategory = ''
      state.currentCategoryTree = []
      console.warn('没有找到任何顶级分类')
    }
  } catch (error) {
    console.error('获取分类树失败:', error)
  }
}

/** 更新当前显示的分类树 */
const updateCurrentCategoryTree = () => {
  if (state.activeRootCategory) {
    // 查找当前选中的顶级分类
    const activeRootCategory = state.rootCategories.find(
      (c: any) => c.id === parseInt(state.activeRootCategory)
    )
    
    // 如果找到了且有children属性
    if (activeRootCategory && activeRootCategory.children) {
      state.currentCategoryTree = activeRootCategory.children
    } else {
      console.warn('没有找到任何子分类')
      state.currentCategoryTree = []
    }
  } else {
    console.warn('没有选中任何顶级分类')
    state.currentCategoryTree = []
  }
}

/** 分类节点点击事件 */
const handleNodeClick = (data: any) => {
  // 检查是否已经选择了这个类别
  const index = state.selectedCategories.findIndex((c: any) => c.id === data.id)
  
  // 如果没选择，则添加到已选列表
  if (index === -1) {
    // 确保data对象包含必要的属性
    state.selectedCategories.push({
      id: data.id,
      name: data.name
    })
    
    // 更新查询参数中的分类ID数组
    state.queryParams.category_ids = state.selectedCategories.map(c => c.id)
    
    // 重置到第一页并重新查询
    state.queryParams.page = 1
    handleQuery()
  }
}

/** 查看模型详情 */
const handleViewDetail = (model: any) => {
  router.push(`/model/models/${model.id}`)
}

/** 分页大小改变 */
const handleSizeChange = (val: number) => {
  state.queryParams.pageSize = val
  handleQuery()
}

/** 页码改变 */
const handleCurrentChange = (val: number) => {
  state.queryParams.page = val
  handleQuery()
}

// 页面加载时
onMounted(() => {
  loadCategoryTree()
  handleQuery()
})

// 监听activeRootCategory的变化
watch(() => state.activeRootCategory, (newVal) => {
  if (newVal) {
    updateCurrentCategoryTree()
  }
})

// 移除已选类别
const removeSelectedCategory = (category: any) => {
  // 从已选列表中移除
  state.selectedCategories = state.selectedCategories.filter((c: any) => c.id !== category.id)
  
  // 更新查询参数中的分类ID数组
  state.queryParams.category_ids = state.selectedCategories.map(c => c.id)
  
  // 重置到第一页并重新查询
  state.queryParams.page = 1
  handleQuery()
}

// 清空已选类别
const clearSelectedCategories = () => {
  state.selectedCategories = []
  state.queryParams.category_ids = []
  state.queryParams.page = 1
  handleQuery()
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
}

// 格式化完整日期（包含时间）
const formatDateFull = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' });
}

// 格式化数字（如1000->1k）
const formatCount = (count: number) => {
  if (!count && count !== 0) return '0';
  if (count < 1000) return count.toString();
  return (count / 1000).toFixed(1) + 'k';
}

// 打开任务语义检索对话框
const openSemanticSearchDialog = () => {
  semanticSearchVisible.value = true;
}

// 提交任务语义检索
const submitSemanticSearch = async () => {
  // 验证描述是否填写
  if (!semanticSearchForm.description.trim()) {
    ElMessage.error('任务需求描述不能为空');
    return;
  }
  
  semanticSearchLoading.value = true;
  try {
    // 调用语义检索API
    const res = await semanticSearchModels({
      description: semanticSearchForm.description,
      input: semanticSearchForm.input,
      output: semanticSearchForm.output
    });
    
    // 处理返回结果
    if (res.data && Array.isArray(res.data)) {
      semanticSearchResults.value = res.data.map((item: any) => ({
        id: item.id,
        name: item.name,
        description: item.description,
        score: item.similarity_score*5 || 0
      }));
    } else {
      // 如果接口尚未实现，使用模拟数据
      console.warn('使用模拟数据，实际开发中请替换为真实API数据');
      semanticSearchResults.value = [
        {
          id: 1,
          name: '智能文本分类模型',
          description: '基于深度学习的文本分类模型，支持多语言分类任务。',
          score: 4.5
        },
        {
          id: 2,
          name: '自然语言处理通用模型',
          description: '支持多种NLP任务的通用模型，包括文本分类、实体识别等。',
          score: 3.8
        },
        {
          id: 3,
          name: '语义相似度计算模型',
          description: '计算文本之间语义相似度的专用模型，适用于搜索引擎、推荐系统等场景。',
          score: 4.2
        }
      ];
    }
    
    semanticSearchVisible.value = false;
    searchResultVisible.value = true;
  } catch (error) {
    console.error('任务语义检索失败:', error);
  } finally {
    semanticSearchLoading.value = false;
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .left-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    position: sticky;
    top: 24px;
    min-height: 600px;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px;
    }

    .card-header {
      border-bottom: 1px solid #EBEEF5;
      margin-bottom: 16px;
      .card-header-text {
        font-size: 16px;
        font-weight: 600;
        color: #1F2A44;
        display: flex;
        align-items: center;
        
        &::before {
          content: '';
          width: 4px;
          height: 16px;
          background-color: #6B48FF;
          margin-right: 8px;
          border-radius: 2px;
        }
      }
    }
  }

  .selected-categories {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #e0e0e0;

    .selected-title {
      font-weight: 500;
      margin-bottom: 8px;
      color: #1F2A44;
      display: flex;
      justify-content: space-between;
      align-items: center;

      :deep(.el-button) {
        padding: 0;
        height: auto;
        font-size: 13px;
        
        .svg-icon {
          margin-right: 4px;
          font-size: 14px;
        }
        
        &:hover {
          opacity: 0.8;
        }
      }
    }

    .selected-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      :deep(.el-tag) {
        background-color: #f0f7ff;
        color: #1F2A44;
        font-size: 12px;
        border: 1px solid #d9ecff;
        border-radius: 4px;
        padding: 3px 8px;
        margin-bottom: 4px;
        
        .el-tag__close {
          color: #409EFF;
          
          &:hover {
            background-color: #409EFF;
            color: #fff;
          }
        }
      }
    }
  }

  .root-tabs {
    width: 100%;
    margin-bottom: 16px;
    
    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
    
    :deep(.el-tabs__nav) {
      width: 100%;
    }
    
    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      font-size: 14px;
      padding: 0 10px;
    }
    
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
    }
    
    :deep(.el-tabs__active-bar) {
      background-color: #6B48FF;
    }
  }

  .category-tree-container {
    flex: 1;
    min-height: 350px;
    overflow-y: auto;
    padding: 12px 6px;
    border-radius: 8px;
    background-color: #fff;
    border: 1px solid #f0f0f0;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
      
      &:hover {
        background-color: #c0c4cc;
      }
    }
    
    .category-tree {
      flex: 1;
      min-height: 300px;
      background-color: transparent;
      
      :deep(.el-tree-node) {
        margin: 4px 0;
      }
      
      :deep(.el-tree-node__content) {
        height: 36px;
        border-radius: 6px;
        transition: all 0.2s ease;
        padding: 0 12px;
        
        &:hover {
          background-color: #f5f1ff;
        }
        
        &.is-current {
          background-color: #efe7ff;
          color: #6B48FF;
          font-weight: 500;
        }
      }
      
      :deep(.el-tree-node__label) {
        font-size: 14px;
      }
      
      :deep(.el-tree-node__expand-icon) {
        color: #6B48FF;
        
        &.is-leaf {
          color: transparent;
        }
      }
    }
    
    .custom-tree-node {
      display: flex;
      align-items: center;
      width: 100%;
      font-size: 14px;
      
      .folder-icon {
        margin-right: 8px;
        width: 16px;
        height: 16px;
        color: #6B48FF;
      }
      
      .node-label {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .node-count {
        margin-left: 8px;
        background-color: #f0f7ff;
        color: #409EFF;
        padding: 0 6px;
        border-radius: 10px;
        font-size: 12px;
      }
    }
  }

  .empty-category-tip {
    font-size: 14px;
    color: #909399;
    margin: 10px 0;
    text-align: center;
    
    a {
      color: #6B48FF;
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .empty-tree {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 300px;
  }

  .search-card {
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .search-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    :deep(.el-form-item) {
      margin-bottom: 0;

      .el-form-item__label {
        color: #1F2A44;
        font-size: 14px;
        font-weight: 500;
      }

      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 0 0 1px #E4E7ED;
        
        &:hover {
          box-shadow: 0 0 0 1px #6B48FF;
        }
        
        &.is-focus {
          box-shadow: 0 0 0 1px #6B48FF;
        }
      }
    }

    :deep(.el-button) {
      border-radius: 8px;
      font-size: 14px;
      padding: 8px 20px;
      height: 40px;
    }
  }

  .form-tip {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
  }

  .model-list {
    margin-top: 12px;
  }

  .model-card-wrapper {
    margin-bottom: 16px;
    height: 100%;
  }

  .model-card {
    height: 210px;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    border: 1px solid #EAECF0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 16px rgba(107, 72, 255, 0.15);
      border-color: #6B48FF;
      
      .model-title {
        color: #6B48FF;
      }
    }
    
    :deep(.el-card__body) {
      padding: 0;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
    .model-card-header {
      padding: 12px 16px 8px;
      border-bottom: 1px solid #F2F4F7;
      
      .model-title-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        
        .model-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #101828;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 90%;
        }
        
        .new-badge {
          margin-left: 8px;
          background-color: #ECFDF3;
          color: #027A48;
          padding: 2px 6px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 500;
          flex-shrink: 0;
        }
      }
      
      .model-categories {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 4px;
        min-height: 24px;
        
        .category-tag {
          background-color: #F9F5FF;
          border-color: #F9F5FF;
          color: #6941C6;
          font-weight: normal;
          border-radius: 16px;
          padding: 0 8px;
          height: 20px;
          line-height: 18px;
        }
        
        .more-categories {
          font-size: 12px;
          color: #6941C6;
          background-color: #F9F5FF;
          padding: 0 6px;
          border-radius: 16px;
          height: 20px;
          line-height: 20px;
        }
      }
    }
    
    .model-description {
      flex: 1;
      padding: 10px 16px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .model-code {
        margin-bottom: 6px;
        
        .code-tag {
          font-family: 'Courier New', monospace;
          border-radius: 4px;
          padding: 0 6px;
          height: 22px;
          line-height: 20px;
          font-size: 12px;
          background-color: #f5f7fa;
          border-color: #e4e7ed;
          transition: all 0.2s ease;
        }
      }
      
      .description-text {
        color: #475467;
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        max-height: 80px; /* 3 lines * 1.5 line height * 14px font size */
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          right: 0;
          width: 30%;
          height: 1.5em;
          background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
          pointer-events: none;
        }
      }
    }
    
    .model-card-footer {
      padding: 10px 16px;
      border-top: 1px solid #F2F4F7;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #FAFBFC;
      
      .model-meta {
        display: flex;
        gap: 12px;
        
        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
          
          .meta-icon {
            color: #667085;
            font-size: 14px;
          }
          
          .meta-text {
            color: #667085;
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80px;
          }
          
          &.creator .meta-text {
            font-weight: 500;
            color: #6941C6;
          }
        }
      }
      
      .model-stats {
        display: flex;
        gap: 12px;
        
        .stats-item {
          display: flex;
          align-items: center;
          gap: 4px;
          
          .stats-icon {
            color: #667085;
            font-size: 14px;
          }
          
          .stats-count {
            color: #667085;
            font-size: 12px;
            font-weight: 500;
          }
          
          &.stars {
            .stats-icon {
              color: #F79009;
            }
            .stats-count {
              color: #F79009;
            }
          }
        }
      }
    }
  }
  
  .empty-state {
    margin: 60px 0;
    text-align: center;
    
    :deep(.el-empty__description) {
      margin-top: 16px;
      font-size: 16px;
      color: #667085;
    }
    
    .el-button {
      margin-top: 24px;
      padding: 10px 24px;
      font-weight: 600;
      border-radius: 8px;
    }
  }
  
  .loading-state {
    margin: 24px 0;
    
    .skeleton-card {
      height: 210px;
      padding: 16px;
      border-radius: 8px;
      border: 1px solid #EAECF0;
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
      background-color: #fff;
      
      .skeleton-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }
      
      .skeleton-categories {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
      }
      
      .skeleton-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      
      .skeleton-footer {
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
      }
    }
  }

  .category-group {
    margin-bottom: 10px;

    .root-category-label {
      font-weight: 500;
      margin-bottom: 5px;
    }

    .category-cascader {
      width: 100%;
    }
  }

  .category-select-container {
    max-height: 250px;
    overflow-y: auto;
    padding: 10px;
    border-radius: 8px;
    background-color: #f9f9f9;
    margin-bottom: 10px;
  }
  
  // 语义检索相关样式
  .search-loading {
    padding: 20px;
  }
  
  .empty-result {
    text-align: center;
    padding: 40px 0;
  }
  
  .search-results {
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .result-card {
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 200px;
    display: flex;
    flex-direction: column;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(107, 72, 255, 0.1);
    }
    
    :deep(.el-card__body) {
      padding: 16px;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
      
      .result-title {
        font-size: 16px;
        font-weight: 600;
        color: #1F2A44;
        margin: 0;
      }
      
      .result-score {
        display: flex;
        align-items: center;
        
        .score-label {
          font-size: 14px;
          color: #606266;
          margin-right: 8px;
        }
      }
    }
    
    .result-content {
      color: #606266;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 16px;
      flex: 1;
      overflow: hidden;
      
      p {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 0;
      }
    }
    
    .result-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: auto;
    }
  }
  
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    
    .el-dialog__header {
      background-color: #f8f9fa;
      padding: 16px 20px;
      margin: 0;
      border-bottom: 1px solid #ebeef5;
      
      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style>