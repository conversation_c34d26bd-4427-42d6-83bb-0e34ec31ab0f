<template>
  <div class="app-container">
    <el-card shadow="always">
      <!-- 查询 -->
      <el-form
          :model="state.queryParams"
          ref="queryForm"
          :inline="true"
          label-width="68px"
      >
        <el-form-item label="关键词" prop="search">
          <el-input
              placeholder="请输入模型名称/描述模糊查询"
              clearable
              @keyup.enter="handleQuery"
              style="width: 240px"
              v-model="state.queryParams.search"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
              v-model="state.queryParams.current_status"
              @change="handleQuery"
              placeholder="模型状态"
              clearable
              style="width: 240px"
          >
            <el-option
                v-for="dict in state.statusOptions"
                :key="dict.dict_value"
                :label="dict.dict_label"
                :value="dict.dict_value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="handleQuery">
            <SvgIcon name="elementSearch"/>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <SvgIcon name="elementRefresh"/>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="card-header-text">模型列表</span>
          <div>
            <el-button
                type="primary"
                plain
                v-auth="'model:maintenance:add'"
                @click="onOpenAddModule"
            >
              <SvgIcon name="elementPlus"/>
              新增
            </el-button>
            <el-button
                type="danger"
                plain
                v-auth="'model:maintenance:delete'"
                :disabled="state.multiple"
                @click="onTabelRowDel"
            >
              <SvgIcon name="elementDelete"/>
              删除
            </el-button>
          </div>
        </div>
      </template>
      <!--数据表格-->
      <el-table
          v-loading="state.loading"
          :data="state.tableData"
          @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center"/>
        <!-- <el-table-column label="模型ID" align="center" prop="id"/>
        <el-table-column label="模型编码" align="center" prop="code">
          <template #default="scope">
            <el-tag type="info" effect="plain" v-if="scope.row.code">{{ scope.row.code }}</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column> -->
        <el-table-column label="模型名称" align="center" prop="name"/>
        <el-table-column label="项目组" align="center" prop="group" width="150"/>
        <el-table-column label="模型分类" align="center">
          <template #default="scope">
            <el-tag 
              v-for="category in scope.row.categories" 
              :key="category.id" 
              size="small"
              style="margin-right: 5px; margin-bottom: 5px">
              {{ category.name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建者" align="center" prop="creator_name" width="100"/>
        <el-table-column label="创建时间" align="center" prop="create_datetime"/>
        <el-table-column label="存储路径" align="center" prop="minio_path"/>
        <el-table-column
            label="状态"
            align="center"
            prop="status"
            width="120"
        >
          <template #default="scope">
            <el-tag
                v-if="scope.row.status === 'dev_done'"
                type="info"
                disable-transitions
            >待训练</el-tag>
            <el-tag
                v-else-if="scope.row.status === 'train_done'"
                type="warning"
                disable-transitions
            >待测试</el-tag>
            <el-tag
                v-else-if="scope.row.status === 'test_pass'"
                type="success"
                disable-transitions
            >测试通过</el-tag>
            <el-tag
                v-else-if="scope.row.status === 'test_fail'"
                type="danger"
                disable-transitions
            >测试未通过</el-tag>
            <el-tag
                v-else-if="scope.row.status === 'online'"
                type="success"
                effect="dark"
                disable-transitions
            >已上架</el-tag>
            <el-tag
                v-else-if="scope.row.status === 'offline'"
                type="info"
                effect="dark"
                disable-transitions
            >已下架</el-tag>
            <el-tag
                v-else
                type="info"
                disable-transitions
            >{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="120"
        >
          <template #default="scope">
            <el-popover placement="left">
              <template #reference>
                <el-button type="primary" circle>
                  <SvgIcon name="elementStar"/>
                </el-button>
              </template>
              <div>
                <el-button text type="primary" v-auth="'model:maintenance:edit'" @click="onOpenEditModule(scope.row)">
                  <SvgIcon name="elementEdit"/>
                  修改
                </el-button>
              </div>
              <div>
                <el-button text type="primary" v-auth="'model:maintenance:delete'" @click="onTabelRowDel(scope.row)">
                  <SvgIcon name="elementDelete"/>
                  删除
                </el-button>
              </div>
              <div>
                <el-button
                  text
                  type="primary"
                  :disabled="!scope.row.latest_version || scope.row.latest_version.status !== 'test_pass'"
                  @click="onSetOnline(scope.row)"
                >
                  <SvgIcon name="elementUpload"/>
                  上架
                </el-button>
              </div>
              <div>
                <el-button
                  text
                  type="success"
                  :disabled="!scope.row.latest_version || !['test_pass', 'online'].includes(scope.row.latest_version.status) || !scope.row.latest_version.docker_image"
                  @click="onDeploy(scope.row)"
                >
                  <SvgIcon name="elementCpu"/>
                  部署
                </el-button>
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页设置-->
      <div v-show="state.total > 0">
        <el-divider></el-divider>
        <el-pagination
            background
            :total="state.total"
            :current-page="state.queryParams.page"
            :page-size="state.queryParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <!-- 添加或修改模型对话框 -->
    <EditModule ref="editModuleRef" :title="state.title"/>
  </div>
</template>

<script lang="ts" setup>
import {
  ref,
  reactive,
  onMounted,
  getCurrentInstance,
  onUnmounted,
} from "vue";
import {ElMessageBox, ElMessage} from "element-plus";
import {getModelList, deleteModel} from "@/api/model/models";
import EditModule from "./component/editModule.vue";
import { updateVersion } from "@/api/model/versions";
import { useRouter } from 'vue-router';

const {proxy} = getCurrentInstance() as any;
const editModuleRef = ref();
const router = useRouter();
const state = reactive({
  // 遮罩层
  loading: true,
  // 选中数组
  ids: [],
  // 非单个禁用
  single: true,
  // 非多个禁用
  multiple: true,
  // 弹出层标题
  title: "",
  // 模型表格数据
  tableData: [],
  // 总条数
  total: 0,
  // 状态数据字典
  statusOptions: [] as any[],
  // 查询参数
  queryParams: {
    // 页码
    page: 1,
    // 每页大小
    pageSize: 10,
    search: '',
    status: undefined,
    current_status: undefined,
  },
});

/** 查询模型列表 */
const handleQuery = () => {
  state.loading = true;
  getModelList(state.queryParams).then((response) => {
    state.tableData = response.data.data;
    state.tableData.forEach((item: any) => {
      if (item.latest_version) {
        item.status = item.latest_version.status
      } else {
        item.status = 'offline'
      }
    })
    state.total = response.data.total;
    state.loading = false;
  });
};
/** 重置按钮操作 */
const resetQuery = () => {
  state.queryParams.search = '';
  state.queryParams.current_status = undefined;
  handleQuery();
};

const handleCurrentChange = (val: number) => {
  state.queryParams.page = val
  handleQuery()
}
const handleSizeChange = (val: number) => {
  state.queryParams.pageSize = val
  handleQuery()
}

// 打开新增模型弹窗
const onOpenAddModule = () => {
  state.title = "添加模型";
  editModuleRef.value.openDialog({});
};
// 打开编辑模型弹窗
const onOpenEditModule = (row: object) => {
  state.title = "修改模型";
  editModuleRef.value.openDialog(row);
};
/** 删除按钮操作 */
const onTabelRowDel = (row: any) => {
  const modelIds = row.id || state.ids;
  ElMessageBox({
    message: '是否确认删除模型编号为"' + modelIds + '"的数据项?',
    title: "警告",
    showCancelButton: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(function () {
    return deleteModel(modelIds).then(() => {
      handleQuery();
      ElMessage.success("删除成功");
    });
  });
};
// 多选框选中数据
const handleSelectionChange = (selection: any) => {
  state.ids = selection.map((item: any) => item.id);
  state.single = selection.length != 1;
  state.multiple = !selection.length;
};

/** 导出按钮操作 */
const handleExport = () => {
  ElMessageBox({
    message: "是否确认导出所有数据项?",
    title: "警告",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(function () {
        // 这里需要后端提供导出接口
        ElMessage.warning("导出功能待实现");
        return Promise.resolve();
      });
};

const onSetOnline = async (row: any) => {
  if (!row.latest_version) {
    ElMessage.warning("没有可上架的版本！");
    return;
  }
  try {
    await updateVersion(row.latest_version.id, { status: "online" });
    ElMessage.success("上架成功！");
    handleQuery(); // 重新刷新列表
  } catch (e) {
    ElMessage.error("上架失败！");
  }
};

// 部署模型
const onDeploy = (row: any) => {
  if (!row.latest_version) {
    ElMessage.warning("没有可部署的版本！");
    return;
  }

  if (!row.latest_version.docker_image) {
    ElMessage.warning("该模型版本缺少Docker镜像信息，无法部署！");
    return;
  }

  if (!['test_pass', 'online'].includes(row.latest_version.status)) {
    ElMessage.warning("只有测试通过或已上架的模型才能部署！");
    return;
  }

  // 跳转到部署管理页面，并传递模型版本ID
  router.push({
    path: '/deploy/deploy/list',
    query: {
      model_version_id: row.latest_version.id,
      model_name: row.name,
      version_number: row.latest_version.version_number
    }
  });
};

// 页面加载时
onMounted(() => {
  // 查询模型信息
  handleQuery();
  // 查询模型状态数据字典
  proxy.getDicts("model_status").then((response: any) => {
    state.statusOptions = response.data.data;
  }).catch(() => {
    // 如果model_status字典不存在，使用默认状态选项
    state.statusOptions = [
      { dict_value: 'online', dict_label: '上架' },
      { dict_value: 'offline', dict_label: '下架' }
    ];
  });
  proxy.mittBus.on("onEditModelModule", () => {
    handleQuery();
  });
});
// 页面卸载时
onUnmounted(() => {
  proxy.mittBus.off("onEditModelModule");
});
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-header-text {
  font-size: 18px;
  font-weight: bold;
}
</style>
