<template>
  <div class="system-model-container">
    <el-dialog v-model="state.isShowDialog" width="80%" center class="model-upload-dialog">
      <template #header>
        <div style="font-size: large; cursor: move;" class="dialog-drag-header">{{title}}</div>
      </template>
      
      <!-- 步骤条 -->
      <el-steps 
        :active="state.activeStep" 
        finish-status="success" 
        process-status="process" 
        class="upload-steps"
        align-center
      >
        <el-step title="基础信息" :icon="stepOneIcon" />
        <el-step title="模型描述" :icon="stepTwoIcon" />
        <el-step title="版本管理和文件" :icon="stepThreeIcon" />
      </el-steps>
      
      <el-form
        :model="state.ruleForm"
        :rules="state.ruleRules"
        ref="ruleFormRef"
        label-width="120px"
      >
        <!-- 步骤一：基础信息 -->
        <div v-if="state.activeStep === 0">
          <div class="step-description">
            <div class="step-title">第一步：填写模型基本信息</div>
            <div class="step-info">请提供模型的名称、所属项目组、分类信息以及关联数据集</div>
          </div>
          <el-form-item label="模型名称" prop="name">
            <el-input 
              v-model="state.ruleForm.name" 
              placeholder="请输入模型名称"
            />
          </el-form-item>
          <el-form-item label="模型编码" prop="code">
            <el-input 
              v-model="state.ruleForm.code" 
              placeholder="请输入模型编码，不填写系统会自动生成"
              @blur="validateModelCode"
            />
            <div class="form-tip">
              <template v-if="state.codeValidating">
                <el-icon class="is-loading"><Loading /></el-icon> 正在验证编码唯一性...
              </template>
              <template v-else-if="state.codeValidated">
                <template v-if="state.codeValid">
                  <el-icon color="#67C23A"><CircleCheck /></el-icon> 编码可用
                </template>
                <template v-else>
                  <el-icon color="#F56C6C"><CircleClose /></el-icon> 编码已存在，请更换
                </template>
              </template>
              <template v-else>
                用于与外部系统交互的唯一标识，如不填写将自动生成
              </template>
            </div>
          </el-form-item>
          <el-form-item label="项目组" prop="group">
            <el-input v-model="state.ruleForm.group" placeholder="请输入项目组名称" />
          </el-form-item>
          <el-form-item label="关联数据集" prop="dataset_ids">
            <el-select
              v-model="state.ruleForm.dataset_ids"
              multiple
              filterable
              placeholder="请选择关联的数据集"
              style="width: 100%;"
            >
              <el-option
                v-for="item in state.datasetList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
                <span>{{ item.name }}</span>
                <span style="color: #8492a6; font-size: 13px">({{ item.group }})</span>
              </el-option>
            </el-select>
            <div class="form-tip">选择相关的训练数据集，可以多选</div>
          </el-form-item>
          <el-form-item label="模型分类" prop="category_ids">
            <div class="category-select-container">
              <div v-for="root in state.rootCategories" :key="root.id" class="category-group">
                <div class="root-category-label">{{ root.name }}：</div>
                <el-cascader
                  v-model="state.ruleForm.categoryMap[root.id]"
                  :options="[root]"
                  :props="{
                    multiple: true,
                    expandTrigger: 'hover',
                    checkStrictly: false,
                    emitPath: false,
                    children: 'children',
                    label: 'name',
                    value: 'id'
                  }"
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="1"
                  filterable
                  placeholder="请选择分类"
                  class="category-cascader"
                  clearable
                />
              </div>
            </div>
          </el-form-item>
        </div>
        
        <!-- 步骤二：模型描述 -->
        <div v-if="state.activeStep === 1">
          <div class="step-description">
            <div class="step-title">第二步：编写模型描述</div>
            <div class="step-info">请使用Markdown格式详细描述模型的功能、使用场景、使用说明等信息，左侧编辑右侧预览</div>
          </div>
          <el-form-item label="模型描述" prop="description">
            <md-editor
              v-model="state.ruleForm.description"
              :toolbars="['bold', 'underline', 'italic', 'strikeThrough', 'title', 'sub', 'sup', 'quote', 'unorderedList', 'orderedList', 'task', 'codeRow', 'code', 'link', 'image', 'table', 'revoke', 'next', 'save']"
              theme="light" 
              language="zh-CN"
              preview-theme="github"
              height="300px"
              model-value-type="string"
              :preview="true"
              :code-theme="codeTheme"
              class="markdown-editor"
              :show-code-row-number="true"
              placeholder="请输入模型描述（支持Markdown格式）"
            />
          </el-form-item>
        </div>
        
        <!-- 步骤三：模型文件 -->
        <div v-if="state.activeStep === 2">
          <div class="step-description">
            <div class="step-title">第三步：上传模型文件</div>
            <div class="step-info" v-if="state.isEdit">请选择需要上传的模型文件或文件夹，支持批量上传和管理模型版本</div>
            <div class="step-info" v-else>创建模型后，可通过编辑模型来上传文件和管理版本</div>
          </div>
          
          <el-form-item>
            <div class="centered-container">
              <!-- 版本管理部分 -->
              <div class="version-management-section">
                <div class="version-header">
                  <div class="version-title">模型版本列表</div>
                  <div class="version-actions">
                    <el-button type="primary" @click="createNewVersion" :disabled="state.versionLoading">
                      <el-icon><Plus /></el-icon> 新建版本
                    </el-button>
                  </div>
                </div>
                
                <el-skeleton :loading="state.versionLoading" animated>
                  <template #template>
                    <el-skeleton-item variant="p" style="width: 100%; height: 60px; margin-bottom: 8px" />
                    <el-skeleton-item variant="p" style="width: 100%; height: 60px; margin-bottom: 8px" />
                    <el-skeleton-item variant="p" style="width: 100%; height: 60px; margin-bottom: 8px" />
                  </template>
                  <template #default>
                    <div v-if="state.versions.length === 0" class="no-versions">
                      <el-empty description="暂无版本，请点击创建新版本" />
                    </div>
                    <el-table 
                      v-else 
                      class="version-table" 
                      :data="state.versions"
                      highlight-current-row
                      :row-class-name="tableRowClassName"
                    >
                      <el-table-column label="版本编码" min-width="120">
                        <template #default="scope">
                          <span :class="{'current-version': state.currentVersion && state.currentVersion.id === scope.row.id}">
                            {{ scope.row.code }}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="版本号">
                        <template #default="scope">
                          <span :class="{'current-version': state.currentVersion && state.currentVersion.id === scope.row.id}">
                            {{ scope.row.version_number }}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="创建者">
                        <template #default="scope">
                          {{ scope.row.creator_name || '未知' }}
                        </template>
                      </el-table-column>
                      <el-table-column label="文件数">
                        <template #default="scope">
                          {{ scope.row.files ? scope.row.files.length : 0 }}
                        </template>
                      </el-table-column>
                      <el-table-column label="版本说明" min-width="200">
                        <template #default="scope">
                          {{ scope.row.description || '-' }}
                        </template>
                      </el-table-column>
                      <el-table-column label="创建时间" min-width="120">
                        <template #default="scope">
                          {{ formatDate(scope.row.create_datetime) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" min-width="220">
                        <template #default="scope">
                          <el-button size="small" type="primary" plain @click.stop="selectVersionAndSwitchTab(scope.row)">
                            <el-icon><View /></el-icon> 切换版本
                          </el-button>
                          <el-button size="small" type="danger" plain @click.stop="handleDeleteVersion(scope.row)">
                            <el-icon><Delete /></el-icon> 删除版本
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </template>
                </el-skeleton>
              </div>
              
              <!-- 版本文件部分 -->
              <div v-if="state.currentVersion" class="version-files-section">
                <div class="divider">版本文件管理</div>
                
                <div class="version-info">
                  <el-descriptions :column="3" border>
                    <el-descriptions-item label="当前版本">
                      <span class="current-version">{{ state.currentVersion.version_number }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="创建者">
                      {{ state.currentVersion.creator_name || '未知' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="创建时间">
                      {{ formatDate(state.currentVersion.create_datetime) }}
                    </el-descriptions-item>
                  </el-descriptions>
                  
                  <div class="model-status-container">
                    <div class="status-title">模型状态</div>
                    <el-radio-group v-model="state.currentVersion.status" class="status-group">
                      <el-radio label="dev_done">待训练</el-radio>
                      <el-radio label="train_done">待测试</el-radio>
                      <el-radio label="test_pass">测试通过</el-radio>
                      <el-radio label="test_fail">测试未通过</el-radio>
                      <el-radio label="online">已上架</el-radio>
                      <el-radio label="offline">已下架</el-radio>
                    </el-radio-group>
                    
                    <!-- 测试失败原因输入框 -->
                    <div v-if="state.currentVersion && state.currentVersion.status === 'test_fail'" class="failure-reason-container">
                      <div class="reason-title">测试失败原因：</div>
                      <el-input
                        v-model="state.currentVersion.test_failure_reason"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入测试失败原因"
                        maxlength="500"
                        show-word-limit
                      />
                    </div>
                  </div>
                  
                  <div class="docker-image-input">
                    <el-input v-model="state.currentVersion.docker_image" placeholder="请输入Docker镜像地址">
                      <template #prepend>Docker镜像</template>
                    </el-input>
                  </div>
                </div>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-card shadow="never" class="file-card">
                      <template #header><div>模型权重</div></template>
                      <div class="file-card-content">
                        <div v-if="state.uploading.model_weights">
                          <el-progress :percentage="state.uploadProgress.model_weights" />
                        </div>
                        <div v-else-if="state.currentVersion.model_weights_path" class="file-info">
                          <el-icon><Document /></el-icon>
                          <el-tooltip :content="state.currentVersion.model_weights_path" placement="top">
                            <span class="file-name">{{ getFileName(state.currentVersion.model_weights_path) }}</span>
                          </el-tooltip>
                          <el-button
                            type="danger"
                            size="small"
                            circle
                            style="margin-left: 8px;"
                            @click="handleDeleteFile('model_weights')"
                          >
                            <el-icon><Close /></el-icon>
                          </el-button>
                        </div>
                        <div v-else class="file-empty">
                          <el-icon class="empty-icon"><Document /></el-icon>
                          <span class="empty-text">暂无文件</span>
                        </div>
                        <el-upload action="#" :auto-upload="false" :show-file-list="false" :on-change="(file: UploadFile) => handleResourceUpload(file, 'model_weights')" :disabled="state.uploading.model_weights">
                          <el-button type="primary" :loading="state.uploading.model_weights">上传权重文件</el-button>
                        </el-upload>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card shadow="never" class="file-card">
                      <template #header><div>模型文档</div></template>
                      <div class="file-card-content">
                        <div v-if="state.uploading.model_docs">
                          <el-progress :percentage="state.uploadProgress.model_docs" />
                        </div>
                        <div v-else-if="state.currentVersion.model_docs_path" class="file-info">
                          <el-icon><Document /></el-icon>
                          <el-tooltip :content="state.currentVersion.model_docs_path" placement="top">
                            <span class="file-name">{{ getFileName(state.currentVersion.model_docs_path) }}</span>
                          </el-tooltip>
                          <el-button
                            type="danger"
                            size="small"
                            circle
                            style="margin-left: 8px;"
                            @click="handleDeleteFile('model_docs')"
                          >
                            <el-icon><Close /></el-icon>
                          </el-button>
                        </div>
                        <div v-else class="file-empty">
                          <el-icon class="empty-icon"><Document /></el-icon>
                          <span class="empty-text">暂无文件</span>
                        </div>
                        <el-upload action="#" :auto-upload="false" :show-file-list="false" :on-change="(file: UploadFile) => handleResourceUpload(file, 'model_docs')" :disabled="state.uploading.model_docs">
                          <el-button type="primary" :loading="state.uploading.model_docs">上传文档</el-button>
                        </el-upload>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card shadow="never" class="file-card">
                      <template #header><div>测试报告</div></template>
                      <div class="file-card-content">
                        <div v-if="state.uploading.test_report">
                          <el-progress :percentage="state.uploadProgress.test_report" />
                        </div>
                        <div v-else-if="state.currentVersion.test_report_path" class="file-info">
                          <el-icon><Document /></el-icon>
                          <el-tooltip :content="state.currentVersion.test_report_path" placement="top">
                            <span class="file-name">{{ getFileName(state.currentVersion.test_report_path) }}</span>
                          </el-tooltip>
                          <el-button
                            type="danger"
                            size="small"
                            circle
                            style="margin-left: 8px;"
                            @click="handleDeleteFile('test_report')"
                          >
                            <el-icon><Close /></el-icon>
                          </el-button>
                        </div>
                        <div v-else class="file-empty">
                          <el-icon class="empty-icon"><Document /></el-icon>
                          <span class="empty-text">暂无文件</span>
                        </div>
                        <el-upload action="#" :auto-upload="false" :show-file-list="false" :on-change="(file: UploadFile) => handleResourceUpload(file, 'test_report')" :disabled="state.uploading.test_report">
                          <el-button type="primary" :loading="state.uploading.test_report">上传报告</el-button>
                        </el-upload>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
              <div v-else class="empty-version-files">
                <el-empty description="请先选择一个版本查看文件" />
              </div>
            </div>
          </el-form-item>
        </div>
      </el-form>
      
      <!-- 步骤导航按钮 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel">取消</el-button>
          <el-button v-if="state.activeStep > 0" @click="prevStep">上一步</el-button>
          <el-button 
            v-if="state.activeStep < 2" 
            type="primary" 
            @click="nextStep"
          >下一步</el-button>
          <el-button 
            v-if="state.activeStep === 2" 
            type="primary" 
            :loading="state.loading" 
            @click="onSubmit"
          >保存</el-button>
        </span>
      </template>
    </el-dialog>
    

  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, unref, getCurrentInstance, computed, watch, onMounted } from "vue";
import { createModel, updateModel, getUploadUrl, uploadModel, getModelList, checkModelCodeUnique } from "@/api/model/models";
import { getModelCategoryTree } from "@/api/model/categories";
import { 
  getVersionList, 
  createVersion,
  deleteVersion,
  getVersionUploadUrl,
  getVersion,
  updateVersion,
  getMultipartUploadUrl,
  completeMultipartUpload,
  deleteFileFromMinio
} from "@/api/model/versions";
import { ElMessage, ElMessageBox, type FormInstance, type UploadFile } from "element-plus";
import { useUploadStore } from '@/stores/uploadStore';
import { MdEditor } from 'md-editor-v3';
import 'md-editor-v3/lib/style.css';
// 导入图标
import { FolderOpened, Folder, Document, Download, Delete, Back, Upload, ArrowDown, Plus, View, Edit, Guide, CopyDocument, Loading, CircleCheck, CircleClose, Close } from '@element-plus/icons-vue';
import { getDatasetList } from '@/api/dataset/datasets';

const props = defineProps({
  title: {
    type: String,
    default: () => "",
  },
})

const { proxy } = getCurrentInstance() as any;
const ruleFormRef = ref<FormInstance | null>(null);
// 上传相关
const uploadStore = useUploadStore();

// Markdown编辑器配置
const codeTheme = 'atom';

const state = reactive({
  // 是否显示弹出层
  isShowDialog: false,
  loading: false,
  isEdit: false, // 是否为编辑模式
  activeStep: 0, // 当前步骤
  // 模型对象
  ruleForm: {
    id: 0, // 模型ID
    name: "", // 模型名称
    description: "", // 模型描述
    category: undefined, // 模型分类
    group: "", // 项目组
    status: "online", // 模型状态
    categoryMap: {} as Record<string, number | null>, // 分类映射
    file_list: [] as any[], // 已有文件列表
    files: [] as any[], // 新上传文件
    latest_version: null as any, // 最新版本信息
    dataset_ids: [] as number[], // 添加数据集ID数组
    code: "" // 模型编码
  },
  // 上传进度相关
  uploadProgressVisible: false,
  rootCategories: [] as any[], // 根分类列表
  // 表单校验
  ruleRules: {
    name: [
      { required: true, message: "模型名称不能为空", trigger: "blur" }
    ],
    description: [
      { required: true, message: "模型描述不能为空", trigger: "blur" }
    ],
    group: [
      { required: true, message: "项目组不能为空", trigger: "blur" }
    ],
    code: [
      { 
        validator: (rule: any, value: string, callback: Function) => {
          if (value && !state.codeValid && state.codeValidated) {
            callback(new Error('模型编码已存在，请更换'));
          } else {
            callback();
          }
        }, 
        trigger: 'blur' 
      }
    ]
  },
  // 文件和版本相关
  versionLoading: false,
  versions: [] as any[],
  currentVersion: null as any,
  datasetList: [] as any[], // 数据集列表
  uploading: {
    model_weights: false,
    model_docs: false,
    test_report: false,
  },
  uploadProgress: {
    model_weights: 0,
    model_docs: 0,
    test_report: 0,
  },
  dockerImageSaving: false,
  codeValidating: false,
  codeValidated: false,
  codeValid: false,
});

// 步骤图标
const stepOneIcon = computed(() => {
  return state.activeStep > 0 ? 'Check' : 'Edit';
});

const stepTwoIcon = computed(() => {
  if (state.activeStep > 1) return 'Check';
  if (state.activeStep === 1) return 'EditPen';
  return 'Edit';
});

const stepThreeIcon = computed(() => {
  if (state.activeStep === 2) return 'Upload';
  return 'Edit';
});

// 在打开编辑模型对话框时加载版本列表或切换到版本管理标签页时加载
watch(
  () => state.activeStep,
  (newStep) => {
    // 当进入第三步且处于编辑模式时加载版本列表
    if (newStep === 2 && state.isEdit && state.ruleForm.id) {
      loadVersions();
    }
  }
);

// 加载版本列表
const loadVersions = async () => {
  if (!state.ruleForm.id) return;
  
  state.versionLoading = true;
  try {
    const response = await getVersionList({ model_id: state.ruleForm.id });
    state.versions = response.data || [];
    
    // 自动选择最新版本并加载文件
    if (state.versions.length > 0) {
      // 假设版本按创建时间倒序排列，第一个是最新的
      const latestVersion = state.versions[0];
      selectVersion(latestVersion);
      // 加载最新版本的文件
      await getVersionDetail(latestVersion.id);
    }
  } catch (error) {
    console.error('获取版本列表失败:', error);
    ElMessage.error('获取版本列表失败');
  } finally {
    state.versionLoading = false;
  }
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 加载模型版本文件
const loadModelVersionFiles = async (versionId: number) => {
  if (!versionId) return;
  
  try {
    await getVersionDetail(versionId);
  } catch (error) {
    console.error('加载版本文件失败:', error);
    ElMessage.error('加载版本文件失败');
  }
};

// 选择版本并切换到文件选项卡
const selectVersionAndSwitchTab = (version: any) => {
  state.currentVersion = version;
  // 获取版本文件
  loadModelVersionFiles(version.id);
};

// 自动保存并进入版本管理
const saveAndProceedToVersions = async () => {
  const form = unref(ruleFormRef);
  if (!form) return;

  try {
    await form.validate();
  } catch (fields) {
    ElMessage.warning('请先完成所有必填项再进入下一步');
    // 如果是name或group校验失败，则跳回第一步
    if ((fields as Record<string, unknown>)?.name || (fields as Record<string, unknown>)?.group) {
      state.activeStep = 0;
    }
    return;
  }
  
  state.loading = true;
  try {
    // 提取分类ID
    const categoryIds: number[] = [];
    for (const rootId in state.ruleForm.categoryMap) {
      const categoryId = state.ruleForm.categoryMap[rootId];
      if (categoryId && typeof categoryId === 'number') {
        categoryIds.push(categoryId);
      } else if (Array.isArray(categoryId)) {
        categoryId.forEach(id => categoryIds.push(id));
      }
    }
    
    // 创建模型
    const { data } = await createModel({
      name: state.ruleForm.name,
      group: state.ruleForm.group || 'default',
      description: state.ruleForm.description,
      status: state.ruleForm.status,
      category_ids: categoryIds,
      dataset_ids: state.ruleForm.dataset_ids,
      minio_path: `${state.ruleForm.group}/${state.ruleForm.name}`
    });

    ElMessage.success("模型基础信息已保存，请继续管理版本");

    // 更新状态，进入编辑模式
    state.ruleForm = { ...state.ruleForm, ...data };
    state.isEdit = true;
    
    // 进入下一步
    state.activeStep++;

  } catch (error) {
    console.error('创建模型失败:', error);
    ElMessage.error('保存模型基础信息失败，请重试');
  } finally {
    state.loading = false;
  }
};

// 下一步
const nextStep = async () => {
  // 当从第2步进入第3步，并且是新建模式时，先保存模型
  if (state.activeStep === 1 && !state.isEdit) {
    await saveAndProceedToVersions();
    return;
  }

  // 表单验证
  if (state.activeStep === 0) {
    const form = unref(ruleFormRef);
    if (!form) return;
    try {
      await form.validateField('name');
      await form.validateField('group');
    } catch (e) {
      ElMessage.warning('请填写模型名称和项目组');
      return;
    }
  }

  state.activeStep++;
};

// 上一步
const prevStep = () => {
  if (state.activeStep > 0) {
    state.activeStep--;
  }
};

// 处理分类数据，从categories设置categoryMap
const setupCategoryMap = (row: any) => {
  if (row.categories && row.categories.length > 0) {
    // 重置分类映射
    state.ruleForm.categoryMap = {};
    
    // 遍历叶子分类
    row.categories.forEach((category: any) => {
      if (category.parent) {
        // 找到此叶子分类所属的顶级分类
        const rootCategory = findRootCategory(category.parent);
        if (rootCategory) {
          state.ruleForm.categoryMap[rootCategory] = category.id;
        }
      }
    });
  }
};

// 查找顶级分类ID
const findRootCategory = (parentId: number): number | null => {
  // 先查找直接父类
  const parent = state.rootCategories.find((cat: any) => cat.id === parentId);
  if (parent) {
    return parent.id;
  }
  
  // 如果直接父类不是顶级分类，则递归向上查找
  for (const category of state.rootCategories) {
    const children = category.children || [];
    const found = children.find((c: any) => c.id === parentId);
    if (found) {
      return category.id;
    }
  }
  
  return null;
};

// 加载分类树
const loadCategoryTree = async () => {
  try {
    const res = await getModelCategoryTree();
    // 过滤出顶级分类（parent === null的分类）
    state.rootCategories = res.data.filter((category: any) => category.parent === null);
    
    // 初始化分类映射
    if (state.rootCategories && state.rootCategories.length > 0) {
      state.rootCategories.forEach((root: any) => {
        // 每个顶级分类初始化为null
        if (!state.ruleForm.categoryMap[root.id]) {
          state.ruleForm.categoryMap[root.id] = null;
        }
      });
    }
  } catch (error) {
    console.error('获取分类树失败:', error);
  }
};

// 打开弹窗
const openDialog = async (row: any) => {
  // 清空状态
  uploadStore.$reset();
  state.activeStep = 0;
  state.isEdit = !!row.id;
  state.currentVersion = null; // 重置当前版本
  
  // 如果有ID，表示是编辑模式
  if (row.id) {
    state.ruleForm = JSON.parse(JSON.stringify(row));
    state.ruleForm.categoryMap = {}; // 初始化分类映射
    
    // 设置最新版本为默认当前版本
    if (row.latest_version) {
      state.currentVersion = row.latest_version;
    }
  } else {
    // 新增模式
    state.ruleForm = {
      id: 0,
      name: "",
      code: "",
      description: "",
      category: undefined,
      group: "",
      status: "online",
      categoryMap: {},
      file_list: [],
      files: [],
      latest_version: null,
      dataset_ids: []
    };
  }
  
  state.isShowDialog = true;
  state.loading = false;
  
  // 加载分类树
  await loadCategoryTree();
  
  // 获取数据集列表
  try {
    const res = await getDatasetList({});
    state.datasetList = res.data.data
  } catch (error) {
    console.error('获取数据集列表失败:', error);
  }
  
  // 如果是编辑模式，设置分类映射
  if (state.isEdit && row.categories) {
    setupCategoryMap(row);
  }
};

// 关闭弹窗
const closeDialog = (row?: object) => {
  proxy.mittBus.emit("onEditModelModule", row);
  state.isShowDialog = false;
};

// 取消
const onCancel = () => {
  closeDialog();
};

// 保存
const onSubmit = async () => {
  state.loading = true;
  
  try {
    // 处理分类ID
    const categoryIds: number[] = [];
    for (const rootId in state.ruleForm.categoryMap) {
      const categoryId = state.ruleForm.categoryMap[rootId];
      if (categoryId) {
        if (Array.isArray(categoryId)) {
          categoryId.forEach(id => categoryIds.push(id));
        } else {
          categoryIds.push(categoryId);
        }
      }
    }
    
    // 最终的提交逻辑，只处理更新
    if (state.isEdit) {
      // 修改已有模型
      await updateModel(state.ruleForm.id, {
        ...state.ruleForm,
        category_ids: categoryIds,
        dataset_ids: state.ruleForm.dataset_ids
      });
      
      // 如果有当前版本，同时更新版本的状态
      if (state.currentVersion) {
        const updateData: any = {
          status: state.currentVersion.status,
          docker_image: state.currentVersion.docker_image
        };
        
        // 如果是测试未通过状态，添加失败原因
        if (state.currentVersion.status === 'test_fail') {
          updateData.test_failure_reason = state.currentVersion.test_failure_reason;
        }
        
        await updateVersion(state.currentVersion.id, updateData);
      }
      
      ElMessage.success("保存成功");
      
      state.loading = false;
      closeDialog(state.ruleForm);
    } else {
      // 此分支理论上不会再进入，因为新建模式在进入第三步时已保存
      ElMessage.error("发生意外错误，请刷新后重试");
      state.loading = false;
    }
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('操作失败，请重试');
    state.loading = false;
  }
};

// 获取版本详情
const getVersionDetail = async (versionId: number) => {
  try {
    const response = await getVersion(versionId);
    state.currentVersion = response.data;
  } catch (error) {
    console.error('获取版本详情失败:', error);
    ElMessage.error('获取版本详情失败');
  }
};

// 选择版本
const selectVersion = (version: any) => {
  state.currentVersion = version;
};

// 设置表格行类名
const tableRowClassName = ({ row }: { row: any }) => {
  if (state.currentVersion && state.currentVersion.id === row.id) {
    return 'current-version-row';
  }
  return '';
};

// 创建新版本
const createNewVersion = async () => {
  state.versionLoading = true;
  try {
    // 自动生成版本号
    let newVersionNumber = "v1.0";
    let version_code = `${state.ruleForm.code}_v1.0`;
    if (state.versions.length > 0) {
      const latestVersion = state.versions[0].version_number; // "v1.0"
      const match = latestVersion.match(/v(\d+)\.\d+/);
      if (match) {
        const currentNum = parseInt(match[1], 10);
        newVersionNumber = `v${currentNum + 1}.0`;
        version_code = `${state.ruleForm.code}_${newVersionNumber}`;
      }
    }

    const model_id = state.ruleForm.id;
    const res = await createVersion({
      model: model_id,
      version_number: newVersionNumber,
      description: '',
      code: version_code
    });

    ElMessage.success('创建版本成功');
    await loadVersions(); // 重新加载版本列表

    // 自动选择新创建的版本
    selectVersion(res.data);

  } catch (error: any) {
    if (error.response && error.response.data) {
      ElMessage.error(error.response.data.msg || '创建版本失败');
    } else {
      ElMessage.error('创建版本失败');
    }
    console.error('创建版本失败:', error);
  } finally {
    state.versionLoading = false;
  }
};

// 删除版本
const handleDeleteVersion = async (version: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除版本 ${version.version_number} 吗？此操作不可恢复！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await deleteVersion(version.id);
    ElMessage.success('版本删除成功');
    
    // 重新加载版本列表
    await loadVersions();
    
    // 如果当前正在查看的版本被删除，则清空当前版本
    if (state.currentVersion && state.currentVersion.id === version.id) {
      state.currentVersion = null;
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除版本失败:', error);
      ElMessage.error('删除版本失败');
    }
  }
};

const handleResourceUpload = async (file: UploadFile, type: 'model_weights' | 'model_docs' | 'test_report') => {
  if (!state.currentVersion) {
    ElMessage.warning('请先选择一个版本');
    return;
  }

  state.uploading[type] = true;
  state.uploadProgress[type] = 0;
  
  const versionId = state.currentVersion.id;
  const filename = file.name;
  const contentType = file.raw?.type || 'application/octet-stream';
  const fileSize = file.raw?.size || 0;
  const versionNumber = state.currentVersion.version_number;
  
  try {
    // 检查文件大小，大于10MB使用分片上传
    const USE_MULTIPART_THRESHOLD = 10 * 1024 * 1024; // 10MB
    const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB
    
    if (fileSize > USE_MULTIPART_THRESHOLD) {
      // 使用分片上传
      await handleMultipartUpload(file.raw as File, versionId, type, filename, contentType);
    } else {
      // 使用普通上传
      await handleSimpleUpload(file.raw as File, versionId, type, filename, contentType);
    }
    
    ElMessage.success(`${filename} 上传成功`);
    await getVersionDetail(versionId);
  } catch (error) {
    console.error('Upload error:', error);
    ElMessage.error(`${filename} 上传失败`);
  } finally {
    state.uploading[type] = false;
  }
};

// 普通上传方法
const handleSimpleUpload = async (file: File, versionId: number, fileType: 'model_weights' | 'model_docs' | 'test_report', filename: string, contentType: string) => {
  // 获取上传URL
  const { data } = await getVersionUploadUrl(versionId, {
    filename: filename,
    content_type: contentType,
    file_type: fileType
  });
  
  // 上传文件
  await uploadFile(file, data.url, (progress: number) => {
    state.uploadProgress[fileType] = progress;
  });
  
  // 更新版本信息
  const path = `${state.currentVersion.version_number}/${filename}`;
  let updatePayload: any = {};
  if (fileType === 'test_report') {
    updatePayload.test_report_path = path;
  } else if (fileType === 'model_docs') {
    updatePayload.model_docs_path = path;
  } else if (fileType === 'model_weights') {
    updatePayload.model_weights_path = path;
  }
  await updateVersion(versionId, updatePayload);
};

// 分片上传方法
const handleMultipartUpload = async (file: File, versionId: number, fileType: 'model_weights' | 'model_docs' | 'test_report', filename: string, contentType: string) => {
  // 创建上传任务
  const uploadStore = useUploadStore();
  const task = uploadStore.createUploadTask(file, versionId, fileType);
  
  // 获取分片上传URL
  const { data } = await getMultipartUploadUrl(versionId, {
    filename: filename,
    content_type: contentType,
    file_type: fileType,
    file_size: file.size
  });
  
  if (!data.upload_id) {
    throw new Error('获取分片上传ID失败');
  }
  
  // 保存上传ID和对象名称
  uploadStore.setTaskUploadId(task.id, data.upload_id, data.object_name);
  
  // 开始上传分片
  const parts: {part_number: number, etag: string}[] = [];
  const chunkSize = uploadStore.chunkSize;
  const totalChunks = Math.ceil(file.size / chunkSize);
  
  // 更新任务状态
  uploadStore.updateTaskStatus(task.id, 'uploading');
  
  // 上传所有分片
  for (let i = 0; i < totalChunks; i++) {
    // 检查任务状态，如果已暂停则中断上传
    const currentTask = uploadStore.getUploadTask(task.id);
    if (currentTask?.status === 'paused') {
      throw new Error('上传已暂停');
    }
    
    // 如果分片已上传成功，跳过
    if (currentTask?.chunks[i]?.status === 'success' && currentTask?.chunks[i]?.etag) {
      parts.push({
        part_number: i + 1,
        etag: currentTask.chunks[i].etag as string
      });
      continue;
    }
    
    // 准备分片数据
    const start = i * chunkSize;
    const end = Math.min(file.size, start + chunkSize);
    const chunk = file.slice(start, end);
    
    try {
      // 更新分片状态
      uploadStore.updateChunkStatus(task.id, i, 'uploading');
      
      // 获取分片上传URL
      const partUrl = data.part_urls[i + 1];
      
      // 上传分片
      const etag = await uploadChunk(chunk, partUrl, (progress) => {
        uploadStore.updateTaskProgress(task.id, i, progress);
        
        // 计算总体进度
        const totalProgress = task.chunks.reduce((sum, chunk) => sum + chunk.progress, 0) / totalChunks;
        state.uploadProgress[fileType] = Math.round(totalProgress);
      });
      
      // 更新分片状态
      uploadStore.updateChunkStatus(task.id, i, 'success', etag);
      
      // 添加到分片列表
      parts.push({
        part_number: i + 1,
        etag: etag
      });
    } catch (error) {
      // 更新分片状态
      uploadStore.updateChunkStatus(task.id, i, 'error');
      throw error;
    }
  }
  
  // 完成分片上传
  await completeMultipartUpload(versionId, {
    upload_id: data.upload_id,
    object_name: data.object_name,
    parts: parts,
    file_type: fileType
  });
  
  // 更新任务状态
  uploadStore.updateTaskStatus(task.id, 'success');
};

// 上传单个分片
const uploadChunk = async (chunk: Blob, url: string, onProgress: (progress: number) => void): Promise<string> => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    
    xhr.upload.onprogress = (e) => {
      if (e.lengthComputable) {
        const percentComplete = Math.round((e.loaded / e.total) * 100);
        onProgress(percentComplete);
      }
    };
    
    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        // 获取ETag，通常在响应头中
        const etag = xhr.getResponseHeader('ETag') || `"${Date.now()}"`; // 如果没有ETag，使用时间戳
        resolve(etag.replace(/"/g, '')); // 去掉引号
      } else {
        reject(new Error(`Upload failed with status ${xhr.status}`));
      }
    };
    
    xhr.onerror = () => reject(new Error('Upload failed'));
    xhr.onabort = () => reject(new Error('Upload aborted'));
    
    xhr.open('PUT', url, true);
    xhr.setRequestHeader('Content-Type', 'application/octet-stream');
    xhr.send(chunk);
  });
};

// 修改普通上传文件方法，支持断点续传
const uploadFile = async (file: File, uploadUrl: string, onProgress: (progress: number) => void) => {
  // 获取文件大小
  const fileSize = file.size;
  
  // 检查本地存储中是否有该文件的上传记录
  const storageKey = `upload_${file.name}_${fileSize}`;
  const uploadedBytes = parseInt(localStorage.getItem(storageKey) || '0');
  
  try {
    const xhr = new XMLHttpRequest();
    
    await new Promise((resolve, reject) => {
      xhr.upload.onprogress = (e) => {
        if (e.lengthComputable) {
          const percentComplete = Math.round(((uploadedBytes + e.loaded) / fileSize) * 100);
          onProgress(percentComplete);
          
          // 每次进度更新时保存已上传的字节数
          localStorage.setItem(storageKey, String(uploadedBytes + e.loaded));
        }
      };

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          // 上传成功后清除本地存储的记录
          localStorage.removeItem(storageKey);
          resolve(xhr.response);
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };

      xhr.onerror = () => reject(new Error('Upload failed'));
      xhr.onabort = () => reject(new Error('Upload aborted'));

      // 如果有已上传的部分，使用Range头指定续传位置
      if (uploadedBytes > 0) {
        xhr.open('PUT', uploadUrl, true);
        xhr.setRequestHeader('Content-Type', file.type || 'application/octet-stream');
        xhr.setRequestHeader('Content-Range', `bytes ${uploadedBytes}-${fileSize-1}/${fileSize}`);
        
        // 截取文件的剩余部分进行上传
        const remainingPart = file.slice(uploadedBytes);
        xhr.send(remainingPart);
      } else {
        // 首次上传
        xhr.open('PUT', uploadUrl, true);
        xhr.setRequestHeader('Content-Type', file.type || 'application/octet-stream');
        xhr.send(file);
      }
    });

    onProgress(100);
    return true;
  } catch (error) {
    console.error('Upload error:', error);
    throw error;
  }
};

const handleUpdateVersion = async () => {
  if (!state.currentVersion) return;
  state.dockerImageSaving = true;
  try {
    await updateVersion(state.currentVersion.id, {
      docker_image: state.currentVersion.docker_image
    });
    ElMessage.success('Docker镜像地址已更新');
  } catch (error) {
    console.error('更新版本信息失败:', error);
    ElMessage.error('更新失败，请重试');
  } finally {
    state.dockerImageSaving = false;
  }
};

const getFileName = (path: string) => {
  if (!path) return '';
  return path.split('/').pop();
};

// 添加验证模型编码的方法
const validateModelCode = async () => {
  const code = state.ruleForm.code;
  if (!code) {
    state.codeValidated = false;
    return;
  }
  
  state.codeValidating = true;
  try {
    const res = await checkModelCodeUnique(code, state.isEdit ? state.ruleForm.id : undefined);
    state.codeValid = res.data.unique;
    state.codeValidated = true;
  } catch (error) {
    console.error('验证模型编码失败:', error);
    state.codeValid = false;
    state.codeValidated = true;
  } finally {
    state.codeValidating = false;
  }
};

const handleDeleteFile = async (type: 'model_weights' | 'model_docs' | 'test_report') => {
  if (!state.currentVersion) return;
  let filePath = '';
  if (type === 'model_weights') filePath = state.currentVersion.model_weights_path;
  if (type === 'model_docs') filePath = state.currentVersion.model_docs_path;
  if (type === 'test_report') filePath = state.currentVersion.test_report_path;
  if (!filePath) return;

  try {
    await ElMessageBox.confirm('确定要删除该文件吗？', '提示', { type: 'warning' });
    // 1. 删除minio文件
    await deleteFileFromMinio({ file_path: filePath });
    // 2. 更新数据库字段
    const updatePayload: any = {};
    if (type === 'model_weights') updatePayload.model_weights_path = '';
    if (type === 'model_docs') updatePayload.model_docs_path = '';
    if (type === 'test_report') updatePayload.test_report_path = '';
    await updateVersion(state.currentVersion.id, updatePayload);
    ElMessage.success('删除成功');
    await getVersionDetail(state.currentVersion.id); // 刷新
  } catch (e) {
    ElMessage.error('删除失败');
  }
};

defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

.upload-buttons {
  display: flex;
  gap: 10px;
}

.file-list-table {
  margin-top: 15px;
}

.step-description {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #6B48FF;
  
  .step-title {
    font-size: 16px;
    font-weight: 600;
    color: #1F2A44;
    margin-bottom: 5px;
  }
  
  .step-info {
    font-size: 14px;
    color: #64748B;
    line-height: 1.5;
  }
}

.model-upload-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 25px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .upload-steps {
    margin-bottom: 30px;
    
    :deep(.el-step__title) {
      font-size: 16px;
      font-weight: 500;
      
      &.is-process {
        color: #6B48FF;
        font-weight: 600;
      }
      
      &.is-success {
        color: #67C23A;
      }
    }
    
    :deep(.el-step__head.is-process) {
      color: #6B48FF;
      border-color: #6B48FF;
    }
    
    :deep(.el-step__head.is-success) {
      color: #67C23A;
      border-color: #67C23A;
    }
  }
}

.category-select-container {
  max-height: 250px;
  overflow-y: auto;
  padding: 10px;
  border-radius: 8px;
  background-color: #f9f9fa;
  margin-bottom: 10px;
}

.category-group {
  margin-bottom: 10px;

  .root-category-label {
    font-weight: 500;
    margin-bottom: 5px;
  }

  .category-cascader {
    width: 100%;
  }
}

:deep(.markdown-editor) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #E4E7ED;
  
  .cm-editor {
    font-size: 14px;
    font-family: 'Consolas', 'Monaco', monospace;
  }
  
  .md-editor-preview-wrapper {
    padding: 16px;
    font-size: 14px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  width: 100%;
}

.file-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 15px;
  }
  
  :deep(.el-tabs__content) {
    padding: 5px;
  }
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.version-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.version-actions {
  display: flex;
  gap: 10px;
}

.version-table {
  margin-bottom: 20px;
}

.divider {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.version-info {
  margin-bottom: 20px;
}

.docker-image-input {
  margin-top: 15px;
}

.current-version {
  font-weight: 600;
  color: #409EFF;
}

.empty-version-files {
  padding: 40px 0;
  text-align: center;
}

.centered-container {
  width: 100%;
  margin: 0 auto;
}

.version-management-section {
  margin-bottom: 20px;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.version-actions {
  display: flex;
  gap: 10px;
}

.version-table {
  margin-bottom: 20px;
}

.divider {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.version-info {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #303133;
}

.file-browser {
  margin-top: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.file-browser-item-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: #606266;
}

.file-browser-item-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
}

.file-browser-item-size {
  width: 100px;
  text-align: right;
  color: #909399;
  font-size: 13px;
  margin-right: 10px;
}

.file-browser-item-time {
  width: 180px;
  text-align: right;
  color: #909399;
  font-size: 13px;
  margin-right: 10px;
}

.file-browser-item-action {
  width: 100px;
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}

.file-browser-upload {
  margin-left: 10px;
}

.hidden-upload {
  display: none;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  text-align: center;
}

:deep(.el-table td) {
  text-align: center;
}

.file-browser-list {
  padding: 10px;
  max-height: 400px;
  overflow-y: auto;
  min-height: 200px;
  width: 100%;
}

.file-browser-row {
  width: 100%;
  height: 45px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 5px;
  border-bottom: 1px solid #f0f0f0;
  justify-content: space-between;
}

.file-browser-item:hover {
  background-color: #f5f7fa;
}

.file-browser-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e0e0e0;
  width: 100%;
}

.file-browser-breadcrumb {
  flex: 1;
}

.file-browser-header {
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f9f9fa;
}

.file-browser-empty {
  width: 100%;
  padding: 30px 20px;
  text-align: center;
}

.file-browser-section {
  margin-top: 20px;
}

:deep(.current-version-row) {
  background-color: #ecf5ff;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
  margin-bottom: 12px;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.el-card {
  margin-bottom: 20px;
}

.file-card {
  height: 100%;
  margin-bottom: 20px;
}

.file-card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 12px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.file-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  color: var(--el-text-color-secondary);
}

.empty-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  font-weight: 500;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-progress) {
  margin-bottom: 12px;
}

.model-status-container {
  margin: 16px 0;
  padding: 12px 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
  border-left: 4px solid var(--el-color-primary);
}

.status-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 12px;
}

.status-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.status-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  border-top: 1px solid var(--el-border-color-lighter);
  padding-top: 12px;
}

.docker-image-input {
  margin-top: 16px;
}

.failure-reason-container {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed var(--el-border-color);
}

.reason-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
  margin-bottom: 8px;
}
</style> 