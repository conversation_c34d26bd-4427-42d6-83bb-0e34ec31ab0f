<template>
  <div class="app-container">
    <!-- 加载状态 -->
    <div v-if="state.loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <template v-else>

      <!-- 数据集基本信息卡片 -->
      <el-card class="model-info-card">
        <div class="model-header">
          <div class="model-title">
            <h1>{{ state.datasetDetail.name }}</h1>
            <el-tag size="small" type="success">{{ state.datasetDetail.category?.name }}</el-tag>
            <el-tag size="small" type="info" class="ml-2">{{ state.datasetDetail.group }}</el-tag>
            <el-tag size="small" :type="state.datasetDetail.status === 'online' ? 'primary' : 'danger'" class="ml-2">
              {{ state.datasetDetail.status === 'online' ? '已上架' : '已下架' }}
            </el-tag>
          </div>
          <div class="model-actions">
            <el-button type="primary" @click="handleStar">
              <el-icon><Star /></el-icon>
              收藏 ({{ state.datasetDetail.stars }})
            </el-button>
            <el-button type="success" @click="handleDownload">
              <el-icon><Download /></el-icon>
              下载 ({{ state.datasetDetail.downloads }})
            </el-button>
          </div>
        </div>

        <div class="model-meta">
          <div class="meta-item">
            <el-icon><User /></el-icon>
            <span>创建者: {{ state.datasetDetail.creator?.username || '未知' }}</span>
          </div>
          <div class="meta-item">
            <el-icon><Calendar /></el-icon>
            <span>更新时间: {{ formatDate(state.datasetDetail.update_datetime) }}</span>
          </div>
          <div class="meta-item">
            <el-icon><Box /></el-icon>
            <span>MinIO路径: {{ state.datasetDetail.minio_path }}</span>
          </div>
          
          <!-- 添加关联模型显示 -->
          <div class="meta-item models-container" v-if="state.datasetDetail.models && state.datasetDetail.models.length > 0">
            <el-icon><Connection /></el-icon>
            <span>训练模型: </span>
            <div class="model-tags">
              <el-tag
                v-for="model in state.datasetDetail.models"
                :key="model.id"
                type="success"
                effect="plain"
                class="model-tag"
                @click="navigateToModel(model.id)"
              >
                {{ model.name }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 数据集参数和指标 -->
        <div class="model-metrics">
          <div class="metrics-section">
            <h3>数据集参数</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item v-for="(value, key) in state.datasetDetail.parameters" :key="`param-${key}`" :label="key">
                {{ value }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="metrics-section">
            <h3>测试指标</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item v-for="(value, key) in state.datasetDetail.metrics" :key="`metric-${key}`" :label="key">
                {{ value }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>

      <!-- 面包屑导航 -->
      <el-card class="tab-card">
        <el-tabs v-model="state.activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="数据集详情" name="detail"></el-tab-pane>
          <el-tab-pane label="数据集文件" name="files"></el-tab-pane>
          <el-tab-pane label="数据集评论" name="comments"></el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 内容区域 -->
      <el-card class="content-card">
        <!-- 数据集详情 -->
        <div v-if="state.activeTab === 'detail'" class="model-detail-content">
          <div v-if="state.datasetDetail.description" class="description-content">
            <h3>数据集描述</h3>
            <md-preview
              :modelValue="state.datasetDetail.description"
              class="md-preview"
              :preview-theme="previewTheme"
              :code-theme="codeTheme"
            />
          </div>
          <el-empty v-else description="暂无详细描述"></el-empty>
        </div>

        <!-- 数据集文件 -->
        <div v-else-if="state.activeTab === 'files'" class="model-files">
          <el-table v-if="state.fileList.length > 0" :data="state.fileList" style="width: 100%">
            <el-table-column prop="name" label="文件名" min-width="200">
              <template #default="{ row }">
                <el-icon><Document /></el-icon>
                <span class="ml-2">{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="大小" width="180">
              <template #default="{ row }">
                {{ formatSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="last_modified" label="修改时间" width="200">
              <template #default="{ row }">
                {{ formatDate(row.last_modified) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button type="primary" link @click="downloadFile(row.name)">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-empty v-else description="暂无文件"></el-empty>
        </div>

        <!-- 数据集评论 -->
        <div v-else-if="state.activeTab === 'comments'" class="model-comments">
          <!-- 评论列表 -->
          <div class="comments-list">
            <template v-if="state.comments.length > 0">
              <div class="comment-tree">
                <comment-item
                  v-for="comment in state.comments"
                  :key="comment.id"
                  :comment="comment"
                  @reply="handleReply"
                  @delete="handleDeleteComment"
                />
              </div>
            </template>
            <el-empty v-else description="暂无评论"></el-empty>
          </div>

          <!-- 评论输入框 -->
          <div class="comment-form">
            <h3>{{ state.replyTo ? `回复: ${state.replyTo.user.username}` : '发表评论' }}</h3>
            <div v-if="state.replyTo" class="reply-info">
              <span>{{ state.replyTo.content }}</span>
              <el-button type="text" @click="cancelReply">取消回复</el-button>
            </div>
            <el-input
              v-model="state.commentContent"
              type="textarea"
              :rows="4"
              placeholder="请输入评论内容..."
            />
            <div class="form-actions">
              <el-button type="primary" @click="submitComment" :loading="state.submittingComment">
                发表评论
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 下载进度对话框 -->
      <el-dialog
        v-model="downloadState.visible"
        title="文件下载"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        width="400px"
      >
        <div class="download-progress">
          <div class="file-info">
            <el-icon><Document /></el-icon>
            <span>{{ downloadState.fileName }}</span>
          </div>
          <el-progress 
            :percentage="downloadState.progress"
            :format="(p: number) => `${p}%`"
            :stroke-width="20"
            status="success"
          />
          <div class="download-speed">
            <span>下载速度: {{ downloadState.speed }}</span>
          </div>
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ArrowLeft, Star, Download, User, Calendar, Box, Document, Connection
} from '@element-plus/icons-vue'
import { MdPreview } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import CommentItem from './CommentItem.vue'
import { 
  getDataset, 
  starDataset, 
  downloadDataset, 
  downloadDatasetFile
} from '@/api/dataset/datasets'
import { getCommentList as getComments, createComment } from '@/api/dataset/comments'

const route = useRoute()
const router = useRouter()

// 状态管理
const state = reactive({
  loading: true,
  datasetDetail: {} as any,
  datasetId: 0,
  activeTab: 'detail',
  fileList: [] as any[],
  comments: [] as any[],
  commentContent: '',
  replyTo: null as any,
  submittingComment: false
})

// 下载状态
const downloadState = reactive({
  visible: false,
  fileName: '',
  progress: 0,
  speed: '0 KB/s',
  startTime: 0,
  downloadedBytes: 0
})

// Markdown 主题
const previewTheme = 'github'
const codeTheme = 'github'

// 初始化
onMounted(async () => {
  // 获取路由参数中的数据集ID
  const id = route.params.id
  console.log(id)
  if (id) {
    state.datasetId = Number(id)
    await fetchDatasetDetail()
  } else {
    ElMessage.error('数据集ID不存在')
    router.push('/dataset')
  }
})

// 获取数据集详情
const fetchDatasetDetail = async () => {
  try {
    state.loading = true
    const res = await getDataset(state.datasetId)
    state.datasetDetail = res.data
    
    // 获取文件列表
    if (state.datasetDetail.file_list) {
      state.fileList = state.datasetDetail.file_list
    }
    
    // 获取评论列表
    await fetchComments()
  } catch (error) {
    console.error('获取数据集详情失败:', error)
    ElMessage.error('获取数据集详情失败')
  } finally {
    state.loading = false
  }
}

// 处理标签页切换
const handleTabChange = (tab: string) => {
  state.activeTab = tab
}

// 收藏数据集
const handleStar = async () => {
  try {
    await starDataset(state.datasetId)
    state.datasetDetail.stars++
    ElMessage.success('收藏成功')
  } catch (error) {
    console.error('收藏失败:', error)
    ElMessage.error('收藏失败')
  }
}

// 下载数据集
const handleDownload = async () => {
  try {
    await downloadDataset(state.datasetId)
    state.datasetDetail.downloads++
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString()
}

// 格式化文件大小
const formatSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 下载文件
const downloadFile = async (fileName: string) => {
  try {
    // 初始化下载状态
    downloadState.visible = true
    downloadState.fileName = fileName
    downloadState.progress = 0
    downloadState.speed = '0 KB/s'
    downloadState.startTime = Date.now()
    downloadState.downloadedBytes = 0
    
    // 下载文件
    const res = await downloadDatasetFile(state.datasetId, fileName, (progress) => {
      // 更新下载进度
      downloadState.progress = progress
      
      // 计算下载速度
      const now = Date.now()
      const timeElapsed = (now - downloadState.startTime) / 1000 // 秒
      if (timeElapsed > 0) {
        const file = state.fileList.find(f => f.name === fileName)
        if (file) {
          const totalBytes = file.size
          const downloadedBytes = totalBytes * (progress / 100)
          const bytesPerSecond = downloadedBytes / timeElapsed
          downloadState.speed = formatSize(bytesPerSecond) + '/s'
        }
      }
    })
    
    // 创建下载链接
    const blob = new Blob([res as unknown as BlobPart])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // 下载完成
    downloadState.progress = 100
    setTimeout(() => {
      downloadState.visible = false
    }, 1000)
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败')
    downloadState.visible = false
  }
}

// 获取评论列表
const fetchComments = async () => {
  try {
    const res = await getComments({
      model: state.datasetId,
      parent__isnull: true
    })
    state.comments = res.data || []
  } catch (error) {
    console.error('获取评论列表失败:', error)
  }
}

// 提交评论
const submitComment = async () => {
  if (!state.commentContent.trim()) {
    ElMessage.warning('评论内容不能为空')
    return
  }
  
  try {
    state.submittingComment = true
    const commentData = {
      model: state.datasetId,
      content: state.commentContent,
      parent: state.replyTo ? state.replyTo.id : null
    }
    
    await createComment(commentData)
    ElMessage.success('评论发表成功')
    
    // 清空评论内容
    state.commentContent = ''
    
    // 清除回复状态
    if (state.replyTo) {
      state.replyTo = null
    }
    
    // 重新获取评论列表
    await fetchComments()
  } catch (error) {
    console.error('评论发表失败:', error)
    ElMessage.error('评论发表失败')
  } finally {
    state.submittingComment = false
  }
}

// 处理回复评论
const handleReply = (comment: any) => {
  state.replyTo = comment
  // 滚动到评论框
  setTimeout(() => {
    const commentForm = document.querySelector('.comment-form')
    if (commentForm) {
      commentForm.scrollIntoView({ behavior: 'smooth' })
    }
  }, 100)
}

// 取消回复
const cancelReply = () => {
  state.replyTo = null
}

// 处理删除评论
const handleDeleteComment = async (commentId: number) => {
  try {
    ElMessageBox.confirm(
      '确定要删除此评论吗？',
      '删除评论',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      // 在此处调用删除评论的API
      // await deleteComment(commentId)
      ElMessage.success('评论已删除')
      // 重新获取评论列表
      await fetchComments()
    })
  } catch (error) {
    console.error('删除评论失败:', error)
    ElMessage.error('删除评论失败')
  }
}

// 导航到模型详情
const navigateToModel = (modelId: number) => {
  router.push(`/model/models/${modelId}`);
}
</script>

<style lang="scss" scoped>
.app-container {
  .loading-container {
    padding: 20px;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  .ml-2 {
    margin-left: 8px;
  }

  .model-info-card {
    margin-bottom: 16px;
  }

  .tab-card {
    margin-bottom: 10px;
  }

  .content-card {
    min-height: 400px;
  }

  .model-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .model-title {
      h1 {
        margin: 0 0 10px 0;
      }
    }
  }

  .model-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #606266;
    }
  }

  .model-metrics {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .metrics-section {
      flex: 1;
      min-width: 300px;

      h3 {
        margin-top: 0;
        margin-bottom: 16px;
      }
    }
  }

  .model-detail-content {
    padding: 10px 0;
    
    .description-content {
      h3 {
        margin-bottom: 16px;
        font-size: 18px;
        color: #1F2A44;
      }
      
      :deep(.md-preview) {
        border: none;
        background-color: transparent;
        
        .md-editor-preview-wrapper {
          padding: 0;
          
          code {
            background-color: #f8f8f8;
            padding: 2px 4px;
            border-radius: 4px;
            color: #d56161;
          }
          
          pre {
            background-color: #f8f8f8;
            border-radius: 4px;
            padding: 12px;
          }
          
          img {
            max-width: 100%;
            border-radius: 4px;
          }
          
          h1, h2, h3 {
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 8px;
            margin-top: 24px;
          }
          
          table {
            border-collapse: collapse;
            margin: 16px 0;
            
            th, td {
              border: 1px solid #ebeef5;
              padding: 8px 16px;
            }
            
            th {
              background-color: #f8f8f8;
            }
          }
          
          blockquote {
            border-left: 4px solid #6B48FF;
            padding-left: 16px;
            color: #606266;
            background-color: #f9f9fa;
            padding: 8px 16px;
            margin: 16px 0;
            border-radius: 4px;
          }
        }
      }
    }
  }

  .model-files {
    .el-table {
      margin-bottom: 20px;
    }
  }

  .model-comments {
    .comments-list {
      margin-bottom: 30px;
    }

    .comment-form {
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 4px;

      h3 {
        margin-top: 0;
        margin-bottom: 16px;
      }

      .reply-info {
        background-color: #ecf5ff;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .form-actions {
        margin-top: 16px;
        text-align: right;
      }
    }
  }

  .download-progress {
    padding: 20px;

    .file-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      color: #606266;
    }

    .download-speed {
      margin-top: 12px;
      text-align: right;
      color: #909399;
      font-size: 14px;
    }
  }
}

.models-container {
  .model-tags {
    display: inline-flex;
    flex-wrap: wrap;
    margin-left: 4px;
    gap: 8px;
    
    .model-tag {
      cursor: pointer;
      
      &:hover {
        opacity: 0.8;
        color: var(--el-color-primary);
      }
    }
  }
}
</style> 