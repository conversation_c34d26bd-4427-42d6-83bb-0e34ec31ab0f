<template>
  <div class="system-model-container">
    <el-dialog v-model="state.isShowDialog" width="70%" center class="model-upload-dialog">
      <template #header>
        <div style="font-size: large" v-drag="['.system-model-container .el-dialog', '.system-model-container .el-dialog__header']">{{title}}</div>
      </template>
      
      <!-- 步骤条 -->
      <el-steps 
        :active="state.activeStep" 
        finish-status="success" 
        process-status="process" 
        class="upload-steps"
        align-center
      >
        <el-step title="基础信息" :icon="stepOneIcon" />
        <el-step title="数据集描述" :icon="stepTwoIcon" />
        <el-step title="数据集文件" :icon="stepThreeIcon" />
      </el-steps>
      
      <el-form
        :model="state.ruleForm"
        :rules="state.ruleRules"
        ref="ruleFormRef"
        label-width="120px"
      >
        <!-- 步骤一：基础信息 -->
        <div v-if="state.activeStep === 0">
          <div class="step-description">
            <div class="step-title">第一步：填写数据集基本信息</div>
            <div class="step-info">请提供数据集的名称、所属项目组以及分类信息</div>
          </div>
          <el-form-item label="数据集名称" prop="name">
            <el-input 
              v-model="state.ruleForm.name" 
              placeholder="请输入数据集名称（3-63字符，只能包含小写字母、数字、点和连字符）"
            />
            <div class="form-tip">名称将用于MinIO存储路径，只能包含小写字母、数字、点和连字符</div>
          </el-form-item>
          <el-form-item label="项目组" prop="group">
            <el-input v-model="state.ruleForm.group" placeholder="请输入项目组名称" />
          </el-form-item>
          <el-form-item label="数据集分类" prop="category_ids">
            <div class="category-select-container">
              <div v-for="root in state.rootCategories" :key="root.id" class="category-group">
                <div class="root-category-label">{{ root.name }}：</div>
                <el-cascader
                  v-model="state.ruleForm.categoryMap[root.id]"
                  :options="[root]"
                  :props="{
                    checkStrictly: false,
                    emitPath: false,
                    children: 'children',
                    label: 'name',
                    value: 'id'
                  }"
                  placeholder="请选择分类"
                  class="category-cascader"
                  clearable
                />
              </div>
            </div>
            <div class="form-tip">请至少选择一个顶级分类下的叶子节点</div>
          </el-form-item>
          <el-form-item label="数据集状态" prop="status">
            <el-radio-group v-model="state.ruleForm.status">
              <el-radio label="online">上架</el-radio>
              <el-radio label="offline">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        
        <!-- 步骤二：数据集描述 -->
        <div v-if="state.activeStep === 1">
          <div class="step-description">
            <div class="step-title">第二步：编写数据集描述</div>
            <div class="step-info">请使用Markdown格式详细描述数据集的功能、使用场景、技术参数等信息，左侧编辑右侧预览</div>
          </div>
          <el-form-item label="数据集描述" prop="description">
            <md-editor
              v-model="state.ruleForm.description"
              :toolbars="['bold', 'underline', 'italic', 'strikeThrough', 'title', 'sub', 'sup', 'quote', 'unorderedList', 'orderedList', 'task', 'codeRow', 'code', 'link', 'image', 'table', 'revoke', 'next', 'save']"
              theme="light" 
              language="zh-CN"
              preview-theme="github"
              height="300px"
              model-value-type="string"
              :preview="true"
              :code-theme="codeTheme"
              class="markdown-editor"
              :show-code-row-number="true"
              placeholder="请输入数据集描述（支持Markdown格式）"
            />
          </el-form-item>
        </div>
        
        <!-- 步骤三：数据集文件 -->
        <div v-if="state.activeStep === 2">
          <div class="step-description">
            <div class="step-title">第三步：上传数据集文件</div>
            <div class="step-info">请选择需要上传的数据集文件或文件夹，支持批量上传</div>
          </div>
          
          <!-- 编辑模式下显示已有文件列表 -->
          <div v-if="state.isEdit && state.ruleForm.file_list && state.ruleForm.file_list.length > 0">
            <el-form-item label="已有文件">
              <el-table class="file-list-table" :data="state.ruleForm.file_list">
                <el-table-column label="文件名" prop="name" />
                <el-table-column label="大小" width="120">
                  <template #default="scope">
                    {{ formatFileSize(scope.row.size) }}
                  </template>
                </el-table-column>
                <el-table-column label="最后修改时间" width="180" prop="last_modified" />
              </el-table>
            </el-form-item>
          </div>
          
          <!-- 仅在新增模式下显示文件上传区域 -->
          <el-form-item label="数据集文件" v-if="!state.isEdit" required>
            <div class="upload-buttons">
              <!-- 文件上传 -->
              <el-upload
                ref="uploadRef"
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                :file-list="fileListRef"
                multiple
                :http-request="() => {}"
                class="model-upload"
              >
                <el-button type="primary">
                  <SvgIcon name="elementUpload"/>
                  选择文件
                </el-button>
              </el-upload>
              
              <!-- 文件夹上传 -->
              <input
                ref="uploadFolderRef"
                type="file"
                style="display: none"
                @change="handleFolderChange"
                webkitdirectory
                multiple
              />
              <el-button type="primary" @click="triggerFolderUpload">
                <SvgIcon name="elementFolder"/>
                选择文件夹
              </el-button>
            </div>
            <div class="form-tip">支持单独选择文件或整个文件夹上传</div>
            
            <!-- 已选文件列表 -->
            <el-table v-if="uploadStore.files.length > 0" class="file-list-table" :data="uploadStore.files">
              <el-table-column label="文件名" prop="name" />
              <el-table-column label="路径" prop="relativePath" width="200" />
              <el-table-column label="大小" width="120">
                <template #default="scope">
                  {{ formatFileSize(scope.row.size) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button type="danger" link @click="handleFileRemove(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </div>
      </el-form>
      
      <!-- 步骤导航按钮 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel">取消</el-button>
          <el-button v-if="state.activeStep > 0" @click="prevStep">上一步</el-button>
          <el-button 
            v-if="state.activeStep < 2" 
            type="primary" 
            @click="nextStep"
          >下一步</el-button>
          <el-button 
            v-if="state.activeStep === 2" 
            type="primary" 
            :loading="state.loading" 
            @click="onSubmit"
          >保存</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 上传进度组件 -->
    <UploadProgress
      v-if="state.uploadProgressVisible"
      v-model:visible="state.uploadProgressVisible"
      :files="state.uploadFiles"
      @close="handleUploadComplete"
    />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, unref, getCurrentInstance, computed, watch } from "vue";
import { createDataset, updateDataset, getUploadUrl, uploadDataset } from "@/api/dataset/datasets";
import { getDatasetCategoryTree } from "@/api/dataset/categories";
import { ElMessage } from "element-plus";
import { useUploadStore } from '@/stores/uploadStore';
import UploadProgress from '@/components/UploadProgress.vue';
// @ts-ignore
import { MdEditor } from 'md-editor-v3';
import 'md-editor-v3/lib/style.css';

const props = defineProps({
  title: {
    type: String,
    default: () => "",
  },
})

const { proxy } = getCurrentInstance() as any;
const ruleFormRef = ref<HTMLElement | null>(null);
// 上传相关
const fileListRef = ref<any[]>([]);
const uploadRef = ref();
const uploadFolderRef = ref();
const uploadStore = useUploadStore();

// Markdown编辑器配置
const codeTheme = 'atom';

const state = reactive({
  // 是否显示弹出层
  isShowDialog: false,
  loading: false,
  isEdit: false, // 是否为编辑模式
  activeStep: 0, // 当前步骤
  // 数据集对象
  ruleForm: {
    id: 0, // 数据集ID
    name: "", // 数据集名称
    description: "", // 数据集描述
    category: undefined, // 数据集分类
    group: "", // 项目组
    status: "online", // 数据集状态
    categoryMap: {} as Record<string, number | null>, // 分类映射
    file_list: [] as any[], // 已有文件列表
    files: [] as any[] // 新上传文件
  },
  // 上传进度相关
  uploadProgressVisible: false,
  uploadFiles: [] as any[],
  rootCategories: [] as any[], // 根分类列表
  // 表单校验
  ruleRules: {
    name: [
      { required: true, message: "数据集名称不能为空", trigger: "blur" }
    ],
    description: [
      { required: true, message: "数据集描述不能为空", trigger: "blur" }
    ],
    group: [
      { required: true, message: "项目组不能为空", trigger: "blur" }
    ]
  },
});

// 步骤图标
const stepOneIcon = computed(() => {
  return state.activeStep > 0 ? 'Check' : 'Edit';
});

const stepTwoIcon = computed(() => {
  if (state.activeStep > 1) return 'Check';
  if (state.activeStep === 1) return 'EditPen';
  return 'Edit';
});

const stepThreeIcon = computed(() => {
  if (state.activeStep === 2) return 'Upload';
  return 'Edit';
});

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 加载分类树
const loadCategoryTree = async () => {
  try {
    const res = await getDatasetCategoryTree();
    // 过滤出顶级分类（parent === null的分类）
    state.rootCategories = res.data.filter((category: any) => category.parent === null);
    
    // 初始化分类映射
    if (state.rootCategories && state.rootCategories.length > 0) {
      state.rootCategories.forEach((root: any) => {
        // 每个顶级分类初始化为null
        if (!state.ruleForm.categoryMap[root.id]) {
          state.ruleForm.categoryMap[root.id] = null;
        }
      });
    }
  } catch (error) {
    console.error('获取分类树失败:', error);
  }
};

// 下一步
const nextStep = () => {
  // 表单验证
  if (state.activeStep === 0) {
    // 验证基础信息
    if (!state.ruleForm.name) {
      ElMessage.warning('请填写数据集名称');
      return;
    }
    if (!state.ruleForm.group) {
      ElMessage.warning('请填写项目组');
      return;
    }

    // 验证分类选择
    if (!state.isEdit) { // 仅在新增模式下验证分类选择
      const categoryIds: number[] = [];
      const selectedRootCategories = new Set<number>();
      
      for (const rootId in state.ruleForm.categoryMap) {
        const categoryId = state.ruleForm.categoryMap[rootId];
        if (categoryId) {
          categoryIds.push(categoryId);
          selectedRootCategories.add(Number(rootId));
        }
      }
      
      if (selectedRootCategories.size === 0) {
        ElMessage.warning('请至少选择一个顶级分类下的叶子节点');
        return;
      }
    }
  }

  state.activeStep++;
};

// 上一步
const prevStep = () => {
  if (state.activeStep > 0) {
    state.activeStep--;
  }
};

// 触发文件夹上传
const triggerFolderUpload = () => {
  if (uploadFolderRef.value) {
    uploadFolderRef.value.click();
  }
};

// 处理文件夹变更
const handleFolderChange = (event: any) => {
  if (!state.ruleForm.name) {
    ElMessage.warning('请先填写数据集名称');
    return;
  }
  
  const files = event.target.files;
  if (files && files.length > 0) {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      // 添加到上传状态管理
      uploadStore.addFile(file);
    }
  }
  // 重置input以允许再次选择相同文件夹
  if (uploadFolderRef.value) {
    uploadFolderRef.value.value = '';
  }
};

// 处理文件变更
const handleFileChange = (file: any) => {
  if (!state.ruleForm.name) {
    ElMessage.warning('请先填写数据集名称');
    return false;
  }
  
  // 将文件添加到上传状态管理
  uploadStore.addFile(file);
  return true;
};

// 处理文件删除
const handleFileRemove = (file: any) => {
  const index = uploadStore.files.findIndex(f => f.name === file.name && f.relativePath === file.relativePath);
  if (index > -1) {
    uploadStore.files.splice(index, 1);
  }
};

// 上传单个文件
const uploadSingleFile = async (file: any) => {
  try {
    const fileName = file.relativePath;
    
    // 获取预签名上传URL
    const { data } = await getUploadUrl({
      filename: fileName,
      name: state.ruleForm.name,
      group: state.ruleForm.group
    });
    
    if (data.url) {
      // 上传文件
      await uploadFile(file.raw, data.url, (progress: number) => {
        uploadStore.updateFileProgress(file.name, progress);
      });
      
      // 将文件信息添加到表单
      state.ruleForm.files.push({
        name: fileName,
        size: file.raw.size,
        type: file.raw.type,
        object_name: data.object_name,
        relative_path: file.relativePath
      });

      uploadStore.updateFileStatus(file.name, 'success');
      return true;
    }
    return false;
  } catch (error) {
    console.error('文件上传失败:', error);
    uploadStore.updateFileStatus(file.name, 'error');
    return false;
  }
};

// 上传文件
const uploadFile = async (file: File, uploadUrl: string, onProgress: (progress: number) => void) => {
  try {
    const xhr = new XMLHttpRequest();
    
    await new Promise((resolve, reject) => {
      xhr.upload.onprogress = (e) => {
        if (e.lengthComputable) {
          const percentComplete = Math.round((e.loaded / e.total) * 100);
          onProgress(percentComplete);
        }
      };

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve(xhr.response);
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };

      xhr.onerror = () => reject(new Error('Upload failed'));
      xhr.onabort = () => reject(new Error('Upload aborted'));

      xhr.open('PUT', uploadUrl, true);
      xhr.setRequestHeader('Content-Type', file.type || 'application/octet-stream');
      xhr.send(file);
    });

    onProgress(100);
  } catch (error) {
    console.error('Upload error:', error);
    throw error;
  }
};

// 处理上传完成
const handleUploadComplete = () => {
  state.uploadFiles = [];
  closeDialog(state.ruleForm);
};

// 处理分类数据，从leaf_categories设置categoryMap
const setupCategoryMap = (row: any) => {
  if (row.leaf_categories && row.leaf_categories.length > 0) {
    // 重置分类映射
    state.ruleForm.categoryMap = {};
    
    // 遍历叶子分类
    row.leaf_categories.forEach((category: any) => {
      if (category.parent) {
        // 找到此叶子分类所属的顶级分类
        const rootCategory = findRootCategory(category.parent);
        if (rootCategory) {
          state.ruleForm.categoryMap[rootCategory] = category.id;
        }
      }
    });
  }
};

// 查找顶级分类ID
const findRootCategory = (parentId: number): number | null => {
  // 先查找直接父类
  const parent = state.rootCategories.find((cat: any) => cat.id === parentId);
  if (parent) {
    return parent.id;
  }
  
  // 如果直接父类不是顶级分类，则递归向上查找
  for (const category of state.rootCategories) {
    const children = category.children || [];
    const found = children.find((c: any) => c.id === parentId);
    if (found) {
      return category.id;
    }
  }
  
  return null;
};

// 打开弹窗
const openDialog = async (row: any) => {
  // 清空状态
  uploadStore.$reset();
  state.activeStep = 0;
  state.isEdit = !!row.id;
  
  // 如果有ID，表示是编辑模式
  if (row.id) {
    state.ruleForm = JSON.parse(JSON.stringify(row));
    state.ruleForm.categoryMap = {}; // 初始化分类映射
  } else {
    // 新增模式
    state.ruleForm = {
      id: 0,
      name: "",
      description: "",
      category: undefined,
      group: "",
      status: "online",
      categoryMap: {},
      file_list: [],
      files: []
    };
  }
  
  state.isShowDialog = true;
  state.loading = false;
  
  // 加载分类树
  await loadCategoryTree();
  
  // 如果是编辑模式，设置分类映射
  if (state.isEdit && row.leaf_categories) {
    setupCategoryMap(row);
  }
};

// 关闭弹窗
const closeDialog = (row?: object) => {
  proxy.mittBus.emit("onEditDatasetModule", row);
  state.isShowDialog = false;
};

// 取消
const onCancel = () => {
  closeDialog();
};

// 保存
const onSubmit = async () => {
  // 仅在新增模式下验证
  if (!state.isEdit && uploadStore.files.length === 0) {
    ElMessage.warning('请选择至少一个数据集文件');
    return;
  }
  
  state.loading = true;
  
  try {
    // 处理分类ID
    const categoryIds: number[] = [];
    for (const rootId in state.ruleForm.categoryMap) {
      const categoryId = state.ruleForm.categoryMap[rootId];
      if (categoryId) {
        categoryIds.push(categoryId);
      }
    }
    
    if (state.isEdit) {
      // 修改已有数据集
      await updateDataset(state.ruleForm.id, {
        ...state.ruleForm,
        category_ids: categoryIds
      });
      ElMessage.success("修改成功");
      state.loading = false;
      closeDialog(state.ruleForm);
    } else {
      // 新增数据集，先上传文件
      if (uploadStore.files.length > 0) {
        for (const file of uploadStore.files) {
          if (file.status === 'pending') {
            await uploadSingleFile(file);
          }
        }
        
        // 提交表单
        const formData = new FormData();
        formData.append('name', state.ruleForm.name);
        formData.append('group', state.ruleForm.group || 'default');
        formData.append('description', state.ruleForm.description);
        formData.append('status', state.ruleForm.status);
        
        // 添加类别ID
        categoryIds.forEach(id => {
          formData.append('category_ids', id.toString());
        });
        
        formData.append('files', JSON.stringify(state.ruleForm.files));
        
        await uploadDataset(formData);
        ElMessage.success("数据集上传成功");
      }
      
      state.loading = false;
      closeDialog(state.ruleForm);
    }
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('操作失败，请重试');
    state.loading = false;
  }
};

defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

.upload-buttons {
  display: flex;
  gap: 10px;
}

.file-list-table {
  margin-top: 15px;
}

.step-description {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #6B48FF;
  
  .step-title {
    font-size: 16px;
    font-weight: 600;
    color: #1F2A44;
    margin-bottom: 5px;
  }
  
  .step-info {
    font-size: 14px;
    color: #64748B;
    line-height: 1.5;
  }
}

.model-upload-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 25px;
  }

  .upload-steps {
    margin-bottom: 30px;
    
    :deep(.el-step__title) {
      font-size: 16px;
      font-weight: 500;
      
      &.is-process {
        color: #6B48FF;
        font-weight: 600;
      }
      
      &.is-success {
        color: #67C23A;
      }
    }
    
    :deep(.el-step__head.is-process) {
      color: #6B48FF;
      border-color: #6B48FF;
    }
    
    :deep(.el-step__head.is-success) {
      color: #67C23A;
      border-color: #67C23A;
    }
  }
}

.category-select-container {
  max-height: 250px;
  overflow-y: auto;
  padding: 10px;
  border-radius: 8px;
  background-color: #f9f9fa;
  margin-bottom: 10px;
}

.category-group {
  margin-bottom: 10px;

  .root-category-label {
    font-weight: 500;
    margin-bottom: 5px;
  }

  .category-cascader {
    width: 100%;
  }
}

:deep(.markdown-editor) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #E4E7ED;
  
  .cm-editor {
    font-size: 14px;
    font-family: 'Consolas', 'Monaco', monospace;
  }
  
  .md-editor-preview-wrapper {
    padding: 16px;
    font-size: 14px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  width: 100%;
}
</style> 