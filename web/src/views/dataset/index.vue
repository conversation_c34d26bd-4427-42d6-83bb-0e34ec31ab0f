<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧分类树 -->
      <el-col :span="6" class="left-card">
        <el-card shadow="always">
          <template #header>
            <div class="card-header">
              <span class="card-header-text">数据集分类</span>
            </div>
          </template>
          
          <!-- 已选择的类别 -->
          <div class="selected-categories" v-if="state.selectedCategories.length > 0">
            <div class="selected-title">
              <span>已选类别：</span>
              <el-button 
                type="primary" 
                link 
                size="small" 
                @click="clearSelectedCategories"
              >
                <SvgIcon name="elementDelete" />
                清空
              </el-button>
            </div>
            <div class="selected-tags">
              <el-tag
                v-for="category in state.selectedCategories"
                :key="category.id"
                closable
                size="small"
                @close="removeSelectedCategory(category)"
              >
                {{ category.name }}
              </el-tag>
            </div>
          </div>
          
          <!-- 顶级分类导航 -->
          <div class="top-category-nav">
            <el-tabs v-model="state.activeRootCategory" class="root-tabs">
              <el-tab-pane 
                v-for="category in state.rootCategories" 
                :key="category.id"
                :label="category.name"
                :name="category.id.toString()"
              ></el-tab-pane>
              <!-- 当没有顶级分类时显示提示 -->
              <div v-if="!state.rootCategories || state.rootCategories.length === 0" class="empty-category-tip">
                暂无数据集分类，请先<router-link to="/dataset/category">创建分类</router-link>
              </div>
            </el-tabs>
          </div>
          
          <!-- 分类树 -->
          <div class="category-tree-container">
            <el-tree
              ref="treeRef"
              :data="state.currentCategoryTree"
              :props="state.defaultProps"
              @node-click="handleNodeClick"
              default-expand-all
              highlight-current
              v-if="state.currentCategoryTree.length > 0"
              node-key="id"
              class="category-tree"
            >
              <!-- 自定义节点渲染 -->
              <template #default="{ node, data }">
                <div class="custom-tree-node">
                  <SvgIcon 
                    v-if="node.isLeaf" 
                    name="elementFolderOpened" 
                    class="folder-icon"
                  />
                  <SvgIcon 
                    v-else 
                    name="elementFolder" 
                    class="folder-icon"
                  />
                  <span class="node-label">{{ node.label }}</span>
                  <span class="node-count" v-if="data.count">{{ data.count }}</span>
                </div>
              </template>
            </el-tree>
            <div v-else class="empty-tree">
              <el-empty description="暂无子分类" :image-size="60" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧内容区 -->
      <el-col :span="18">
        <!-- 搜索栏 -->
        <el-card shadow="always" class="search-card">
          <div class="search-header">
            <el-form :model="state.queryParams" ref="queryForm" :inline="true">
              <el-form-item label="数据集名称" prop="search">
                <el-input
                  v-model="state.queryParams.search"
                  placeholder="请输入数据集名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" plain @click="handleQuery">
                  <SvgIcon name="elementSearch"/>
                  搜索功能
                </el-button>
                <el-button @click="resetQuery">
                  <SvgIcon name="elementRefresh"/>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>

        <!-- 数据集列表 -->
        <el-row :gutter="16" class="model-list">
          <el-col :xs="24" :sm="12" :md="8" :lg="8" v-for="dataset in state.datasetList" :key="dataset.id">
            <div class="model-card-wrapper">
              <el-card class="model-card" shadow="hover" @click="handleViewDetail(dataset)">
                <!-- 数据集卡片顶部：标题和标签 -->
                <div class="model-card-header">
                  <div class="model-title-wrapper">
                    <h3 class="model-title">{{ dataset.name }}</h3>
                    <span class="new-badge" v-if="dataset.isNew">NEW</span>
                  </div>
                  <div class="model-categories">
                    <el-tag 
                      v-for="category in dataset.leaf_categories.slice(0, 2)" 
                      :key="category.id" 
                      size="small"
                      class="category-tag">
                      {{ category.name }}
                    </el-tag>
                    <span class="more-categories" v-if="dataset.leaf_categories.length > 2">+{{ dataset.leaf_categories.length - 2 }}</span>
                  </div>
                </div>
                
                <!-- 数据集卡片中部：描述 -->
                <div class="model-description">
                  <p class="description-text">{{ dataset.description || '暂无描述' }}</p>
                </div>
                
                <!-- 数据集卡片底部：元数据和统计信息 -->
                <div class="model-card-footer">
                  <div class="model-meta">
                    <div class="meta-item creator">
                      <SvgIcon name="elementUser" class="meta-icon"/> 
                      <span class="meta-text">{{ dataset.creator_name }}</span>
                    </div>
                    <div class="meta-item date">
                      <SvgIcon name="elementCalendar" class="meta-icon"/> 
                      <span class="meta-text">{{ formatDate(dataset.create_datetime) }}</span>
                    </div>
                  </div>
                  
                  <div class="model-stats">
                    <div class="stats-item stars">
                      <SvgIcon name="elementStar" class="stats-icon"/>
                      <span class="stats-count">{{ formatCount(dataset.stars) }}</span>
                    </div>
                    <div class="stats-item downloads">
                      <SvgIcon name="elementDownload" class="stats-icon"/>
                      <span class="stats-count">{{ formatCount(dataset.downloads) }}</span>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>

        <!-- 无数据时的空状态 -->
        <div class="empty-state" v-if="state.datasetList.length === 0 && !state.loading">
          <el-empty 
            description="暂无符合条件的数据集" 
            :image-size="200">
          </el-empty>
        </div>

        <!-- 加载状态 -->
        <div class="loading-state" v-if="state.loading">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>

        <!-- 分页 -->
        <div v-show="state.total > 0">
          <el-divider></el-divider>
          <el-pagination
              background
              :total="state.total"
              :current-page="state.queryParams.page"
              :page-size="state.queryParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { getDatasetList, deleteDataset } from '@/api/dataset/datasets'
import { getDatasetCategoryTree } from '@/api/dataset/categories'
import 'md-editor-v3/lib/style.css';
import { ElMessage, ElMessageBox } from 'element-plus'

// 路由实例
const router = useRouter()

// 状态管理
const state = reactive({
  loading: false,
  datasetList: [] as any[],
  categoryTree: [] as any[],
  total: 0,
  queryParams: {
    page: 1,
    pageSize: 10,
    category_ids: [] as number[],
    search: undefined
  },
  defaultProps: {
    children: 'children',
    label: 'name'
  },
  uploadForm: {
    name: '',
    group: '',
    categoryMap: {} as Record<string, number | null>,
    description: '',
    parameters: {} as Record<string, any>,
    metrics: {} as Record<string, any>,
    files: [] as any[],
  },
  rootCategories: [] as any[],
  activeRootCategory: '',
  currentCategoryTree: [] as any[],
  selectedCategories: [] as any[]
})

/** 查询数据集列表 */
const handleQuery = async () => {
  state.loading = true
  try {
    // 构建查询参数
    const params: any = { ...state.queryParams }
    
    // 将选中的类别ID转换为请求参数
    if (state.selectedCategories.length > 0) {
      params.category_ids = state.selectedCategories.map((c: any) => c.id)
      // 确保移除旧的category参数，避免冲突
      if ('category' in params) {
        delete params.category
      }
    }
    
    console.log('查询参数:', params) // 添加日志便于调试
    const res = await getDatasetList(params)
    state.datasetList = res.data.data
    state.total = res.data.total
  } catch (error) {
    console.error('获取数据集列表失败:', error)
  } finally {
    state.loading = false
  }
}

/** 重置查询参数 */
const resetQuery = () => {
  state.queryParams.search = undefined
  state.selectedCategories = []
  state.queryParams.page = 1
  handleQuery()
}

/** 获取分类树数据 */
const loadCategoryTree = async () => {
  try {
    const res = await getDatasetCategoryTree()
    state.categoryTree = res.data
    // 过滤出顶级分类（parent === null的分类）
    state.rootCategories = res.data.filter((category: any) => category.parent === null)
    
    // 确保有顶级分类后再设置activeRootCategory
    if (state.rootCategories && state.rootCategories.length > 0) {
      state.activeRootCategory = state.rootCategories[0].id.toString()
      // 获取当前选中顶级分类的子分类
      updateCurrentCategoryTree()
    } else {
      // 没有顶级分类，清空当前树
      state.activeRootCategory = ''
      state.currentCategoryTree = []
      console.warn('没有找到任何顶级分类')
    }
  } catch (error) {
    console.error('获取分类树失败:', error)
  }
}

/** 更新当前显示的分类树 */
const updateCurrentCategoryTree = () => {
  if (state.activeRootCategory) {
    // 查找当前选中的顶级分类
    const activeRootCategory = state.rootCategories.find(
      (c: any) => c.id === parseInt(state.activeRootCategory)
    )
    
    // 如果找到了且有children属性
    if (activeRootCategory && activeRootCategory.children) {
      state.currentCategoryTree = activeRootCategory.children
    } else {
      console.warn('没有找到任何子分类')
      state.currentCategoryTree = []
    }
  } else {
    console.warn('没有选中任何顶级分类')
    state.currentCategoryTree = []
  }
}

/** 分类节点点击事件 */
const handleNodeClick = (data: any) => {
  // 检查是否已经选择了这个类别
  const index = state.selectedCategories.findIndex((c: any) => c.id === data.id)
  
  // 如果没选择，则添加到已选列表
  if (index === -1) {
    // 确保data对象包含必要的属性
    state.selectedCategories.push({
      id: data.id,
      name: data.name
    })
    
    console.log('已选类别:', state.selectedCategories) // 添加日志便于调试
    
    // 重置到第一页并重新查询
    state.queryParams.page = 1
    handleQuery()
  }
}

/** 查看数据集详情 */
const handleViewDetail = (dataset: any) => {
  router.push(`/dataset/datasets/${dataset.id}`)
}

/** 分页大小改变 */
const handleSizeChange = (val: number) => {
  state.queryParams.pageSize = val
  handleQuery()
}

/** 页码改变 */
const handleCurrentChange = (val: number) => {
  state.queryParams.page = val
  handleQuery()
}

// 页面加载时
onMounted(() => {
  loadCategoryTree()
  handleQuery()
})

// 监听activeRootCategory的变化
watch(() => state.activeRootCategory, (newVal) => {
  if (newVal) {
    updateCurrentCategoryTree()
  }
})

// 移除已选类别
const removeSelectedCategory = (category: any) => {
  // 从已选列表中移除
  state.selectedCategories = state.selectedCategories.filter((c: any) => c.id !== category.id)
  
  // 记录日志
  console.log('移除类别后的已选类别:', state.selectedCategories)
  
  // 重置到第一页并重新查询
  state.queryParams.page = 1
  handleQuery()
}

// 清空已选类别
const clearSelectedCategories = () => {
  state.selectedCategories = [];
  state.queryParams.page = 1;
  handleQuery();
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
}

// 格式化数字（如1000->1k）
const formatCount = (count: number) => {
  if (!count && count !== 0) return '0';
  if (count < 1000) return count.toString();
  return (count / 1000).toFixed(1) + 'k';
}

// 处理模型点击
const handleModelClick = (model: any) => {
  router.push({
    path: `/model/maintenance`,
    query: { 
      id: model.id,
      mode: 'view'
    }
  })
}

// 查看详情
const handleView = (dataset: any) => {
  router.push({
    path: `/dataset/detail/${dataset.id}`
  })
}

// 编辑数据集
const handleEdit = (dataset: any) => {
  router.push({
    path: `/dataset/maintenance`,
    query: { 
      id: dataset.id,
      mode: 'edit'
    }
  })
}

// 删除数据集
const handleDelete = (dataset: any) => {
  ElMessageBox.confirm(
    '确定要删除该数据集吗？此操作不可恢复',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteDataset(dataset.id)
      ElMessage.success('删除成功')
      handleQuery()
    } catch (error) {
      console.error('删除数据集失败:', error)
    }
  })
}

// 新增数据集
const handleAdd = () => {
  router.push({
    path: '/dataset/maintenance',
    query: { mode: 'create' }
  })
}
</script>

<style lang="scss" scoped>
.app-container {
  .left-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    position: sticky;
    top: 24px;
    min-height: 600px;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px;
    }

    .card-header {
      border-bottom: 1px solid #EBEEF5;
      margin-bottom: 16px;
      .card-header-text {
        font-size: 16px;
        font-weight: 600;
        color: #1F2A44;
        display: flex;
        align-items: center;
        
        &::before {
          content: '';
          width: 4px;
          height: 16px;
          background-color: #6B48FF;
          margin-right: 8px;
          border-radius: 2px;
        }
      }
    }
  }

  .selected-categories {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #e0e0e0;

    .selected-title {
      font-weight: 500;
      margin-bottom: 8px;
      color: #1F2A44;
      display: flex;
      justify-content: space-between;
      align-items: center;

      :deep(.el-button) {
        padding: 0;
        height: auto;
        font-size: 13px;
        
        .svg-icon {
          margin-right: 4px;
          font-size: 14px;
        }
        
        &:hover {
          opacity: 0.8;
        }
      }
    }

    .selected-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      :deep(.el-tag) {
        background-color: #f0f7ff;
        color: #1F2A44;
        font-size: 12px;
        border: 1px solid #d9ecff;
        border-radius: 4px;
        padding: 3px 8px;
        margin-bottom: 4px;
        
        .el-tag__close {
          color: #409EFF;
          
          &:hover {
            background-color: #409EFF;
            color: #fff;
          }
        }
      }
    }
  }

  .root-tabs {
    width: 100%;
    margin-bottom: 16px;
    
    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
    
    :deep(.el-tabs__nav) {
      width: 100%;
    }
    
    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      font-size: 14px;
      padding: 0 10px;
    }
    
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
    }
    
    :deep(.el-tabs__active-bar) {
      background-color: #6B48FF;
    }
  }

  .category-tree-container {
    flex: 1;
    min-height: 350px;
    overflow-y: auto;
    padding: 12px 6px;
    border-radius: 8px;
    background-color: #fff;
    border: 1px solid #f0f0f0;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
      
      &:hover {
        background-color: #c0c4cc;
      }
    }
    
    .category-tree {
      flex: 1;
      min-height: 300px;
      background-color: transparent;
      
      :deep(.el-tree-node) {
        margin: 4px 0;
      }
      
      :deep(.el-tree-node__content) {
        height: 36px;
        border-radius: 6px;
        transition: all 0.2s ease;
        padding: 0 12px;
        
        &:hover {
          background-color: #f5f1ff;
        }
        
        &.is-current {
          background-color: #efe7ff;
          color: #6B48FF;
          font-weight: 500;
        }
      }
      
      :deep(.el-tree-node__label) {
        font-size: 14px;
      }
      
      :deep(.el-tree-node__expand-icon) {
        color: #6B48FF;
        
        &.is-leaf {
          color: transparent;
        }
      }
    }
    
    .custom-tree-node {
      display: flex;
      align-items: center;
      width: 100%;
      font-size: 14px;
      
      .folder-icon {
        margin-right: 8px;
        width: 16px;
        height: 16px;
        color: #6B48FF;
      }
      
      .node-label {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .node-count {
        margin-left: 8px;
        background-color: #f0f7ff;
        color: #409EFF;
        padding: 0 6px;
        border-radius: 10px;
        font-size: 12px;
      }
    }
  }

  .empty-category-tip {
    font-size: 14px;
    color: #909399;
    margin: 10px 0;
    text-align: center;
    
    a {
      color: #6B48FF;
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .empty-tree {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 300px;
  }

  .search-card {
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .search-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    :deep(.el-form-item) {
      margin-bottom: 0;

      .el-form-item__label {
        color: #1F2A44;
        font-size: 14px;
        font-weight: 500;
      }

      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 0 0 1px #E4E7ED;
        
        &:hover {
          box-shadow: 0 0 0 1px #6B48FF;
        }
        
        &.is-focus {
          box-shadow: 0 0 0 1px #6B48FF;
        }
      }
    }

    :deep(.el-button) {
      border-radius: 8px;
      font-size: 14px;
      padding: 8px 20px;
      height: 40px;
    }
  }

  .form-tip {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
  }

  .model-list {
    margin-top: 16px;
  }

  .model-card-wrapper {
    margin-bottom: 24px;
    height: 100%;
    perspective: 1000px;
  }

  .model-card {
    height: 240px;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    border: 1px solid #EAECF0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(107, 72, 255, 0.15);
      border-color: #6B48FF;
    }
    
    .model-card-header {
      padding: 16px 16px 12px;
      border-bottom: 1px solid #F2F4F7;
      
      .model-title-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .model-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #101828;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 85%;
        }
        
        .new-badge {
          margin-left: 8px;
          background-color: #ECFDF3;
          color: #027A48;
          padding: 2px 8px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 500;
        }
      }
      
      .model-categories {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 6px;
        
        .category-tag {
          background-color: #F9F5FF;
          border-color: #F9F5FF;
          color: #6941C6;
          font-weight: normal;
          border-radius: 16px;
        }
        
        .more-categories {
          font-size: 12px;
          color: #6941C6;
          background-color: #F9F5FF;
          padding: 2px 8px;
          border-radius: 16px;
        }
      }
    }
    
    .model-description {
      flex: 1;
      padding: 12px 16px;
      overflow: hidden;
      
      .description-text {
        color: #475467;
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
    
    .model-card-footer {
      padding: 12px 16px;
      border-top: 1px solid #F2F4F7;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .model-meta {
        display: flex;
        gap: 16px;
        
        .meta-item {
          display: flex;
          align-items: center;
          gap: 6px;
          
          .meta-icon {
            color: #667085;
            font-size: 14px;
          }
          
          .meta-text {
            color: #667085;
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100px;
          }
          
          &.creator .meta-text {
            font-weight: 500;
            color: #6941C6;
          }
        }
      }
      
      .model-stats {
        display: flex;
        gap: 16px;
        
        .stats-item {
          display: flex;
          align-items: center;
          gap: 6px;
          
          .stats-icon {
            color: #667085;
            font-size: 14px;
          }
          
          .stats-count {
            color: #667085;
            font-size: 12px;
            font-weight: 500;
          }
          
          &.stars {
            .stats-icon {
              color: #F79009;
            }
            .stats-count {
              color: #F79009;
            }
          }
        }
      }
    }
  }
  
  .empty-state {
    margin: 60px 0;
    text-align: center;
    
    :deep(.el-empty__description) {
      margin-top: 16px;
      font-size: 16px;
      color: #667085;
    }
    
    .el-button {
      margin-top: 24px;
      padding: 10px 24px;
      font-weight: 600;
      border-radius: 8px;
    }
  }
  
  .loading-state {
    margin: 24px 0;
    
    :deep(.el-skeleton) {
      margin-bottom: 16px;
    }
  }

  .category-group {
    margin-bottom: 10px;

    .root-category-label {
      font-weight: 500;
      margin-bottom: 5px;
    }

    .category-cascader {
      width: 100%;
    }
  }

  .category-select-container {
    max-height: 250px;
    overflow-y: auto;
    padding: 10px;
    border-radius: 8px;
    background-color: #f9f9f9;
    margin-bottom: 10px;
  }

  .related-models {
    margin-top: 16px;
    
    .section-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
    }
    
    .model-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .model-tag {
        cursor: pointer;
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>