<template>
  <div class="system-category-container">
    <el-dialog v-model="state.isShowDialog" width="700px" center>
      <template #header>
        <div style="font-size: large">{{ title }}</div>
      </template>
      <el-form
        :model="state.ruleForm"
        :rules="state.ruleRules"
        ref="ruleFormRef"
        label-width="80px"
      >
        <el-row :gutter="30">
          <el-col :span="24" >
            <el-form-item label="上级类别" prop="parent">
              <el-cascader
                v-model="state.ruleForm.parent"
                :options="state.categoryOptions"
                class="w100"
                :props="{
                  value: 'id',
                  label: 'name',
                  checkStrictly: true,
                  emitPath: false,
                  expandTrigger: 'hover'
                }"
                clearable
                placeholder="不选择则为顶级类别"
                :show-all-levels="false"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" >
            <el-form-item label="类别名称" prop="name">
              <el-input
                v-model="state.ruleForm.name"
                placeholder="请输入类别名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" >
            <el-form-item label="类别编码" prop="code">
              <el-input
                v-model="state.ruleForm.code"
                placeholder="请输入类别编码"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" >
            <el-form-item label="类别状态" prop="is_active">
              <el-radio-group v-model="state.ruleForm.is_active">
                <el-radio :label="true">激活</el-radio>
                <el-radio :label="false">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" >
            <el-form-item label="排序" prop="order">
              <el-input-number
                v-model="state.ruleForm.order"
                placeholder="类别排序"
                clearable
                controls-position="right"
                :min="0"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel">取 消</el-button>
          <el-button type="primary" @click="onSubmit" :loading="state.loading">
            {{ state.ruleForm.id ? '修 改' : '新 增' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, unref, defineProps, defineEmits } from "vue";
import { createDatasetCategory, updateDatasetCategory, getDatasetCategoryTree } from "@/api/dataset/categories";
import { ElMessage } from "element-plus";

const props = defineProps({
  title: {
    type: String,
    default: () => "",
  },
})

const emit = defineEmits(['refresh']);

const ruleFormRef = ref<HTMLElement | null>(null);
const state = reactive({
  // 是否显示弹出层
  isShowDialog: false,
  loading: false,
  // 类别对象
  ruleForm: {
    id: undefined, // 类别ID
    name: "", // 类别名称
    code: "", // 类别编码
    parent: undefined, // 父类别ID
    order: 1, // 类别排序
    is_active: true, // 类别状态
  },
  // 类别树选项
  categoryOptions: [] as any[],
  // 表单校验
  ruleRules: {
    name: [
      { required: true, message: "类别名称不能为空", trigger: "blur" },
    ],
    code: [
      { required: true, message: "类别编码不能为空", trigger: "blur" },
    ],
    is_active: [
      { required: true, message: "类别状态不能为空", trigger: "blur" },
    ],
    order: [
      { required: true, message: "排序不能为空", trigger: "blur" },
    ],
  },
});

// 打开弹窗
const openDialog = (row: any) => {
  state.ruleForm = JSON.parse(JSON.stringify(row));
  state.isShowDialog = true;
  state.loading = false;

  // 查询类别下拉树结构
  getDatasetCategoryTree().then((response: any) => {
    state.categoryOptions = [];
    const rootCategory: any = { id: null, name: '顶级类别', children: [] };
    rootCategory.children = response.data
    state.categoryOptions.push(rootCategory)
  }).catch(error => {
    ElMessage.error(`获取类别树失败: ${error.message}`);
    console.error('获取类别树失败:', error);
  });
};

// 关闭弹窗
const closeDialog = () => {
  state.isShowDialog = false;
};

// 取消
const onCancel = () => {
  closeDialog();
};

// 保存
const onSubmit = () => {
  const formWrap = unref(ruleFormRef) as any;
  if (!formWrap) return;
  formWrap.validate((valid: boolean) => {
    if (valid) {
      state.loading = true;
      
      // 构建提交数据
      const submitData = {
        id: state.ruleForm.id,
        name: state.ruleForm.name,
        code: state.ruleForm.code,
        parent: state.ruleForm.parent || null,
        order: state.ruleForm.order,
        is_active: state.ruleForm.is_active
      };
      
      if (state.ruleForm.id !== undefined) {
        // 修改
        updateDatasetCategory(state.ruleForm.id, submitData).then(() => {
          ElMessage.success("修改成功");
          state.loading = false;
          closeDialog();
          emit('refresh'); // 通知父组件刷新数据
        }).catch(error => {
          console.error("修改失败:", error);
          state.loading = false;
          ElMessage.error("修改失败");
        });
      } else {
        // 新增
        createDatasetCategory(submitData).then(() => {
          ElMessage.success("新增成功");
          state.loading = false;
          closeDialog();
          emit('refresh'); // 通知父组件刷新数据
        }).catch(error => {
          console.error("新增失败:", error);
          state.loading = false;
          ElMessage.error("新增失败");
        });
      }
    }
  });
};

// 暴露方法给父组件调用
defineExpose({
  openDialog,
});
</script>

<style scoped>
.w100 {
  width: 100%;
}
</style>
