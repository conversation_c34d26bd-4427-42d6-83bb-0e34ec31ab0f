<template>
  <div class="app-container">
    <el-card shadow="always">
      <!-- 查询 -->
      <el-form :model="state.queryParams" ref="queryForm" :inline="true" label-width="68px">
        <el-form-item label="类别名称" prop="name">
          <el-input
              placeholder="请输入类别名称模糊查询"
              clearable
              @keyup.enter.native="handleQuery"
              v-model="state.queryParams.search"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
              v-model="state.queryParams.is_active"
              placeholder="类别状态"
              clearable
              style="width: 150px"
              @change="handleQuery"
          >
            <el-option label="激活" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格主体 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="card-header-text">数据集类别列表</span>
          <div>
            <el-button type="primary"
                       plain
                       @click="onOpenAddModule">
              <el-icon><Plus /></el-icon>
              新增
            </el-button>
          </div>
        </div>
      </template>
      <el-table
          v-loading="state.loading"
          :data="state.tableData"
          row-key="id"
          border
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          default-expand-children="false"
      >
        <el-table-column
            prop="name"
            label="类别名称"
            width="200"
        ></el-table-column>
        <el-table-column
            prop="code"
            label="类别编码"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="create_datetime"
            label="创建时间"
            width="180"
        >
          <template #default="scope">
            <span>{{ dateStrFormat(scope.row.create_datetime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
            prop="order"
            label="排序"
            width="80"
        ></el-table-column>
        <el-table-column
            prop="is_active"
            label="状态"
            width="100"
        >
          <template #default="scope">
            <el-tag
                :type="scope.row.is_active ? 'success' : 'danger'"
                disable-transitions
            >{{ scope.row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
            label="子类别数"
            align="center"
            width="100"
        >
          <template #default="scope">
            {{ Array.isArray(scope.row.children) ? scope.row.children.length : 0 }}
          </template>
        </el-table-column>
        <el-table-column
            label="关联数据集数"
            align="center"
            width="100"
        >
          <template #default="scope">
            {{ typeof scope.row.datasets_count === 'number' ? scope.row.datasets_count : 0 }}
          </template>
        </el-table-column>
        <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
        >
          <template #default="scope">
            <el-button type="primary" size="small" @click="onOpenEditModule(scope.row)">
              <el-icon><Edit /></el-icon>
              修改
            </el-button>
            <el-button type="success" size="small" @click="onOpenAddModule(scope.row)">
              <el-icon><Plus /></el-icon>
              添加子类别
            </el-button>
            <el-button type="danger" size="small" @click="onTableRowDel(scope.row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加或修改类别对话框 -->
    <edit-module ref="editModuleRef" :title="state.title" @refresh="handleQuery"/>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import {
	deleteDatasetCategory,
	getDatasetCategoryList,
} from '@/api/dataset/categories';
import { Search, Refresh, Plus, Edit, Delete } from '@element-plus/icons-vue';
import EditModule from "./component/editModule.vue";
import { dateStrFormat } from "@/utils/formatTime";
import { toArrayTree } from '@/utils/transListDataToTreeData';

const editModuleRef = ref();
const state = reactive({
  // 遮罩层
  loading: true,
  // 弹出层标题
  title: "",
  // 类别表格树数据
  tableData: [] as any,
  // 类别树
  categoryTree: [] as any,
  // 当前选中的类别ID
  currentCategoryId: null as number | null,
  // 查询参数
  queryParams: {
    search: undefined,
    is_active: undefined,
    pageSize: 10000,
    page: 1
  },
});

/** 查询类别列表 */
const handleQuery = () => {
  state.loading = true;
  getDatasetCategoryList(state.queryParams).then((response: any) => {
    state.tableData = toArrayTree(response.data.data);
    state.loading = false;
  });
};

/** 重置按钮操作 */
const resetQuery = () => {
  state.queryParams.search = undefined;
  state.queryParams.is_active = undefined;
  handleQuery();
};

// 打开新增类别弹窗
const onOpenAddModule = (row: any = {}) => {
  let parentId = row.id || null;
  const newRow = { parent: parentId };
  state.title = parentId ? "添加子类别" : "添加顶级类别";
  editModuleRef.value.openDialog(newRow);
};

// 打开编辑类别弹窗
const onOpenEditModule = (row: object) => {
  state.title = "修改类别";
  editModuleRef.value.openDialog(row);
};

/** 删除按钮操作 */
const onTableRowDel = (row: any) => {
  // 首先检查是否有子类别
  if (row.subcat && row.subcat.length > 0) {
    ElMessage.warning("该类别下有子类别，无法直接删除");
    return;
  }
  
  // 检查是否有关联的数据集
  if (row.datasets && row.datasets.length > 0) {
    ElMessage.warning("该类别下有关联的数据集，无法直接删除");
    return;
  }
  
  ElMessageBox({
    message: '是否确认删除名称为"' + row.name + '"的类别?',
    title: "警告",
    showCancelButton: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(function () {
    return deleteDatasetCategory(row.id).then(() => {
      handleQuery();
      ElMessage.success("删除成功");
    });
  }).catch(() => {});
};

// 页面加载时
onMounted(() => {
  // 查询类别信息
  handleQuery();
});
</script>

<style scoped>
</style>
