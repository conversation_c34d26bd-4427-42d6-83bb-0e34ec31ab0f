<template>
  <el-form
      ref="loginFormRef"
      size="large"
      :model="state.loginForm"
      :rules="state.rules"
      class="login-content-form"
  >
    <el-form-item class="login-animation-one">
      <el-input
          type="text"
          :placeholder="$t('message.account.accountPlaceholder1')"
          v-model="state.loginForm.username"
          clearable
          autocomplete="off"
      >
        <template #prefix>
          <el-icon class="el-input__icon"><elementUser /></el-icon>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item class="login-animation-two">
      <el-input
          :type="state.isShowPassword ? 'text' : 'password'"
          :placeholder="$t('message.account.accountPlaceholder2')"
          v-model="state.loginForm.password"
          autocomplete="off"
      >
        <template #prefix>
          <el-icon class="el-input__icon"><elementUnlock /></el-icon>
        </template>
        <template #suffix>
          <i
              class="iconfont el-input__icon login-content-password"
              :class="state.isShowPassword ? 'icon-yincangmima' : 'icon-xianshimima'"
              @click="state.isShowPassword = !state.isShowPassword"
          >
          </i>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item class="login-animation-four">
      <el-button
          type="primary"
          class="login-content-submit"
          round
          @click="login"
          :loading="state.loading.signIn"
      >
        <span>{{ $t("message.account.accountBtnText") }}</span>
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import { initBackEndControlRoutes } from "@/router";
import { Session } from "@/utils/storage";
import Cookies from 'js-cookie';
import { signIn } from "@/api/login";
import { formatAxis } from "@/utils/formatTime";

const { t } = useI18n();
const loginFormRef: any = ref(null);

const route = useRoute();
const router = useRouter();
const state = reactive({
  loginForm: {
    username: "admin",
    password: "123456",
  },
  rules: {
    username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
    password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  },
  isShowPassword: false,
  loading: {
    signIn: false,
  },
});

onMounted(() => {
});

const login = () => {
  loginFormRef.value.validate((valid: boolean) => {
    if (valid) {
      onSignIn();
    } else {
      return false;
    }
  });
};

const onSignIn = async () => {
  state.loading.signIn = true;
  try {
    const loginRespon = await signIn(state.loginForm);
    const loginRes = loginRespon.data;
    Session.set("token", "JWT " + loginRes.access);
    Cookies.set('userName', state.loginForm.username);

    await initBackEndControlRoutes();
    signInSuccess();
  } catch (e) {
    state.loading.signIn = false;
    ElMessage.error("登录失败，请检查用户名和密码是否正确");
  }
};

const signInSuccess = () => {
  const currentTimeInfo = formatAxis(new Date());
  if (route.query?.redirect) {
    router.push({
      path: route.query?.redirect as string,
      query:
          Object.keys(route.query?.params as string).length > 0
              ? JSON.parse(route.query?.params as string)
              : "",
    });
  } else {
    router.push("/");
  }
  setTimeout(() => {
    state.loading.signIn = false;
    const signInText = t("message.signInText");
    ElMessage.success(`${currentTimeInfo}，${signInText}`);
  }, 300);
};
</script>

<style scoped lang="scss">
.login-content-form {
  margin-top: 20px;
  .login-animation-one,
  .login-animation-two,
  .login-animation-three,
  .login-animation-four {
    opacity: 0;
    animation-name: error-num;
    animation-duration: 0.5s;
    animation-fill-mode: forwards;
  }
  .login-animation-one {
    animation-delay: 0.1s;
  }
  .login-animation-two {
    animation-delay: 0.2s;
  }
  .login-animation-three {
    animation-delay: 0.3s;
  }
  .login-animation-four {
    animation-delay: 0.4s;
    margin-bottom: 5px;
  }

  .login-content-password {
    display: inline-block;
    width: 25px;
    cursor: pointer;
    &:hover {
      color: #909399;
    }
  }
  .login-content-code {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .login-content-code-img {
      width: 100%;
      height: 40px;
      line-height: 40px;
      background-color: #ffffff;
      border: 1px solid rgb(220, 223, 230);
      color: #333;
      font-size: 16px;
      font-weight: 700;
      letter-spacing: 5px;
      text-indent: 5px;
      text-align: center;
      cursor: pointer;
      transition: all ease 0.2s;
      border-radius: 4px;
      user-select: none;
      &:hover {
        border-color: #c0c4cc;
        transition: all ease 0.2s;
      }
    }
  }
  .login-content-submit {
    width: 100%;
    letter-spacing: 2px;
    font-weight: 300;
    margin-top: 15px;
  }
}
</style>
