/**
 * 最顶部 card
 * @returns 返回模拟数据
 */
export const topCardItemList = [
	{
		title: '项目总数',
		titleNum: '10+',
		tip: '开源项目',
		tipNum: '5',
		color: '#3DD2B4',
		iconColor: '#14DAB2',
		icon: 'iconfont icon-github',
	},
	{
		title: '文章总数',
		titleNum: '50+',
		tip: '技术博客',
		tipNum: '持续更新',
		color: '#8595F4',
		iconColor: '#92A1F4',
		icon: 'iconfont icon-blog',
	},
	{
		title: '平台用户',
		titleNum: '100+',
		tip: '活跃用户',
		tipNum: '30+',
		color: '#E88662',
		iconColor: '#DE5C2C',
		icon: 'iconfont icon-user',
	},
];

/**
 * 个人链接
 * @returns 返回模拟数据
 */
export const environmentList = [
	{
		icon: 'iconfont icon-github',
		label: 'GitHub',
		value: 'github.com/taskPyroer',
		iconColor: '#24292e',
		link: 'https://github.com/taskPyroer'
	},
	{
		icon: 'iconfont icon-gitee',
		label: 'Gitee',
		value: 'gitee.com/hu_yupeng123',
		iconColor: '#c71d23',
		link: 'https://gitee.com/hu_yupeng123/projects'
	},
	{
		icon: 'iconfont icon-wechat',
		label: '公众号',
		value: '布鲁的Python之旅',
		iconColor: '#07c160'
	},
	{
		icon: 'iconfont icon-spider',
		label: '爬虫平台',
		value: 'docs.taskpyro.cn',
		iconColor: '#1890ff',
		link: 'http://docs.taskpyro.cn'
	},
];

/**
 * 动态信息
 * @returns 返回模拟数据
 */
export const activitiesList = [
	{
		time1: '今天',
		time2: '12:20:30',
		title: '平台更新',
		label: '爬虫管理平台2.0版本发布'
	},
	{
		time1: '昨天',
		time2: '18:30:00',
		title: '文章发布',
		label: '发布新的Python技术文章'
	},
	{
		time1: '本周',
		time2: '09:15:00',
		title: '项目开源',
		label: '新开源项目上线GitHub'
	},
];
