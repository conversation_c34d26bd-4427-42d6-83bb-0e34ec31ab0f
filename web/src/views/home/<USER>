<template>
  <div class="home-container">
    <el-row :gutter="15">
      <el-col :sm="6" class="mb15">
        <div class="home-card-item home-card-first">
          <div class="flex-margin flex">
            <img :src="getUserInfos.photo" />
            <div class="home-card-first-right ml15">
              <div class="flex-margin">
                <div class="home-card-first-right-title">
                  {{ currentTime }}，{{
                    getUserInfos.username === "" ? "test" : getUserInfos.username
                  }}！
                </div>
                <div class="home-card-first-right-msg mt5">
                  {{ getUserInfos.username === "admin" ? "超级管理" : "普通用户" }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <!-- AI系统指标卡片 -->
      <el-col :sm="6" class="mb15" v-for="(v, k) in state.aiMetricList" :key="k">
        <div class="home-card-item home-card-item-box" :style="{ background: v.color }">
          <div class="home-card-item-flex">
            <div class="home-card-item-title pb3">{{ v.title }}</div>
            <div class="home-card-item-title-num pb6">{{ v.titleNum }}</div>
            <div class="home-card-item-tip pb3">{{ v.tip }}</div>
            <div class="home-card-item-tip-num">{{ v.tipNum }}</div>
          </div>
          <i :class="v.icon" :style="{ color: v.iconColor }"></i>
        </div>
      </el-col>
    </el-row>

    <!-- AI资源统计卡片 -->
    <el-row :gutter="15" class="mb15">
      <el-col :sm="8" class="mb15" v-for="(v, k) in state.aiResourceList" :key="k">
        <div class="ai-resource-card" :class="v.class">
          <div class="ai-resource-card-content">
            <div class="ai-resource-icon">
              <i :class="v.icon"></i>
            </div>
            <div class="ai-resource-info">
              <div class="ai-resource-title">{{ v.title }}</div>
              <div class="ai-resource-count">
                <span class="count-number">{{ v.count }}</span>
                <span class="count-unit">{{ v.unit }}</span>
              </div>
              <div class="ai-resource-growth">
                <i :class="v.growthIcon"></i>
                <span>{{ v.growthRate }}</span>
                <span class="growth-period">{{ v.period }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 已部署模型展示 -->
    <el-row :gutter="15" class="mb15">
      <el-col :span="24">
        <el-card shadow="hover" header="已部署模型" class="deployed-models-card">
          <div class="deployed-models-header">
            <div class="header-status">
              <span class="status-dot online"></span>
              <span>在线运行</span>
              <span class="status-dot offline"></span>
              <span>已停止</span>
            </div>
            <el-button type="primary" size="small">部署新模型</el-button>
          </div>
          <el-table :data="state.deployedModels" style="width: 100%" stripe>
            <el-table-column prop="name" label="模型名称">
              <template #default="scope">
                <div class="model-name-cell">
                  <el-avatar :size="30" :src="scope.row.avatar"></el-avatar>
                  <span class="ml10">{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="模型类型"></el-table-column>
            <el-table-column prop="version" label="版本号"></el-table-column>
            <el-table-column prop="apiCalls" label="API调用量"></el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="scope.row.status === '在线' ? 'success' : 'info'">{{ scope.row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="primary" size="small" plain>查看</el-button>
                <el-button type="success" size="small" plain v-if="scope.row.status !== '在线'">启动</el-button>
                <el-button type="warning" size="small" plain v-else>停止</el-button>
                <el-button type="danger" size="small" plain>删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- AI应用场景展示 -->
    <el-row :gutter="15" class="mb15">
      <el-col :span="24">
        <el-card shadow="hover" header="AI应用场景">
          <el-carousel :interval="4000" type="card" height="200px">
            <el-carousel-item v-for="(item, index) in state.aiScenarioList" :key="index">
              <div class="ai-scenario-item" :style="{ backgroundImage: `url(${item.image})` }">
                <div class="ai-scenario-content">
                  <h3>{{ item.title }}</h3>
                  <p>{{ item.description }}</p>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </el-card>
      </el-col>
    </el-row>

    <!-- 模型性能监控 -->
    <el-row :gutter="15">
      <el-col :xs="24" :sm="14" :md="14" :lg="16" :xl="16" class="mb15">
        <el-card shadow="hover" header="模型性能监控" class="monitor-card">
          <div style="height: 400px" ref="modelPerformanceChartRef"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="10" :md="10" :lg="8" :xl="8">
        <el-card shadow="hover" header="硬件监控" class="monitor-card">
          <div class="hardware-monitor">
            <div class="monitor-section">
              <h4>GPU 状态</h4>
              <el-progress 
                :percentage="state.modelMonitoring.hardware.gpu.usage" 
                :color="getProgressColor(state.modelMonitoring.hardware.gpu.usage)"
                :format="(percentage: number) => `使用率: ${percentage}%`"
              />
              <div class="monitor-details">
                <span>温度: {{ state.modelMonitoring.hardware.gpu.temperature }}°C</span>
                <span>显存: {{ state.modelMonitoring.hardware.gpu.memory }}GB</span>
              </div>
            </div>
            
            <div class="monitor-section">
              <h4>CPU 状态</h4>
              <el-progress 
                :percentage="state.modelMonitoring.hardware.cpu.usage" 
                :color="getProgressColor(state.modelMonitoring.hardware.cpu.usage)"
                :format="(percentage: number) => `使用率: ${percentage}%`"
              />
              <div class="monitor-details">
                <span>温度: {{ state.modelMonitoring.hardware.cpu.temperature }}°C</span>
              </div>
            </div>
            
            <div class="monitor-section">
              <h4>内存状态</h4>
              <el-progress 
                :percentage="(state.modelMonitoring.hardware.memory.used / state.modelMonitoring.hardware.memory.total) * 100" 
                :color="getProgressColor((state.modelMonitoring.hardware.memory.used / state.modelMonitoring.hardware.memory.total) * 100)"
                :format="(percentage: number) => `使用率: ${percentage}%`"
              />
              <div class="monitor-details">
                <span>已用: {{ state.modelMonitoring.hardware.memory.used }}GB</span>
                <span>总计: {{ state.modelMonitoring.hardware.memory.total }}GB</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 模型状态监控 -->
    <el-row :gutter="15" class="mb15">
      <el-col :span="24">
        <el-card shadow="hover" header="红蓝对抗状态监控">
          <div class="status-monitor">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="status-card team-card red">
                  <div class="status-title">红队任务成功率</div>
                  <div class="status-value">{{ state.modelMonitoring.status.redTeam.taskSuccessRate }}%</div>
                  <div class="status-icon">
                    <i class="el-icon-trophy"></i>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="status-card team-card blue">
                  <div class="status-title">蓝队任务成功率</div>
                  <div class="status-value">{{ state.modelMonitoring.status.blueTeam.taskSuccessRate }}%</div>
                  <div class="status-icon">
                    <i class="el-icon-trophy"></i>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="status-card">
                  <div class="status-title">资源利用率</div>
                  <div class="status-value">{{ state.modelMonitoring.status.redTeam.resourceUtilization }}%</div>
                  <div class="status-icon success">
                    <i class="el-icon-data-analysis"></i>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mt15">
              <el-col :span="8">
                <div class="status-card">
                  <div class="status-title">策略执行效率</div>
                  <div class="status-value">{{ state.modelMonitoring.status.redTeam.strategyEfficiency }}%</div>
                  <div class="status-icon warning">
                    <i class="el-icon-time"></i>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="status-card">
                  <div class="status-title">智能体存活率</div>
                  <div class="status-value">{{ state.modelMonitoring.status.redTeam.agentSurvivalRate }}%</div>
                  <div class="status-icon">
                    <i class="el-icon-user"></i>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="status-card">
                  <div class="status-title">通信成功率</div>
                  <div class="status-value">{{ state.modelMonitoring.status.redTeam.communicationSuccessRate }}%</div>
                  <div class="status-icon success">
                    <i class="el-icon-connection"></i>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import {
  reactive,
  onMounted,
  nextTick,
  computed,
  getCurrentInstance,
  watch,
  onActivated,
  onUnmounted,
} from "vue";
import * as echarts from "echarts";
import { CountUp } from "countup.js";
import { formatAxis } from "@/utils/formatTime";
import { useTagsViewRoutesStore } from "@/stores/tagsViewRoutes";
import { useUserInfosState } from "@/stores/userInfos";

const { proxy } = getCurrentInstance() as any;

const tagsViewRoutes = useTagsViewRoutesStore();
const userInfos = useUserInfosState();

interface EChartsOption {
  [key: string]: any;
}

interface PerformanceDataPoint {
  time: number;
  value: number;
}

interface ModelMonitoring {
  hardware: {
    gpu: {
      usage: number;
      temperature: number;
      memory: number;
    };
    cpu: {
      usage: number;
      temperature: number;
    };
    memory: {
      total: number;
      used: number;
    };
  };
  status: {
    redTeam: {
      taskSuccessRate: number;
      resourceUtilization: number;
      strategyEfficiency: number;
      agentSurvivalRate: number;
      communicationSuccessRate: number;
    };
    blueTeam: {
      taskSuccessRate: number;
      resourceUtilization: number;
      strategyEfficiency: number;
      agentSurvivalRate: number;
      communicationSuccessRate: number;
    };
  };
  performance: {
    taskSuccessRate: PerformanceDataPoint[];
    resourceUtilization: PerformanceDataPoint[];
    strategyEfficiency: PerformanceDataPoint[];
    agentSurvivalRate: PerformanceDataPoint[];
    communicationSuccessRate: PerformanceDataPoint[];
  };
}

const state = reactive({
  aiMetricList: [
    {
      title: "总API调用量",
      titleNum: "538,246",
      tip: "今日调用次数",
      tipNum: "2,856",
      color: "#1890ff",
      icon: "iconfont icon-chart-pie",
      iconColor: "rgba(255, 255, 255, 0.3)",
    },
    {
      title: "模型训练次数",
      titleNum: "236",
      tip: "本周训练",
      tipNum: "32",
      color: "#13ce66",
      icon: "iconfont icon-tree",
      iconColor: "rgba(255, 255, 255, 0.3)",
    },
    {
      title: "计算资源负载",
      titleNum: "74%",
      tip: "GPU使用率",
      tipNum: "85%",
      color: "#ffba00",
      icon: "iconfont icon-server",
      iconColor: "rgba(255, 255, 255, 0.3)",
    },
  ],
  systemStatusList: [
    {
      label: "CPU使用率",
      value: 68,
      icon: "iconfont icon-cpu",
      iconColor: "#409eff",
      showProgress: true,
      progressColor: "#409eff",
      link: undefined
    },
    {
      label: "内存使用率",
      value: 45,
      icon: "iconfont icon-memory",
      iconColor: "#67c23a",
      showProgress: true,
      progressColor: "#67c23a",
      link: undefined
    },
    {
      label: "GPU使用率",
      value: 85,
      icon: "iconfont icon-gpu",
      iconColor: "#e6a23c",
      showProgress: true,
      progressColor: "#e6a23c",
      link: undefined
    },
    {
      label: "系统版本",
      value: "AI-Manager v2.4.1",
      icon: "iconfont icon-info",
      iconColor: "#909399",
      showProgress: false,
      link: undefined
    },
  ],
  myCharts: [] as echarts.ECharts[],
  // AI资源统计卡片数据
  aiResourceList: [
    {
      title: '数据集',
      count: '2,568',
      unit: '个',
      growthRate: '+15.8%',
      period: '本月',
      icon: 'iconfont icon-database',
      growthIcon: 'el-icon-top',
      class: 'dataset-card'
    },
    {
      title: '模型库',
      count: '389',
      unit: '个',
      growthRate: '+9.3%',
      period: '本月',
      icon: 'iconfont icon-model',
      growthIcon: 'el-icon-top',
      class: 'model-card'
    },
    {
      title: '算法库',
      count: '1,245',
      unit: '个',
      growthRate: '+12.5%',
      period: '本月',
      icon: 'iconfont icon-algorithm',
      growthIcon: 'el-icon-top',
      class: 'algorithm-card'
    }
  ],
  // 已部署模型数据
  deployedModels: [
    {
      name: 'GPT-NeoX',
      avatar: 'https://via.placeholder.com/40?text=AI',
      type: '自然语言处理',
      version: 'v1.2.5',
      apiCalls: '125,632',
      status: '在线'
    },
    {
      name: 'ResNet-152',
      avatar: 'https://via.placeholder.com/40?text=CV',
      type: '计算机视觉',
      version: 'v2.0.1',
      apiCalls: '83,215',
      status: '在线'
    },
    {
      name: 'BERT-Large',
      avatar: 'https://via.placeholder.com/40?text=NLP',
      type: '自然语言处理',
      version: 'v3.1.0',
      apiCalls: '92,467',
      status: '在线'
    },
    {
      name: 'YOLOv5',
      avatar: 'https://via.placeholder.com/40?text=CV',
      type: '对象检测',
      version: 'v6.2.1',
      apiCalls: '56,934',
      status: '停止'
    },
    {
      name: 'Transformer-XL',
      avatar: 'https://via.placeholder.com/40?text=NLP',
      type: '自然语言处理',
      version: 'v1.0.8',
      apiCalls: '35,641',
      status: '停止'
    }
  ],
  // AI应用场景数据
  aiScenarioList: [
    {
      title: '智能客服',
      description: '提供24/7全天候服务，快速响应用户需求',
      image: 'https://via.placeholder.com/400x200?text=智能客服'
    },
    {
      title: '数据分析',
      description: '深度学习分析海量数据，提供精准业务决策',
      image: 'https://via.placeholder.com/400x200?text=数据分析'
    },
    {
      title: '图像识别',
      description: '高精度识别与分类，应用于安防监控、医疗诊断等领域',
      image: 'https://via.placeholder.com/400x200?text=图像识别'
    },
    {
      title: '自然语言处理',
      description: '智能文本生成与分析，支持多语言实时翻译',
      image: 'https://via.placeholder.com/400x200?text=自然语言处理'
    }
  ],
  // 添加模型监控相关数据
  modelMonitoring: {
    hardware: {
      gpu: {
        usage: 0,
        temperature: 0,
        memory: 0
      },
      cpu: {
        usage: 0,
        temperature: 0
      },
      memory: {
        total: 32,
        used: 0
      }
    },
    status: {
      redTeam: {
        taskSuccessRate: 0,
        resourceUtilization: 0,
        strategyEfficiency: 0,
        agentSurvivalRate: 0,
        communicationSuccessRate: 0
      },
      blueTeam: {
        taskSuccessRate: 0,
        resourceUtilization: 0,
        strategyEfficiency: 0,
        agentSurvivalRate: 0,
        communicationSuccessRate: 0
      }
    },
    performance: {
      taskSuccessRate: [] as PerformanceDataPoint[],
      resourceUtilization: [] as PerformanceDataPoint[],
      strategyEfficiency: [] as PerformanceDataPoint[],
      agentSurvivalRate: [] as PerformanceDataPoint[],
      communicationSuccessRate: [] as PerformanceDataPoint[]
    }
  } as ModelMonitoring,
});

// 获取用户信息 pinia
const getUserInfos = computed(() => {
  return userInfos.userInfos;
});

// 当前时间提示语
const currentTime = computed(() => {
  return formatAxis(new Date());
});

// 添加模拟数据生成函数
const generateRandomData = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// 更新硬件监控数据
const updateHardwareMetrics = () => {
  state.modelMonitoring.hardware.gpu.usage = generateRandomData(30, 95);
  state.modelMonitoring.hardware.gpu.temperature = generateRandomData(40, 85);
  state.modelMonitoring.hardware.gpu.memory = generateRandomData(4, 12);
  state.modelMonitoring.hardware.cpu.usage = generateRandomData(20, 80);
  state.modelMonitoring.hardware.cpu.temperature = generateRandomData(35, 75);
  state.modelMonitoring.hardware.memory.used = generateRandomData(8, 24);
};

// 更新状态监控数据
const updateStatusMetrics = () => {
  // 红队数据
  state.modelMonitoring.status.redTeam.taskSuccessRate = generateRandomData(60, 95);
  state.modelMonitoring.status.redTeam.resourceUtilization = generateRandomData(70, 98);
  state.modelMonitoring.status.redTeam.strategyEfficiency = generateRandomData(65, 90);
  state.modelMonitoring.status.redTeam.agentSurvivalRate = generateRandomData(50, 85);
  state.modelMonitoring.status.redTeam.communicationSuccessRate = generateRandomData(80, 99);

  // 蓝队数据
  state.modelMonitoring.status.blueTeam.taskSuccessRate = generateRandomData(60, 95);
  state.modelMonitoring.status.blueTeam.resourceUtilization = generateRandomData(70, 98);
  state.modelMonitoring.status.blueTeam.strategyEfficiency = generateRandomData(65, 90);
  state.modelMonitoring.status.blueTeam.agentSurvivalRate = generateRandomData(50, 85);
  state.modelMonitoring.status.blueTeam.communicationSuccessRate = generateRandomData(80, 99);
};

// 更新性能监控数据
const updatePerformanceMetrics = () => {
  const now = new Date().getTime();
  
  // 更新任务成功率
  state.modelMonitoring.performance.taskSuccessRate.push({
    time: now,
    value: generateRandomData(60, 95)
  });
  
  // 更新资源利用率
  state.modelMonitoring.performance.resourceUtilization.push({
    time: now,
    value: generateRandomData(70, 98)
  });
  
  // 更新策略执行效率
  state.modelMonitoring.performance.strategyEfficiency.push({
    time: now,
    value: generateRandomData(65, 90)
  });
  
  // 更新智能体存活率
  state.modelMonitoring.performance.agentSurvivalRate.push({
    time: now,
    value: generateRandomData(50, 85)
  });
  
  // 更新通信成功率
  state.modelMonitoring.performance.communicationSuccessRate.push({
    time: now,
    value: generateRandomData(80, 99)
  });
  
  // 保持最近30个数据点
  const maxDataPoints = 30;
  if (state.modelMonitoring.performance.taskSuccessRate.length > maxDataPoints) {
    state.modelMonitoring.performance.taskSuccessRate.shift();
    state.modelMonitoring.performance.resourceUtilization.shift();
    state.modelMonitoring.performance.strategyEfficiency.shift();
    state.modelMonitoring.performance.agentSurvivalRate.shift();
    state.modelMonitoring.performance.communicationSuccessRate.shift();
  }
  
  // 更新性能监控图表
  updatePerformanceChart();
};

// 更新性能监控图表
const updatePerformanceChart = () => {
  const chartElement = proxy.$refs.modelPerformanceChartRef;
  if (!chartElement) return;
  
  const myChart = echarts.init(chartElement);
  
  const option: EChartsOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['红队任务成功率', '蓝队任务成功率', '资源利用率', '策略执行效率', '智能体存活率', '通信成功率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      boundaryGap: false
    },
    yAxis: [
      {
        type: 'value',
        name: '百分比(%)',
        min: 0,
        max: 100,
        position: 'left'
      }
    ],
    series: [
      {
        name: '红队任务成功率',
        type: 'line',
        smooth: true,
        data: state.modelMonitoring.performance.taskSuccessRate.map(item => [item.time, item.value]),
        itemStyle: {
          color: '#f56c6c'
        }
      },
      {
        name: '蓝队任务成功率',
        type: 'line',
        smooth: true,
        data: state.modelMonitoring.performance.taskSuccessRate.map(item => [item.time, item.value]),
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '资源利用率',
        type: 'line',
        smooth: true,
        data: state.modelMonitoring.performance.resourceUtilization.map(item => [item.time, item.value]),
        itemStyle: {
          color: '#67c23a'
        }
      },
      {
        name: '策略执行效率',
        type: 'line',
        smooth: true,
        data: state.modelMonitoring.performance.strategyEfficiency.map(item => [item.time, item.value]),
        itemStyle: {
          color: '#e6a23c'
        }
      },
      {
        name: '智能体存活率',
        type: 'line',
        smooth: true,
        data: state.modelMonitoring.performance.agentSurvivalRate.map(item => [item.time, item.value]),
        itemStyle: {
          color: '#909399'
        }
      },
      {
        name: '通信成功率',
        type: 'line',
        smooth: true,
        data: state.modelMonitoring.performance.communicationSuccessRate.map(item => [item.time, item.value]),
        itemStyle: {
          color: '#9c27b0'
        }
      }
    ]
  };
  
  myChart.setOption(option);
  state.myCharts = [myChart];
};

// 启动定时更新
let monitoringInterval: number;

// 初始化AI资源统计动画
const initResourceCountUp = () => {
  const countUpOptions = {
    duration: 2.5,
    useEasing: true
  };
  
  // 为每个资源卡片设置动画
  state.aiResourceList.forEach((item, index) => {
    const countValue = parseInt(item.count.replace(/,/g, ''));
    const countElement = document.querySelectorAll('.count-number')[index] as HTMLElement;
    if (countElement) {
      new CountUp(countElement, countValue, countUpOptions);
    }
  });
};

// 批量设置 echarts resize
const initEchartsResizeFun = () => {
  nextTick(() => {
    for (let i = 0; i < state.myCharts.length; i++) {
      state.myCharts[i].resize();
    }
  });
};

// 批量设置 echarts resize
const initEchartsResize = () => {
  window.addEventListener("resize", initEchartsResizeFun);
};

// 页面加载时
onMounted(() => {
  initEchartsResize();
  initResourceCountUp();
  
  // 初始化监控数据
  updateHardwareMetrics();
  updateStatusMetrics();
  updatePerformanceMetrics();
  
  // 设置定时更新
  monitoringInterval = window.setInterval(() => {
    updateHardwareMetrics();
    updateStatusMetrics();
    updatePerformanceMetrics();
  }, 3000);
});

// 由于页面缓存原因，keep-alive
onActivated(() => {
  initEchartsResizeFun();
});

// 监听 vuex 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等
watch(
  () => tagsViewRoutes.isTagsViewCurrenFull,
  () => {
    initEchartsResizeFun();
  }
);

// 组件卸载时清理定时器
onUnmounted(() => {
  if (monitoringInterval) {
    clearInterval(monitoringInterval);
  }
});

const getProgressColor = (percentage: number) => {
  if (percentage < 60) return '#67C23A';
  if (percentage < 80) return '#E6A23C';
  return '#F56C6C';
};
</script>

<style scoped lang="scss">
.home-container {
  overflow-x: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  min-height: 100vh;
  padding: 20px;
  
  .home-card-item {
    width: 100%;
    height: 103px;
    background: var(--el-text-color-secondary);
    border-radius: 15px;
    transition: all ease 0.3s;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      transition: all ease 0.3s;
    }
  }

  .home-card-item-box {
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    &:hover {
      i {
        right: 0px !important;
        bottom: 0px !important;
        transition: all ease 0.3s;
      }
    }
    i {
      position: absolute;
      right: -10px;
      bottom: -10px;
      font-size: 70px;
      transform: rotate(-30deg);
      transition: all ease 0.3s;
    }
    .home-card-item-flex {
      padding: 0 20px;
      color: var(--color-whites);
      .home-card-item-title,
      .home-card-item-tip {
        font-size: 13px;
      }
      .home-card-item-title-num {
        font-size: 18px;
      }
      .home-card-item-tip-num {
        font-size: 13px;
      }
    }
  }

  .home-card-first {
    background: var(--el-color-white);
    border: 1px solid var(--el-border-color-light, #ebeef5);
    display: flex;
    align-items: center;
    img {
      width: 60px;
      height: 60px;
      border-radius: 100%;
      border: 2px solid var(--color-primary-light-5);
    }
    .home-card-first-right {
      flex: 1;
      display: flex;
      flex-direction: column;
      .home-card-first-right-title {
        color: var(--el-color-black);
      }
      .home-card-first-right-msg {
        font-size: 13px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .home-monitor {
    height: 280px;
    .flex-warp-item {
      width: 50%;
      height: 140px;
      display: flex;
      .flex-warp-item-box {
        margin: auto;
        height: auto;
        width: 100%;
        text-align: center;
        color: var(--el-text-color-primary);
        
        i {
          font-size: 30px;
          margin-bottom: 10px;
        }
      }
    }
  }

  .ai-resource-card {
    height: 120px;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px 0 rgb(0 0 0 / 15%);
    }
    
    &.dataset-card {
      background: linear-gradient(135deg, #42b983, #33a06f);
    }
    
    &.model-card {
      background: linear-gradient(135deg, #5e7ce0, #3b5eb5);
    }
    
    &.algorithm-card {
      background: linear-gradient(135deg, #e74c3c, #c0392b);
    }
    
    .ai-resource-card-content {
      display: flex;
      height: 100%;
      color: white;
      
      .ai-resource-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        
        i {
          font-size: 40px;
        }
      }
      
      .ai-resource-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        
        .ai-resource-title {
          font-size: 16px;
          margin-bottom: 5px;
        }
        
        .ai-resource-count {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 10px;
          
          .count-unit {
            font-size: 14px;
            margin-left: 5px;
          }
        }
        
        .ai-resource-growth {
          font-size: 13px;
          
          i {
            margin-right: 5px;
          }
          
          .growth-period {
            opacity: 0.8;
            margin-left: 5px;
          }
        }
      }
    }
  }

  .ai-scenario-item {
    height: 100%;
    background-size: cover;
    background-position: center;
    border-radius: 10px;
    position: relative;
    
    .ai-scenario-content {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 15px;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      color: white;
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
      
      h3 {
        margin: 0 0 5px 0;
      }
      
      p {
        margin: 0;
        font-size: 12px;
        opacity: 0.9;
      }
    }
  }
  
  .deployed-models-card {
    .deployed-models-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      .header-status {
        display: flex;
        align-items: center;
        
        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 5px;
          
          &.online {
            background-color: #67c23a;
          }
          
          &.offline {
            background-color: #909399;
          }
        }
        
        span:not(.status-dot) {
          margin-right: 15px;
        }
      }
    }
    
    .model-name-cell {
      display: flex;
      align-items: center;
    }
    
    .ml10 {
      margin-left: 10px;
    }
  }

  .status-monitor {
    .status-card {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 15px;
      padding: 25px;
      position: relative;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      overflow: hidden;
      height: 160px;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-success));
        opacity: 0.8;
      }
      
      .status-title {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        margin-bottom: 15px;
        font-weight: 500;
      }
      
      .status-value {
        font-size: 32px;
        font-weight: bold;
        color: var(--el-text-color-primary);
        margin-bottom: 10px;
        background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-success));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      
      .status-icon {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 32px;
        color: var(--el-color-primary);
        opacity: 0.8;
        
        &.warning {
          color: var(--el-color-warning);
        }
        
        &.success {
          color: var(--el-color-success);
        }
      }
      
      &.team-card {
        &.red {
          background: linear-gradient(135deg, rgba(245, 108, 108, 0.1), rgba(192, 57, 43, 0.1));
          border: 1px solid rgba(245, 108, 108, 0.2);
          
          &::before {
            background: linear-gradient(90deg, #f56c6c, #c0392b);
          }
          
          .status-value {
            background: linear-gradient(45deg, #f56c6c, #c0392b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        
        &.blue {
          background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(44, 62, 80, 0.1));
          border: 1px solid rgba(64, 158, 255, 0.2);
          
          &::before {
            background: linear-gradient(90deg, #409eff, #2c3e50);
          }
          
          .status-value {
            background: linear-gradient(45deg, #409eff, #2c3e50);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
  }

  .monitor-card {
    height: 100%;
    
    :deep(.el-card__body) {
      height: calc(100% - 55px);
      padding: 20px;
    }
  }

  .hardware-monitor {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    .monitor-section {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 15px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }
      
      h4 {
        margin: 0 0 15px 0;
        color: var(--el-text-color-primary);
        font-size: 16px;
        font-weight: 500;
        display: flex;
        align-items: center;
        
        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 16px;
          background: var(--el-color-primary);
          margin-right: 8px;
          border-radius: 2px;
        }
      }
      
      .monitor-details {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        color: var(--el-text-color-secondary);
        font-size: 13px;
        
        span {
          background: rgba(0, 0, 0, 0.03);
          padding: 4px 12px;
          border-radius: 12px;
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba(0, 0, 0, 0.05);
          }
        }
      }
    }
  }

  :deep(.el-card) {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .el-card__header {
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      padding: 15px 20px;
      font-weight: 500;
    }
  }

  :deep(.el-progress-bar__outer) {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
  }

  :deep(.el-progress-bar__inner) {
    border-radius: 8px;
    transition: all 0.3s ease;
  }
}

// 添加动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.el-row {
  animation: fadeIn 0.5s ease-out;
}

// 响应式调整
@media screen and (max-width: 768px) {
  .home-container {
    padding: 10px;
  }
  
  .monitor-card {
    margin-bottom: 15px;
    
    :deep(.el-card__body) {
      height: auto;
    }
  }
  
  .hardware-monitor {
    height: auto;
    
    .monitor-section {
      margin-bottom: 15px;
    }
  }
  
  .status-monitor {
    .status-card {
      padding: 15px;
      
      .status-value {
        font-size: 24px;
      }
      
      .status-icon {
        font-size: 24px;
      }
    }
  }
}
</style>
