<template>
  <el-popover
    v-model:visible="popoverVisible"
    :width="400"
    trigger="click"
    placement="bottom-end"
    popper-class="upload-progress-popover"
  >
    <template #reference>
      <el-badge :value="pendingCount" :hidden="pendingCount === 0">
        <el-button 
          :type="hasError ? 'danger' : 'primary'"
          link
        >
          <el-icon><elementUpload /></el-icon>
        </el-button>
      </el-badge>
    </template>

    <div class="upload-progress-container">
      <div class="upload-progress-header">
        <div class="title">
          <el-icon><Upload /></el-icon>
          文件上传
          <el-tag size="small" type="info" class="count-tag">
            {{ completedCount }}/{{ props.files.length }}
          </el-tag>
        </div>
        <div class="actions">
          <el-button link type="primary" @click="clearCompletedFiles" v-if="hasCompletedFiles">
            <el-icon><Delete /></el-icon> 清理已完成
          </el-button>
          <el-button link type="primary" @click="close" v-if="canClose">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
      <div class="upload-progress-content">
        <div v-for="file in files" :key="file.name" class="file-item">
          <div class="file-info">
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
          </div>
          <el-progress 
            :percentage="file.progress" 
            :status="getProgressStatus(file.status)"
            :format="(val: number) => `${val}%`"
          />
          <div class="file-status-row">
            <el-tag size="small" :type="getStatusType(file.status)">
              {{ getStatusText(file.status) }}
            </el-tag>
            <div class="file-actions">
              <!-- 暂停/恢复按钮 -->
              <el-button 
                v-if="file.status === 'uploading'" 
                size="small" 
                circle 
                @click="pauseUpload(file)"
                title="暂停"
              >
                <el-icon><VideoPause /></el-icon>
              </el-button>
              <el-button 
                v-if="file.status === 'pending' || file.status === 'paused'" 
                size="small" 
                circle 
                @click="resumeUpload(file)"
                title="继续"
              >
                <el-icon><VideoPlay /></el-icon>
              </el-button>
              <!-- 重试按钮 -->
              <el-button 
                v-if="file.status === 'error'" 
                size="small" 
                circle 
                @click="retryUpload(file)"
                title="重试"
              >
                <el-icon><Refresh /></el-icon>
              </el-button>
              <!-- 删除按钮 -->
              <el-button 
                size="small" 
                circle 
                @click="removeFile(file)"
                title="删除"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Upload, Close, Delete, VideoPause, VideoPlay, Refresh } from '@element-plus/icons-vue'
import { useUploadStore } from '@/stores/uploadStore'

interface UploadFile {
  name: string
  size: number
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error' | 'paused'
  taskId?: string
}

const props = defineProps<{
  visible: boolean
  files: UploadFile[]
}>()

const emit = defineEmits(['update:visible', 'close', 'pause', 'resume', 'retry', 'remove', 'clear-completed'])

const popoverVisible = ref(false)
const uploadStore = useUploadStore()

// 在组件挂载时加载本地存储的任务
onMounted(() => {
  uploadStore.loadTasksFromLocalStorage()
})

// 监听外部visible属性变化
watch(() => props.visible, (val) => {
  popoverVisible.value = val
})

// 监听内部popover状态变化
watch(popoverVisible, (val) => {
  emit('update:visible', val)
})

const pendingCount = computed(() => {
  return props.files.filter(file => 
    file.status === 'pending' || file.status === 'uploading' || file.status === 'paused'
  ).length
})

const completedCount = computed(() => {
  return props.files.filter(file => 
    file.status === 'success' || file.status === 'error'
  ).length
})

const hasError = computed(() => {
  return props.files.some(file => file.status === 'error')
})

const canClose = computed(() => {
  return props.files.every(file => 
    file.status === 'success' || file.status === 'error'
  )
})

const hasCompletedFiles = computed(() => {
  return completedCount.value > 0
})

const close = () => {
  emit('close')
  emit('update:visible', false)
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
}

const getProgressStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '',  // 等待上传使用默认状态
    paused: 'warning', // 暂停使用警告状态
    uploading: '', // 上传中使用默认状态
    success: 'success',
    error: 'exception'
  }
  return statusMap[status] || ''
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    paused: 'warning',
    uploading: 'warning',
    success: 'success',
    error: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '等待上传',
    paused: '已暂停',
    uploading: '上传中',
    success: '上传成功',
    error: '上传失败'
  }
  return texts[status] || status
}

// 暂停上传
const pauseUpload = (file: UploadFile) => {
  if (file.taskId) {
    uploadStore.pauseTask(file.taskId)
    emit('pause', file)
  }
}

// 恢复上传
const resumeUpload = (file: UploadFile) => {
  if (file.taskId) {
    uploadStore.resumeTask(file.taskId)
    emit('resume', file)
  }
}

// 重试上传
const retryUpload = (file: UploadFile) => {
  emit('retry', file)
}

// 删除文件
const removeFile = (file: UploadFile) => {
  if (file.taskId) {
    uploadStore.clearTask(file.taskId)
  }
  emit('remove', file)
}

// 清理已完成的文件
const clearCompletedFiles = () => {
  uploadStore.clearCompletedTasks()
  emit('clear-completed')
}
</script>

<style lang="scss" scoped>
.upload-trigger-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  height: 40px;
  padding: 0 10px;
  
  .el-icon {
    font-size: 16px;
  }
}

:deep(.upload-progress-popover) {
  padding: 0;
  min-width: 400px;
}

.upload-progress-container {
  .upload-progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f8f9fa;

    .title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      
      .el-icon {
        color: #409EFF;
      }

      .count-tag {
        margin-left: 8px;
      }
    }

    .actions {
      display: flex;
      gap: 4px;
    }
  }

  .upload-progress-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 12px;

    .file-item {
      padding: 12px;
      border-radius: 6px;
      background: #f8f9fa;
      margin-bottom: 10px;
      border: 1px solid #ebeef5;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .file-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        .file-name {
          font-size: 13px;
          color: #606266;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-right: 12px;
        }

        .file-size {
          font-size: 12px;
          color: #909399;
        }
      }

      .file-status-row {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .file-actions {
          display: flex;
          gap: 4px;
          
          .el-button {
            padding: 4px;
          }
        }
      }
    }
  }
}
</style> 