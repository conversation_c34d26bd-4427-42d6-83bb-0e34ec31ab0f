<template>
  <div class="task-item">
    <div class="task-header">
      <div class="task-name">{{ task.fileName }}</div>
      <div class="task-status">
        <el-tag :type="statusType" size="small">{{ statusText }}</el-tag>
      </div>
    </div>
    
    <div class="task-progress">
      <el-progress 
        :percentage="task.totalProgress" 
        :status="progressStatus"
        :format="(val: number) => `${val}%`"
      />
    </div>
    
    <div class="task-info">
      <div class="file-info">
        <span class="file-size">{{ formatFileSize(task.fileSize) }}</span>
        <span class="file-type">{{ task.fileType || '未知类型' }}</span>
      </div>
      
      <div class="task-actions">
        <!-- 暂停/恢复按钮 -->
        <el-button 
          v-if="task.status === 'uploading'" 
          size="small" 
          circle 
          @click="pauseTask"
          title="暂停"
        >
          <el-icon><VideoPause /></el-icon>
        </el-button>
        <el-button 
          v-if="task.status === 'pending' || task.status === 'paused'" 
          size="small" 
          circle 
          @click="resumeTask"
          title="继续"
        >
          <el-icon><VideoPlay /></el-icon>
        </el-button>
        <!-- 重试按钮 -->
        <el-button 
          v-if="task.status === 'error'" 
          size="small" 
          circle 
          @click="retryTask"
          title="重试"
        >
          <el-icon><Refresh /></el-icon>
        </el-button>
        <!-- 删除按钮 -->
        <el-button 
          size="small" 
          circle 
          @click="removeTask"
          title="删除"
        >
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="task.errorMessage" class="error-message">
      <el-alert
        :title="task.errorMessage"
        type="error"
        :closable="false"
        show-icon
      />
    </div>
    
    <!-- 分片信息 -->
    <div v-if="showChunks" class="chunks-info">
      <div class="chunks-header" @click="toggleChunksVisible">
        分片信息
        <el-icon class="toggle-icon" :class="{ 'is-active': chunksVisible }">
          <ArrowDown />
        </el-icon>
      </div>
      <div v-if="chunksVisible" class="chunks-list">
        <div 
          v-for="(chunk, index) in task.chunks" 
          :key="index"
          class="chunk-item"
        >
          <span class="chunk-index">分片 {{ index + 1 }}</span>
          <span class="chunk-status">
            <el-tag 
              size="small" 
              :type="getChunkStatusType(chunk.status)"
            >
              {{ getChunkStatusText(chunk.status) }}
            </el-tag>
          </span>
          <el-progress 
            :percentage="chunk.progress" 
            :status="getChunkProgressStatus(chunk.status)"
            :format="(val: number) => `${val}%`"
            :stroke-width="5"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { VideoPause, VideoPlay, Refresh, Delete, ArrowDown } from '@element-plus/icons-vue';

interface UploadChunk {
  index: number;
  start: number;
  end: number;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  etag?: string;
}

interface UploadTask {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadId?: string;
  objectName?: string;
  versionId?: number;
  fileCategory?: string;
  chunks: UploadChunk[];
  totalProgress: number;
  status: 'pending' | 'uploading' | 'paused' | 'success' | 'error';
  errorMessage?: string;
}

const props = defineProps<{
  task: UploadTask;
}>();

const emit = defineEmits(['pause', 'resume', 'retry', 'remove']);

const chunksVisible = ref(false);

const toggleChunksVisible = () => {
  chunksVisible.value = !chunksVisible.value;
};

// 计算属性
const statusType = computed(() => {
  const types: Record<string, string> = {
    pending: 'info',
    paused: 'warning',
    uploading: 'primary',
    success: 'success',
    error: 'danger'
  };
  return types[props.task.status] || 'info';
});

const statusText = computed(() => {
  const texts: Record<string, string> = {
    pending: '等待上传',
    paused: '已暂停',
    uploading: '上传中',
    success: '上传成功',
    error: '上传失败'
  };
  return texts[props.task.status] || props.task.status;
});

const progressStatus = computed(() => {
  const statusMap: Record<string, string> = {
    pending: '',
    paused: 'warning',
    uploading: '',
    success: 'success',
    error: 'exception'
  };
  return statusMap[props.task.status] || '';
});

const showChunks = computed(() => {
  return props.task.chunks && props.task.chunks.length > 1;
});

// 方法
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`;
};

const getChunkStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    uploading: 'primary',
    success: 'success',
    error: 'danger'
  };
  return types[status] || 'info';
};

const getChunkStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '等待',
    uploading: '上传中',
    success: '完成',
    error: '失败'
  };
  return texts[status] || status;
};

const getChunkProgressStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '',
    uploading: '',
    success: 'success',
    error: 'exception'
  };
  return statusMap[status] || '';
};

const pauseTask = () => {
  emit('pause', props.task.id);
};

const resumeTask = () => {
  emit('resume', props.task.id);
};

const retryTask = () => {
  emit('retry', props.task.id);
};

const removeTask = () => {
  emit('remove', props.task.id);
};
</script>

<style lang="scss" scoped>
.task-item {
  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .task-name {
      font-weight: 500;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 70%;
    }
  }
  
  .task-progress {
    margin-bottom: 8px;
  }
  
  .task-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .file-info {
      font-size: 12px;
      color: #909399;
      
      .file-size {
        margin-right: 8px;
      }
      
      .file-type {
        background-color: #f5f7fa;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }
    
    .task-actions {
      display: flex;
      gap: 4px;
      
      .el-button {
        padding: 4px;
      }
    }
  }
  
  .error-message {
    margin-top: 8px;
  }
  
  .chunks-info {
    margin-top: 12px;
    border-top: 1px dashed #EBEEF5;
    padding-top: 8px;
    
    .chunks-header {
      font-size: 13px;
      color: #606266;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      
      .toggle-icon {
        transition: transform 0.3s;
        
        &.is-active {
          transform: rotate(180deg);
        }
      }
    }
    
    .chunks-list {
      .chunk-item {
        padding: 6px 0;
        border-bottom: 1px solid #F2F6FC;
        
        &:last-child {
          border-bottom: none;
        }
        
        .chunk-index {
          font-size: 12px;
          color: #909399;
          margin-right: 8px;
        }
        
        .chunk-status {
          margin-bottom: 4px;
          display: inline-block;
        }
      }
    }
  }
}
</style> 