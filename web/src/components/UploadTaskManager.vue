<template>
  <div class="upload-task-manager">
    <el-drawer
      v-model="drawerVisible"
      title="上传任务管理"
      size="400px"
      :destroy-on-close="false"
    >
      <div class="task-manager-container">
        <!-- 任务统计 -->
        <div class="task-stats">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-card pending">
                <div class="stat-value">{{ pendingCount }}</div>
                <div class="stat-label">等待中</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-card active">
                <div class="stat-value">{{ activeCount }}</div>
                <div class="stat-label">上传中</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-card completed">
                <div class="stat-value">{{ completedCount }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 任务列表 -->
        <div class="task-list">
          <div v-if="!hasTasks" class="empty-tasks">
            <el-empty description="暂无上传任务" />
          </div>
          
          <!-- 活动任务 -->
          <template v-if="activeTasks.length > 0">
            <div class="task-section-title">正在上传</div>
            <el-card 
              v-for="task in activeTasks" 
              :key="task.id" 
              class="task-card"
              shadow="hover"
            >
              <task-item 
                :task="task" 
                @pause="pauseTask"
                @resume="resumeTask"
                @remove="removeTask"
              />
            </el-card>
          </template>
          
          <!-- 等待任务 -->
          <template v-if="pendingTasks.length > 0">
            <div class="task-section-title">等待上传</div>
            <el-card 
              v-for="task in pendingTasks" 
              :key="task.id" 
              class="task-card"
              shadow="hover"
            >
              <task-item 
                :task="task" 
                @pause="pauseTask"
                @resume="resumeTask"
                @remove="removeTask"
              />
            </el-card>
          </template>
          
          <!-- 已完成任务 -->
          <template v-if="completedTasks.length > 0">
            <div class="task-section-title">
              已完成
              <el-button 
                link 
                type="primary" 
                size="small"
                @click="clearCompletedTasks"
              >
                清理全部
              </el-button>
            </div>
            <el-card 
              v-for="task in completedTasks" 
              :key="task.id" 
              class="task-card"
              shadow="hover"
            >
              <task-item 
                :task="task" 
                @remove="removeTask"
              />
            </el-card>
          </template>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useUploadStore } from '@/stores/uploadStore';
import TaskItem from './TaskItem.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible']);

const uploadStore = useUploadStore();
const drawerVisible = ref(props.visible);

// 监听visible属性变化
watch(() => props.visible, (val) => {
  drawerVisible.value = val;
});

// 监听drawer状态变化
watch(drawerVisible, (val) => {
  emit('update:visible', val);
});

// 在组件挂载时加载本地存储的任务
onMounted(() => {
  uploadStore.loadTasksFromLocalStorage();
});

// 计算属性
const pendingTasks = computed(() => uploadStore.pendingTasks);
const activeTasks = computed(() => uploadStore.activeTasks);
const completedTasks = computed(() => uploadStore.completedTasks);

const pendingCount = computed(() => pendingTasks.value.length);
const activeCount = computed(() => activeTasks.value.length);
const completedCount = computed(() => completedTasks.value.length);

const hasTasks = computed(() => 
  pendingCount.value > 0 || activeCount.value > 0 || completedCount.value > 0
);

// 方法
const pauseTask = (taskId: string) => {
  uploadStore.pauseTask(taskId);
};

const resumeTask = (taskId: string) => {
  uploadStore.resumeTask(taskId);
};

const removeTask = (taskId: string) => {
  uploadStore.clearTask(taskId);
};

const clearCompletedTasks = () => {
  uploadStore.clearCompletedTasks();
};
</script>

<style lang="scss" scoped>
.upload-task-manager {
  .task-manager-container {
    padding: 16px;
    
    .task-stats {
      margin-bottom: 24px;
      
      .stat-card {
        padding: 16px;
        border-radius: 8px;
        text-align: center;
        color: white;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
        }
        
        .stat-label {
          font-size: 14px;
          margin-top: 4px;
        }
        
        &.pending {
          background-color: #909399;
        }
        
        &.active {
          background-color: #E6A23C;
        }
        
        &.completed {
          background-color: #67C23A;
        }
      }
    }
    
    .task-list {
      .empty-tasks {
        padding: 40px 0;
      }
      
      .task-section-title {
        font-size: 16px;
        font-weight: 500;
        margin: 16px 0 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid #EBEEF5;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .task-card {
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style> 