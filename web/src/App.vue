<template>
  <el-config-provider :locale="zhCn">
    <router-view v-show="getThemeConfig.lockScreenTime !== 0" />
    <LockScreen v-if="getThemeConfig.isLockScreen" />
    <Setings ref="setingsRef" v-show="getThemeConfig.lockScreenTime !== 0" />
    <CloseFull />
    <!-- 添加全局上传任务管理器 -->
    <div class="global-upload-manager">
      <el-button 
        type="primary" 
        circle 
        class="upload-manager-button"
        @click="showUploadManager"
        v-if="hasUploadTasks"
      >
        <el-badge :value="uploadTaskCount" :max="99">
          <el-icon><Upload /></el-icon>
        </el-badge>
      </el-button>
      <upload-task-manager v-model:visible="uploadManagerVisible" />
    </div>
  </el-config-provider>
</template>

<script lang="ts">
import {
  computed,
  ref,
  getCurrentInstance,
  onBeforeMount,
  onMounted,
  onUnmounted,
  nextTick,
  defineComponent,
  watch,
  reactive,
  toRefs,
} from "vue";
import { useRoute } from "vue-router";
import { useThemeConfigStateStore } from "@/stores/themeConfig";
import { useTagsViewRoutesStore } from "@/stores/tagsViewRoutes";
import other from "@/utils/other";
import { Local, Session } from "@/utils/storage";
import setIntroduction from "@/utils/setIconfont";
import LockScreen from "@/layout/lockScreen/index.vue";
import Setings from "@/layout/navBars/breadcrumb/setings.vue";
import CloseFull from "@/layout/navBars/breadcrumb/closeFull.vue";
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { useUploadStore } from '@/stores/uploadStore';
import UploadTaskManager from '@/components/UploadTaskManager.vue';
import { Upload } from '@element-plus/icons-vue';

export default defineComponent({
  name: "app",
  components: { LockScreen, Setings, CloseFull },
  setup() {
    const { proxy } = getCurrentInstance() as any;
    const setingsRef = ref();
    const route = useRoute();
    const theme = useThemeConfigStateStore();
    const tagsViewRoutes = useTagsViewRoutesStore();
    const state: any = reactive({
      i18nLocale: null,
    });

    const uploadStore = useUploadStore();
    const uploadManagerVisible = ref(false);

    // 获取布局配置信息
    const getThemeConfig = computed(() => {
      return theme.themeConfig;
    });
    // 布局配置弹窗打开
    const openSetingsDrawer = () => {
      setingsRef.value.openDrawer();
    };
    // 设置初始化，防止刷新时恢复默认
    onBeforeMount(() => {
      // 设置批量第三方 icon 图标
      setIntroduction.cssCdn();
      // 设置批量第三方 js
      setIntroduction.jsCdn();
    });
    // 页面加载时
    onMounted(() => {
      nextTick(() => {
        // 监听布局配置弹窗点击打开
        proxy.mittBus.on("openSetingsDrawer", () => {
          openSetingsDrawer();
        });
        // 获取缓存中的布局配置
        if (Local.get("themeConfig")) {
          theme.setThemeConfig(Local.get("themeConfig"));
          document.documentElement.style.cssText = Local.get("themeConfigStyle");
        }
        // 获取缓存中的全屏配置
        if (Session.get("isTagsViewCurrenFull")) {
          tagsViewRoutes.setCurrenFullscreen(Session.get("isTagsViewCurrenFull"));
        }
        // 在组件挂载时加载本地存储的任务
        uploadStore.loadTasksFromLocalStorage();
      });
    });
    // 页面销毁时，关闭监听布局配置/i18n监听
    onUnmounted(() => {
      proxy.mittBus.off("openSetingsDrawer", () => {});
      proxy.mittBus.off("getI18nConfig", () => {});
    });
    // 监听路由的变化，设置网站标题
    watch(
      () => route.path,
      () => {
        other.useTitle();
      }
    );

    // 计算是否有上传任务
    const hasUploadTasks = computed(() => {
      return uploadStore.uploadTasks.length > 0;
    });

    // 计算上传任务数量
    const uploadTaskCount = computed(() => {
      return uploadStore.uploadTasks.length;
    });

    // 显示上传任务管理器
    const showUploadManager = () => {
      uploadManagerVisible.value = true;
    };

    return {
      zhCn,
      setingsRef,
      getThemeConfig,
      ...toRefs(state),
      uploadManagerVisible,
      hasUploadTasks,
      uploadTaskCount,
      showUploadManager,
    };
  },
});
</script>

<style lang="scss">
.global-upload-manager {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 2000;
  
  .upload-manager-button {
    width: 50px;
    height: 50px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  }
}
</style>
