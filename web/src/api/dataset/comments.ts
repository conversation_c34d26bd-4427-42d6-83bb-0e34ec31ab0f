import request from '@/utils/request'

/**
 * 获取评论列表
 * @param params 查询参数 { model: number, page?: number, limit?: number }
 * @returns 返回评论列表数据
 */
export function getCommentList(params: object) {
  return request({
    url: '/dataset/comments/',
    method: 'get',
    params
  })
}

/**
 * 创建评论
 * @param data 评论数据 { model: number, content: string, parent_id?: number }
 * @returns 返回创建的评论信息
 */
export function createComment(data: object) {
  return request({
    url: '/dataset/comments/',
    method: 'post',
    data
  })
}

/**
 * 更新评论
 * @param id 评论ID
 * @param data 更新的评论数据 { content: string }
 * @returns 返回更新后的评论信息
 */
export function updateComment(id: number, data: object) {
  return request({
    url: '/dataset/comments/' + id + '/',
    method: 'put',
    data
  })
}

/**
 * 删除评论
 * @param id 评论ID
 * @returns 返回操作结果
 */
export function deleteComment(id: number) {
  return request({
    url: '/dataset/comments/' + id + '/',
    method: 'delete'
  })
} 