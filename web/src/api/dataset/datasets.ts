import request from '@/utils/request'

/**
 * 获取数据集列表
 * @param params 查询参数 { page: number, limit: number, category?: number, category_ids?: number[] }
 * @returns 返回数据集列表数据
 */
export function getDatasetList(params: object) {
  return request({
    url: '/dataset/datasets/',
    method: 'get',
    params,
    paramsSerializer: (params: any) => {
      // 确保数组参数如category_ids正确序列化为category_ids=1&category_ids=2的形式
      const queryParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (Array.isArray(params[key])) {
          params[key].forEach((value: any) => {
            queryParams.append(key, value);
          });
        } else if (params[key] !== undefined) {
          queryParams.append(key, params[key]);
        }
      });
      return queryParams.toString();
    }
  })
}

/**
 * 获取数据集详情
 * @param id 数据集ID
 * @returns 返回数据集详细信息
 */
export function getDataset(id: number) {
  return request({
    url: '/dataset/datasets/' + id + '/',
    method: 'get'
  })
}

/**
 * 创建新数据集
 * @param data 数据集数据
 * @returns 返回创建的数据集信息
 */
export function createDataset(data: object) {
  return request({
    url: '/dataset/datasets/',
    method: 'post',
    data
  })
}

/**
 * 更新数据集信息
 * @param id 数据集ID
 * @param data 更新的数据集数据
 * @returns 返回更新后的数据集信息
 */
export function updateDataset(id: number, data: object) {
  return request({
    url: '/dataset/datasets/' + id + '/',
    method: 'put',
    data
  })
}

/**
 * 删除数据集
 * @param id 数据集ID
 * @returns 返回操作结果
 */
export function deleteDataset(id: number) {
  return request({
    url: '/dataset/datasets/' + id + '/',
    method: 'delete'
  })
}

/**
 * 获取数据集文件的下载链接
 * @param datasetId 数据集ID
 * @param fileName 文件名
 * @returns 返回文件的下载URL
 */
export function getDatasetFileUrl(datasetId: number, fileName: string) {
  return request({
    url: '/dataset/datasets/' + datasetId + '/file_url/',
    method: 'get',
    params: {
      file_path: fileName
    }
  })
}

/**
 * 收藏数据集
 * @param id 数据集ID
 * @returns 返回操作结果
 */
export function starDataset(id: number) {
  return request({
    url: '/dataset/datasets/' + id + '/star/',
    method: 'post'
  })
}

/**
 * 下载数据集
 * @param id 数据集ID
 * @returns 返回操作结果
 */
export function downloadDataset(id: number) {
  return request({
    url: '/dataset/datasets/' + id + '/download/',
    method: 'post'
  })
}

/**
 * 上传数据集文件
 * @param data 数据集数据和文件
 * @returns 返回上传结果
 */
export function uploadDataset(data: FormData) {
  return request({
    url: '/dataset/datasets/upload/',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    transformRequest: [(data) => data]
  })
}

/**
 * 下载数据集文件(支持进度显示)
 * @param datasetId 数据集ID
 * @param fileName 文件名
 * @param onProgress 进度回调函数
 * @returns 返回下载的文件blob
 */
export function downloadDatasetFile(datasetId: number, fileName: string, onProgress?: (progress: number) => void) {
  return request({
    url: '/dataset/datasets/' + datasetId + '/download_file/',
    method: 'get',
    params: {
      file_path: fileName
    },
    responseType: 'blob',
    onDownloadProgress: (progressEvent: any) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

/**
 * 获取预签名上传URL
 * @param data 文件信息
 * @returns 返回预签名URL
 */
export function getUploadUrl(data: {
  filename: string
  name: string
  group?: string
}) {
  return request({
    url: '/dataset/datasets/get_upload_url/',
    method: 'post',
    data
  })
} 