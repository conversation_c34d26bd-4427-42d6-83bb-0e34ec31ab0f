import request from '@/utils/request'

/**
 * 数据集分类相关API
 */
export function getDatasetCategoryList(params?: any) {
  return request({
    url: '/dataset/categories/',
    method: 'get',
    params
  })
}

export function getDatasetCategoryTree() {
  return request({
    url: '/dataset/categories/tree/',
    method: 'get'
  })
}

export function createDatasetCategory(data: any) {
  return request({
    url: '/dataset/categories/',
    method: 'post',
    data
  })
}

export function updateDatasetCategory(id: number, data: any) {
  return request({
    url: `/dataset/categories/${id}/`,
    method: 'put',
    data
  })
}

export function deleteDatasetCategory(id: number) {
  return request({
    url: `/dataset/categories/${id}/`,
    method: 'delete'
  })
}