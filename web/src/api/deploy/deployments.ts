import request from '@/utils/request'

/**
 * 获取部署列表
 * @param params 查询参数
 * @returns 返回部署列表数据
 */
export function getDeploymentList(params: object) {
  return request({
    url: '/deploy/deployments/',
    method: 'get',
    params
  })
}

/**
 * 获取部署详情
 * @param id 部署ID
 * @returns 返回部署详细信息
 */
export function getDeployment(id: number) {
  return request({
    url: '/deploy/deployments/' + id + '/',
    method: 'get'
  })
}

/**
 * 创建部署
 * @param data 部署数据
 * @returns 返回创建的部署信息
 */
export function createDeployment(data: object) {
  return request({
    url: '/deploy/deployments/',
    method: 'post',
    data
  })
}

/**
 * 部署模型
 * @param data 部署配置
 * @returns 返回部署结果
 */
export function deployModel(data: {
  model_version_id: number
  deployment_name: string
  service_port?: number
  deploy_config?: object
}) {
  return request({
    url: '/deploy/deployments/deploy/',
    method: 'post',
    data
  })
}

/**
 * 停止部署
 * @param id 部署ID
 * @returns 返回操作结果
 */
export function stopDeployment(id: number) {
  return request({
    url: '/deploy/deployments/' + id + '/stop/',
    method: 'post'
  })
}

/**
 * 重启部署
 * @param id 部署ID
 * @returns 返回操作结果
 */
export function restartDeployment(id: number) {
  return request({
    url: '/deploy/deployments/' + id + '/restart/',
    method: 'post'
  })
}

/**
 * 删除部署
 * @param id 部署ID
 * @returns 返回操作结果
 */
export function removeDeployment(id: number) {
  return request({
    url: '/deploy/deployments/' + id + '/remove/',
    method: 'delete'
  })
}

/**
 * 获取部署日志
 * @param id 部署ID
 * @param tail 获取最后几行日志
 * @returns 返回日志内容
 */
export function getDeploymentLogs(id: number, tail: number = 100) {
  return request({
    url: '/deploy/deployments/' + id + '/logs/',
    method: 'get',
    params: { tail }
  })
}

/**
 * 获取部署状态
 * @param id 部署ID
 * @returns 返回状态信息
 */
export function getDeploymentStatus(id: number) {
  return request({
    url: '/deploy/deployments/' + id + '/status/',
    method: 'get'
  })
}

/**
 * 获取部署统计信息
 * @returns 返回统计数据
 */
export function getDeploymentStats() {
  return request({
    url: '/deploy/deployments/stats/',
    method: 'get'
  })
}
