import request from '@/utils/request'

/**
 * 获取服务指标列表
 * @param params 查询参数
 * @returns 返回指标列表数据
 */
export function getMetricsList(params: object) {
  return request({
    url: '/deploy/metrics/',
    method: 'get',
    params
  })
}

/**
 * 获取图表数据
 * @param params 查询参数 { service_id: number, hours: number }
 * @returns 返回图表数据
 */
export function getChartData(params: {
  service_id: number
  hours?: number
}) {
  return request({
    url: '/deploy/metrics/chart_data/',
    method: 'get',
    params
  })
}

/**
 * 获取推理日志列表
 * @param params 查询参数
 * @returns 返回日志列表数据
 */
export function getInferenceLogList(params: object) {
  return request({
    url: '/deploy/logs/',
    method: 'get',
    params
  })
}
