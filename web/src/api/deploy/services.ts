import request from '@/utils/request'

/**
 * 获取服务列表
 * @param params 查询参数
 * @returns 返回服务列表数据
 */
export function getServiceList(params: object) {
  return request({
    url: '/deploy/services/',
    method: 'get',
    params
  })
}

/**
 * 获取服务详情
 * @param id 服务ID
 * @returns 返回服务详细信息
 */
export function getService(id: number) {
  return request({
    url: '/deploy/services/' + id + '/',
    method: 'get'
  })
}

/**
 * 服务健康检查
 * @param id 服务ID
 * @returns 返回健康检查结果
 */
export function healthCheckService(id: number) {
  return request({
    url: '/deploy/services/' + id + '/health_check/',
    method: 'post'
  })
}

/**
 * 获取服务统计信息
 * @param id 服务ID
 * @returns 返回统计数据
 */
export function getServiceStats(id: number) {
  return request({
    url: '/deploy/services/' + id + '/stats/',
    method: 'get'
  })
}

/**
 * 获取服务推理日志
 * @param id 服务ID
 * @param params 查询参数
 * @returns 返回日志列表
 */
export function getServiceLogs(id: number, params: object = {}) {
  return request({
    url: '/deploy/services/' + id + '/logs/',
    method: 'get',
    params
  })
}

/**
 * 模型推理
 * @param id 服务ID
 * @param formData 包含图像文件和参数的FormData
 * @returns 返回推理结果
 */
export function predictImage(id: number, formData: FormData) {
  return request({
    url: '/deploy/services/' + id + '/predict/',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 通用推理接口
 * @param serviceId 服务ID
 * @param formData 包含图像文件和参数的FormData
 * @returns 返回推理结果
 */
export function inferencePredict(serviceId: number, formData: FormData) {
  return request({
    url: '/deploy/inference/predict/' + serviceId + '/',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 批量推理接口
 * @param serviceId 服务ID
 * @param formData 包含多个图像文件和参数的FormData
 * @returns 返回批量推理结果
 */
export function inferenceBatchPredict(serviceId: number, formData: FormData) {
  return request({
    url: '/deploy/inference/batch_predict/' + serviceId + '/',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 服务健康检查（通用接口）
 * @param serviceId 服务ID
 * @returns 返回健康检查结果
 */
export function inferenceHealthCheck(serviceId: number) {
  return request({
    url: '/deploy/inference/health/' + serviceId + '/',
    method: 'get'
  })
}
