import request from '@/utils/request'

/**
 * 获取模型列表
 * @param params 查询参数 { page: number, limit: number, category?: number, category_ids?: number[] }
 * @returns 返回模型列表数据
 */
export function getModelList(params: object) {
  return request({
    url: '/model/models/',
    method: 'get',
    params,
    paramsSerializer: (params: any) => {
      // 确保数组参数如category_ids正确序列化为category_ids=1&category_ids=2的形式
      const queryParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (Array.isArray(params[key])) {
          params[key].forEach((value: any) => {
            queryParams.append(key, value);
          });
        } else if (params[key] !== undefined) {
          queryParams.append(key, params[key]);
        }
      });
      return queryParams.toString();
    }
  })
}

/**
 * 获取模型详情
 * @param id 模型ID
 * @returns 返回模型详细信息
 */
export function getModel(id: number) {
  return request({
    url: '/model/models/' + id + '/',
    method: 'get'
  })
}

/**
 * 创建新模型
 * @param data 模型数据
 * @returns 返回创建的模型信息
 */
export function createModel(data: object) {
  return request({
    url: '/model/models/',
    method: 'post',
    data
  })
}

/**
 * 更新模型信息
 * @param id 模型ID
 * @param data 更新的模型数据
 * @returns 返回更新后的模型信息
 */
export function updateModel(id: number, data: object) {
  return request({
    url: '/model/models/' + id + '/',
    method: 'put',
    data
  })
}

/**
 * 删除模型
 * @param id 模型ID
 * @returns 返回操作结果
 */
export function deleteModel(id: number) {
  return request({
    url: '/model/models/' + id + '/',
    method: 'delete'
  })
}

/**
 * 获取模型文件的下载链接
 * @param modelId 模型ID
 * @param fileName 文件名
 * @returns 返回文件的下载URL
 */
export function getModelFileUrl(modelId: number, fileName: string) {
  return request({
    url: '/model/models/' + modelId + '/file_url/',
    method: 'get',
    params: {
      file_path: fileName
    }
  })
}

/**
 * 收藏模型
 * @param id 模型ID
 * @returns 返回操作结果
 */
export function starModel(id: number) {
  return request({
    url: '/model/models/' + id + '/star/',
    method: 'post'
  })
}

/**
 * 下载模型
 * @param id 模型ID
 * @returns 返回操作结果
 */
export function downloadModel(id: number) {
  return request({
    url: '/model/models/' + id + '/download/',
    method: 'post'
  })
}

/**
 * 上传模型文件
 * @param data 模型数据和文件
 * @returns 返回上传结果
 */
export function uploadModel(data: FormData) {
  return request({
    url: '/model/models/upload/',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    transformRequest: [(data: any) => data]
  })
}

/**
 * 下载模型文件(支持进度显示)
 * @param modelId 模型ID
 * @param fileName 文件名
 * @param onProgress 进度回调函数
 * @returns 返回下载的文件blob
 */
export function downloadModelFile(modelId: number, fileName: string, onProgress?: (progress: number) => void) {
  return request({
    url: '/model/models/' + modelId + '/download_file/',
    method: 'get',
    params: {
      file_path: fileName
    },
    responseType: 'blob',
    onDownloadProgress: (progressEvent: any) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

/**
 * 获取预签名上传URL
 * @param data 文件信息
 * @returns 返回预签名URL
 */
export function getUploadUrl(data: {
  filename: string
  name: string
  group?: string
}) {
  return request({
    url: '/model/models/get_upload_url/',
    method: 'post',
    data
  })
}

/**
 * 语义检索模型
 * @param data 检索条件 { description: string, input?: string, output?: string }
 * @returns 返回匹配的模型列表
 */
export function semanticSearchModels(data: {
  description: string
  input?: string
  output?: string
}) {
  return request({
    url: '/model/models/semantic_search/',
    method: 'post',
    data
  })
}

/**
 * 删除模型（包括所有版本和文件）
 * @param modelId 模型ID
 * @returns 请求结果
 */
export function deleteModelWithFiles(modelId: string | number) {
  return request({
    url: '/model/models/delete_model/',
    method: 'post',
    data: { model_id: modelId },
  });
}

/**
 * 检查模型编码是否唯一
 * @param code 模型编码
 * @param id 当前模型ID（编辑模式下使用）
 * @returns 请求结果
 */
export function checkModelCodeUnique(code: string, id?: string | number) {
  return request({
    url: '/model/models/check_code_unique/',
    method: 'post',
    data: { code, id },
  });
} 