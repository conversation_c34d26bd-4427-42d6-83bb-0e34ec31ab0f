import request from '@/utils/request'

/**
 * 获取模型版本列表
 * @param params 查询参数 { model_id: number }
 * @returns 返回版本列表数据
 */
export function getVersionList(params: object) {
  return request({
    url: '/model/versions/',
    method: 'get',
    params
  })
}

/**
 * 获取模型版本详情
 * @param id 版本ID
 * @returns 返回版本详细信息
 */
export function getVersion(id: number) {
  return request({
    url: '/model/versions/' + id + '/',
    method: 'get'
  })
}

/**
 * 创建新版本
 * @param data 版本数据 {model_id: number, version_number: number, description: string}
 * @returns 返回创建的版本信息
 */
export function createVersion(data: object) {
  return request({
    url: '/model/versions/',
    method: 'post',
    data
  })
}

/**
 * 发布版本
 * @param id 版本ID
 * @returns 返回操作结果
 */
export function publishVersion(id: number) {
  return request({
    url: '/model/versions/' + id + '/publish/',
    method: 'post'
  })
}

/**
 * 删除版本
 * @param id 版本ID
 * @returns 返回操作结果
 */
export function deleteVersion(id: number) {
  return request({
    url: '/model/versions/' + id + '/',
    method: 'delete'
  })
}

/**
 * 获取版本文件上传预签名URL
 * @param versionId 版本ID
 * @param data 文件信息 {filename: string, content_type?: string}
 * @returns 返回预签名URL
 */
export function getVersionUploadUrl(versionId: number, data: object) {
  return request({
    url: '/model/versions/' + versionId + '/get_upload_url/',
    method: 'post',
    data
  })
}

/**
 * 获取分片上传的预签名URL
 * @param versionId 版本ID
 * @param data 文件信息 {filename: string, content_type?: string, file_size: number, use_multipart: true}
 * @returns 返回分片上传信息
 */
export function getMultipartUploadUrl(versionId: number, data: object) {
  return request({
    url: '/model/versions/' + versionId + '/get_upload_url/',
    method: 'post',
    data: {
      ...data,
      use_multipart: true
    }
  })
}

/**
 * 完成分片上传
 * @param versionId 版本ID
 * @param data 分片信息 {upload_id: string, object_name: string, parts: Array<{part_number: number, etag: string}>, file_type: string}
 * @returns 返回操作结果
 */
export function completeMultipartUpload(versionId: number, data: object) {
  return request({
    url: '/model/versions/' + versionId + '/complete_multipart_upload/',
    method: 'post',
    data
  })
}

/**
 * 取消分片上传
 * @param versionId 版本ID
 * @param data 分片信息 {upload_id: string, object_name: string}
 * @returns 返回操作结果
 */
export function abortMultipartUpload(versionId: number, data: object) {
  return request({
    url: '/model/versions/' + versionId + '/abort_multipart_upload/',
    method: 'post',
    data
  })
}

/**
 * 更新版本文件
 * @param versionId 版本ID
 * @param data 文件操作数据
 * @returns 返回操作结果
 */
export function updateVersionFiles(versionId: number, data: object) {
  return request({
    url: '/model/versions/' + versionId + '/update_files/',
    method: 'post',
    data
  })
}

/**
 * 下载版本的所有文件（打包）
 * @param id 版本ID
 * @returns 返回打包文件的下载链接
 */
export function downloadVersionAllFiles(id: number) {
  return request({
    url: '/model/versions/' + id + '/download_all/',
    method: 'get'
  })
}

/**
 * 获取版本文件打包状态
 * @param id 版本ID
 * @returns 返回打包状态信息
 */
export function getPackageStatus(id: number) {
  return request({
    url: '/model/versions/' + id + '/package_status/',
    method: 'get'
  })
}

/**
 * 更新版本信息（如docker_image、权重、报告等）
 * @param id 版本ID
 * @param data 需要更新的字段
 * @returns 返回操作结果
 */
export function updateVersion(id: number, data: object) {
  return request({
    url: '/model/versions/' + id + '/',
    method: 'patch',
    data
  })
}

/**
 * 删除MinIO文件
 */
export function deleteFileFromMinio(data: { file_path: string }) {
  return request({
    url: '/model/versions/delete_file_from_minio/',
    method: 'post',
    data
  });
} 