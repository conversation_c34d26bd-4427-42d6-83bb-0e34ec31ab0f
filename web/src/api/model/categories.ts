import request from '@/utils/request'

/**
 * 模型分类相关API
 */
export function getModelCategoryList(params?: any) {
  return request({
    url: '/model/categories/',
    method: 'get',
    params
  })
}

export function getModelCategoryTree() {
  return request({
    url: '/model/categories/tree/',
    method: 'get'
  })
}

export function createModelCategory(data: any) {
  return request({
    url: '/model/categories/',
    method: 'post',
    data
  })
}

export function updateModelCategory(id: number, data: any) {
  return request({
    url: `/model/categories/${id}/`,
    method: 'put',
    data
  })
}

export function deleteModelCategory(id: number) {
  return request({
    url: `/model/categories/${id}/`,
    method: 'delete'
  })
}