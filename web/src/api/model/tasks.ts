import request from '@/utils/request'

/**
 * OCR识别图片中的文本
 * @param data 包含图片文件的FormData对象
 * @returns 返回识别结果
 */
export function ocrRecognize(data: FormData) {
  return request({
    url: '/model/tasks/ocr/',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    transformRequest: [(data: any) => data]
  })
}

/**
 * 翻译文本
 * @param data 包含要翻译的文本和目标语言
 * @returns 返回翻译结果
 */
export function translateText(data: {
  text: string
  target_language: string
  model_id?: string
}) {
  return request({
    url: '/model/tasks/translate/',
    method: 'post',
    data
  })
}

/**
 * 执行任务链（处理流程）
 * @param data 任务链定义和参数
 * @returns 返回执行结果
 */
export function executeTaskChain(data: {
  tasks: Array<{
    type: string
    params: any
    next?: string
  }>
  model_ids?: string[]
  initial_task: string
}) {
  return request({
    url: '/model/tasks/chain/',
    method: 'post',
    data
  })
}

/**
 * 获取任务执行状态
 * @param taskId 任务ID
 * @returns 返回任务状态和结果
 */
export function getTaskStatus(taskId: string) {
  return request({
    url: `/model/tasks/status/${taskId}/`,
    method: 'get'
  })
}

/**
 * 任务规划器API - 分解任务为子任务
 * @param data 包含任务描述的数据
 */
export function planTask(data: any) {
  return request({
    url: '/multi-agent/task-planner/',
    method: 'post',
    data,
  });
}

/**
 * 专家智能体API - 执行特定子任务
 * @param data 包含专家角色、任务描述和任务输入的数据
 */
export function executeExpertTask(data: any) {
  return request({
    url: '/multi-agent/expert-agent/',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 任务执行API - 协调和执行整个任务流程
 * @param data 包含任务描述和其他必要参数的数据
 */
export function executeTask(data: any) {
  return request({
    url: '/multi-agent/task-execution/',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
} 