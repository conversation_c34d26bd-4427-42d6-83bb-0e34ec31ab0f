import request from '@/utils/request'

/**
 * 算法库分类相关API
 */
export function getAlgorithmCategoryList(params?: any) {
  return request({
    url: '/algorithm/categories/',
    method: 'get',
    params
  })
}

export function getAlgorithmCategoryTree() {
  return request({
    url: '/algorithm/categories/tree/',
    method: 'get'
  })
}

export function createAlgorithmCategory(data: any) {
  return request({
    url: '/algorithm/categories/',
    method: 'post',
    data
  })
}

export function updateAlgorithmCategory(id: number, data: any) {
  return request({
    url: `/algorithm/categories/${id}/`,
    method: 'put',
    data
  })
}

export function deleteAlgorithmCategory(id: number) {
  return request({
    url: `/algorithm/categories/${id}/`,
    method: 'delete'
  })
}