import request from '@/utils/request'

/**
 * 获取算法库列表
 * @param params 查询参数 { page: number, limit: number, category?: number, category_ids?: number[] }
 * @returns 返回算法库列表算法
 */
export function getAlgorithmList(params: object) {
  return request({
    url: '/algorithm/algorithms/',
    method: 'get',
    params,
    paramsSerializer: (params: any) => {
      // 确保数组参数如category_ids正确序列化为category_ids=1&category_ids=2的形式
      const queryParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (Array.isArray(params[key])) {
          params[key].forEach((value: any) => {
            queryParams.append(key, value);
          });
        } else if (params[key] !== undefined) {
          queryParams.append(key, params[key]);
        }
      });
      return queryParams.toString();
    }
  })
}

/**
 * 获取算法库详情
 * @param id 算法库ID
 * @returns 返回算法库详细信息
 */
export function getAlgorithm(id: number) {
  return request({
    url: '/algorithm/algorithms/' + id + '/',
    method: 'get'
  })
}

/**
 * 创建新算法库
 * @param data 算法库算法
 * @returns 返回创建的算法库信息
 */
export function createAlgorithm(data: object) {
  return request({
    url: '/algorithm/algorithms/',
    method: 'post',
    data
  })
}

/**
 * 更新算法库信息
 * @param id 算法库ID
 * @param data 更新的算法库算法
 * @returns 返回更新后的算法库信息
 */
export function updateAlgorithm(id: number, data: object) {
  return request({
    url: '/algorithm/algorithms/' + id + '/',
    method: 'put',
    data
  })
}

/**
 * 删除算法库
 * @param id 算法库ID
 * @returns 返回操作结果
 */
export function deleteAlgorithm(id: number) {
  return request({
    url: '/algorithm/algorithms/' + id + '/',
    method: 'delete'
  })
}

/**
 * 获取算法库文件的下载链接
 * @param algorithmId 算法库ID
 * @param fileName 文件名
 * @returns 返回文件的下载URL
 */
export function getAlgorithmFileUrl(algorithmId: number, fileName: string) {
  return request({
    url: '/algorithm/algorithms/' + algorithmId + '/file_url/',
    method: 'get',
    params: {
      file_path: fileName
    }
  })
}

/**
 * 收藏算法库
 * @param id 算法库ID
 * @returns 返回操作结果
 */
export function starAlgorithm(id: number) {
  return request({
    url: '/algorithm/algorithms/' + id + '/star/',
    method: 'post'
  })
}

/**
 * 下载算法库
 * @param id 算法库ID
 * @returns 返回操作结果
 */
export function downloadAlgorithm(id: number) {
  return request({
    url: '/algorithm/algorithms/' + id + '/download/',
    method: 'post'
  })
}

/**
 * 上传算法库文件
 * @param data 算法库算法和文件
 * @returns 返回上传结果
 */
export function uploadAlgorithm(data: FormData) {
  return request({
    url: '/algorithm/algorithms/upload/',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    transformRequest: [(data) => data]
  })
}

/**
 * 下载算法库文件(支持进度显示)
 * @param algorithmId 算法库ID
 * @param fileName 文件名
 * @param onProgress 进度回调函数
 * @returns 返回下载的文件blob
 */
export function downloadAlgorithmFile(algorithmId: number, fileName: string, onProgress?: (progress: number) => void) {
  return request({
    url: '/algorithm/algorithms/' + algorithmId + '/download_file/',
    method: 'get',
    params: {
      file_path: fileName
    },
    responseType: 'blob',
    onDownloadProgress: (progressEvent: any) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

/**
 * 获取预签名上传URL
 * @param data 文件信息
 * @returns 返回预签名URL
 */
export function getUploadUrl(data: {
  filename: string
  name: string
  group?: string
}) {
  return request({
    url: '/algorithm/algorithms/get_upload_url/',
    method: 'post',
    data
  })
}

/**
 * 获取算法库文件树
 * @param algorithmId 算法库ID
 * @returns 返回文件树结构
 */
export function getAlgorithmFileTree(algorithmId: number) {
  return request({
    url: `/algorithm/algorithms/${algorithmId}/file_tree/`,
    method: 'get'
  })
}

/**
 * 获取算法库README内容
 * @param algorithmId 算法库ID
 * @returns 返回README内容
 */
export function getAlgorithmReadme(algorithmId: number) {
  return request({
    url: `/algorithm/algorithms/${algorithmId}/readme/`,
    method: 'get'
  })
}

/**
 * 更新算法库README内容
 * @param algorithmId 算法库ID
 * @param data README内容
 * @returns 返回更新结果
 */
export function updateAlgorithmReadme(algorithmId: number, data: { content: string }) {
  return request({
    url: `/algorithm/algorithms/${algorithmId}/update_readme/`,
    method: 'post',
    data
  })
}

/**
 * 获取算法库文件内容
 * @param algorithmId 算法库ID
 * @param params 文件路径参数
 * @returns 返回文件内容
 */
export function getAlgorithmFileContent(algorithmId: number, params: { file_path: string }) {
  return request({
    url: `/algorithm/algorithms/${algorithmId}/file_content/`,
    method: 'get',
    params
  })
}

/**
 * 获取算法库主文档内容
 * @param algorithmId 算法库ID
 * @returns 返回主文档内容
 */
export function getAlgorithmMainDoc(algorithmId: number) {
  return request({
    url: `/algorithm/algorithms/${algorithmId}/main_doc/`,
    method: 'get'
  })
}