import { defineStore } from 'pinia'

interface UploadFile {
  name: string
  size: number
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  raw: File
  relativePath: string
  customPath?: string
}

interface UploadChunk {
  index: number
  start: number
  end: number
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  etag?: string
}

interface UploadTask {
  id: string
  fileName: string
  fileSize: number
  fileType: string
  uploadId?: string
  objectName?: string
  versionId?: number
  fileCategory?: string
  chunks: UploadChunk[]
  totalProgress: number
  status: 'pending' | 'uploading' | 'paused' | 'success' | 'error'
  errorMessage?: string
}

export const useUploadStore = defineStore('upload', {
  state: () => ({
    visible: false,
    files: [] as UploadFile[],
    uploadTasks: [] as UploadTask[],
    currentUploads: 0,
    maxConcurrentUploads: 3,
    chunkSize: 5 * 1024 * 1024, // 5MB
  }),
  
  getters: {
    pendingTasks(): UploadTask[] {
      return this.uploadTasks.filter(task => 
        task.status === 'pending' || task.status === 'paused'
      )
    },
    
    activeTasks(): UploadTask[] {
      return this.uploadTasks.filter(task => task.status === 'uploading')
    },
    
    completedTasks(): UploadTask[] {
      return this.uploadTasks.filter(task => 
        task.status === 'success' || task.status === 'error'
      )
    },
    
    hasPendingTasks(): boolean {
      return this.pendingTasks.length > 0
    }
  },
  
  actions: {
    addFile(file: File, customPath?: string) {
      const fileInfo: UploadFile = {
        raw: file,
        name: file.name,
        size: file.size,
        progress: 0,
        status: 'pending',
        relativePath: file.webkitRelativePath || file.name
      }
      
      // 如果提供了自定义路径，使用它
      if (customPath) {
        fileInfo.customPath = customPath;
      }
      
      this.files.push(fileInfo)
      this.visible = true
    },

    updateFileProgress(fileName: string, progress: number) {
      const file = this.files.find(f => f.name === fileName)
      if (file) {
        file.progress = progress
        file.status = progress < 100 ? 'uploading' : 'success'
      }
    },

    updateFileStatus(fileName: string, status: UploadFile['status']) {
      const file = this.files.find(f => f.name === fileName)
      if (file) {
        file.status = status
      }
    },

    clearFiles() {
      this.files = []
      this.visible = false
    },
    
    // 断点续传相关方法
    createUploadTask(file: File, versionId: number, fileCategory: string): UploadTask {
      const taskId = `${file.name}_${file.size}_${Date.now()}`
      const totalChunks = Math.ceil(file.size / this.chunkSize)
      
      // 创建分片信息
      const chunks: UploadChunk[] = []
      for (let i = 0; i < totalChunks; i++) {
        const start = i * this.chunkSize
        const end = Math.min(file.size, start + this.chunkSize)
        chunks.push({
          index: i,
          start,
          end,
          progress: 0,
          status: 'pending'
        })
      }
      
      // 创建上传任务
      const task: UploadTask = {
        id: taskId,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        versionId,
        fileCategory,
        chunks,
        totalProgress: 0,
        status: 'pending'
      }
      
      // 保存到本地存储
      this.saveTaskToLocalStorage(task)
      
      // 添加到任务列表
      this.uploadTasks.push(task)
      
      return task
    },
    
    getUploadTask(taskId: string): UploadTask | undefined {
      return this.uploadTasks.find(task => task.id === taskId)
    },
    
    updateTaskProgress(taskId: string, chunkIndex: number, progress: number) {
      const task = this.getUploadTask(taskId)
      if (task && task.chunks[chunkIndex]) {
        // 更新分片进度
        task.chunks[chunkIndex].progress = progress
        
        // 更新总进度
        this.recalculateTaskProgress(task)
        
        // 保存到本地存储
        this.saveTaskToLocalStorage(task)
      }
    },
    
    updateChunkStatus(taskId: string, chunkIndex: number, status: UploadChunk['status'], etag?: string) {
      const task = this.getUploadTask(taskId)
      if (task && task.chunks[chunkIndex]) {
        // 更新分片状态
        task.chunks[chunkIndex].status = status
        
        // 如果上传成功，保存ETag
        if (status === 'success' && etag) {
          task.chunks[chunkIndex].etag = etag
        }
        
        // 检查任务是否完成
        this.checkTaskCompletion(task)
        
        // 保存到本地存储
        this.saveTaskToLocalStorage(task)
      }
    },
    
    updateTaskStatus(taskId: string, status: UploadTask['status'], errorMessage?: string) {
      const task = this.getUploadTask(taskId)
      if (task) {
        task.status = status
        
        if (errorMessage) {
          task.errorMessage = errorMessage
        }
        
        // 如果任务完成或失败，更新计数器
        if (status === 'success' || status === 'error') {
          this.currentUploads--
        } else if (status === 'uploading') {
          this.currentUploads++
        }
        
        // 保存到本地存储
        this.saveTaskToLocalStorage(task)
        
        // 如果有等待的任务，开始上传
        this.processNextPendingTask()
      }
    },
    
    setTaskUploadId(taskId: string, uploadId: string, objectName: string) {
      const task = this.getUploadTask(taskId)
      if (task) {
        task.uploadId = uploadId
        task.objectName = objectName
        
        // 保存到本地存储
        this.saveTaskToLocalStorage(task)
      }
    },
    
    recalculateTaskProgress(task: UploadTask) {
      // 计算总进度
      const totalProgress = task.chunks.reduce((sum, chunk) => sum + chunk.progress, 0) / task.chunks.length
      task.totalProgress = Math.round(totalProgress)
      
      // 更新对应的文件进度
      const file = this.files.find(f => f.name === task.fileName)
      if (file) {
        file.progress = task.totalProgress
        file.status = task.status === 'success' ? 'success' : 
                      task.status === 'error' ? 'error' : 'uploading'
      }
    },
    
    checkTaskCompletion(task: UploadTask) {
      // 检查所有分片是否都已上传成功
      const allChunksUploaded = task.chunks.every(chunk => chunk.status === 'success')
      
      if (allChunksUploaded) {
        task.status = 'success'
        task.totalProgress = 100
        
        // 更新对应的文件状态
        const file = this.files.find(f => f.name === task.fileName)
        if (file) {
          file.progress = 100
          file.status = 'success'
        }
        
        // 更新计数器
        this.currentUploads--
        
        // 处理下一个任务
        this.processNextPendingTask()
      }
    },
    
    processNextPendingTask() {
      // 如果当前上传数小于最大并发数，开始上传等待的任务
      if (this.currentUploads < this.maxConcurrentUploads && this.pendingTasks.length > 0) {
        const nextTask = this.pendingTasks[0]
        this.updateTaskStatus(nextTask.id, 'uploading')
      }
    },
    
    saveTaskToLocalStorage(task: UploadTask) {
      try {
        localStorage.setItem(`upload_task_${task.id}`, JSON.stringify(task))
      } catch (e) {
        console.error('保存上传任务到本地存储失败:', e)
      }
    },
    
    loadTasksFromLocalStorage() {
      try {
        // 查找所有以 upload_task_ 开头的存储项
        const tasks: UploadTask[] = []
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith('upload_task_')) {
            const taskData = localStorage.getItem(key)
            if (taskData) {
              const task = JSON.parse(taskData) as UploadTask
              tasks.push(task)
            }
          }
        }
        
        // 添加到任务列表
        this.uploadTasks = tasks
        
        // 更新当前上传数
        this.currentUploads = this.activeTasks.length
      } catch (e) {
        console.error('从本地存储加载上传任务失败:', e)
      }
    },
    
    clearTask(taskId: string) {
      // 从任务列表中移除
      this.uploadTasks = this.uploadTasks.filter(task => task.id !== taskId)
      
      // 从本地存储中移除
      localStorage.removeItem(`upload_task_${taskId}`)
    },
    
    clearCompletedTasks() {
      // 获取已完成的任务ID
      const completedTaskIds = this.completedTasks.map(task => task.id)
      
      // 从任务列表中移除
      this.uploadTasks = this.uploadTasks.filter(task => 
        task.status !== 'success' && task.status !== 'error'
      )
      
      // 从本地存储中移除
      completedTaskIds.forEach(id => {
        localStorage.removeItem(`upload_task_${id}`)
      })
    },
    
    pauseTask(taskId: string) {
      const task = this.getUploadTask(taskId)
      if (task && task.status === 'uploading') {
        this.updateTaskStatus(taskId, 'paused')
        this.currentUploads--
        this.processNextPendingTask()
      }
    },
    
    resumeTask(taskId: string) {
      const task = this.getUploadTask(taskId)
      if (task && task.status === 'paused') {
        // 如果当前上传数小于最大并发数，立即恢复上传
        if (this.currentUploads < this.maxConcurrentUploads) {
          this.updateTaskStatus(taskId, 'uploading')
        } else {
          // 否则将状态改为等待
          task.status = 'pending'
          this.saveTaskToLocalStorage(task)
        }
      }
    }
  }
}) 