/**
* scss 怎么动态创建变量
* 本来想用 @function，@for 好像不可以动态创建
**/

/* 定义初始颜色
------------------------------- */
$--color-primary: #569CFF !default;
$--color-whites: #ffffff !default;
$--color-primary-light-1: mix($--color-whites, $--color-primary, 10%) !default;
$--color-primary-light-2: mix($--color-whites, $--color-primary, 20%) !default;
$--color-primary-light-3: mix($--color-whites, $--color-primary, 30%) !default;
$--color-primary-light-4: mix($--color-whites, $--color-primary, 40%) !default;
$--color-primary-light-5: mix($--color-whites, $--color-primary, 50%) !default;
$--color-primary-light-6: mix($--color-whites, $--color-primary, 60%) !default;
$--color-primary-light-7: mix($--color-whites, $--color-primary, 70%) !default;
$--color-primary-light-8: mix($--color-whites, $--color-primary, 80%) !default;
$--color-primary-light-9: mix($--color-whites, $--color-primary, 90%) !default;
$--color-success: #67c23a !default;
$--color-success-light-1: mix($--color-whites, $--color-success, 10%) !default;
$--color-success-light-2: mix($--color-whites, $--color-success, 20%) !default;
$--color-success-light-3: mix($--color-whites, $--color-success, 30%) !default;
$--color-success-light-4: mix($--color-whites, $--color-success, 40%) !default;
$--color-success-light-5: mix($--color-whites, $--color-success, 50%) !default;
$--color-success-light-6: mix($--color-whites, $--color-success, 60%) !default;
$--color-success-light-7: mix($--color-whites, $--color-success, 70%) !default;
$--color-success-light-8: mix($--color-whites, $--color-success, 80%) !default;
$--color-success-light-9: mix($--color-whites, $--color-success, 90%) !default;
$--color-info: #909399 !default;
$--color-info-light-1: mix($--color-whites, $--color-info, 10%) !default;
$--color-info-light-2: mix($--color-whites, $--color-info, 20%) !default;
$--color-info-light-3: mix($--color-whites, $--color-info, 30%) !default;
$--color-info-light-4: mix($--color-whites, $--color-info, 40%) !default;
$--color-info-light-5: mix($--color-whites, $--color-info, 50%) !default;
$--color-info-light-6: mix($--color-whites, $--color-info, 60%) !default;
$--color-info-light-7: mix($--color-whites, $--color-info, 70%) !default;
$--color-info-light-8: mix($--color-whites, $--color-info, 80%) !default;
$--color-info-light-9: mix($--color-whites, $--color-info, 90%) !default;
$--color-warning: #e6a23c !default;
$--color-warning-light-1: mix($--color-whites, $--color-warning, 10%) !default;
$--color-warning-light-2: mix($--color-whites, $--color-warning, 20%) !default;
$--color-warning-light-3: mix($--color-whites, $--color-warning, 30%) !default;
$--color-warning-light-4: mix($--color-whites, $--color-warning, 40%) !default;
$--color-warning-light-5: mix($--color-whites, $--color-warning, 50%) !default;
$--color-warning-light-6: mix($--color-whites, $--color-warning, 60%) !default;
$--color-warning-light-7: mix($--color-whites, $--color-warning, 70%) !default;
$--color-warning-light-8: mix($--color-whites, $--color-warning, 80%) !default;
$--color-warning-light-9: mix($--color-whites, $--color-warning, 90%) !default;
$--color-danger: #f56c6c !default;
$--color-danger-light-1: mix($--color-whites, $--color-danger, 10%) !default;
$--color-danger-light-2: mix($--color-whites, $--color-danger, 20%) !default;
$--color-danger-light-3: mix($--color-whites, $--color-danger, 30%) !default;
$--color-danger-light-4: mix($--color-whites, $--color-danger, 40%) !default;
$--color-danger-light-5: mix($--color-whites, $--color-danger, 50%) !default;
$--color-danger-light-6: mix($--color-whites, $--color-danger, 60%) !default;
$--color-danger-light-7: mix($--color-whites, $--color-danger, 70%) !default;
$--color-danger-light-8: mix($--color-whites, $--color-danger, 80%) !default;
$--color-danger-light-9: mix($--color-whites, $--color-danger, 90%) !default;
$--bg-topBar: #191A23;
$--bg-menuBar: #191A23;
$--bg-columnsMenuBar: #545c64;
$--bg-topBarColor: #F4F6F8;
$--bg-menuBarColor: #eaeaea;
$--bg-columnsMenuBarColor: #e6e6e6;

/* 赋值给:root
------------------------------- */
:root {
	--color-primary: #{$--color-primary};
	--color-whites: #{$--color-whites};
	--color-primary-light-1: #{$--color-primary-light-1};
	--color-primary-light-2: #{$--color-primary-light-2};
	--color-primary-light-3: #{$--color-primary-light-3};
	--color-primary-light-4: #{$--color-primary-light-4};
	--color-primary-light-5: #{$--color-primary-light-5};
	--color-primary-light-6: #{$--color-primary-light-6};
	--color-primary-light-7: #{$--color-primary-light-7};
	--color-primary-light-8: #{$--color-primary-light-8};
	--color-primary-light-9: #{$--color-primary-light-9};
	--color-success: #{$--color-success};
	--color-success-light-1: #{$--color-success-light-1};
	--color-success-light-2: #{$--color-success-light-2};
	--color-success-light-3: #{$--color-success-light-3};
	--color-success-light-4: #{$--color-success-light-4};
	--color-success-light-5: #{$--color-success-light-5};
	--color-success-light-6: #{$--color-success-light-6};
	--color-success-light-7: #{$--color-success-light-7};
	--color-success-light-8: #{$--color-success-light-8};
	--color-success-light-9: #{$--color-success-light-9};
	--color-info: #{$--color-info};
	--color-info-light-1: #{$--color-info-light-1};
	--color-info-light-2: #{$--color-info-light-2};
	--color-info-light-3: #{$--color-info-light-3};
	--color-info-light-4: #{$--color-info-light-4};
	--color-info-light-5: #{$--color-info-light-5};
	--color-info-light-6: #{$--color-info-light-6};
	--color-info-light-7: #{$--color-info-light-7};
	--color-info-light-8: #{$--color-info-light-8};
	--color-info-light-9: #{$--color-info-light-9};
	--color-warning: #{$--color-warning};
	--color-warning-light-1: #{$--color-warning-light-1};
	--color-warning-light-2: #{$--color-warning-light-2};
	--color-warning-light-3: #{$--color-warning-light-3};
	--color-warning-light-4: #{$--color-warning-light-4};
	--color-warning-light-5: #{$--color-warning-light-5};
	--color-warning-light-6: #{$--color-warning-light-6};
	--color-warning-light-7: #{$--color-warning-light-7};
	--color-warning-light-8: #{$--color-warning-light-8};
	--color-warning-light-9: #{$--color-warning-light-9};
	--color-danger: #{$--color-danger};
	--color-danger-light-1: #{$--color-danger-light-1};
	--color-danger-light-2: #{$--color-danger-light-2};
	--color-danger-light-3: #{$--color-danger-light-3};
	--color-danger-light-4: #{$--color-danger-light-4};
	--color-danger-light-5: #{$--color-danger-light-5};
	--color-danger-light-6: #{$--color-danger-light-6};
	--color-danger-light-7: #{$--color-danger-light-7};
	--color-danger-light-8: #{$--color-danger-light-8};
	--color-danger-light-9: #{$--color-danger-light-9};
	--bg-topBar: #{$--bg-topBar};
	--bg-menuBar: #{$--bg-menuBar};
	--bg-columnsMenuBar: #{$--bg-columnsMenuBar};
	--bg-topBarColor: #{$--bg-topBarColor};
	--bg-menuBarColor: #{$--bg-menuBarColor};
	--bg-columnsMenuBarColor: #{$--bg-columnsMenuBarColor};
}
