.content_box{
	width: 100%;
	display: flex;
	flex-wrap: wrap;

	.content_image_item{
		position: relative;
		margin: 12px;
		width: 300px;
		height: 220px;
		box-sizing: border-box;
		flex-direction: column;
	}
	.content_table_item{
		position: relative;
		margin: 12px;
		width: 400px;
		height: 220px;
		box-sizing: border-box;
		flex-direction: column;
	}
}

.el-card.ft-card {
	width: 100%;
	height: 100%;
	box-shadow: h-shadow v-shadow blur spread color inset;
	.ft-tag{
		padding: 10px;
		height: 40px;
		.ml-3{
			margin-left: 6px;
		}
	}
	.ft-head{
		width: 100%;
		height: 170px;
		border-bottom: 1px solid var(--color-primary);
		background: linear-gradient(141.6deg,var(--color-primary-light-8) 0%,rgba(255,255,255,0) 70%);
	}
	.ft-body{
		margin-top: 5px;
		width: 400px;
		height: 130px;
		padding: 10px;
		display: flex;
		justify-content: space-between;

		.ft-body-image{
			width: 40%;
			height: 100%;
			text-align: center;
			margin-right: 10px;
		}
		.ft-body-item{
			width: 60%;
		}
		.item-mb{
			width: 100%;
			margin-bottom: 8px;
			text-overflow: ellipsis
		}
	}

	.ft-image{
		width: 300px;
		height: 170px;
		border-bottom: 1px solid var(--color-primary)
	}

	.ft-foot{
		padding: 0 10px;
		height: 50px;
		line-height: 50px;
		display: flex;
		justify-content: space-between;

		.ft-item-name{
			font-size: 16px;
			font-weight: bold;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis
		}
	}
}


.image-slot {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	color: var(--el-text-color-secondary);
	font-size: 30px;
}
