/**
 * 模型部署相关配置
 */

// 从环境变量获取配置，如果没有则使用默认值
export const DEPLOY_CONFIG = {
  // 服务器主机地址
  SERVER_HOST: import.meta.env.VITE_MODEL_DEPLOY_SERVER_HOST || '************',
  
  // 服务器端口
  SERVER_PORT: import.meta.env.VITE_MODEL_DEPLOY_SERVER_PORT || '8000',
  
  // 获取完整的服务器地址
  get SERVER_URL() {
    return `http://${this.SERVER_HOST}:${this.SERVER_PORT}`
  },
  
  // 获取API基础路径
  get API_BASE_URL() {
    return `${this.SERVER_URL}/deploy/inference`
  }
}

/**
 * 获取系统接口URL
 */
export const getSystemApiUrl = (endpoint: string, serviceId?: number | string) => {
  const baseUrl = DEPLOY_CONFIG.API_BASE_URL
  if (serviceId) {
    return `${baseUrl}/${endpoint}/${serviceId}/`
  }
  return `${baseUrl}/${endpoint}/`
}

/**
 * 获取直接服务接口URL
 */
export const getDirectApiUrl = (serviceEndpoint: string, endpoint: string) => {
  if (!serviceEndpoint) return `http://service-ip:port/${endpoint}`
  
  // 如果serviceEndpoint已经包含了endpoint，直接返回
  if (serviceEndpoint.includes(endpoint)) {
    return serviceEndpoint
  }
  
  // 否则替换或添加endpoint
  const baseUrl = serviceEndpoint.replace(/\/predict$/, '').replace(/\/health$/, '')
  return `${baseUrl}/${endpoint}`
}

/**
 * API端点常量
 */
export const API_ENDPOINTS = {
  PREDICT: 'predict',
  BATCH_PREDICT: 'batch_predict', 
  HEALTH: 'health'
} as const

/**
 * 生成API调用示例
 */
export const generateApiExamples = (serviceInfo: any) => {
  const systemUrls = {
    predict: getSystemApiUrl(API_ENDPOINTS.PREDICT, serviceInfo?.id),
    batchPredict: getSystemApiUrl(API_ENDPOINTS.BATCH_PREDICT, serviceInfo?.id),
    health: getSystemApiUrl(API_ENDPOINTS.HEALTH, serviceInfo?.id)
  }
  
  const directUrls = {
    predict: getDirectApiUrl(serviceInfo?.api_endpoint, API_ENDPOINTS.PREDICT),
    batchPredict: getDirectApiUrl(serviceInfo?.api_endpoint, API_ENDPOINTS.BATCH_PREDICT),
    health: getDirectApiUrl(serviceInfo?.health_check_url, API_ENDPOINTS.HEALTH)
  }
  
  return {
    system: systemUrls,
    direct: directUrls
  }
}
